<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <json_preview_wizard_name> _view_form-->
    <record id="json_preview_wizard_view_form" model="ir.ui.view">
        <field name="name">iot.json_preview_wizard.form</field>
        <field name="model">iot.json_preview_wizard</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <group>
                        <field name="json_data" readonly="1"/>
                    </group>
                </sheet>
                 <!-- Add your fields here -->
                    <footer>
                        <button name="action_download_json_file" icon="fa-download" title ="Download JSON"
                                type="object" class="oe_highlight"/>

                        <button name="action_open_json_editor" icon="fa-folder" string="Open JSON Editor"
                                type="object"/>

                        <button special="cancel" string="Cancel"/>
                    </footer>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: <json_preview_wizard_name> _action-->
    <record id="json_preview_wizard_action" model="ir.actions.act_window">
        <field name="name">Models</field>
        <field name="res_model">iot.json_preview_wizard</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>

<!--    <record id="action_download_json" model="ir.actions.act_url">-->
<!--    <field name="name">Download JSON</field>-->
<!--    <field name="url">/download_json_file</field>-->
<!--    <field name="target">new</field>-->
<!--</record>-->

</odoo>