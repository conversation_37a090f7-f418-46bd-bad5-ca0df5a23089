from odoo import models, fields
import logging

from odoo.exceptions import UserError

_logger = logging.getLogger('iot-Manager')


class APIBaseModel(models.AbstractModel):
    _name = "iot_base.api_base_model"
    _inherit = ["iot_base.api_base_response_model"]
    _description = "API Base Model"

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # record_id = fields.Char(string="Record ID", index=True, required=True, default="0",
    #                         help="Integration system record id")
    name = fields.Char(string="Name", required=True, index=True, tracking=True)
    synced = fields.Boolean(tracking=True)
    push_status = fields.Selection(tracking=True,
                                   selection=[("pending", "Pending"), ("pushed", "Pushed"), ("failed", "Failed")],
                                   default="pending", store=True)
    last_sync_date = fields.Datetime()

    # endregion

    # region  Special

    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Relational Fields --------------------------------


    # endregion

    # region ---------------------- TODO[IMP]: Compute Methods ----------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods ------------------------------------

    # endregion
    # region ---------------------- TODO[IMP]: Business Methods ------------------------------------
    @property
    def logger(self):
        return _logger


    @property
    def api_token(self):
        return ""

    @property
    def api_client(self):
        return ""

    def handle_api_sync_response(self, response):
        """
        Handle the synchronous API response and update the values if the response code is "1" (indicating success).
        The function takes the response as a parameter and does not return any values.
        """
        if self.is_success_response(response):
            vals = self.get_response_dict(response)
            if vals:
                self.create_or_update(vals)
        else:
            self.assign_error(response)

    def handle_api_push_response(self, response):
        """
        Handle the API push response and update the internal state accordingly.

        Args:
            response: The response object from the API.

        Returns:
            bool: The ID of the push record if created, False otherwise.
        """
        push_record_id = False  # record created on backend controller
        if self.is_success_response(response):
            # result = self.get_response_result(response)
            # if result and isinstance(result, dict):
            #     push_record_id = result.get("id", False)
            #     if push_record_id:
            #         self.record_id = push_record_id
            # elif result and isinstance(result, str):
            #     self.record_id = result
            # elif result and isinstance(result, list) and len(result) == 1:
            #     if result[0] not in ["", None]:
            #         self.record_id = result[0]
            # elif result:
            #     self.last_error = f"Not expected result : {result}"

            self.synced = True
            self.push_status = "pushed"
            self.last_sync_date = fields.Datetime.now()
        else:
            self.push_status = "failed"
            self.assign_error(response)

        return push_record_id


    def create_or_update(self, vals):
        """
        Create or update a record based on the provided record values.

        :param vals: A dictionary containing the values for the zone
        :type vals: dict
        :return: The updated or newly created  record
        :rtype:  Recordset
        """
        #api_record_id = vals.get("record_id")
        api_record_name = vals.get("name")
        # TODO: Add site_id to the search
        record_obj = self.env[self._name].search([("name", "=", api_record_name)], limit=1)

        vals.update({"synced": True, "push_status": "pushed", "last_sync_date": fields.Datetime.now()})

        if record_obj:
            record_obj.update(vals)
            return self.browse(record_obj.id)
        else:
            new_record = self.create(vals)
            return new_record
    # endregion