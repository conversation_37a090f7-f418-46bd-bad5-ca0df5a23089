from odoo import models, fields, api
from odoo.exceptions import ValidationError
import os

class IrAttachment(models.Model):
    _inherit = 'ir.attachment'

    is_sound = fields.Boolean(string="Is Sound File", default=False)

    @api.constrains('name', 'is_sound')
    def _check_sound_file_extension(self):
        allowed_extensions = ['.mp3', '.wav']
        for record in self:
            if record.is_sound:
                extension = os.path.splitext(record.name)[1].lower()
                if extension not in allowed_extensions:
                    raise ValidationError("Only .mp3 and .wav files are allowed for sound attachments.")