import ast
from odoo import models, fields, api
import logging
from odoo.api import depends, depends_context
from odoo.http import request

_logger = logging.getLogger(__name__)


class SecyAbstractModel(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.abstract_model"
    _description = "iot Abstract Model"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    company_id = fields.Many2one('res.company', string="Company")

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_open_json_preview_wizard(self):
        """
        A function that opens a JSON request wizard and returns a dictionary with specific keys and values.
        """
        file_name = self.env.context.get('file_name', self._name).replace('/', '_').replace('.', '_')
        return {
            'name': 'JSON Preview',
            'res_model': "iot.json_preview_wizard",
            'view_mode': 'form',
            'target': 'new',
            'type': 'ir.actions.act_window',
            'context': {
                "default_json_data": self.env.context.get('payload', False),
                "default_file_name": file_name

            }
        }

    # endregion

    # region ---------------------- TODO[IMP]: API Helper Methods -------------------------------------
    # endregion
    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def send_auto_refresh(self):
        """auto refresh for treeview send trigger to bus channel 'auto_refresh' exist in module lp_auto_refresh
         so to work properly need install module lp_auto_refresh manually"""
        self.env['bus.bus']._sendone('auto_refresh', 'auto_refresh',
                                     {'model': self._name, 'id': self.id})
    # endregion
