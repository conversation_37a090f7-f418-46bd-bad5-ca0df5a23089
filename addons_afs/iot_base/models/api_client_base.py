from odoo import models
import inspect
import requests
from requests.adapters import H<PERSON><PERSON>dapter
import ssl

import logging

_logger = logging.getLogger('Controller-API')
# 'http': 'http://localhost:8080',
PROXIES = {
    'https': 'http://127.0.0.1:5555'
}
DISABLE_SSL_WARNINGS = True  # TODO: for developer mode usages
ENABLE_PROXY = False


class NoHostnameVerificationAdapter(HTTPAdapter):
    """this class for developer mode only not production
     to Override the default behavior to not verify the hostname"""

    def init_poolmanager(self, *args, **kwargs):
        # Override the default behavior to not verify the hostname
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        kwargs['ssl_context'] = context

        return super().init_poolmanager(*args, **kwargs)


class ClientBaseAPI(models.AbstractModel):
    """this base controller model act as adapter layer for all controller API models"""
    _name = "iot_base.client_base_api"
    _description = "Client API Base Model"

    # region Properties -----------------------------------------------------------------
    @property
    def session(self):
        # TODO: enable reusable session instead of creating session every time
        api_session = requests.Session()
        api_session.verify = False  # Disable SSL verification for all requests in this session
        if ENABLE_PROXY:
            api_session.proxies.update(PROXIES)  # Apply proxy settings to all requests

        if DISABLE_SSL_WARNINGS:
            # Mount adapters to handle both HTTP and HTTPS
            api_session.mount('http://', NoHostnameVerificationAdapter())
            api_session.mount('https://', NoHostnameVerificationAdapter())

        return api_session

    @property
    def logger(self):
        return _logger

    # @property
    # def api_base_url(self):
    #     return self.env['ir.config_parameter'].sudo().get_param('mdu_controller.api_url')
    #
    # @property
    # def api_username(self):
    #     return self.env['ir.config_parameter'].sudo().get_param('mdu_controller.username')
    #
    # @property
    # def api_password(self):
    #     return self.env['ir.config_parameter'].sudo().get_param('mdu_controller.password')


    def not_implemented_response(self,method_name=''):
        return {
            'response_code': '0',
            'response_status_code': '5000',
            'response_message': f'Method [{method_name}] Not Implemented in this controller {self._name}',
            'response_result': ''
        }

    # endregion]

    # region Helper Methods -----------------------------------------------------
    def convert_to_boolean(self, value):
        """ Convert JSON value to boolean"""
        if isinstance(value, str):
            if value.lower() == 'true':
                return True
            elif value.lower() == 'false':
                return False
        return bool(value)

    def get_field_value(self, json_data, field_path):
        """
        Retrieve the value of a nested field in a JSON object

        Args:
            json_data (dict): The JSON data as a dictionary.
            field_path (str): The path of the nested field, e.g., 'field1.field2.field3.field4'.

        Returns:
            The value of the nested field if it exists, otherwise None.
        """
        fields = field_path.split('.')
        current_level = json_data

        try:
            for field in fields:
                current_level = current_level[field]
            return current_level
        except (KeyError, TypeError):
            return None
    # endregion
