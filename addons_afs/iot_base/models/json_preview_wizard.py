# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
import json

from odoo.http import Response
from odoo.http import request


class JSONPreviewWizard(models.TransientModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.json_preview_wizard"
    _description = "JSON Preview Wizard"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    json_data = fields.Text(help="json formatted")
    file_name = fields.Char()

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------

    def action_download_json_file(self):
        """
        A method to download a JSON file based on the provided json_data and file_name.
        Returns an Odoo action to trigger the download in a new window/tab.
        """
        self.ensure_one()
        json_string = {}
        if self.json_data:
            json_data = json.loads(self.json_data)
            json_string = json.dumps(json_data, indent=None)
            # json_string = json.dumps(json_data)  # , separators=(',', ': '))

        # Construct the URL for the download with the 'json_data' query parameter
        download_url = f'/download_json_file?file_name={self.file_name}&json_data={json_string}'

        # Create an Odoo action to open the download URL in a new window/tab
        action = {
            'type': 'ir.actions.act_url',
            'url': download_url,
            'target': 'new',
        }

        # Return the action to trigger the download
        return action

    def action_open_json_editor(self):
        json_string = {}
        if self.json_data:
            json_data = json.loads(self.json_data)
            json_string = json.dumps(json_data, indent=None)

        url = f"https://jsoneditoronline.org/?json={json_string}"
        return {
            'type': 'ir.actions.act_url',
            'name': 'Open JSON Editor',
            'target': 'new',
            'url': url,
        }


    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
