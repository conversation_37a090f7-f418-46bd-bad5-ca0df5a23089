from odoo import models, fields, api
from odoo.exceptions import UserError


class APIBaseResponseModel(models.AbstractModel):
    _name = "iot_base.api_base_response_model"
    _description = "API Base Response Model"

    last_error = fields.Text(tracking=True)
    error = fields.Boolean()

    def reset_error(self):
        self.write({"last_error": "", "error": False})

    def assign_error(self, response, raise_error=False):
        error_code = response.get("response_code", "-1")
        error_msg = response.get("response_message", "Unknown Error")
        error = f"Error Code:{error_code}, Message:{error_msg}"
        self.write({"last_error": error, "error": True})
        if raise_error:
            raise UserError(error)

    def is_success_response(self, response):
        """return True if the response code is 1"""
        return response.get("response_code") == "1"

    def get_response_result(self, response):
        """return the response result"""
        return response.get("response_result")

    def get_response_list(self, response):
        """return the response list"""
        return self.get_response_result(response) or []

    def get_response_dict(self, response):
        """return the response dict"""
        return self.get_response_result(response) or {}

    def get_response_string(self, response):
        """return the response string"""
        return self.get_response_result(response) or ""
    # endregion
