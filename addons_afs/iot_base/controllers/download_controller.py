from odoo import http
from odoo.http import request
import json
from werkzeug.wrappers import Response


class JSONFileController(http.Controller):

    @http.route('/download_json_file', type='http', auth='public')
    def download_json_file(self, **kw):
        # Retrieve the JSON data from the query parameters
        json_data_prm = kw.get('json_data')
        file_name = f"{kw.get('file_name', 'data')}.json"

        if not json_data_prm:
            return 'No JSON data provided.'

        json_data = json.loads(json_data_prm)
        json_string = json.dumps(json_data, indent=4)

        # Convert the JSON data to bytes
        json_bytes = json_string.encode('utf-8')

        # Create a Werkzeug response with the JSON content
        # content_type = f"'Content-Disposition': 'attachment; filename='{file_name}'"
        content_disposition = f'attachment; filename="{file_name}"'
        response = Response(
            json_bytes,
            content_type='application/json',
            headers={'Content-Disposition': content_disposition}
        )

        return response
