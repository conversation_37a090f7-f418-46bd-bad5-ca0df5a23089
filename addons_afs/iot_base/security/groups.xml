<?xml version="1.0" encoding="utf-8"?>
<odoo>
     <record model="ir.module.category" id="iot_group_category">
        <field name="name">IoT GROUPS</field>
        <field name="description">IoT Category</field>
        <field name="sequence">10</field>
    </record>

    <!-- iot Executive Manager Group -->
    <record id="group_iot_manager" model="res.groups">
        <field name="name">IoT Manager</field>
        <field name="category_id" ref="iot_group_category"/>
    </record>

    <!-- iot User Group -->
    <record id="group_iot_user" model="res.groups">
        <field name="name">IoT User</field>
        <field name="category_id" ref="iot_group_category"/>
    </record>
    <!-- iot ReadOnly Group -->
    <record id="group_iot_readonly" model="res.groups">
        <field name="name">IoT ReadOnly</field>
        <field name="category_id" ref="iot_group_category"/>
    </record>

</odoo>
