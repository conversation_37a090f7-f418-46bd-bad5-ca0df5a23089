from odoo import http
from odoo.http import request
import json
from datetime import datetime
from werkzeug.wrappers import Response


class CommandQueueController(http.Controller):

    @http.route('/api/command_queue/create', type='json', auth='public', methods=['POST'], csrf=False)
    def create_command_queue(self, **kwargs):
        try:
            # Retrieve and validate required parameters
            # Access JSON payload using get_json() for better compatibility
            json_data = request.httprequest.get_json()
            device_id = json_data.get('device_id')
            command_type = json_data.get('command_type')
            response = json_data.get('response', {})
            attachment_id = json_data.get('attachment_id', False)

            # Check for required parameters
            # if device_id or not command_type or not response:
            #     return {
            #         "response_code": "1001",
            #         "response_message": "Missing required parameters",
            #         "response_result": "device_id, command_type, and response are required"
            #     }

            # Fetch the device record
            device = request.env['iot.device'].sudo().search([('client_device_id', '=', device_id)], limit=1)
            if not device.exists():
                return {
                    "response_code": "1002",
                    "response_message": "Device not found",
                    "response_result": f"Device with ID {device_id} does not exist"
                }

            # Call the `create_command_queue` method
            device.create_command_queue(command_type, response, attachment_id=attachment_id)

            return {
                "response_code": "1",
                "response_message": "Command queue entry created successfully",
                "response_result": "Success"
            }

        except Exception as e:
            # Log error and return structured error response
            request.env['ir.logging'].sudo().create({
                'name': 'Create Command Queue Error',
                'type': 'server',
                'message': str(e),
                'level': 'error'
            })
            return {
                "response_code": "1003",
                "response_message": f"Error creating command queue: {e}",
                "response_result": str(e)
            }
