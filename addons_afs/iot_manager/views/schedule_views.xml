<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: schedule_view_form-->
    <record id="schedule_view_form" model="ir.ui.view">
        <field name="name">iot.schedule.form</field>
        <field name="model">iot.schedule</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_generate_default_schedule_details" type="object" string="Generate Default Schedule Details" class="btn-primary"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                        </group>

                        <group>
                            <field name="default_sound_attachment_id"/>
                            <field name="default_time" widget="float_time"/>
                        </group>
                    </group>
                    <notebook>
                         <page string="Schedule Details">
                            <field name="sound_schedule_details_ids">
                                <list editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="week_day"/>
                                    <field name="sound_attachment_id"/>
                                    <field name="sound_time" widget="float_time"/>
                                    <field name="grace_seconds" optional="hide"/>
                                    <field name="schedule_id" optional="hide"/>
                                </list>
                            </field>
                        </page>
                        <page string="Schedule Info" name="schedule_info">
                            <group>
                                <group>
                                    <field name="timer_elapsed_seconds"/>
                                    <field name="default_grace_seconds"/>
                                </group>
                                <group>
                                    <field name="schedule_type" invisible="1"/>
                                    <field name="cycle_days" invisible="1"/>

                                </group>
                            </group>
                        </page>

                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: schedule_view_list-->
    <record id="schedule_view_list" model="ir.ui.view">
        <field name="name">iot.schedule.list</field>
        <field name="model">iot.schedule</field>
        <field name="arch" type="xml">
            <list string="Schedules">
                <field name="name"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: schedule_view_search-->
    <record id="schedule_view_search" model="ir.ui.view">
        <field name="name">iot.schedule.search</field>
        <field name="model">iot.schedule</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--TODO[IMP]:schedule_action-->
    <record id="schedule_action" model="ir.actions.act_window">
        <field name="name">Schedules</field>
        <field name="res_model">iot.schedule</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a schedule
            </p>
            <p>
                Create a schedule
            </p>
        </field>
    </record>
</odoo>
