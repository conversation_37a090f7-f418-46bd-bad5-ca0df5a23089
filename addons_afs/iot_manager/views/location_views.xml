<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: location_view_form-->
    <record id="location_view_form" model="ir.ui.view">
        <field name="name">iot.location.form</field>
        <field name="model">iot.location</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                             <field name="name"/>
                            <field name="company_id"/>
                        </group>

                        <group>
                            <field name="latitude"/>
                            <field name="longitude"/>
<!--                            <field name="is_predefined"/>-->
                        </group>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: location_view_list-->
    <record id="location_view_list" model="ir.ui.view">
        <field name="name">iot.location.list</field>
        <field name="model">iot.location</field>
        <field name="arch" type="xml">
            <list string="Locations">
                <field name="name"/>
                <field name="latitude"/>
                <field name="longitude"/>
<!--                <field name="is_predefined"/>-->
            </list>
        </field>
    </record>

    <!--TODO[IMP]: location_view_search-->
    <record id="location_view_search" model="ir.ui.view">
        <field name="name">iot.location.search</field>
        <field name="model">iot.location</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="latitude"/>
                <field name="longitude"/>
            </search>
        </field>
    </record>
     <!--TODO[IMP]:location_action-->
    <record id="location_action" model="ir.actions.act_window">
        <field name="name">Locations</field>
        <field name="res_model">iot.location</field>
        <field name="view_mode">list,form</field>
       <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a location
            </p>
            <p>
                Create a location
            </p>
        </field>
    </record>
</odoo>
