<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: device_group_schedule_detail_view_form-->
    <record id="device_group_schedule_detail_view_form" model="ir.ui.view">
        <field name="name">iot.device_group_schedule_detail.form</field>
        <field name="model">iot.device_group_schedule_detail</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
<!--                            <field name="name"/>-->
                            <field name="sound_datetime"/>
                            <field name="device_group_id"/>
                            <field name="sound_attachment_id"/>
                        </group>

                        <group>
                        </group>
                    </group>
                </sheet>
                 <notebook>
                        <page string="Schedule Detail">
                            <group>
                                <field name="week_day" widget="selection" />
                                <field name="sound_time"/>
                                <field name="grace_seconds" widget="float_time"/>
                            </group>
                        </page>
                    </notebook>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: device_group_schedule_detail_view_list-->
    <record id="device_group_schedule_detail_view_list" model="ir.ui.view">
        <field name="name">iot.device_group_schedule_detail.list</field>
        <field name="model">iot.device_group_schedule_detail</field>
        <field name="arch" type="xml">
            <list string="Device Group Schedule Detail">
<!--                <field name="name"/>-->
                 <field name="sound_datetime"/>
                <field name="device_group_id"/>
                <field name="sound_attachment_id"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: device_group_schedule_detail_view_search-->
    <record id="device_group_schedule_detail_view_search" model="ir.ui.view">
        <field name="name">iot.device_group_schedule_detail.search</field>
        <field name="model">iot.device_group_schedule_detail</field>
        <field name="arch" type="xml">
            <search>
<!--                <field name="name"/>-->
                <field name="device_group_id"/>
                <field name="sound_attachment_id"/>

                <group expand="0" string="Group By">
                    <filter name="device_group_id" string="Device Group" context="{'group_by': 'device_group_id'}"/>
                    <filter name="sound_attachment_id" string="Sound Attachment" context="{'group_by': 'sound_attachment_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--TODO[IMP]:device_group_schedule_detail_action-->
    <record id="device_group_schedule_detail_action" model="ir.actions.act_window">
        <field name="name">Device Group Schedule Detail</field>
        <field name="res_model">iot.device_group_schedule_detail</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a device_group_schedule_detail
            </p>
            <p>
                Create a device_group_schedule_detail
            </p>
        </field>
    </record>
</odoo>
