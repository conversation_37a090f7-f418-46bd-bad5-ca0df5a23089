<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: tone_view_form-->
    <record id="tone_view_form" model="ir.ui.view">
        <field name="name">iot.tone.form</field>
        <field name="model">iot.tone</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                        </group>

                        <group>
                            <field name="audio_attachment"/>
                        </group>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: tone_view_list-->
    <record id="tone_view_list" model="ir.ui.view">
        <field name="name">iot.tone.list</field>
        <field name="model">iot.tone</field>
        <field name="arch" type="xml">
            <list string="Tones">
                <field name="name"/>
                <field name="code"/>
                <field name="audio_attachment"/>

            </list>
        </field>
    </record>

    <!--TODO[IMP]: tone_view_search-->
    <record id="tone_view_search" model="ir.ui.view">
        <field name="name">iot.tone.search</field>
        <field name="model">iot.tone</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
            </search>
        </field>
    </record>
    <!--TODO[IMP]:tone_action-->
    <record id="tone_action" model="ir.actions.act_window">
        <field name="name">Tones</field>
        <field name="res_model">iot.tone</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a tone
            </p>
            <p>
                Create a tone
            </p>
        </field>
    </record>
</odoo>
