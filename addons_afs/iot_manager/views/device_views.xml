<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: device_view_form-->
    <record id="device_view_form" model="ir.ui.view">
        <field name="name">iot.device.form</field>
        <field name="model">iot.device</field>
        <field name="arch" type="xml">
            <form>
                <header>
                     <field name="state" widget="statusbar" options="{'clickable': '1' }"/>
                    <button string="Check Status" class="btn-secondary" name="action_check_status" icon="fa-wifi"
                            type="object"/>
                    <button name="action_start_scheduler" type="object" string="Start Scheduler" class="btn-secondary"
                            icon="fa-play" invisible="scheduler_state=='running'"/>
                    <button name="action_stop_scheduler" type="object" string=" Stop Scheduler" class="btn-secondary"
                            icon="fa-stop" invisible="scheduler_state=='stopped'"/>
                    <button name="action_reload_scheduler" type="object" string=" Reload Scheduler"
                            class="btn-secondary" icon="fa-refresh"/>

                </header>
                <sheet>
                     <div class="oe_button_box" name="button_box">
                         <button name="action_open_config_json_preview_wizard" type="object" title="Preview Config"
                                icon="fa-code"
                                string="Preview Config"/>

                        <button name="action_open_command_views" type="object" title="Command Queues"
                                icon="fa-tasks"
                                string="Command Queues"
                                class="ms-2"
                                />

                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="zone_id"/>
                            <field name="building_id"/>
                            <field name="location_id"/>
                            <field name="scheduler_state" decoration-danger="scheduler_state=='stopped'"  decoration-success="scheduler_state=='running'" widget="badge"/>
                        </group>

                        <group>
                            <field name="device_group_id"/>
                            <field name="device_service_url" widget="url" placeholder="Enter Device Service URL"/>
                            <field name="client_device_id"/>
                            <field name="activate"/>
                            <field name="is_sound_device"/>
                        </group>
                    </group>
                     <group invisible="not error">
                             <field name="error" invisible="1"/>
                            <field name="last_error" decoration-danger="True"/>
                        </group>
                    <notebook>
                        <page string="Device Info">
                            <group>
                                <field name="state" invisible="1"/>
                                <field name="client_app_version" readonly="1"/>
<!--                                <field name="sound_attachment_ids" widget="many2many_tags"/>-->
                            </group>
                        </page>

                        <page string="Command Queues">
                            <field name="command_queue_ids">
                                <!--                                <list>-->
                                <!--                                    <field name="name"/>-->
                                <!--                                    <field name="device_id"/>-->
                                <!--                                    <field name="command_type"/>-->
                                <!--                                    <field name="state" />-->
                                <!--                                    <field name="sound_datetime" optional="hide"/>-->
                                <!--                                    <field name="create_date" optional="hide"/>-->
                                <!--                                    <field name="executed_datetime" optional="hide"/>-->


                                <!--                                </list>-->
                            </field>
                        </page>



                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: device_view_list-->
    <record id="device_view_list" model="ir.ui.view">
        <field name="name">iot.device.list</field>
        <field name="model">iot.device</field>
        <field name="arch" type="xml">
            <list string="Devices">
                <header>
                    <button name="action_activate_devices" type="object" string="Activate Devices" class="btn-secondary"
                            />
                     <button name="action_deactivate_devices" type="object" string="Deactivate Devices" class="btn-danger"
                           />
                </header>
                <field name="name"/>
                <field name="state" decoration-success="state=='online'" decoration-danger="state=='offline'"
                       widget="badge"/>
                <field name="location_id" optional="hide"/>
                <field name="building_id" optional="show"/>
                <field name="zone_id" optional="show"/>
                <field name="device_group_id" optional="hide"/>
                <field name="activate" optional="show"/>
                <field name="is_sound_device" optional="show"/>
                 <field name="scheduler_state" decoration-danger="scheduler_state=='stopped'"  decoration-success="scheduler_state=='running'" widget="badge"/>

                 <button name="action_open_command_views"
                            type="object"
                            icon="fa-tasks"
                            title="Command Queues"
                            class="oe_highlight"/>

            </list>
        </field>
    </record>

    <!--TODO[IMP]: device_view_search-->
    <record id="device_view_search" model="ir.ui.view">
        <field name="name">iot.device.search</field>
        <field name="model">iot.device</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="device_service_url"/>
                <field name="zone_id"/>
                <field name="building_id"/>
                <field name="location_id"/>
                <field name="device_group_id"/>

                <group expand="0" string="Group By">
                    <filter name="location_id" string="Location" context="{'group_by': 'location_id'}"/>
                    <filter name="building_id" string="Building" context="{'group_by': 'building_id'}"/>
                    <filter name="zone_id" string="Zone" context="{'group_by': 'zone_id'}"/>
                    <filter name="device_group_id" string="Device Group" context="{'group_by': 'device_group_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--TODO[IMP]:device_action-->
    <record id="device_action" model="ir.actions.act_window">
        <field name="name">Devices</field>
        <field name="res_model">iot.device</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a device
            </p>
            <p>
                Create a device
            </p>
        </field>
    </record>
</odoo>
