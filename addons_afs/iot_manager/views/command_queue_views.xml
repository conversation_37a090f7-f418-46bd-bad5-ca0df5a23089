<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: command_queue_view_form-->
    <record id="command_queue_view_form" model="ir.ui.view">
        <field name="name">iot.command_queue.form</field>
        <field name="model">iot.command_queue</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar" options="{'clickable': '1' }"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="device_id"/>
                            <field name="device_group_id"/>
                            <field name="attachment_id"/>
                            <field name="executed_datetime" readonly="1"/>
                        </group>
                        <group>
                            <!--                            <field name="executed_datetime"/>-->
                            <!--                            <field name="sound_datetime"/>-->

                        </group>

                    </group>
                    <group invisible="not comments">
                        <field name="comments" decoration-danger="state=='fail'" readonly="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: command_queue_view_list-->
    <record id="command_queue_view_list" model="ir.ui.view">
        <field name="name">iot.command_queue.list</field>
        <field name="model">iot.command_queue</field>
        <field name="arch" type="xml">
            <list string="Command Queue">
                <field name="name"/>
                <field name="device_id"/>
                <field name="command_type" decoration-warning="is_recent"/>
                <field name="device_group_id" optional="hide"/>
                <field name="state" decoration-success="state=='success'" decoration-danger="state=='fail'"
                       widget="badge"/>
                <field name="comments" optional="hide"/>
                <field name="create_date" optional="hide"/>
                <field name="executed_datetime"/>
                <field name="attachment_id" optional="hide"/>
                <field name="sound_datetime" optional="hide"/>
                <button icon="fa-refresh" type="object" name="action_execute_command" title="Execute Command" invisible="state!='fail'" />

            </list>
        </field>
    </record>

    <!--TODO[IMP]: command_queue_view_search-->
    <record id="command_queue_view_search" model="ir.ui.view">
        <field name="name">iot.command_queue.search</field>
        <field name="model">iot.command_queue</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="state"/>
                <field name="device_id"/>
                <field name="attachment_id"/>
                <field name="comments"/>

                <!-- Search Filters -->
                <filter string="Draft" name="state_draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Success" name="state_success" domain="[('state', '=', 'success')]"/>
                <filter string="Fail" name="state_fail" domain="[('state', '=', 'fail')]"/>
               <!--  seperator-->
                <separator orientation="vertical"/>

                <filter name="upload_file" string="Upload File" domain="[('command_type', '=', 'upload_file')]"/>
                <filter name="delete_file" string="Delete File" domain="[('command_type', '=', 'delete_file')]"/>
                <filter name="play_sound" string="Play Sound" domain="[('command_type', '=', 'play_sound')]"/>
                <filter name="play_sound_manual" string="Play Sound Manually"
                        domain="[('command_type', '=', 'play_sound_manual')]"/>
                <filter name="update_config" string="Update Config" domain="[('command_type', '=', 'update_config')]"/>

                <group expand="1" string="Group By">
                    <filter string="State" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Device" name="group_by_device" context="{'group_by': 'device_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!--TODO[IMP]: command_queue_action-->
    <record id="command_queue_action" model="ir.actions.act_window">
        <field name="name">Command Queues</field>
        <field name="res_model">iot.command_queue</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a command queue
            </p>
            <p>
                Create command queue
            </p>
        </field>
    </record>
</odoo>
