<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: building_view_form-->
    <record id="building_view_form" model="ir.ui.view">
        <field name="name">iot.building.form</field>
        <field name="model">iot.building</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                             <field name="name"/>
                            <field name="location_id"/>
                        </group>

                        <group>
                        </group>
                    </group>
                </sheet>
                 <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: building_view_list-->
    <record id="building_view_list" model="ir.ui.view">
        <field name="name">iot.building.list</field>
        <field name="model">iot.building</field>
        <field name="arch" type="xml">
            <list string="Buildings">
                <field name="name"/>
                <field name="location_id"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: building_view_search-->
    <record id="building_view_search" model="ir.ui.view">
        <field name="name">iot.building.search</field>
        <field name="model">iot.building</field>
        <field name="arch" type="xml">
            <search>
                 <field name="name"/>
                <field name="location_id"/>

                <group expand="0" string="Group By">
                   <filter name="location_id" string="Location" context="{'group_by': 'location_id'}"/>
                </group>
            </search>
        </field>
    </record>
     <!--TODO[IMP]:building_action-->
    <record id="building_action" model="ir.actions.act_window">
        <field name="name">Buildings</field>
        <field name="res_model">iot.building</field>
        <field name="view_mode">list,form</field>
       <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a building
            </p>
            <p>
                Create a building
            </p>
        </field>
    </record>
</odoo>
