<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: zone_view_form-->
    <record id="zone_view_form" model="ir.ui.view">
        <field name="name">iot.zone.form</field>
        <field name="model">iot.zone</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="building_id"/>
                            <field name="location_id"/>
                        </group>

                        <group>
                        </group>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: zone_view_list-->
    <record id="zone_view_list" model="ir.ui.view">
        <field name="name">iot.zone.list</field>
        <field name="model">iot.zone</field>
        <field name="arch" type="xml">
            <list string="Zones">
                <field name="name"/>
                 <field name="building_id"/>
                <field name="location_id"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: zone_view_search-->
    <record id="zone_view_search" model="ir.ui.view">
        <field name="name">iot.zone.search</field>
        <field name="model">iot.zone</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                 <field name="building_id"/>
                <field name="location_id"/>

                <group expand="0" string="Group By">
                    <filter name="building_id" string="Building" context="{'group_by': 'building_id'}"/>
                    <filter name="location_id" string="Location" context="{'group_by': 'location_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--TODO[IMP]:zone_action-->
    <record id="zone_action" model="ir.actions.act_window">
        <field name="name">Zones</field>
        <field name="res_model">iot.zone</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a zone
            </p>
            <p>
                Create a zone
            </p>
        </field>
    </record>
</odoo>
