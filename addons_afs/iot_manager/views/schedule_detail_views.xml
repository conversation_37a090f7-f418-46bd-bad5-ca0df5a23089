<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: schedule_detail_view_form-->
    <record id="schedule_detail_view_form" model="ir.ui.view">
        <field name="name">iot.schedule_detail.form</field>
        <field name="model">iot.schedule_detail</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
<!--                            <field name="name"/>-->
                            <field name="sound_datetime"/>
                            <field name="schedule_id"/>
                            <field name="sound_attachment_id"/>
                        </group>

                        <group>
                        </group>
                    </group>
                    <notebook>
                        <page string="Schedule Detail">
                            <group>
                                <field name="week_day" widget="selection" />
                                <field name="sound_time"/>
                                <field name="grace_seconds" widget="float_time"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: schedule_detail_view_list-->
    <record id="schedule_detail_view_list" model="ir.ui.view">
        <field name="name">iot.schedule_detail.list</field>
        <field name="model">iot.schedule_detail</field>
        <field name="arch" type="xml">
            <list string="Schedule Details">
<!--                <field name="name"/>-->
                 <field name="sound_datetime"/>
                <field name="schedule_id"/>
                <field name="sound_attachment_id"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: schedule_detail_view_search-->
    <record id="schedule_detail_view_search" model="ir.ui.view">
        <field name="name">iot.schedule_detail.search</field>
        <field name="model">iot.schedule_detail</field>
        <field name="arch" type="xml">
            <search>
<!--                <field name="name"/>-->
                <field name="schedule_id"/>
                <field name="sound_attachment_id"/>
                <group expand="0" string="Group By">
                    <filter name="schedule_id" string="Schedule" context="{'group_by': 'schedule_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--TODO[IMP]:schedule_detail_action-->
    <record id="schedule_detail_action" model="ir.actions.act_window">
        <field name="name">Schedule Details</field>
        <field name="res_model">iot.schedule_detail</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a schedule_detail
            </p>
            <p>
                Create a schedule_detail
            </p>
        </field>
    </record>
</odoo>
