<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: device_group_view_form-->
    <record id="device_group_view_form" model="ir.ui.view">
        <field name="name">iot.device_group.form</field>
        <field name="model">iot.device_group</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_apply_schedule" type="object" string="Apply Schedule" class="btn-primary"
                            icon="fa-calendar"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_config_json_preview_wizard" type="object" title="Preview Config"
                                icon="fa-code"
                                string="Preview Config"/>
                        <button name="action_open_command_views" type="object" title="Command Queues"
                                icon="fa-tasks"
                                string="Command Queues"
                                />
                    </div>

                    <group>
                        <group>
                            <field name="name"/>
                            <field name="schedule_id"/>
                        </group>

                        <group>
                            <field name="default_sound_attachment_id" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Schedule Info" name="schedule_info">
                            <group>
                                <field name="sound_attachment_ids" widget="many2many_tags"/>
                            </group>
                            <group>
                                <group>
                                    <field name="default_time" widget="float_time" invisible="1"/>
                                    <field name="timer_elapsed_seconds"/>
                                    <field name="default_grace_seconds"/>
                                </group>
                                <group>
                                    <field name="schedule_type" invisible="1"/>
                                    <field name="cycle_days" invisible="1"/>

                                </group>

                            </group>
                        </page>

                        <page string="Devices">
                            <field name="devices_ids"></field>
                        </page>

                        <page string="Schedule Details">
                            <field name="sound_schedule_details_ids">
                                <list editable="bottom" default_order="create_date desc">
                                    <field name="sequence" widget="handle"/>
                                    <field name="week_day"/>
                                    <field name="sound_attachment_id"/>
                                    <field name="sound_time" widget="float_time"/>
                                    <field name="grace_seconds" optional="hide"/>
                                    <field name="device_group_id" optional="hide"/>

                                </list>
                            </field>
                        </page>
                        <page string="Command Queues">
                            <field name="command_queue_ids"></field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: device_group_view_list-->
    <record id="device_group_view_list" model="ir.ui.view">
        <field name="name">iot.device_group.list</field>
        <field name="model">iot.device_group</field>
        <field name="arch" type="xml">
            <list string="Device groups">
                <field name="name"/>
                <field name="schedule_id"/>
                <button name="action_open_command_views"
                            type="object"
                            icon="fa-tasks"
                            title="Command Queues"
                            class="oe_highlight"/>
            </list>
        </field>
    </record>

    <!--TODO[IMP]: device_group_view_search-->
    <record id="device_group_view_search" model="ir.ui.view">
        <field name="name">iot.device_group.search</field>
        <field name="model">iot.device_group</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="schedule_id"/>
                <group expand="0" string="Group By">
                    <filter name="schedule_id" string="Schedule" context="{'group_by': 'schedule_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--TODO[IMP]:device_group_action-->
    <record id="device_group_action" model="ir.actions.act_window">
        <field name="name">Device groups</field>
        <field name="res_model">iot.device_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a device_group
            </p>
            <p>
                Create a device_group
            </p>
        </field>
    </record>
</odoo>
