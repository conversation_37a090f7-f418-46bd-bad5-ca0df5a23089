# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class BaseSchedule(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.base_schedule"
    _description = "Base Schedule"
    _inherit = ["iot.name_model"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    cycle_days = fields.Integer(string="Cycle Days", default=7)
    timer_elapsed_seconds = fields.Integer(string="Timer Elapsed (seconds)", default=5)
    schedule_type = fields.Selection([
        ('daily', 'Daily'),
        ('weekly', 'Weekly')
    ], string="Schedule Type", default='weekly')

    default_time = fields.Float(string="Default Time")
    default_grace_seconds = fields.Integer(string="Default Grace (seconds)", default=30)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    default_sound_attachment_id = fields.Many2one('ir.attachment', string="Sound Attachment",
                                                  domain=[('is_sound', '=', True)])
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
