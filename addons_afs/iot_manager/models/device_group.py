# -*- coding: utf-8 -*-
from uuid import uuid4

from odoo import api, fields, models
import json

from odoo.addons.iot_manager.helper.helper import *
from odoo.addons.resource.models.utils import float_to_time
from odoo.exceptions import UserError, ValidationError
from odoo.tools import config


class DeviceGroup(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.device_group"
    _description = "Device Group"
    _inherit = ["iot.base_schedule", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    batch_ref_id = fields.Char(string="Batch Ref",default=uuid4())
    # endregion

    # region  Special
    # endregion

    # region  Relational
    devices_ids = fields.One2many('iot.device', 'device_group_id', string="Devices")
    schedule_id = fields.Many2one('iot.schedule', string="Schedule")
    # schedule_detail_ids = fields.One2many('iot.device_group_schedule_detail', 'device_group_id', string="Schedule Details")
    sound_schedule_details_ids = fields.One2many('iot.device_group_sound_schedule_detail', 'device_group_id', string="Sound Schedule Details")
    sound_attachment_ids = fields.Many2many('ir.attachment', string="Sound Files" ,domain=[('is_sound', '=', True)])
    command_queue_ids = fields.One2many('iot.command_queue', 'device_group_id', string="Command Queues")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_apply_schedule(self):

        self.sound_schedule_details_ids.unlink()
        if self.schedule_id:

            schedule = self.schedule_id
            self.write({
                'cycle_days': schedule.cycle_days,
                'timer_elapsed_seconds': schedule.timer_elapsed_seconds,
                'schedule_type': schedule.schedule_type,
                'default_time': schedule.default_time,
                'default_grace_seconds': schedule.default_grace_seconds
            })

            new_schedule_details = []
            for detail in self.schedule_id.sound_schedule_details_ids:
                new_schedule_details.append((0, 0, {
                    'sound_datetime': detail.sound_datetime,
                    'sound_attachment_id': detail.sound_attachment_id.id,
                    'sound_time': detail.sound_time,
                    'sequence': detail.sequence,
                    'grace_seconds': detail.grace_seconds,
                    'week_day': detail.week_day
                }))
            self.sound_schedule_details_ids = new_schedule_details

    def action_open_config_json_preview_wizard(self):
        """
        Open a wizard to preview the config_json
        """
        config_json = json.dumps(self.generate_config(), indent=4)
        if config_json:
            return self.sudo().with_context(payload=config_json,file_name=self.name).action_open_json_preview_wizard()
        else:
            empty_config = {
                "name": self.name or "Device Group",
            }
            return self.sudo().with_context(payload=empty_config,self=self.name).action_open_json_preview_wizard()

    def action_open_command_views(self):
        record = self.env['iot.command_queue'].search([('device_group_id', '=', self.id)], limit=1)
        return {
            'name': 'Command Queue',
            'type': 'ir.actions.act_window',
            'res_model': 'iot.command_queue',
            'res_id': record.id,
            'view_mode': 'list,form',
            'target': 'current',
        }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    def generate_config(self):
        """generate  general config shared between devices in same groups"""

        week_day_dict = dict(self.sound_schedule_details_ids._fields['week_day'].selection)
        device_group_data = {}

        # Group schedule details by week_day
        for detail in self.sound_schedule_details_ids:
            day_name = week_day_dict.get(detail.week_day, "Unknown")

            detail_data = {
                "id": detail.id,
                "time": float_to_time(detail.sound_time).strftime("%H:%M"),  # Convert sound time to HH:MM format
                'file_name': detail.sound_attachment_id.name,
                "sequence": detail.sequence,
                "grace_seconds": detail.grace_seconds
            }

            # Initialize list if the day does not exist in device_group_data
            device_group_data.setdefault(day_name, []).append(detail_data)

        # Base configuration without device-specific keys
        server_api_url = self.env['ir.config_parameter'].sudo().get_param('iot.server_api_url', default='http://localhost:8019/api')
        return {
            "name": self.name,
            "client_device_id":"00000000-0000-0000-0000-000000000000",
            "server_api_url": server_api_url,
            "timer_elapsed_seconds": self.timer_elapsed_seconds,
            "schedule_detail": device_group_data
        }





    # endregion
