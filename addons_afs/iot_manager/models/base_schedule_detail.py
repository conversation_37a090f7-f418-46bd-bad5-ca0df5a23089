# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.addons.iot_manager.helper.helper import *


class BaseScheduleDetail(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.base_schedule_detail"
    _description = "Base Schedule Detail"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    sequence = fields.Integer(string="Sequence", default=10)
    sound_time = fields.Float(string="Sound Time" ,required=True)
    grace_seconds = fields.Integer(string="Grace Time (seconds)")
    week_day = fields.Selection(WEEK_DAYS, string="Day" ,required=True)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    sound_attachment_id = fields.Many2one('ir.attachment', string="Sound Attachment",domain=[('is_sound', '=', True)], required=True)
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
