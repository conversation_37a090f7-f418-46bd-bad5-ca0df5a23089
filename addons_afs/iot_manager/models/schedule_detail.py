# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.addons.iot_manager.helper.helper import *


class ScheduleDetail(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.schedule_detail"
    _description = "Schedule Detail"
    _inherit = ["iot.base_schedule_detail", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    sound_datetime = fields.Datetime("Sound DateTime")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    schedule_id = fields.Many2one('iot.schedule', string="Schedule")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
