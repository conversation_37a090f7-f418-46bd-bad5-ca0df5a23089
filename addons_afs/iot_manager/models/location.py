from odoo import models, fields
from odoo.exceptions import UserError


class Location(models.Model):
    # region ---------------------- [IMP]: Private Attributes --------------------------------
    _name = 'iot.location'
    _description = 'Location'
    _inherit = ["iot.name_model", "mail.thread", "mail.activity.mixin"]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- [IMP]: Fields Declaration ---------------------------------

    # region  Basic
    latitude = fields.Float(string='Latitude')
    longitude = fields.Float(string='Longitude')
    is_predefined = fields.Boolean(string='Is Predefined', default=False)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def unlink(self):
        """
        Override the unlink method to prevent deletion of predefined records.
        """
        if any(record.is_predefined for record in self):
            raise UserError("You cannot delete predefined records.")
        return super(Location, self).unlink()

# endregion

# region ---------------------- TODO[IMP]: Action Methods -------------------------------------
# endregion

# region ---------------------- TODO[IMP]: Business Methods -------------------------------------
# endregion
# region ---------------------- TODO[IMP]: API Methods -------------------------------------


# endregion
