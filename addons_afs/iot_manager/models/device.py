# -*- coding: utf-8 -*-
import json
from datetime import datetime
from uuid import uuid4

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class Device(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.device"
    _description = "Device"
    _inherit = ["iot.name_model","iot_base.api_base_model", "mail.thread", "mail.activity.mixin"]
    _sql_constraints = [
        ('client_device_id_uniq', 'unique(client_device_id)', 'Client Device ID must be unique!')
    ]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    state = fields.Selection([('draft', 'Draft'), ('online', 'Online'), ('offline', 'Offline')], default='draft')
    scheduler_state = fields.Selection([('draft', 'Draft'), ('running', 'Running'), ('stopped', 'Stopped')], default='draft')
    client_app_version = fields.Char()
    device_service_url = fields.Char()
    is_sound_device = fields.Boolean()
    activate = fields.Boolean(help="Used to exclude device from pushing config to client api", default=True)
    # define client_device_id uuid
    client_device_id = fields.Char(string="Client Device ID", default=uuid4(), help="To register Client Device ID",
                                   index=True, copy=False)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    zone_id = fields.Many2one('iot.zone', string="Zone")
    building_id = fields.Many2one('iot.building', related="zone_id.building_id", string="Building")
    location_id = fields.Many2one('iot.location', related="building_id.location_id", string="Location")
    device_group_id = fields.Many2one('iot.device_group', string="Device Group")
    # sound_attachment_ids = fields.Many2many('ir.attachment', string="Sound Files", domain=[('is_sound', '=', True)])
    command_queue_ids = fields.One2many('iot.command_queue', 'device_id', string="Command Queues")
    date = fields.Datetime(string="Date", default=datetime.now())

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def create(self,vals):
        res=super(Device, self).create(vals)
        self.send_auto_refresh()
        return res

    def write(self,vals):
        res=super(Device, self).write(vals)
        self.send_auto_refresh()
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_check_status(self):
        pass
    def action_start_scheduler(self):
        pass

    def action_stop_scheduler(self):
        pass

    def action_reload_scheduler(self):
        pass

    def _auto_device_check_status(self):
        devices = self.env['iot.device'].search([])
        for device in devices:
            device.action_check_status()

        self.send_auto_refresh()

    def action_activate_devices(self):
        for device in self:
            device.activate = True

    def action_deactivate_devices(self):
        for device in self:
            device.activate = False

    def action_open_command_views(self):
        record = self.env['iot.command_queue'].search([('device_group_id', '=', self.id)], limit=1)
        return {
            'name': 'Command Queue',
            'type': 'ir.actions.act_window',
            'res_model': 'iot.command_queue',
            'res_id': record.id,
            'view_mode': 'list,form',
            'target': 'current',
        }

        # endregion

    def action_open_config_json_preview_wizard(self):
        """
        Open a wizard to preview the config_json
        """
        if self.device_group_id:
            base_config = self.device_group_id.generate_config()
            config_json = self.get_device_config(base_config)
            if config_json:
                return self.sudo().with_context(payload=config_json, file_name=self.name).action_open_json_preview_wizard()
            else:
                empty_config = {
                    "name": self.name or "Device Group",
                }
                return self.sudo().with_context(payload=empty_config, self=self.name).action_open_json_preview_wizard()
        else:
            raise UserError("Device Group not found")


    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def get_device_config(self,base_config):
        """update base config to set client_device_id"""
        base_config.update(client_device_id=self.client_device_id)
        return json.dumps(base_config, indent=4)

    def create_command_queue(self, command_type, response, **kwargs):
        """Creates a command queue entry for this device with the specified type and state."""
        command_vals = {
            'command_type': command_type,
            'device_id': self.id,
            'device_group_id': self.device_group_id.id,
            'executed_datetime': datetime.now(),
            'attachment_id': kwargs.get('attachment_id', False),
            'ref_id': kwargs.get('ref_id', False),
        }

        if response.get('response_code') == '1':
            command_vals['state'] = 'success'
            command_vals['executed_datetime'] = datetime.now()
        else:
            command_vals['state'] = 'fail'
            command_vals['comments'] =  response.get('response_message') or response.get('response_result')

        self.env['iot.command_queue'].create(command_vals)
    # endregion
