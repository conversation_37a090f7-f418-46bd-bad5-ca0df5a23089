# -*- coding: utf-8 -*-
from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import datetime
import asyncio



class CommandQueue(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.command_queue"
    _description = "Command Queue"
    _inherit = ["iot.abstract_model"]
    _order = "id desc"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(required=True, readonly=True, index=True, default='New')
    state = fields.Selection([('draft', 'Draft'), ('success', 'Success'), ('fail', 'Fail')], default='draft')
    command_type = fields.Selection(selection=[('upload_file', 'Upload File'), ('delete_file', 'Delete File'),
                                               ('play_sound', 'Play Sound'),
                                               ('play_sound_manual', 'Play Sound Manually'),
                                               ('update_config', 'Update Config'), ])
    executed_datetime = fields.Datetime()
    sound_datetime = fields.Datetime()
    ref_id = fields.Char(string="Reference", help="Reference ID is technical field used to identify specified job")
    comments = fields.Text(help="Contains success or failure message response")
    # endregion

    # region  Special
    # endregion

    # region  Relational

    device_id = fields.Many2one('iot.device', string="Device")
    device_group_id = fields.Many2one('iot.device_group', string="Device Group")
    attachment_id = fields.Many2one('ir.attachment', string="Sound Attachment")
    date = fields.Datetime(string="Date", default=datetime.now())

    # endregion

    # region  Computed
    is_recent = fields.Boolean(string="Is Recent", compute='_compute_is_recent', store=False)


    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('create_date')
    def _compute_is_recent(self):
        now = datetime.now()
        for record in self:
            # Check if create_date exists and is within 600 seconds (10 minutes)
            is_recent_time = record.create_date and (now - record.create_date).total_seconds() <= 600

            # Check if device group and batch references match, then set `is_recent`
            if is_recent_time and record.device_group_id and record.device_group_id.batch_ref_id:
                record.is_recent = record.device_group_id.batch_ref_id == record.ref_id
            else:
                record.is_recent = is_recent_time
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------

    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('iot.seq_command_queue') or 'New'
        res = super(CommandQueue, self).create(vals)
        self.send_auto_refresh()
        return res

    def write(self, vals):
        res = super(CommandQueue, self).write(vals)
        self.send_auto_refresh()
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_play_sound(self):
        # Implementation of sound playing
        pass

    def action_execute_command(self):
        for record in self:
            try:
                response=None
                if record.command_type == 'upload_file':
                    device_info = {
                        "device_id": record.device_id.id,
                        "device_client_id": record.device_id.client_device_id,
                        "attachment_id": record.attachment_id.id
                    }
                    response = record.device_id.upload_file(record.attachment_id.name , record.attachment_id.datas , device_info)

                elif record.command_type in ('play_sound' , 'play_sound_manual'):
                    response= record.device_id.play_sound(record.attachment_id,record.device_id,record.device_id.client_device_id)
                elif record.command_type == 'update_config':
                    device_info = {
                        "device_id": record.device_id.id,
                    }
                    base_config = record.device_group_id.generate_config()
                    response= record.device_id.upload_schedule_config( device_info ,base_config )

                elif record.command_type == 'delete_file':
                   response= record.device_id.remove_file(record.attachment_id.name)

                response_code= response.get('response_code', '')
                if response_code =='1':
                   record.state = 'success'
                   record.comments = "Command executed successfully."
            except Exception as e:
                 record.state = 'fail'
                 record.comments = f"Command execution failed: {str(e)}"
            finally:
                  record.executed_datetime = fields.Datetime.now()

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
