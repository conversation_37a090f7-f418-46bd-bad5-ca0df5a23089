# -*- coding: utf-8 -*-
from odoo import fields, models
from odoo.exceptions import UserError
from odoo.addons.iot_manager.helper.helper import *


class Schedule(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.schedule"
    _description = "Schedule"
    _inherit = ["iot.base_schedule", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    sound_schedule_details_ids = fields.One2many('iot.sound_schedule_detail', 'schedule_id', string="Sound Schedule Details")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_generate_default_schedule_details(self):
        schedule_details = []

        for schedule in self:
            if not schedule.default_sound_attachment_id:
                raise UserError("Please select a sound attachment to generate default schedule details.")

            for day in WEEK_DAYS:
                schedule_details.append(
                    {
                        'schedule_id': schedule.id,
                        'week_day': day[0],
                        'sound_time': schedule.default_time,
                        'grace_seconds': schedule.default_grace_seconds,
                        'sound_attachment_id': schedule.default_sound_attachment_id.id if schedule.default_sound_attachment_id else False
                    }
                )

        try:
            # Bulk create all schedule details at once if possible
            if schedule_details:
                self.sound_schedule_details_ids.unlink()
                self.sound_schedule_details_ids.create(schedule_details)
        except Exception as e:
            raise UserError(f"Failed to generate default schedule details: {e}")

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
