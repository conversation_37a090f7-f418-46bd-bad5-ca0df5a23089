from odoo import fields, models

class NameModel(models.AbstractModel):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.name_model"
    _description = "Name Model"
    _inherit = ["iot.abstract_model"]
    _sql_constraints = [
        ('unique_name', 'unique(name)', 'The name must be unique.')
    ]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(required=True, string="Name")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def copy(self, default=None):
        """
        override the copy method to add some character to the unique fields in order to avoid the redundancy
        which obstruct the duplication action.
        """
        default = dict(default or {})
        added_char = "_Copy(%s)" % self.id
        default.update({
            'name': self.name + added_char,
        })
        return super(NameModel, self).copy(default)
    # endregion
