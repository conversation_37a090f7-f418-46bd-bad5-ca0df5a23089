# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class DeviceGroupSoundScheduleDetail(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.device_group_sound_schedule_detail"
    _description = "Device Group Sound Schedule Detail"
    _inherit = ["iot.base_schedule_detail", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    sound_datetime = fields.Datetime()
    # endregion

    # region  Special
    # endregion

    # region  Relational
    device_group_id = fields.Many2one('iot.device_group', string="Device Group")
    sound_attachment_id = fields.Many2one('ir.attachment', string="Sound Attachment" ,domain=[('is_sound', '=', True)])

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_play_sound(self):
        # Implementation of sound playing
        pass
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
