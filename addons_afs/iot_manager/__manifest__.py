# -*- coding: utf-8 -*-

{
    "name": "IoT Devices Manager",
    "version": "1.0.1",
    "depends": [
        'base', 'iot_base', 'mail', 'web',
    ],
    "author": "laplacesoftware",
    "category": "iot",
    "website": "https://www.laplacesoftware.com/",
    "images": ["static/description/images/main_screenshot.jpg"],
    "price": "0",
    "license": "OPL-1",
    "currency": "USD",
    "summary": "IoT Devices Manager",
    "description": """

Information
======================================================================

* created menus
* created objects
* created views
* logics

""",
    "data": [
        # Security and Access Control
        # "security/security.xml",
        "security/ir.model.access.csv",

        # Data Files
        "data/data.xml",
        "data/command_queue_sequence.xml",
        "data/cron_job_views.xml",

        # Views
        "views/location_views.xml",
        "views/building_views.xml",
        "views/zone_views.xml",
        "views/schedule_views.xml",
        "views/schedule_detail_views.xml",
        # "views/tone_views.xml",
        "views/device_views.xml",
        "views/device_group_views.xml",
        "views/device_group_schedule_detail_views.xml",
        "views/command_queue_views.xml",
        "views/dashboard.xml",

        # Report Templates and Actions

        # Wizards

        # Menus
        "views/menus.xml"
    ],
    'assets': {
        'web.assets_backend': [
            'iot_manager/static/src/dashboard/*',
            'iot_manager/static/src/dashboard/dashboard_item_alert_list.xml',
            'iot_manager/static/src/dashboard/dashboard_item_alert_list.js',
        ],
        'web.assets_common': [],
    },
    "installable": True,
    "auto_install": False,
    "application": True,
}
