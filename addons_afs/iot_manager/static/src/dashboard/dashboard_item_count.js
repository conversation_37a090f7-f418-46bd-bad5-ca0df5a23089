/** @odoo-module **/

import {Component, useState, onWillStart, onWillDestroy} from "@odoo/owl";
import {useService} from "@web/core/utils/hooks";


export class DashboardItemCount extends Component {
    static template = "iot_manager.DashboardItemCount";

    static props = {
        size: {type: Number, default: 18, optional: true},
        label: {type: String, default: "Item Label"},
        icon: {type: String, optional: true},
        chartIcon: {type: String, optional: true},
        buttonLabel: {type: String, default: "Show More", optional: true},
        color: {type: String, default: "#ffffff", optional: true},
        model: {type: String, required: true},
        domain: {type: Array, default: [], required: true},
        views: {type: Array, required: true},
    };

    setup() {
        this.orm = useService("orm");
        this.action = useService("action");
        this.busService = this.env.services.bus_service;

        this.state = useState({
            count: 0,
        });

        onWillStart(async () => {
            await this.fetchCount();

        });

        const refreshBusListener = (payload) => {
            if (payload.model === this.props.model) {
                this.fetchCount();
            }
        }
        this.busService.subscribe('auto_refresh', refreshBusListener);
        this.busService.addChannel('auto_refresh');
        this._refreshStopBus = () => {
            this.busService.unsubscribe('auto_refresh', refreshBusListener);
            this.busService.deleteChannel('auto_refresh');
        }

        onWillDestroy(() => {
            this._refreshStopBus();
        });


    }

    fetchCount() {
        //console.log(this.props.model,this.props.domain)
        this.orm.searchCount(this.props.model, this.props.domain)
            .then((count) =>
                this.state.count = count
            );
    }


    openEntryView() {
        this.action.doAction({
            type: "ir.actions.act_window",
            name: `${this.props.label}`,
            res_model: this.props.model,
            views: this.props.views,
            domain: this.props.domain,
        });
    }

}
