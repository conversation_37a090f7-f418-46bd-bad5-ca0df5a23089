/** @odoo-module **/

import {Component, useState, onWillStart, onWillDestroy} from "@odoo/owl";
import {useService} from "@web/core/utils/hooks";


export class DashboardAlertList extends Component {
    static template = "iot_manager.DashboardAlertList";

    setup() {
        this.orm = useService("orm");
        this.action = useService("action");
        this.busService = this.env.services.bus_service;

        this.currentDate = new Date().toISOString().split('T')[0];
        this.startDate = new Date(Date.now() - (2 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];


        this.state = useState({
            alerts: [],
            loading: true,
        });

        onWillStart(() => {
            this.fetchAlerts();
        });

        const refreshBusListener = (payload) => {
            if (payload.model === "iot.command_queue") {
                this.fetchAlerts();
            }
        }
        this.busService.subscribe('auto_refresh', refreshBusListener);
        this.busService.addChannel('auto_refresh');
        this._refreshStopBus = () => {
            this.busService.unsubscribe('auto_refresh', refreshBusListener);
            this.busService.deleteChannel('auto_refresh');
        }

        onWillDestroy(() => {
            this._refreshStopBus();
        });


    }

    fetchAlerts() {
        const domain = [
            ['state', '=', 'fail'],
            ['date', '>=', this.startDate],
            ['date', '<=', this.currentDate]
        ];

        this.orm.searchRead("iot.command_queue", domain)
            .then((alerts) => {
                this.state.alerts = alerts;
                this.state.loading = false;
            });
    }

    openEntryForm(alert) {
        this.action.doAction({
            type: "ir.actions.act_window",
            name: "View Alert",
            res_model: "iot.command_queue",
            views: [
                [false, "form"],
            ],
            res_id: alert.id,
        });
    }

    async executeCommand(alert) {
    try {
        await this.orm.call("iot.command_queue", "action_execute_command", [[alert.id]]);
        this.fetchAlerts();
    } catch (error) {
        console.error("Error executing command:", error);
    }
}

    async deleteAlert(alert) {
        // Use alert.ref_id instead of record.ref_id
//        await this.orm.call("iot.command_queue", "remove_command_queue", [alert.id,1]);
          this.orm.unlink("iot.command_queue", [alert.id]);
          this.fetchAlerts();
    }


}
