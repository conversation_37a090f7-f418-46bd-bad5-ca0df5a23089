/** @odoo-module **/

import { Component} from "@odoo/owl";
import { registry } from "@web/core/registry";
import { DashboardItemCount } from "./dashboard_item_count";
import {DashboardAlertList} from "./dashboard_item_alert_list";
import { _t } from "@web/core/l10n/translation";

class SoundPlayerDashboard extends Component {
    static template = "iot_manager.AwesomeDashboard";
    static components = {DashboardItemCount , DashboardAlertList};


    get getTranslatedButtonLabel() {
        return  _t("Show More")
    }

    get translateDashboardTitle() {
        return _t("Dashboard");
    }


    getDashboardItems() {
    // Define the current date and start date for the last 2 days
    const currentDate = this.currentDate || new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - (2 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];

    return [
        {
            label: _t("Online Devices"),
            model: "iot.device",
            domain: [
                ['state', '=', 'online'],
                ['date', '<=', currentDate]
            ],
            icon: "fa fa-wifi",
            chartIcon: "fa fa-line-chart",
            color: "bg-success",
            views: [
                    [false, "list"],
                    [false, "form"],
//                    [false, "calendar"],
//                    [false, "activity"],
//                    [false, "kanban"],
                ],
        },
        {
            label: _t("Offline Devices"),
            model: "iot.device",
            domain: [
                ['state', '=', 'offline'],
                ['date', '<=', currentDate]
            ],
            icon: "fa fa-power-off",
            chartIcon: "fa fa-pie-chart",
            color: "bg-danger",
             views: [
                    [false, "list"],
                    [false, "form"],
//                    [false, "calendar"],
//                    [false, "activity"],
//                    [false, "kanban"],
                ],
        },
        {
            label: _t("Alerts Count"),
            model: "iot.command_queue",
            domain: [
                ['state', '=', 'fail'],
//                ['date', '>=', startDate],
//                ['date', '<=', currentDate]
            ],
            icon: "fa fa-bell",
            chartIcon: "fa fa-area-chart",
            color: "bg-info",
             views: [
                    [false, "list"],
                    [false, "form"],
//                    [false, "calendar"],
//                    [false, "activity"],
//                    [false, "kanban"],
                ],
        },
    ];
}

}

registry.category("actions").add("iot_manager.sound_player_dashboard", SoundPlayerDashboard);
