<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-name="iot_manager.AwesomeDashboard">
        <!-- Scrollable container for the entire dashboard -->
        <div class="lp-dashboard_scrollable_container">
            <div class="container-fluid o_secy_container m-3">

                <h3 ><i class="fa fa-tachometer ms-2 text-primary mx-1"></i><t t-esc="translateDashboardTitle"/></h3>

                <div class="row justify-content-start mx-2 mt-5">
                    <t t-foreach="getDashboardItems()" t-as="item" t-key="item.label">
                        <DashboardItemCount
                            size="16"
                            label="item.label"
                            icon="item.icon"
                            chartIcon="item.chartIcon"
                            buttonLabel="this.getTranslatedButtonLabel"
                            color="item.color"
                            model="item.model"
                            domain="item.domain"
                            views="item.views"

                        />
                    </t>
                </div>
            </div>

            <div class="container-fluid o_secy_containe m-3">
                 <hr class="my-4"/>
                <h3><i class="fa fa-bell ms-2 text-warning mx-1"></i>Command Queues</h3>
                <div class="mx-3 mt-2">
                    <DashboardAlertList/>
                </div>


            </div>
        </div>
    </t>
</templates>
