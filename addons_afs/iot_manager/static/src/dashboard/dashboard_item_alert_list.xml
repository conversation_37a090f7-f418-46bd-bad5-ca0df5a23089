<templates xml:space="preserve">
    <t t-name="iot_manager.DashboardAlertList">
        <div class="container-fluid ">
            <div class="col-lg-6 col-md-8 col-sm-12">
                <t>
                    <table class="table table-bordered table-hover">
                        <thead class="thead-light">
                            <tr>

                                <th>Name</th>
                                <th>State</th>
                                <th>Device</th>
                                <th>Comments</th>
                                <th>Type</th>
                                <th>Execution time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="state.alerts" t-as="alert" t-key="alert.id" >
                                <tr class="alert-row">
                                    <td><t t-esc="alert.name"/></td>
                                    <td><t t-esc="alert.state"/></td>
                                    <td><t t-esc="alert.device_id"/></td>
                                    <td><t t-esc="alert.comments"/></td>
                                    <td><t t-esc="alert.command_type"/></td>
                                    <td><t t-esc="alert.executed_datetime"/></td>

                                    <td>
                                        <button
                                            class="btn btn-primary btn-link btn-sm"
                                            t-on-click="() => this.openEntryForm(alert)">
                                            <i class="fa fa-external-link fa-lg" title="Open Related Entry"></i>
                                        </button>
                                    </td>
                                     <td>
                                        <button
                                            class="btn btn-primary btn-link btn-sm"
                                            t-on-click="() => this.executeCommand(alert)">
                                            <i class="fa fa-refresh fa-lg" title="Execute Command"></i>
                                        </button>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-danger btn-link btn-sm"
                                                t-on-click="() => this.deleteAlert(alert)">
                                            <i class="fa fa-trash fa-lg" title="Delete"></i>
                                        </button>
                                    </td>

                                </tr>
                            </t>
                        </tbody>
                    </table>

                </t>
            </div>
        </div>
    </t>
</templates>
