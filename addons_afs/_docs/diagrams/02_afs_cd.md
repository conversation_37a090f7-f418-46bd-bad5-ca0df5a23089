# Audio Flow System Class Diagrams

## Master Data (Configuration) Class Diagram
    this class diagram contain all models for master data should be defined in first installation.
```mermaid
classDiagram
    class iotAbstractModel {
        - _name='iot.abstract_model'
        - company_id: Many2one
    }

    class NameModel {
        - name: Char # required, Unique
    }

    class Tone {
        - audio_attachment: Binary
        - code: Char
    }
    
    class BaseSchedule {
        _name='iot.base_schedule'
        - cycle_days: Integer
        - timer_elapsed_seconds: Float
        - schedule_type: Selection['daily', 'weekly']
        - default_time: Float 
        - default_tone_id: Many2one 
        - default_grace_seconds: Integer #
      
    }
    class BaseScheduleDetail {
        _name='iot.base_schedule_detail'
        - week_day:Selection(WEEK_DAYS)
        - tone_id: Many2one
        - sound_time: Float
        - grace_seconds: Integer
    }
    
    class Schedule {
        - schedule_details_ids: one2many
    }

    class ScheduleDetail {
        - schedule_id: Many2one
        - sound_datetime: Datetime
        - tone_id: Many2one
    }

    class Location {
        - longitude: Float
        - latitude: Float
    }

    class Building {
        - location_id: Many2one
    }
    class Zone {
        - building_id: Many2one
        - location_id: Many2one // RELATED FIELD
    }
    class Device {
        - Zone_id: Many2one
        - building_id: Many2one // RELATED FIELD
        - location_id: Many2one // RELATED FIELD
        - state: Selection
        - device_service_url: Char
        - device_group_id: Many2one
    }

    class DeviceGroup {
        - devices_ids: One2many
        - schedule_id: Many2one
        - schedule_details_ids: One2many
        + action_apply_schedule()
    }
    class DeviceGroupScheduleDetails {
        - device_group_id: Many2one
        - sound_datetime: Datetime
        - tone_id: Many2one
        + action_play_sound()
    }

    iotAbstractModel <|-- NameModel: _inherit
    NameModel <|-- Tone: _inherit
    NameModel <|-- Location: _inherit
    NameModel <|-- Building: _inherit
    NameModel <|-- Zone: _inherit
    NameModel <|-- Device: _inherit
    NameModel <|-- BaseSchedule: _inherit
    BaseSchedule <|-- DeviceGroup: _inherit
    BaseSchedule <|-- Schedule: _inherit
    BaseScheduleDetail <|-- DeviceGroupScheduleDetails: _inherit
    BaseScheduleDetail <|-- ScheduleDetail: _inherit
    DeviceGroup "1" -- "0..*" DeviceGroupScheduleDetails : schedule_detail_ids
    Schedule "1" -- "0..*" ScheduleDetail : schedule_details_ids
    
%%    Contact --o ContactType : contact_type_id
%%    Contact --o Title : title_id
%%    Contact --o IDType : id_type_id
%%    Contact --o Position : contact_position_id


%%    Contact "1" -- "0..*" Appointment : appointment_ids
%%    Contact "1" -- "0..*" AppointmentRequest : appointment_request_ids
%%    Contact "1" -- "0..*" Visit : visit_ids
%%    Contact "1" -- "0..*" Invitation : invitation_ids

```


## Operations ( CommandQueue ) Class Diagram
    this class diagram shows the operations that can be performed.
```mermaid
classDiagram
    class iotAbstractModel {
        - _name='iot.abstract_model'
        - company_id: Many2one
    }
    class CommandQueue {
        - _name='iot.command_queue'
        - name: Char // Odoo Sequence 
        - state: Selection
        - executed_datetime: Datetime
        - device_id: Many2one
        - tone_id: Many2one
        - sound_datetime: Datetime
        - comments: Text

        + action_play_sound()
        
    }
    
    iotAbstractModel <|-- CommandQueue: _inherit
    

```




