    def action_generate_config_json(self):
        week_day_dict = dict(self.schedule_detail_ids._fields['week_day'].selection)
        device_group_data = {}

        # Group schedule details by week_day
        for detail in self.schedule_detail_ids:
            day_name = week_day_dict.get(detail.week_day, "Unknown")
            hour = int(detail.sound_time)
            minute = int((detail.sound_time - hour) * 60)
            time_str = "{:02}:{:02}".format(hour, minute)
            detail_data = {
                "time":  time_str,
                "tone_name": detail.tone_id.name,
                "grace_seconds": detail.grace_seconds
            }

            # Initialize list if the day does not exist in device_group_data
            if day_name not in device_group_data:
                device_group_data[day_name] = []

            device_group_data[day_name].append(detail_data)

        # Convert the device_group_data dictionary to a JSON-formatted string
        self.config_json = json.dumps({
            "name": self.name,
            "date": fields.Date.today().strftime("%Y-%m-%d"),
            "schedule_detail": device_group_data
        }, indent=4)


<page string="Config JSON">
                        <group>
                            <field name="config_json" readonly="1" widget="text" nolabel="1"/>
                        </group>
                    </page>

<button name="action_generate_config_json" type="object" string="Config JSON" class="btn-secondary"/>
