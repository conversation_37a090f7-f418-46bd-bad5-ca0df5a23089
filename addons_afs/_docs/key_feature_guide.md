# Key Feature Guide for IoT Device Manager

## Overview
IoT Device Manager is a system for managing sound devices through centralized control, offering scheduling, playback, and remote management.

## Key Features

### System Components
- **Server (Device Management System - Odoo):** Handles device configurations and command queues.
- **Server API:** Enables communication with IoT devices (e.g., `create_command_queue`).
- **Client App:** Sound Player Web Portal for managing playback.
- **Client Service:** Background tasks for device communication.

### Device Management
- Add, list, and update configurations for IoT devices.
- Manage multiple devices seamlessly over a network.

### Audio File Management
- Upload, remove, or retrieve tones.
- **Upload Sound File**: Upload sound files from the client device or Odoo server.
- **Sync Sound File**: Sync sound files from any client device to the Odoo server.
- Play sounds on designated IoT devices.

### Remote Control
- Start, stop, and check the status of services on IoT devices.

### API Integration
- **Server API:** For queuing commands.
- **Client API:** For operations like `upload_tone`, `play_sound`, and `get_service_status`.

### Sound Scheduling
- **Generate Default Schedule**: Automatically generate a default playback schedule for devices.
- **Edit and Customize Schedule**: Modify and create custom schedules for device groups.
- **Apply Schedule**: Apply the created schedule to the selected device or device group.
- **Control of Scheduling on Devices**:
  - **Scheduler State**: View and manage the state of the device’s scheduler (e.g., active, inactive).
  - **Start Scheduler**: Start the playback schedule for the device.
  - **Stop Scheduler**: Stop the playback schedule for the device.
  - **Reload Scheduler**: Reload the schedule to apply any updates or changes.

### Broadcast Sound
- **Select Target**:
  - Devices or device groups.
  - Option to include inactive devices.
- **Sound File Selection**: Choose the file to broadcast.
- **Operations Monitoring**:
  - **Progress**: 0% to 100%.
  - **Total Devices**: Number of targeted devices.
  - **Success Count**: Number of successful operations.
  - **Failure Count**: Number of failed operations.

### Dashboard
- **Overview Information**:
  - Online Devices
  - Offline Devices
  - Alerts Count
- **Autorefresh**: Updates in the background.

---
