# -*- coding: utf-8 -*-
from odoo import fields, models
from odoo.exceptions import UserError
import asyncio

class Device(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "iot.device"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    attachment_ids = fields.One2many('iot.device_sound_attachment', 'device_id')
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_upload_files(self):
        for file in self.attachment_ids:
            response = self.upload_file(file.sound_attachment_id.name, file.sound_attachment_id.datas)

    def action_check_status(self):
        response = self.env["iot.sound_client_api"].is_alive(self.device_service_url)
        self.handle_api_push_response(response)
        if response['response_code'] == "1":
            self.state = "online"
            response_result = response.get('response_result', {})
            self.scheduler_state = response_result.get('scheduler_state', "stopped")
            self.client_app_version = response_result.get('client_app_version', "")
            self.reset_error()
        else:
            self.state = "offline"
            self.scheduler_state = "stopped"


        self.send_auto_refresh()


    def action_start_scheduler(self):
        response = self.env["iot.sound_client_api"].start_scheduler(self.device_service_url)
        self.handle_api_push_response(response)
        if response['response_code'] == "1":
            self.scheduler_state = "running"
            self.reset_error()
        else:
            self.scheduler_state = "stopped"

    def action_stop_scheduler(self):
        response = self.env["iot.sound_client_api"].stop_scheduler(self.device_service_url)
        self.handle_api_push_response(response)
        if response['response_code'] == "1":
            self.scheduler_state = "stopped"
            self.reset_error()

    def action_reload_scheduler(self):
        response = self.env["iot.sound_client_api"].reload_scheduler(self.device_service_url)
        self.handle_api_push_response(response)
        if response['response_code'] == "1":
            self.scheduler_state = "running"
            self.reset_error()


    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def play_sound(self, attachment , device_id,device_client_id):
        """Play the sound associated with the given attachment."""
        # TODO: call play_tone , handle response with command queue
        response = self.env["iot.sound_client_api"].play_tone(self.device_service_url,attachment.name,device_id,device_client_id)
        return response


    def create_attachment(self, attachment_id):
        """
        Creates an attachment record in iot.device_sound_attachment if it doesn't already exist
        for the current device.

        :param attachment_id: ID of the sound attachment to link to the device
        """
        # Check if attachment already exists for this device
        existing_attachment = self.env['iot.device_sound_attachment'].search([
            ('device_id', '=', self.id),
            ('sound_attachment_id', '=', attachment_id)
        ], limit=1)

        if not existing_attachment:
            self.env['iot.device_sound_attachment'].create({
                'device_id': self.id,
                'sound_attachment_id': attachment_id
            })
        return True

    def upload_schedule_config(self,  device_info ,base_config=None ):
        if self.device_group_id:
            config_json = self.get_device_config(base_config)
            return self.env["iot.sound_client_api"].save_schedule_config(self.device_service_url, config_json , device_info)

        else:
            raise UserError("Device Group not found")


    def _upload_schedule_config_on_device(self, base_config, device_info):
        if self.device_group_id:
            loop = asyncio.get_event_loop()

            config_json = self.get_device_config(base_config)

            return loop.run_in_executor(None, self.sudo().env["iot.sound_client_api"].save_schedule_config,
                                        self.device_service_url,
                                        config_json,
                                        device_info)
        else:
            raise UserError("Device Group not found")


    def upload_file(self,file_name,datas , device_info):

        payload = {
            "file_name": file_name,
            "file_base64": datas
        }
        return self.env["iot.sound_client_api"].upload_file(self.device_service_url,payload , device_info)

    def remove_file(self, file_name):
        return self.env["iot.sound_client_api"].remove_tone(self.device_service_url, file_name)


    def _upload_file_on_device(self, file_name,datas,device_info):
        """Run the upload_file method in an executor to avoid blocking the event loop."""
        payload = {
            "file_name": file_name,
            "file_base64": datas
        }
        loop = asyncio.get_event_loop()
        return loop.run_in_executor(None, self.sudo().env["iot.sound_client_api"].upload_file,
                                    self.device_service_url,
                                    payload,
                                    device_info)


    # endregion
