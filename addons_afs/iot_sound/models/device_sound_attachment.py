from odoo import api, fields, models
from odoo.exceptions import UserError


class DeviceSoundAttachment(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "iot.device_sound_attachment"
    _inherit ="iot.abstract_model"
    _description = "Device Sound Attachment"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    device_id = fields.Many2one('iot.device', string="Device")
    sound_attachment_id = fields.Many2one('ir.attachment', string="Sound Attachment" ,domain=[('is_sound', '=', True)])
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_play_sound(self):
        """Play sound using the associated device and handle responses."""
        for record in self:
            response = record.device_id.play_sound(record.sound_attachment_id,record.device_id ,record.device_id.client_device_id)
            record.device_id.create_command_queue(command_type='play_sound_manual', response=response,attachment_id=record.sound_attachment_id.id)

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def unlink(self):
        for record in self:
            if record.sound_attachment_id:
                    file_name = record.sound_attachment_id.name
                    response = record.device_id.remove_file(file_name)
                    record.device_id.create_command_queue(command_type='delete_file', response=response,attachment_id=record.sound_attachment_id.id)

        return super(DeviceSoundAttachment, self).unlink()
    # endregion





    # endregion
