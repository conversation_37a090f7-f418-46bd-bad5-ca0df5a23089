# -*- coding: utf-8 -*-
import logging

import requests

from odoo import models
import json


class SoundClientAPI(models.AbstractModel):
    """Manage Ruckus Controller API"""
    _name = "iot.sound_client_api"
    _description = "Sound Client API"
    _inherit = "iot_base.client_base_api"

    # endregion

    # region Request Auth ---------------------------------------------------------------
    @property
    def END_POINTS(self):
        return {
            "HOME": "/home",
            "UPLOAD_FILE": "/upload-tone/",
            "GET_SOUND_LIST": "/tones/",
            "MANAGE_TONES": "/manage_tones",
            "REMOVE_TONE": "/remove-tone",
            "PLAY_TONE": "/play-tone/",
            "LIST_DEVICES": "/devices/",
            "GET_CONFIG": "/get-config",
            "GET_SCHEDULE_CONFIG": "/get-schedule-config",
            "SAVE_SCHEDULE_CONFIG": "/save-schedule-config",
            "GET_SCHEDULE_STATUS": "/scheduler-status",
            "SAVE_CONFIG": "/save-config",
            "START_SCHEDULE": "/start-scheduler",
            "RELOAD_SCHEDULE_CONFIG": "/reload-scheduler-config",
            "STOP_SCHEDULE": "/stop-scheduler",
            "IS_ALIVE": "/is-alive",
            "GET_FILES": "/files",

        }

    def _handle_request_exceptions(self, exception, response=None):
        """Centralize exception handling for API requests."""
        response_status_code = response.status_code if response else 500
        response_code = "0"
        response_message = "An unexpected error occurred."
        response_result = None


        if isinstance(exception, requests.exceptions.ConnectionError) or response_status_code == 404:
            response_message = "Connection error: Unable to connect to the sound player API (Client App)."
        elif isinstance(exception, requests.exceptions.Timeout):
            response_message = "Timeout error: The request timed out."
        elif isinstance(exception, requests.exceptions.HTTPError):
            response_message = f"HTTP error occurred: {exception}"
        elif isinstance(exception, requests.exceptions.RequestException):
            response_message = f"An error occurred during the request: {exception}"
        elif isinstance(exception, ValueError):
            response_message = "The (Client App) returned an invalid Value."
        elif isinstance(exception, json.JSONDecodeError) :
            response_message = "Error parsing response: The sound player API (Client App) returned an invalid JSON."


        return {
            'response_code': response_code,
            'response_status_code': response_status_code,
            'response_message': response_message,
            'response_result': response_result
        }

    def get_response_dict(self,response):
        try:
            response_json = response.json()
            return {
                'response_code': response_json.get('response_code', ''),
                'response_status_code': response.status_code,
                'response_message': response_json.get('response_message', ''),
                'response_result': response_json.get('response_result', '')
            }

        except Exception as e:
            return self._handle_request_exceptions(e, response)


    def is_alive(self, base_url, token="", **kwargs):  # /IS_ALIVE
        url = f"{base_url}{self.END_POINTS['IS_ALIVE']}"
        response = None
        try:
            response = self.session.get(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)
        except Exception as e:
            return self._handle_request_exceptions(e,response)


    def start_scheduler(self, base_url, token="", **kwargs):  # /START_SCHEDULE
        url = f"{base_url}{self.END_POINTS['START_SCHEDULE']}"
        response = None
        try:
            response = self.session.post(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)
        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def stop_scheduler(self, base_url, token="", **kwargs):  # /STOP_SCHEDULE
        url = f"{base_url}{self.END_POINTS['STOP_SCHEDULE']}"
        response = None
        try:
            response = self.session.post(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)

        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def reload_scheduler(self, base_url, token="", **kwargs):  # /RELOAD_SCHEDULE
        url = f"{base_url}{self.END_POINTS['RELOAD_SCHEDULE_CONFIG']}"
        response = None
        try:
            response = self.session.post(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)
        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def get_files(self, base_url, token="", **kwargs):  # /GET_FILES
        url = f"{base_url}{self.END_POINTS['GET_FILES']}"
        response = None
        try:
            response = self.session.get(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)

        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def remove_tone(self, base_url, file_name, token="", **kwargs):  # /REMOVE_TONE
        url = f"{base_url}{self.END_POINTS['REMOVE_TONE']}/{file_name}"
        response = None
        try:
            response = self.session.delete(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)
        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def play_tone(self, base_url, file_name,device_id,device_client_id, token="", **kwargs):  # /PLAY_TONE
        url = f"{base_url}{self.END_POINTS['PLAY_TONE']}?tone_file_name={file_name}"
        response = None
        try:
            response = self.session.post(url, headers=self.get_default_headers(token),verify=False)
            response_dict= self.get_response_dict(response)
            response_dict.update({"device_id": device_id, "device_client_id": device_client_id})
            return response_dict

        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def get_config(self, base_url, token="", **kwargs):  # /GET_CONFIG
        url = f"{base_url}{self.END_POINTS['GET_CONFIG']}"

        response = None
        try:
            response = self.session.get(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)

        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def get_schedule_config(self, base_url, token="", **kwargs):  # get_schedule_config
        url = f"{base_url}{self.END_POINTS['GET_SCHEDULE_CONFIG']}"

        response = None
        try:
            response = self.session.get(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)

        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def save_schedule_config(self, base_url, payload, device_info, token="", **kwargs):
        url = f"{base_url}{self.END_POINTS['SAVE_SCHEDULE_CONFIG']}"

        response = None
        try:
            response = self.session.post( url, headers=self.get_default_headers(token), data=payload, verify=False )

            # Check for HTTP errors
            response.raise_for_status()

            response_dict = self.get_response_dict(response)
            response_dict.update(device_info=device_info)
            return response_dict

        except Exception as e:
            response_dict = self._handle_request_exceptions(e,response)
            response_dict.update(device_info=device_info)
            return response_dict

    def save_config(self, base_url, token="", **kwargs):  # save_config
        url = f"{base_url}{self.END_POINTS['SAVE_CONFIG']}"

        response = None
        try:
            response = self.session.post(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)

        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def update_api_token(self, token="", **kwargs):
        """update controller (token) in ir.config_parameter"""
        # if not token:
        #     token = self.get_token(self.api_base_url, self.api_username, self.api_password, **kwargs)
        # self.env['ir.config_parameter'].sudo().set_param('mdu_controller.token', token)
        return ""

    def get_default_headers(self, token="", **kwargs):
        """return default headers and combined kwargs"""
        headers = {
            "Content-Type": "application/json",
        }
        # "Authorization": f"AccessToken={token}"
        # loop kwargs to update headers dictionary
        for key, value in kwargs.items():
            headers[key] = value

        return headers

    def get_token(self, base_url, username, password, **kwargs):
        # url = "https://use1-omada-northbound.tplinkcloud.com/openapi/authorize/token?grant_type=client_credentials"
        return ""

        # endregion

        # region Sample API ----------------------------------------------------------------------

    def get_sound_list(self, base_url, token="", **kwargs):
        url = f"{base_url}{self.END_POINTS['GET_SOUND_LIST']}"

        response = None
        try:
            response = self.session.get(url, headers=self.get_default_headers(token), verify=False)
            return self.get_response_dict(response)

        except Exception as e:
            return self._handle_request_exceptions(e,response)

    def upload_file(self, base_url, payload,device_info,  token="", **kwargs):
        url = f"{base_url}{self.END_POINTS['UPLOAD_FILE']}"
        response = None
        try:
            response = self.session.post( url, headers=self.get_default_headers(token), json=payload,  verify=False)
            # Check for HTTP errors
            response.raise_for_status()

            response_dict= self.get_response_dict(response)
            response_dict.update(device_info=device_info)
            return response_dict

        except Exception as e:
            response_dict = self._handle_request_exceptions(e,response)
            response_dict.update(device_info=device_info)
            return response_dict

    def add_base64_padding(self, base64_string):
        pad = '=' * ((4 - len(base64_string) % 4) % 4)
        return base64_string + pad
    # endregion
