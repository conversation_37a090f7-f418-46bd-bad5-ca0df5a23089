# -*- coding: utf-8 -*-
from uuid import uuid4

from odoo import models
import asyncio





class DeviceGroup(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "iot.device_group"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_push_config(self):
        """Uploads the schedule configuration for each device in the group and logs the result in the command queue."""
        self.batch_ref_id = uuid4()

        base_config = self.generate_config()
        for device in self.devices_ids:
            device_info = {
                "device_id":device.id,
            }
            if device.activate:
               response = device.upload_schedule_config( device_info,base_config)
               device.create_command_queue(command_type='update_config', response=response,ref_id=self.batch_ref_id)

    def action_push_config_async(self):
        self.batch_ref_id = uuid4()
        base_config = self.generate_config()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        tasks = []

        for device in self.devices_ids:
            if device.activate:
                device_info = {
                    "device_id": device.id,
                }
                tasks.append(device._upload_schedule_config_on_device(base_config, device_info))

        responses = loop.run_until_complete(asyncio.gather(*tasks))
        for response in responses:
            device_info = response.get('device_info', {})
            device_id = device_info.get('device_id', 0)
            device = self.env['iot.device'].sudo().search([('id', '=', device_id)])
            device.create_command_queue(command_type='update_config', response=response, ref_id=self.batch_ref_id)

        loop.close()

    def action_upload_sound_files(self):
        self.batch_ref_id = uuid4()
        for device in self.devices_ids:
            if device.activate:
                for file in self.sound_attachment_ids:
                    # file_data_base64 = base64.b64encode(file.datas).decode('utf-8')
                    file_data_base64=file.datas.decode('utf-8')
                    response = device.upload_file(file.name, file_data_base64)
                    device.create_command_queue(command_type='upload_file', response=response,attachment_id=file.id,ref_id=self.batch_ref_id)
                    device.create_attachment(file.id)


    def action_upload_sound_files_async(self):
        self.batch_ref_id = uuid4()
        # Set up the event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        tasks = []

        for device in self.devices_ids:
            if device.activate:
                for file in self.sound_attachment_ids:
                    file_data_base64=file.datas.decode('utf-8')
                    device_info = {
                        "device_id": device.id,
                        "device_client_id": device.client_device_id,
                        "attachment_id": file.id
                    }

                    tasks.append(device._upload_file_on_device(file.name, file_data_base64,device_info))

        # Run tasks concurrently and check results
        responses = loop.run_until_complete(asyncio.gather(*tasks))
        for response in responses:
            device_info = response.get('device_info', {})
            device_id=device_info.get('device_id', 0)
            attachment_id=device_info.get('attachment_id', 0)
            device = self.env['iot.device'].sudo().search([('id', '=', device_id)])
            device.create_command_queue(command_type='upload_file', response=response,attachment_id=attachment_id,
                                        ref_id=self.batch_ref_id)

            device.create_attachment(attachment_id)
        # Close the event loop
        loop.close()



    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    # endregion
