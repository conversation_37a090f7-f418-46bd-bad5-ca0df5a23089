# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError
import base64
import logging

_logger = logging.getLogger(__name__)


class NebularFloorPlan(models.Model):
    """Floor Plan Model for Nebular Dashboard System"""
    
    # 1. Private attributes
    _name = 'nebular.floor.plan'
    _description = 'Nebular Floor Plan'
    _inherit = ['mail.thread', 'mail.activity.schedule']
    _order = 'building_id, floor_id, name'
    _rec_name = 'display_name'
    
    # 2. Default methods
    def _default_scale_factor(self):
        """Default scale factor for floor plan"""
        return 1.0
    
    def _default_grid_size(self):
        """Default grid size for floor plan"""
        return 10
    
    # 3. Field declarations
    name = fields.Char(
        string='Plan Name',
        required=True,
        tracking=True,
        help='Name of the floor plan'
    )
    description = fields.Text(
        string='Description',
        help='Description of the floor plan'
    )
    building_id = fields.Many2one(
        'nebular.building',
        string='Building',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Building this floor plan belongs to'
    )
    floor_id = fields.Many2one(
        'nebular.floor',
        string='Floor',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Floor this plan represents'
    )
    plan_image = fields.Binary(
        string='Floor Plan Image',
        attachment=True,
        help='Floor plan image file'
    )
    plan_image_filename = fields.Char(
        string='Image Filename',
        help='Filename of the floor plan image'
    )
    image_width = fields.Integer(
        string='Image Width',
        help='Width of the floor plan image in pixels'
    )
    image_height = fields.Integer(
        string='Image Height',
        help='Height of the floor plan image in pixels'
    )
    scale_factor = fields.Float(
        string='Scale Factor',
        default=_default_scale_factor,
        help='Scale factor for the floor plan (meters per pixel)'
    )
    grid_size = fields.Integer(
        string='Grid Size',
        default=_default_grid_size,
        help='Grid size for marker positioning'
    )
    is_active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True,
        help='Whether this floor plan is active'
    )
    is_published = fields.Boolean(
        string='Published',
        default=False,
        tracking=True,
        help='Whether this floor plan is published for use'
    )
    version = fields.Char(
        string='Version',
        default='1.0',
        help='Version of the floor plan'
    )
    
    # Relationships
    marker_ids = fields.One2many(
        'nebular.marker',
        'floor_plan_id',
        string='Markers',
        help='Markers placed on this floor plan'
    )
    zone_ids = fields.Many2many(
        'nebular.zone',
        string='Zones',
        help='Zones covered by this floor plan'
    )
    room_ids = fields.Many2many(
        'nebular.room',
        string='Rooms',
        help='Rooms shown on this floor plan'
    )
    
    # 4. Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Display name for the floor plan'
    )
    marker_count = fields.Integer(
        string='Marker Count',
        compute='_compute_marker_count',
        help='Number of markers on this floor plan'
    )
    room_count = fields.Integer(
        string='Room Count',
        compute='_compute_room_count',
        help='Number of rooms on this floor plan'
    )
    zone_count = fields.Integer(
        string='Zone Count',
        compute='_compute_zone_count',
        help='Number of zones on this floor plan'
    )
    has_image = fields.Boolean(
        string='Has Image',
        compute='_compute_has_image',
        help='Whether this floor plan has an image'
    )
    image_info = fields.Char(
        string='Image Info',
        compute='_compute_image_info',
        help='Information about the floor plan image'
    )
    
    # 5. Compute methods
    @api.depends('name', 'building_id.name', 'floor_id.name')
    def _compute_display_name(self):
        """Compute display name"""
        for record in self:
            if record.building_id and record.floor_id:
                record.display_name = f"{record.building_id.name} - {record.floor_id.name} - {record.name}"
            else:
                record.display_name = record.name or 'New Floor Plan'
    
    @api.depends('marker_ids')
    def _compute_marker_count(self):
        """Compute marker count"""
        for record in self:
            record.marker_count = len(record.marker_ids)
    
    @api.depends('room_ids')
    def _compute_room_count(self):
        """Compute room count"""
        for record in self:
            record.room_count = len(record.room_ids)
    
    @api.depends('zone_ids')
    def _compute_zone_count(self):
        """Compute zone count"""
        for record in self:
            record.zone_count = len(record.zone_ids)
    
    @api.depends('plan_image')
    def _compute_has_image(self):
        """Compute has image"""
        for record in self:
            record.has_image = bool(record.plan_image)
    
    @api.depends('image_width', 'image_height', 'plan_image_filename')
    def _compute_image_info(self):
        """Compute image info"""
        for record in self:
            if record.image_width and record.image_height:
                record.image_info = f"{record.image_width}x{record.image_height}"
                if record.plan_image_filename:
                    record.image_info += f" ({record.plan_image_filename})"
            else:
                record.image_info = record.plan_image_filename or 'No image'
    
    # 6. Constraints
    @api.constrains('scale_factor')
    def _check_scale_factor(self):
        """Check scale factor is positive"""
        for record in self:
            if record.scale_factor <= 0:
                raise ValidationError("Scale factor must be positive")
    
    @api.constrains('grid_size')
    def _check_grid_size(self):
        """Check grid size is positive"""
        for record in self:
            if record.grid_size <= 0:
                raise ValidationError("Grid size must be positive")
    
    @api.constrains('image_width', 'image_height')
    def _check_image_dimensions(self):
        """Check image dimensions are positive"""
        for record in self:
            if record.image_width and record.image_width <= 0:
                raise ValidationError("Image width must be positive")
            if record.image_height and record.image_height <= 0:
                raise ValidationError("Image height must be positive")
    
    @api.constrains('floor_id', 'building_id')
    def _check_floor_building_consistency(self):
        """Check floor belongs to the specified building"""
        for record in self:
            if record.floor_id and record.building_id:
                if record.floor_id.building_id != record.building_id:
                    raise ValidationError("Floor must belong to the specified building")
    
    # 7. Onchange methods
    @api.onchange('building_id')
    def _onchange_building_id(self):
        """Update floor domain when building changes"""
        if self.building_id:
            return {
                'domain': {
                    'floor_id': [('building_id', '=', self.building_id.id)]
                }
            }
        else:
            return {
                'domain': {
                    'floor_id': []
                }
            }
    
    @api.onchange('floor_id')
    def _onchange_floor_id(self):
        """Update zones and rooms when floor changes"""
        if self.floor_id:
            # Update zones
            zone_domain = [('floor_id', '=', self.floor_id.id)]
            zones = self.env['nebular.zone'].search(zone_domain)
            self.zone_ids = [(6, 0, zones.ids)]
            
            # Update rooms
            room_domain = [('floor_id', '=', self.floor_id.id)]
            rooms = self.env['nebular.room'].search(room_domain)
            self.room_ids = [(6, 0, rooms.ids)]
    
    # 8. CRUD overrides
    @api.model
    def create(self, vals):
        """Override create to set default values"""
        # Set default name if not provided
        if not vals.get('name'):
            building = self.env['nebular.building'].browse(vals.get('building_id'))
            floor = self.env['nebular.floor'].browse(vals.get('floor_id'))
            if building and floor:
                vals['name'] = f"{building.name} - {floor.name} Plan"
        
        record = super().create(vals)
        
        # Auto-populate zones and rooms if floor is specified
        if record.floor_id:
            zones = self.env['nebular.zone'].search([('floor_id', '=', record.floor_id.id)])
            rooms = self.env['nebular.room'].search([('floor_id', '=', record.floor_id.id)])
            record.write({
                'zone_ids': [(6, 0, zones.ids)],
                'room_ids': [(6, 0, rooms.ids)]
            })
        
        return record
    
    def write(self, vals):
        """Override write to handle special cases"""
        # Handle floor change
        if 'floor_id' in vals:
            floor_id = vals['floor_id']
            if floor_id:
                zones = self.env['nebular.zone'].search([('floor_id', '=', floor_id)])
                rooms = self.env['nebular.room'].search([('floor_id', '=', floor_id)])
                vals.update({
                    'zone_ids': [(6, 0, zones.ids)],
                    'room_ids': [(6, 0, rooms.ids)]
                })
        
        return super().write(vals)
    
    def unlink(self):
        """Override unlink to check dependencies"""
        for record in self:
            if record.marker_ids:
                raise UserError(f"Cannot delete floor plan '{record.display_name}' because it has markers")
        return super().unlink()
    
    # 9. Action methods
    def action_publish(self):
        """Publish floor plan"""
        self.ensure_one()
        if not self.plan_image:
            raise UserError("Cannot publish floor plan without an image")
        self.is_published = True
        self.message_post(body="Floor plan published")
    
    def action_unpublish(self):
        """Unpublish floor plan"""
        self.ensure_one()
        self.is_published = False
        self.message_post(body="Floor plan unpublished")
    
    def action_activate(self):
        """Activate floor plan"""
        self.ensure_one()
        self.is_active = True
        self.message_post(body="Floor plan activated")
    
    def action_deactivate(self):
        """Deactivate floor plan"""
        self.ensure_one()
        self.is_active = False
        self.message_post(body="Floor plan deactivated")
    
    def action_view_markers(self):
        """View markers on this floor plan"""
        self.ensure_one()
        return {
            'name': f'Markers - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.marker',
            'view_mode': 'list,form',
            'domain': [('floor_plan_id', '=', self.id)],
            'context': {'default_floor_plan_id': self.id}
        }
    
    def action_duplicate(self):
        """Duplicate floor plan"""
        self.ensure_one()
        copy_data = {
            'name': f"{self.name} (Copy)",
            'version': '1.0',
            'is_published': False
        }
        new_plan = self.copy(copy_data)
        return {
            'name': 'Duplicated Floor Plan',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.floor.plan',
            'res_id': new_plan.id,
            'view_mode': 'form',
            'target': 'current'
        }
    
    # 10. Other business methods
    def get_marker_positions(self):
        """Get all marker positions on this floor plan"""
        self.ensure_one()
        positions = []
        for marker in self.marker_ids:
            positions.append({
                'id': marker.id,
                'name': marker.name,
                'x': marker.position_x,
                'y': marker.position_y,
                'type': marker.marker_type,
                'status': marker.status
            })
        return positions
    
    def add_marker_at_position(self, x, y, marker_type='info', name=None):
        """Add a marker at specific position"""
        self.ensure_one()
        if not name:
            name = f"Marker {len(self.marker_ids) + 1}"
        
        marker = self.env['nebular.marker'].create({
            'name': name,
            'floor_plan_id': self.id,
            'position_x': x,
            'position_y': y,
            'marker_type': marker_type
        })
        return marker
    
    def get_plan_bounds(self):
        """Get floor plan boundaries"""
        self.ensure_one()
        return {
            'width': self.image_width or 0,
            'height': self.image_height or 0,
            'scale_factor': self.scale_factor,
            'grid_size': self.grid_size
        }
    
    def convert_pixel_to_meters(self, pixels):
        """Convert pixels to meters using scale factor"""
        self.ensure_one()
        return pixels * self.scale_factor
    
    def convert_meters_to_pixels(self, meters):
        """Convert meters to pixels using scale factor"""
        self.ensure_one()
        if self.scale_factor:
            return meters / self.scale_factor
        return 0