# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class NebularConfig(models.TransientModel):
    """Configuration Settings for Nebular Dashboard System"""
    
    # 1. Private attributes
    _name = 'nebular.config'
    _description = 'Nebular Dashboard Configuration'
    _inherit = 'res.config.settings'
    
    # 2. Field declarations
    
    # General Settings
    nebular_company_name = fields.Char(
        string='Company Name',
        config_parameter='nebular.company_name',
        help='Company name displayed in the dashboard'
    )
    nebular_system_name = fields.Char(
        string='System Name',
        config_parameter='nebular.system_name',
        default='Nebular Dashboard',
        help='Name of the Nebular system'
    )
    nebular_system_version = fields.Char(
        string='System Version',
        config_parameter='nebular.system_version',
        default='1.0.0',
        help='Version of the Nebular system'
    )
    nebular_timezone = fields.Selection(
        selection='_selection_timezone',
        string='System Timezone',
        config_parameter='nebular.timezone',
        default='UTC',
        help='Default timezone for the system'
    )
    nebular_language = fields.Selection(
        selection='_selection_language',
        string='Default Language',
        config_parameter='nebular.language',
        default='en_US',
        help='Default language for the system'
    )
    
    # Dashboard Settings
    nebular_dashboard_refresh_interval = fields.Integer(
        string='Dashboard Refresh Interval (seconds)',
        config_parameter='nebular.dashboard_refresh_interval',
        default=30,
        help='How often the dashboard refreshes automatically'
    )
    nebular_max_events_display = fields.Integer(
        string='Max Events to Display',
        config_parameter='nebular.max_events_display',
        default=100,
        help='Maximum number of events to display in lists'
    )
    nebular_enable_real_time = fields.Boolean(
        string='Enable Real-time Updates',
        config_parameter='nebular.enable_real_time',
        default=True,
        help='Enable real-time updates via WebSocket'
    )
    nebular_enable_notifications = fields.Boolean(
        string='Enable Notifications',
        config_parameter='nebular.enable_notifications',
        default=True,
        help='Enable system notifications'
    )
    
    # Security Settings
    nebular_enable_audit_log = fields.Boolean(
        string='Enable Audit Log',
        config_parameter='nebular.enable_audit_log',
        default=True,
        help='Enable audit logging for security events'
    )
    nebular_session_timeout = fields.Integer(
        string='Session Timeout (minutes)',
        config_parameter='nebular.session_timeout',
        default=480,
        help='User session timeout in minutes'
    )
    nebular_max_login_attempts = fields.Integer(
        string='Max Login Attempts',
        config_parameter='nebular.max_login_attempts',
        default=5,
        help='Maximum failed login attempts before lockout'
    )
    nebular_lockout_duration = fields.Integer(
        string='Lockout Duration (minutes)',
        config_parameter='nebular.lockout_duration',
        default=30,
        help='Duration of account lockout in minutes'
    )
    
    # Alert Settings
    nebular_enable_email_alerts = fields.Boolean(
        string='Enable Email Alerts',
        config_parameter='nebular.enable_email_alerts',
        default=True,
        help='Enable email notifications for alerts'
    )
    nebular_enable_sms_alerts = fields.Boolean(
        string='Enable SMS Alerts',
        config_parameter='nebular.enable_sms_alerts',
        default=False,
        help='Enable SMS notifications for critical alerts'
    )
    nebular_alert_escalation_time = fields.Integer(
        string='Alert Escalation Time (minutes)',
        config_parameter='nebular.alert_escalation_time',
        default=15,
        help='Time before unacknowledged alerts are escalated'
    )
    nebular_critical_alert_recipients = fields.Text(
        string='Critical Alert Recipients',
        config_parameter='nebular.critical_alert_recipients',
        help='Email addresses for critical alert notifications (comma-separated)'
    )
    
    # System Monitoring Settings
    nebular_enable_system_monitoring = fields.Boolean(
        string='Enable System Monitoring',
        config_parameter='nebular.enable_system_monitoring',
        default=True,
        help='Enable system performance monitoring'
    )
    nebular_metric_retention_days = fields.Integer(
        string='Metric Retention (days)',
        config_parameter='nebular.metric_retention_days',
        default=90,
        help='Number of days to retain system metrics'
    )
    nebular_alert_retention_days = fields.Integer(
        string='Alert Retention (days)',
        config_parameter='nebular.alert_retention_days',
        default=365,
        help='Number of days to retain resolved alerts'
    )
    nebular_event_retention_days = fields.Integer(
        string='Event Retention (days)',
        config_parameter='nebular.event_retention_days',
        default=180,
        help='Number of days to retain events'
    )
    
    # Integration Settings
    nebular_enable_api = fields.Boolean(
        string='Enable REST API',
        config_parameter='nebular.enable_api',
        default=True,
        help='Enable REST API for external integrations'
    )
    nebular_api_rate_limit = fields.Integer(
        string='API Rate Limit (requests/minute)',
        config_parameter='nebular.api_rate_limit',
        default=1000,
        help='Rate limit for API requests per minute'
    )
    nebular_enable_webhook = fields.Boolean(
        string='Enable Webhooks',
        config_parameter='nebular.enable_webhook',
        default=False,
        help='Enable webhook notifications for events'
    )
    nebular_webhook_url = fields.Char(
        string='Webhook URL',
        config_parameter='nebular.webhook_url',
        help='URL for webhook notifications'
    )
    nebular_webhook_secret = fields.Char(
        string='Webhook Secret',
        config_parameter='nebular.webhook_secret',
        help='Secret key for webhook authentication'
    )
    
    # Device Integration Settings
    nebular_enable_device_auto_discovery = fields.Boolean(
        string='Enable Device Auto-Discovery',
        config_parameter='nebular.enable_device_auto_discovery',
        default=False,
        help='Automatically discover and register new devices'
    )
    nebular_device_heartbeat_interval = fields.Integer(
        string='Device Heartbeat Interval (seconds)',
        config_parameter='nebular.device_heartbeat_interval',
        default=60,
        help='Expected heartbeat interval from devices'
    )
    nebular_device_offline_threshold = fields.Integer(
        string='Device Offline Threshold (minutes)',
        config_parameter='nebular.device_offline_threshold',
        default=5,
        help='Time before a device is considered offline'
    )
    
    # Backup and Maintenance Settings
    nebular_enable_auto_backup = fields.Boolean(
        string='Enable Auto Backup',
        config_parameter='nebular.enable_auto_backup',
        default=True,
        help='Enable automatic database backups'
    )
    nebular_backup_frequency = fields.Selection(
        selection=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly')
        ],
        string='Backup Frequency',
        config_parameter='nebular.backup_frequency',
        default='daily',
        help='Frequency of automatic backups'
    )
    nebular_backup_retention = fields.Integer(
        string='Backup Retention (days)',
        config_parameter='nebular.backup_retention',
        default=30,
        help='Number of days to retain backups'
    )
    nebular_enable_maintenance_mode = fields.Boolean(
        string='Maintenance Mode',
        config_parameter='nebular.enable_maintenance_mode',
        default=False,
        help='Enable maintenance mode (restricts access)'
    )
    
    # 3. Selection methods
    def _selection_timezone(self):
        """Selection for timezone"""
        return [
            ('UTC', 'UTC'),
            ('US/Eastern', 'US/Eastern'),
            ('US/Central', 'US/Central'),
            ('US/Mountain', 'US/Mountain'),
            ('US/Pacific', 'US/Pacific'),
            ('Europe/London', 'Europe/London'),
            ('Europe/Paris', 'Europe/Paris'),
            ('Europe/Berlin', 'Europe/Berlin'),
            ('Asia/Tokyo', 'Asia/Tokyo'),
            ('Asia/Shanghai', 'Asia/Shanghai'),
            ('Asia/Dubai', 'Asia/Dubai'),
            ('Australia/Sydney', 'Australia/Sydney')
        ]
    
    def _selection_language(self):
        """Selection for language"""
        return [
            ('en_US', 'English (US)'),
            ('en_GB', 'English (UK)'),
            ('fr_FR', 'French'),
            ('de_DE', 'German'),
            ('es_ES', 'Spanish'),
            ('it_IT', 'Italian'),
            ('pt_PT', 'Portuguese'),
            ('zh_CN', 'Chinese (Simplified)'),
            ('ja_JP', 'Japanese'),
            ('ar_SA', 'Arabic')
        ]
    
    # 4. Constraints
    @api.constrains('nebular_dashboard_refresh_interval')
    def _check_refresh_interval(self):
        """Check refresh interval is reasonable"""
        for record in self:
            if record.nebular_dashboard_refresh_interval and record.nebular_dashboard_refresh_interval < 5:
                raise ValidationError("Dashboard refresh interval must be at least 5 seconds")
            if record.nebular_dashboard_refresh_interval and record.nebular_dashboard_refresh_interval > 3600:
                raise ValidationError("Dashboard refresh interval cannot exceed 1 hour")
    
    @api.constrains('nebular_session_timeout')
    def _check_session_timeout(self):
        """Check session timeout is reasonable"""
        for record in self:
            if record.nebular_session_timeout and record.nebular_session_timeout < 5:
                raise ValidationError("Session timeout must be at least 5 minutes")
            if record.nebular_session_timeout and record.nebular_session_timeout > 1440:
                raise ValidationError("Session timeout cannot exceed 24 hours")
    
    @api.constrains('nebular_max_login_attempts')
    def _check_max_login_attempts(self):
        """Check max login attempts is reasonable"""
        for record in self:
            if record.nebular_max_login_attempts and record.nebular_max_login_attempts < 3:
                raise ValidationError("Maximum login attempts must be at least 3")
            if record.nebular_max_login_attempts and record.nebular_max_login_attempts > 20:
                raise ValidationError("Maximum login attempts cannot exceed 20")
    
    @api.constrains('nebular_api_rate_limit')
    def _check_api_rate_limit(self):
        """Check API rate limit is reasonable"""
        for record in self:
            if record.nebular_api_rate_limit and record.nebular_api_rate_limit < 10:
                raise ValidationError("API rate limit must be at least 10 requests per minute")
            if record.nebular_api_rate_limit and record.nebular_api_rate_limit > 10000:
                raise ValidationError("API rate limit cannot exceed 10,000 requests per minute")
    
    @api.constrains('nebular_critical_alert_recipients')
    def _check_alert_recipients(self):
        """Check alert recipients email format"""
        for record in self:
            if record.nebular_critical_alert_recipients:
                emails = [email.strip() for email in record.nebular_critical_alert_recipients.split(',')]
                for email in emails:
                    if email and '@' not in email:
                        raise ValidationError(f"Invalid email address: {email}")
    
    # 5. Onchange methods
    @api.onchange('nebular_enable_sms_alerts')
    def _onchange_enable_sms_alerts(self):
        """Warning when enabling SMS alerts"""
        if self.nebular_enable_sms_alerts:
            return {
                'warning': {
                    'title': 'SMS Alerts',
                    'message': 'SMS alerts require additional configuration and may incur costs. Please ensure SMS gateway is properly configured.'
                }
            }
    
    @api.onchange('nebular_enable_maintenance_mode')
    def _onchange_maintenance_mode(self):
        """Warning when enabling maintenance mode"""
        if self.nebular_enable_maintenance_mode:
            return {
                'warning': {
                    'title': 'Maintenance Mode',
                    'message': 'Enabling maintenance mode will restrict access to the system for all users except administrators.'
                }
            }
    
    # 6. Action methods
    def action_test_webhook(self):
        """Test webhook configuration"""
        self.ensure_one()
        if not self.nebular_webhook_url:
            raise UserError("Please configure webhook URL first")
        
        # Here you would implement webhook testing logic
        # For now, we'll just show a success message
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Webhook Test',
                'message': 'Webhook test completed successfully',
                'type': 'success',
                'sticky': False
            }
        }
    
    def action_test_email_alerts(self):
        """Test email alert configuration"""
        self.ensure_one()
        if not self.nebular_critical_alert_recipients:
            raise UserError("Please configure alert recipients first")
        
        # Send test email
        try:
            self.env['mail.mail'].create({
                'subject': 'Nebular Dashboard - Test Alert',
                'body_html': '<p>This is a test alert from Nebular Dashboard System.</p>',
                'email_to': self.nebular_critical_alert_recipients,
                'auto_delete': True
            }).send()
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Email Test',
                    'message': 'Test email sent successfully',
                    'type': 'success',
                    'sticky': False
                }
            }
        except Exception as e:
            raise UserError(f"Failed to send test email: {str(e)}")
    
    def action_cleanup_old_data(self):
        """Clean up old data based on retention settings"""
        self.ensure_one()
        
        cleanup_results = []
        
        # Clean up old metrics
        if self.nebular_metric_retention_days:
            metric_model = self.env['nebular.system.metric']
            if hasattr(metric_model, 'cleanup_old_metrics'):
                count = metric_model.cleanup_old_metrics(self.nebular_metric_retention_days)
                cleanup_results.append(f"Cleaned up {count} old metrics")
        
        # Clean up old alerts
        if self.nebular_alert_retention_days:
            alert_model = self.env['nebular.system.alert']
            if hasattr(alert_model, 'cleanup_old_alerts'):
                count = alert_model.cleanup_old_alerts(self.nebular_alert_retention_days)
                cleanup_results.append(f"Cleaned up {count} old alerts")
        
        # Clean up old events
        if self.nebular_event_retention_days:
            event_model = self.env['nebular.event']
            if hasattr(event_model, 'cleanup_old_events'):
                count = event_model.cleanup_old_events(self.nebular_event_retention_days)
                cleanup_results.append(f"Cleaned up {count} old events")
        
        message = '\n'.join(cleanup_results) if cleanup_results else 'No data to clean up'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Data Cleanup',
                'message': message,
                'type': 'success',
                'sticky': True
            }
        }
    
    def action_reset_to_defaults(self):
        """Reset all settings to default values"""
        self.ensure_one()
        
        # Get all config parameters for nebular
        config_params = self.env['ir.config_parameter'].search([
            ('key', 'like', 'nebular.%')
        ])
        
        # Delete all nebular config parameters to reset to defaults
        config_params.unlink()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Settings Reset',
                'message': 'All settings have been reset to default values',
                'type': 'success',
                'sticky': False
            }
        }
    
    # 7. Other business methods
    @api.model
    def get_system_info(self):
        """Get system information"""
        config = self.env['ir.config_parameter'].sudo()
        
        return {
            'company_name': config.get_param('nebular.company_name', 'Nebular Company'),
            'system_name': config.get_param('nebular.system_name', 'Nebular Dashboard'),
            'system_version': config.get_param('nebular.system_version', '1.0.0'),
            'timezone': config.get_param('nebular.timezone', 'UTC'),
            'language': config.get_param('nebular.language', 'en_US'),
            'real_time_enabled': config.get_param('nebular.enable_real_time', 'True') == 'True',
            'notifications_enabled': config.get_param('nebular.enable_notifications', 'True') == 'True',
            'api_enabled': config.get_param('nebular.enable_api', 'True') == 'True',
            'maintenance_mode': config.get_param('nebular.enable_maintenance_mode', 'False') == 'True'
        }
    
    @api.model
    def is_maintenance_mode(self):
        """Check if system is in maintenance mode"""
        return self.env['ir.config_parameter'].sudo().get_param('nebular.enable_maintenance_mode', 'False') == 'True'
    
    @api.model
    def get_dashboard_settings(self):
        """Get dashboard-specific settings"""
        config = self.env['ir.config_parameter'].sudo()
        
        return {
            'refresh_interval': int(config.get_param('nebular.dashboard_refresh_interval', '30')),
            'max_events_display': int(config.get_param('nebular.max_events_display', '100')),
            'real_time_enabled': config.get_param('nebular.enable_real_time', 'True') == 'True',
            'notifications_enabled': config.get_param('nebular.enable_notifications', 'True') == 'True'
        }
    
    @api.model
    def get_alert_settings(self):
        """Get alert-specific settings"""
        config = self.env['ir.config_parameter'].sudo()
        
        return {
            'email_alerts_enabled': config.get_param('nebular.enable_email_alerts', 'True') == 'True',
            'sms_alerts_enabled': config.get_param('nebular.enable_sms_alerts', 'False') == 'True',
            'escalation_time': int(config.get_param('nebular.alert_escalation_time', '15')),
            'critical_recipients': config.get_param('nebular.critical_alert_recipients', '')
        }
    
    @api.model
    def get_security_settings(self):
        """Get security-specific settings"""
        config = self.env['ir.config_parameter'].sudo()
        
        return {
            'audit_log_enabled': config.get_param('nebular.enable_audit_log', 'True') == 'True',
            'session_timeout': int(config.get_param('nebular.session_timeout', '480')),
            'max_login_attempts': int(config.get_param('nebular.max_login_attempts', '5')),
            'lockout_duration': int(config.get_param('nebular.lockout_duration', '30'))
        }
    
    @api.model
    def validate_system_health(self):
        """Validate system health and configuration"""
        issues = []
        config = self.env['ir.config_parameter'].sudo()
        
        # Check critical settings
        if not config.get_param('nebular.company_name'):
            issues.append("Company name is not configured")
        
        if config.get_param('nebular.enable_email_alerts', 'True') == 'True':
            if not config.get_param('nebular.critical_alert_recipients'):
                issues.append("Email alerts are enabled but no recipients configured")
        
        if config.get_param('nebular.enable_webhook', 'False') == 'True':
            if not config.get_param('nebular.webhook_url'):
                issues.append("Webhooks are enabled but no URL configured")
        
        # Check retention settings
        metric_retention = int(config.get_param('nebular.metric_retention_days', '90'))
        if metric_retention < 7:
            issues.append("Metric retention period is too short (minimum 7 days recommended)")
        
        return {
            'healthy': len(issues) == 0,
            'issues': issues,
            'total_issues': len(issues)
        }