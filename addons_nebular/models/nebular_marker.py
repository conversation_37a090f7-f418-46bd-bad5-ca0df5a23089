# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class NebularMarker(models.Model):
    """Marker Model for Interactive Floor Plans"""
    
    # 1. Private attributes
    _name = 'nebular.marker'
    _description = 'Nebular Interactive Marker'
    _inherit = ['mail.thread', 'mail.activity.schedule']
    _order = 'floor_plan_id, marker_type, name'
    _rec_name = 'display_name'
    
    # 2. Default methods
    def _default_position_x(self):
        """Default X position"""
        return 0.0
    
    def _default_position_y(self):
        """Default Y position"""
        return 0.0
    
    def _default_size(self):
        """Default marker size"""
        return 20
    
    # 3. Selection methods
    def _selection_marker_type(self):
        """Selection for marker type"""
        return [
            ('room', 'Room'),
            ('door', 'Door'),
            ('device', 'Device'),
            ('zone', 'Zone'),
            ('emergency', 'Emergency Exit'),
            ('fire', 'Fire Equipment'),
            ('camera', 'CCTV Camera'),
            ('access', 'Access Point'),
            ('info', 'Information'),
            ('warning', 'Warning'),
            ('custom', 'Custom')
        ]
    
    def _selection_status(self):
        """Selection for marker status"""
        return [
            ('active', 'Active'),
            ('inactive', 'Inactive'),
            ('alert', 'Alert'),
            ('warning', 'Warning'),
            ('error', 'Error'),
            ('maintenance', 'Maintenance')
        ]
    
    def _selection_priority(self):
        """Selection for marker priority"""
        return [
            ('low', 'Low'),
            ('normal', 'Normal'),
            ('high', 'High'),
            ('critical', 'Critical')
        ]
    
    # 4. Field declarations
    name = fields.Char(
        string='Marker Name',
        required=True,
        tracking=True,
        help='Name of the marker'
    )
    description = fields.Text(
        string='Description',
        help='Description of the marker'
    )
    floor_plan_id = fields.Many2one(
        'nebular.floor.plan',
        string='Floor Plan',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Floor plan this marker belongs to'
    )
    marker_type = fields.Selection(
        selection='_selection_marker_type',
        string='Marker Type',
        required=True,
        default='info',
        tracking=True,
        help='Type of the marker'
    )
    position_x = fields.Float(
        string='X Position',
        default=_default_position_x,
        required=True,
        help='X coordinate position on floor plan (pixels)'
    )
    position_y = fields.Float(
        string='Y Position',
        default=_default_position_y,
        required=True,
        help='Y coordinate position on floor plan (pixels)'
    )
    size = fields.Integer(
        string='Marker Size',
        default=_default_size,
        help='Size of the marker in pixels'
    )
    color = fields.Char(
        string='Marker Color',
        default='#007bff',
        help='Color of the marker (hex code)'
    )
    icon = fields.Char(
        string='Icon',
        help='Icon class or name for the marker'
    )
    status = fields.Selection(
        selection='_selection_status',
        string='Status',
        default='active',
        tracking=True,
        help='Current status of the marker'
    )
    priority = fields.Selection(
        selection='_selection_priority',
        string='Priority',
        default='normal',
        help='Priority level of the marker'
    )
    is_visible = fields.Boolean(
        string='Visible',
        default=True,
        help='Whether the marker is visible on the floor plan'
    )
    is_clickable = fields.Boolean(
        string='Clickable',
        default=True,
        help='Whether the marker is clickable'
    )
    is_draggable = fields.Boolean(
        string='Draggable',
        default=False,
        help='Whether the marker can be dragged'
    )
    tooltip_text = fields.Text(
        string='Tooltip Text',
        help='Text to show when hovering over the marker'
    )
    
    # Relationships
    building_id = fields.Many2one(
        'nebular.building',
        string='Building',
        related='floor_plan_id.building_id',
        store=True,
        help='Building this marker belongs to'
    )
    floor_id = fields.Many2one(
        'nebular.floor',
        string='Floor',
        related='floor_plan_id.floor_id',
        store=True,
        help='Floor this marker belongs to'
    )
    room_id = fields.Many2one(
        'nebular.room',
        string='Linked Room',
        help='Room this marker represents'
    )
    door_id = fields.Many2one(
        'nebular.door',
        string='Linked Door',
        help='Door this marker represents'
    )
    device_id = fields.Many2one(
        'nebular.device',
        string='Linked Device',
        help='Device this marker represents'
    )
    zone_id = fields.Many2one(
        'nebular.zone',
        string='Linked Zone',
        help='Zone this marker represents'
    )
    event_ids = fields.One2many(
        'nebular.event',
        'marker_id',
        string='Events',
        help='Events related to this marker'
    )
    
    # 5. Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Display name for the marker'
    )
    position_display = fields.Char(
        string='Position',
        compute='_compute_position_display',
        help='Display position coordinates'
    )
    linked_object_name = fields.Char(
        string='Linked Object',
        compute='_compute_linked_object_name',
        help='Name of the linked object'
    )
    event_count = fields.Integer(
        string='Event Count',
        compute='_compute_event_count',
        help='Number of events related to this marker'
    )
    has_alerts = fields.Boolean(
        string='Has Alerts',
        compute='_compute_has_alerts',
        help='Whether this marker has active alerts'
    )
    status_color = fields.Char(
        string='Status Color',
        compute='_compute_status_color',
        help='Color based on status'
    )
    
    # 6. Compute methods
    @api.depends('name', 'marker_type', 'floor_plan_id.display_name')
    def _compute_display_name(self):
        """Compute display name"""
        for record in self:
            type_name = dict(record._selection_marker_type()).get(record.marker_type, record.marker_type)
            if record.floor_plan_id:
                record.display_name = f"{record.floor_plan_id.display_name} - {type_name}: {record.name}"
            else:
                record.display_name = f"{type_name}: {record.name}"
    
    @api.depends('position_x', 'position_y')
    def _compute_position_display(self):
        """Compute position display"""
        for record in self:
            record.position_display = f"({record.position_x:.1f}, {record.position_y:.1f})"
    
    @api.depends('room_id', 'door_id', 'device_id', 'zone_id')
    def _compute_linked_object_name(self):
        """Compute linked object name"""
        for record in self:
            if record.room_id:
                record.linked_object_name = f"Room: {record.room_id.name}"
            elif record.door_id:
                record.linked_object_name = f"Door: {record.door_id.name}"
            elif record.device_id:
                record.linked_object_name = f"Device: {record.device_id.name}"
            elif record.zone_id:
                record.linked_object_name = f"Zone: {record.zone_id.name}"
            else:
                record.linked_object_name = "No linked object"
    
    @api.depends('event_ids')
    def _compute_event_count(self):
        """Compute event count"""
        for record in self:
            record.event_count = len(record.event_ids)
    
    @api.depends('event_ids.severity', 'status')
    def _compute_has_alerts(self):
        """Compute has alerts"""
        for record in self:
            has_critical_events = any(event.severity == 'critical' for event in record.event_ids)
            record.has_alerts = has_critical_events or record.status in ['alert', 'error']
    
    @api.depends('status', 'has_alerts')
    def _compute_status_color(self):
        """Compute status color"""
        for record in self:
            if record.has_alerts or record.status == 'error':
                record.status_color = '#dc3545'  # Red
            elif record.status == 'warning':
                record.status_color = '#ffc107'  # Yellow
            elif record.status == 'maintenance':
                record.status_color = '#6c757d'  # Gray
            elif record.status == 'active':
                record.status_color = '#28a745'  # Green
            else:
                record.status_color = '#6c757d'  # Gray
    
    # 7. Constraints
    @api.constrains('position_x', 'position_y')
    def _check_position_bounds(self):
        """Check position is within floor plan bounds"""
        for record in self:
            if record.floor_plan_id:
                if record.floor_plan_id.image_width and record.position_x > record.floor_plan_id.image_width:
                    raise ValidationError("X position exceeds floor plan width")
                if record.floor_plan_id.image_height and record.position_y > record.floor_plan_id.image_height:
                    raise ValidationError("Y position exceeds floor plan height")
            
            if record.position_x < 0 or record.position_y < 0:
                raise ValidationError("Position coordinates must be non-negative")
    
    @api.constrains('size')
    def _check_size(self):
        """Check marker size is positive"""
        for record in self:
            if record.size <= 0:
                raise ValidationError("Marker size must be positive")
    
    @api.constrains('color')
    def _check_color_format(self):
        """Check color format is valid hex"""
        import re
        for record in self:
            if record.color and not re.match(r'^#[0-9A-Fa-f]{6}$', record.color):
                raise ValidationError("Color must be in hex format (#RRGGBB)")
    
    @api.constrains('room_id', 'door_id', 'device_id', 'zone_id', 'marker_type')
    def _check_linked_object_consistency(self):
        """Check linked object matches marker type"""
        for record in self:
            if record.marker_type == 'room' and not record.room_id:
                raise ValidationError("Room marker must be linked to a room")
            elif record.marker_type == 'door' and not record.door_id:
                raise ValidationError("Door marker must be linked to a door")
            elif record.marker_type == 'device' and not record.device_id:
                raise ValidationError("Device marker must be linked to a device")
            elif record.marker_type == 'zone' and not record.zone_id:
                raise ValidationError("Zone marker must be linked to a zone")
    
    # 8. Onchange methods
    @api.onchange('marker_type')
    def _onchange_marker_type(self):
        """Update default values when marker type changes"""
        if self.marker_type:
            type_defaults = {
                'room': {'color': '#007bff', 'icon': 'fa-home', 'size': 25},
                'door': {'color': '#28a745', 'icon': 'fa-door-open', 'size': 20},
                'device': {'color': '#17a2b8', 'icon': 'fa-microchip', 'size': 18},
                'zone': {'color': '#6f42c1', 'icon': 'fa-map-marked-alt', 'size': 30},
                'emergency': {'color': '#dc3545', 'icon': 'fa-exclamation-triangle', 'size': 25},
                'fire': {'color': '#fd7e14', 'icon': 'fa-fire-extinguisher', 'size': 22},
                'camera': {'color': '#6c757d', 'icon': 'fa-video', 'size': 20},
                'access': {'color': '#20c997', 'icon': 'fa-key', 'size': 18},
                'info': {'color': '#17a2b8', 'icon': 'fa-info-circle', 'size': 20},
                'warning': {'color': '#ffc107', 'icon': 'fa-exclamation-triangle', 'size': 22},
                'custom': {'color': '#6c757d', 'icon': 'fa-circle', 'size': 20}
            }
            
            defaults = type_defaults.get(self.marker_type, {})
            for field, value in defaults.items():
                setattr(self, field, value)
    
    @api.onchange('room_id')
    def _onchange_room_id(self):
        """Update marker when room changes"""
        if self.room_id:
            self.name = self.room_id.name
            self.marker_type = 'room'
    
    @api.onchange('door_id')
    def _onchange_door_id(self):
        """Update marker when door changes"""
        if self.door_id:
            self.name = self.door_id.name
            self.marker_type = 'door'
    
    @api.onchange('device_id')
    def _onchange_device_id(self):
        """Update marker when device changes"""
        if self.device_id:
            self.name = self.device_id.name
            self.marker_type = 'device'
    
    @api.onchange('zone_id')
    def _onchange_zone_id(self):
        """Update marker when zone changes"""
        if self.zone_id:
            self.name = self.zone_id.name
            self.marker_type = 'zone'
    
    # 9. CRUD overrides
    @api.model
    def create(self, vals):
        """Override create to set default values"""
        # Set default tooltip if not provided
        if not vals.get('tooltip_text') and vals.get('name'):
            vals['tooltip_text'] = vals['name']
        
        # Set default icon based on marker type
        if not vals.get('icon') and vals.get('marker_type'):
            marker_type = vals['marker_type']
            type_icons = {
                'room': 'fa-home',
                'door': 'fa-door-open',
                'device': 'fa-microchip',
                'zone': 'fa-map-marked-alt',
                'emergency': 'fa-exclamation-triangle',
                'fire': 'fa-fire-extinguisher',
                'camera': 'fa-video',
                'access': 'fa-key',
                'info': 'fa-info-circle',
                'warning': 'fa-exclamation-triangle',
                'custom': 'fa-circle'
            }
            vals['icon'] = type_icons.get(marker_type, 'fa-circle')
        
        return super().create(vals)
    
    def write(self, vals):
        """Override write to handle special cases"""
        # Update tooltip when name changes
        if 'name' in vals and not vals.get('tooltip_text'):
            vals['tooltip_text'] = vals['name']
        
        return super().write(vals)
    
    # 10. Action methods
    def action_show_on_plan(self):
        """Show marker on floor plan"""
        self.ensure_one()
        return {
            'name': f'Floor Plan - {self.floor_plan_id.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.floor.plan',
            'res_id': self.floor_plan_id.id,
            'view_mode': 'form',
            'target': 'current',
            'context': {'highlight_marker_id': self.id}
        }
    
    def action_view_events(self):
        """View events related to this marker"""
        self.ensure_one()
        return {
            'name': f'Events - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.event',
            'view_mode': 'list,form',
            'domain': [('marker_id', '=', self.id)],
            'context': {'default_marker_id': self.id}
        }
    
    def action_toggle_visibility(self):
        """Toggle marker visibility"""
        self.ensure_one()
        self.is_visible = not self.is_visible
        status = "visible" if self.is_visible else "hidden"
        self.message_post(body=f"Marker {status}")
    
    def action_move_to_position(self, x, y):
        """Move marker to specific position"""
        self.ensure_one()
        old_pos = f"({self.position_x:.1f}, {self.position_y:.1f})"
        self.write({
            'position_x': x,
            'position_y': y
        })
        new_pos = f"({x:.1f}, {y:.1f})"
        self.message_post(body=f"Marker moved from {old_pos} to {new_pos}")
    
    def action_duplicate(self):
        """Duplicate marker"""
        self.ensure_one()
        copy_data = {
            'name': f"{self.name} (Copy)",
            'position_x': self.position_x + 20,
            'position_y': self.position_y + 20
        }
        new_marker = self.copy(copy_data)
        return {
            'name': 'Duplicated Marker',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.marker',
            'res_id': new_marker.id,
            'view_mode': 'form',
            'target': 'current'
        }
    
    # 11. Other business methods
    def get_marker_data(self):
        """Get marker data for frontend"""
        self.ensure_one()
        return {
            'id': self.id,
            'name': self.name,
            'type': self.marker_type,
            'position': {
                'x': self.position_x,
                'y': self.position_y
            },
            'size': self.size,
            'color': self.color,
            'icon': self.icon,
            'status': self.status,
            'priority': self.priority,
            'visible': self.is_visible,
            'clickable': self.is_clickable,
            'draggable': self.is_draggable,
            'tooltip': self.tooltip_text,
            'has_alerts': self.has_alerts,
            'event_count': self.event_count,
            'linked_object': self.linked_object_name
        }
    
    def update_position(self, x, y):
        """Update marker position"""
        self.ensure_one()
        self.write({
            'position_x': x,
            'position_y': y
        })
    
    def set_status(self, status, message=None):
        """Set marker status with optional message"""
        self.ensure_one()
        self.status = status
        if message:
            self.message_post(body=message)
    
    def create_event(self, event_type, severity='info', description=None):
        """Create an event for this marker"""
        self.ensure_one()
        event_data = {
            'name': f"{event_type.title()} - {self.name}",
            'event_type': event_type,
            'severity': severity,
            'description': description or f"{event_type} event for marker {self.name}",
            'marker_id': self.id,
            'building_id': self.building_id.id,
            'floor_id': self.floor_id.id
        }
        
        # Link to specific objects based on marker type
        if self.room_id:
            event_data['room_id'] = self.room_id.id
        elif self.door_id:
            event_data['door_id'] = self.door_id.id
        elif self.device_id:
            event_data['device_id'] = self.device_id.id
        elif self.zone_id:
            event_data['zone_id'] = self.zone_id.id
        
        return self.env['nebular.event'].create(event_data)