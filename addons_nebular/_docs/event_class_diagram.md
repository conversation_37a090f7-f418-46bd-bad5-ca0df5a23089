### 2.1 Event Models Class Diagram

```mermaid
classDiagram
    class NebularEvent {
        +int id
        +Many2one building_id
        +char building_code
        +Many2one zoon_id
        +char zoon_code
        +Many2one floor_id
        +char floor_code
        +Many2one system_id        %% e.g., fire, access, cctv, gate, pa, presence
        +char system_name
        +char event_type
        +datetime datetime
        +text message
        +char state
        +char command
        +json data_json        %% computed only for API
        +int severity
        +char res_model        %% linked Odoo model
        +int res_id            %% linked record ID
    }

    class NebularFireEvent {
        +int id
        +Many2one event_id
        +char panel_code
        +char panel_name
        +char zone
        +char loop
        +int node_id
        +char node_code
        +char address
    }

    class NebularAccessEvent {
        +int id
        +Many2one event_id
        +int controller_id
        +char controller_code
        +char controller_name
        +int reader_id
        +char reader_code
        +int card_id
        +char card_code
        +int user_id
        +char user_code
        +char door_id
        +char door_name
        +char result
        +char reason
        +int held_open_seconds
    }

    class NebularCCTVEvent {
        +int id
        +Many2one event_id
        +int camera_id
        +char camera_code
        +char camera_name
        +char location
        +char vendor
        +char model
        +char ip
        +int channel
        +char analytic_type
        +int count
        +char face_id
        +char uri
        +char snapshot_url
        +bool recording
    }

    class NebularGateEvent {
        +int id
        +Many2one event_id
        +int gate_id
        +char gate_code
        +char name
        +char location
        +char status
        +char vehicle_plate
        +char trigger
        +float anpr_conf
    }

    class NebularPAEvent {
        +int id
        +Many2one event_id
        +int zone_id
        +char zone_code
        +char zone_name
        +char location
        +int volume
        +text message
        +char announcement_id
        +char script
        +int duration_sec
    }

    class NebularPresenceEvent {
        +int id
        +Many2one event_id
        +int sensor_id
        +char sensor_code
        +char name
        +char location
        +char vendor
        +char model
        +char type           %% pir/microwave/ultrasonic/camera
        +int count
        +bool occupancy
    }

    %% Relationships
    NebularFireEvent --> NebularEvent : event_id
    NebularAccessEvent --> NebularEvent : event_id
    NebularCCTVEvent --> NebularEvent : event_id
    NebularGateEvent --> NebularEvent : event_id
    NebularPAEvent --> NebularEvent : event_id
    NebularPresenceEvent --> NebularEvent : event_id
```