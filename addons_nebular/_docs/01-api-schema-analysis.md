# API Schema Analysis for Odoo Model Conversion - Nebular Module

## Overview

This document provides a comprehensive analysis of the TypeScript interfaces and data models found in the `/src/infrastructure/api` directory, prepared for conversion to Odoo models using the "nebular" namespace.

## API Infrastructure Structure

```
src/infrastructure/api/
├── base/                    # Core API client configuration
├── geography/              # Geographic hierarchy models
│   ├── buildings/         # Building entities
│   ├── zones/            # Zone entities  
│   ├── floors/           # Floor entities
│   ├── rooms/            # Room entities
│   └── doors/            # Door entities
├── events/               # Event and alert system
├── markers/              # Interis_active map markers
├── systems/              # System monitoring
├── categories/           # (Empty - future use)
└── items/               # (Empty - future use)
```

## Core Data Models

### 1. Geographic Hierarchy Models

#### Building Model
```typescript
interface Building {
    id: number;
    name: string;
    shortCode: string;        // e.g., 'A', 'B', 'C'
    address: string;
    description: string;
    totalFloors: number;
    totalRooms: number;
    totalDoors: number;
    isis_active: boolean;
    createdAt: string;
    updatedAt: string;
}
```

#### Zone Model
```typescript
interface Zone {
    id: number;
    name: string;
    description: string;
    buildingId: number;       // Foreign key to Building
    zoneCode: string;         // e.g., 'NW', 'SW', 'E1'
    isis_active: boolean;
    createdAt: string;
    updatedAt: string;
}
```

#### Floor Model
```typescript
interface Floor {
    id: number;
    name: string;
    level: number;
    zoneId: number;           // Foreign key to Zone
    buildingId: number;       // Foreign key to Building
    floorCode: string;        // e.g., 'F1', 'F2', 'B1'
    floorPlanUrl: string;     // Default: '/plans/floorPlan-1.png'
    isis_active: boolean;
    createdAt: string;
    updatedAt: string;
}
```

#### Room Model
```typescript
interface Room {
    id: number;
    name: string;
    roomCode: string;         // e.g., 'R101', 'R202', 'CONF1'
    floorId: number;          // Foreign key to Floor
    roomType: 'office' | 'conference' | 'storage' | 'lobby' | 'restroom' | 'utility' | 'other';
    capacity?: number;
    area?: number;            // in square meters
    isis_active: boolean;
    createdAt: string;
    updatedAt: string;
}
```

#### Door Model
```typescript
interface Door {
    id: number;
    name: string;
    doorCode: string;         // e.g., 'D101A', 'D202B', 'EMRG01'
    type: 'main_entry' | 'emergency_exit' | 'standard' | 'security' | 'fire_exit';
    roomId: number;           // Foreign key to Room
    status: 'open' | 'closed' | 'locked' | 'malfunction';
    accessLevel: 'public' | 'restricted' | 'emergency_only' | 'admin_only';
    isis_active: boolean;
    lastStatusChange: string;
    createdAt: string;
    updatedAt: string;
}
```

### 2. Event System Models

#### Event Model
```typescript
interface Event {
    id: number;
    eventCode: string;
    deviceType: 'camera' | 'door' | 'gate' | 'fire';
    name: string;
    timestamp: string;
    status: EventStatus;      // Union type with device-specific statuses
    isAlert: boolean;
    buildingId: number;       // Foreign key to Building
    zoneId: number;           // Foreign key to Zone
    floorId: number;          // Foreign key to Floor
    description: string;
    publicAddress: string;
}
```

### 3. Marker System Models

#### Marker Model
```typescript
interface Marker {
    id: number;
    name: string;
    type: 'fire' | 'door' | 'gate' | 'camera' | 'people' | 'sensor' | 'emergency' | 'person';
    positionX: number;
    positionY: number;
    positionXPercent?: number;
    positionYPercent?: number;
    status: string;
    isAlert: boolean;
    floorId: number;          // Foreign key to Floor
    zoneId: number;           // Foreign key to Zone
    buildingId: number;       // Foreign key to Building
    title: string;
    subtitle: string;
    description: string;
    zone: string;
    publicAddress: string;
    alertTimestamp: string;
    count?: number;
    source?: string;
}
```

### 4. System Monitoring Models

#### System Model
```typescript
interface System {
    title: string;
    iconName: string;
    iconColor: string;
    metrics: Metric[];
}

interface Metric {
    key: string;
    value: string;
    isAlert: boolean;
}
```

### 5. API Infrastructure Models

#### API Response Wrapper
```typescript
interface ApiResponse<T = unknown> {
    data: T;
    message?: string;
    success: boolean;
    errors?: string[];
}

interface ApiError {
    message: string;
    status?: number;
    code?: string;
    details?: unknown;
}
```

## Relationships and Hierarchy

### Geographic Hierarchy
```
Building (1) → (N) Zone
Zone (1) → (N) Floor  
Floor (1) → (N) Room
Room (1) → (N) Door
```

### Cross-References
- **Events** reference Building, Zone, and Floor
- **Markers** reference Building, Zone, and Floor
- **Systems** contain multiple Metrics

## Key Characteristics for Odoo Conversion

### Common Patterns
1. **Primary Keys**: All entities use `id: number`
2. **Audit Fields**: Most entities have `createdAt` and `updatedAt` timestamps
3. **Status Fields**: Most entities have `isis_active: boolean` for soft deletion
4. **Code Fields**: Entities have human-readable codes (shortCode, zoneCode, floorCode, etc.)
5. **Foreign Key Relationships**: Clear hierarchical relationships with proper referential integrity

### Data Types
- **Strings**: Names, descriptions, codes, URLs
- **Numbers**: IDs, counts, measurements, positions
- **Booleans**: Status flags, alert indicators
- **Enums**: Predefined sets of values (door types, room types, etc.)
- **Timestamps**: ISO string format for dates

### Business Logic
- **Hierarchical Structure**: Geographic entities follow a strict hierarchy
- **Status Management**: Real-time status tracking for doors, events, and markers
- **Alert System**: Boolean flags for alert conditions across multiple entities
- **Positioning System**: X/Y coordinates for floor plan visualization

## Next Steps

1. Map each TypeScript interface to corresponding Odoo model structure
2. Define Odoo field types and constraints
3. Establish Many2one and One2many relationships
4. Create computed fields for statistics and aggregations
5. Define security rules and access controls