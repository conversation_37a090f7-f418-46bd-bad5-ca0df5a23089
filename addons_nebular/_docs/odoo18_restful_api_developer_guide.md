# Odoo 18 RESTful API Developer Guide

## Overview

This guide provides comprehensive patterns and best practices for developing RESTful APIs in Odoo 18, extracted from the analysis of the `ams_ta` module controller architecture. It serves as a blueprint for software architects and developers to create consistent, maintainable, and scalable API endpoints.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Controller Structure](#controller-structure)
3. [Data Transfer Objects (DTOs)](#data-transfer-objects-dtos)
4. [Routing Patterns](#routing-patterns)
5. [Error Handling](#error-handling)
6. [Authentication & Security](#authentication--security)
7. [Helper Utilities](#helper-utilities)
8. [Best Practices](#best-practices)
9. [Implementation Examples](#implementation-examples)

## Architecture Overview

The Odoo 18 RESTful API architecture follows a layered approach:

```
┌─────────────────────────────────────┐
│           HTTP Routes               │
├─────────────────────────────────────┤
│         Controllers                 │
├─────────────────────────────────────┤
│      Business Logic Layer          │
├─────────────────────────────────────┤
│         Data Models                 │
├─────────────────────────────────────┤
│           Database                  │
└─────────────────────────────────────┘
```

### Key Components:
- **Controllers**: Handle HTTP requests and responses
- **DTOs**: Standardize data transfer between layers
- **Helper Utilities**: Provide common functionality
- **Base Controller**: Implements shared functionality

## Controller Structure

### Base Controller Pattern

All API controllers should inherit from a base controller that provides common functionality:

```python
from odoo import http
from odoo.http import request
import logging

class BaseController(http.Controller):
    @property
    def logger(self):
        return logging.getLogger('your_module_api')
    
    @property
    def request_context(self):
        # Set default timezone if not present
        current_user = request.env.user
        if current_user and not current_user.tz:
            current_user.sudo().write({'tz': "Asia/Riyadh"})
        return request
    
    def handle_api_error(self, ex, response_code="100", status=200, response_message=None):
        """Universal API error handler"""
        error_message = str(ex)
        message = response_message or f"{type(ex).__name__}"
        
        res_dict = {
            'response_code': response_code,
            'response_message': message,
            'ErrorMessage': error_message
        }
        
        self.logger.error(f"API error: {res_dict}")
        return request.make_json_response(res_dict, status=status)
```

### Specific Controller Implementation

```python
class YourEntityController(BaseController):
    
    @property
    def entity_model(self):
        return self.request_context.env['your.model'].sudo()
    
    @http.route('/api/entity/<int:entity_id>', type='http', auth='public', 
                methods=['GET'], csrf=False, cors="*")
    def get_entity(self, entity_id, **kwargs):
        try:
            result = self.entity_model.get_entity_by_id(entity_id)
            return request.make_json_response(result.to_dict(), status=200)
        except Exception as ex:
            return self.handle_api_error(ex)
```

## Data Transfer Objects (DTOs)

### DTO Structure Pattern

DTOs should follow a consistent structure with standardized response fields:

```python
from dataclasses import dataclass
from typing import Any, List
from .utils import *

@dataclass
class EntityDTO:
    # Standard response fields
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''  # Arabic support
    error_message: str = ''
    
    # Entity-specific fields
    entity_id: str = ''
    name: str = ''
    description: str = ''
    
    @staticmethod
    def from_dict(obj: Any) -> 'EntityDTO':
        assert isinstance(obj, dict)
        return EntityDTO(
            response_code=from_str(obj.get("ResponseCode")),
            response_message=from_str(obj.get("ResponseMessage")),
            response_message_ar=from_str(obj.get("ResponseMessageAR")),
            error_message=from_str(obj.get("ErrorMessage")),
            entity_id=from_str(obj.get("EntityId")),
            name=from_str(obj.get("Name")),
            description=from_str(obj.get("Description"))
        )
    
    def to_dict(self) -> dict:
        return {
            "ResponseCode": from_str(self.response_code),
            "ResponseMessage": from_str(self.response_message),
            "ResponseMessageAR": from_str(self.response_message_ar),
            "ErrorMessage": from_str(self.error_message),
            "EntityId": from_str(self.entity_id),
            "Name": from_str(self.name),
            "Description": from_str(self.description)
        }
```

### List DTO Pattern

For collections, use a list wrapper DTO:

```python
@dataclass
class EntityListDTO:
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''
    entities: List[EntityDTO] = None
    error_message: str = ''
    
    def __post_init__(self):
        if self.entities is None:
            self.entities = []
    
    @staticmethod
    def from_dict(obj: Any) -> 'EntityListDTO':
        return EntityListDTO(
            response_code=from_str(obj.get("ResponseCode")),
            response_message=from_str(obj.get("ResponseMessage")),
            response_message_ar=from_str(obj.get("ResponseMessageAR")),
            entities=from_list(EntityDTO.from_dict, obj.get("Entities", [])),
            error_message=from_str(obj.get("ErrorMessage"))
        )
    
    def to_dict(self) -> dict:
        return {
            "ResponseCode": from_str(self.response_code),
            "ResponseMessage": from_str(self.response_message),
            "ResponseMessageAR": from_str(self.response_message_ar),
            "Entities": from_list(lambda x: to_class(EntityDTO, x), self.entities),
            "ErrorMessage": from_str(self.error_message)
        }
```

## Routing Patterns

### Standard Route Conventions

Follow RESTful conventions for route naming:

```python
# GET single entity
@http.route('/api/entity/<int:entity_id>', methods=['GET'])

# GET collection with filters
@http.route('/api/entities', methods=['GET'])

# POST create new entity
@http.route('/api/entity', methods=['POST'])

# PUT update entity
@http.route('/api/entity/<int:entity_id>', methods=['PUT'])

# DELETE entity
@http.route('/api/entity/<int:entity_id>', methods=['DELETE'])

# Custom actions
@http.route('/api/entity/<int:entity_id>/activate', methods=['POST'])
```

### Route Configuration

Standard route configuration:

```python
@http.route('/api/endpoint', 
           type='http',           # HTTP type for REST APIs
           auth='public',         # or 'user' for authenticated
           methods=['GET'],       # HTTP methods
           csrf=False,           # Disable CSRF for APIs
           cors="*")             # Enable CORS
```

## Error Handling

### Standardized Error Response

All errors should return a consistent structure:

```json
{
    "response_code": "100",
    "response_message": "ValidationError",
    "ErrorMessage": "Detailed error description"
}
```

### Error Handling Implementation

```python
def handle_api_error(self, ex, response_code="100", status=200, response_message=None):
    """
    Universal API error handler
    
    Args:
        ex: Exception object or error string
        response_code: Business-level response code (default "100")
        status: HTTP status code (default 200)
        response_message: Optional message for API consumer
    """
    error_message = str(ex)
    message = response_message or f"{type(ex).__name__}"
    
    res_dict = {
        'response_code': response_code,
        'response_message': message,
        'ErrorMessage': error_message
    }
    
    self.logger.error(f"API error: {res_dict}")
    return request.make_json_response(res_dict, status=status)
```

### Common Error Codes

| Code | Description |
|------|-------------|
| 000  | Success |
| 100  | General Error |
| 101  | Validation Error |
| 102  | Not Found |
| 103  | Unauthorized |
| 104  | Forbidden |

## Authentication & Security

### Authentication Patterns

```python
# Public endpoint (no authentication)
@http.route('/api/public/endpoint', auth='public')

# User authentication required
@http.route('/api/secure/endpoint', auth='user')

# Custom authentication
@http.route('/api/custom/endpoint', auth='public')
def custom_endpoint(self, **kwargs):
    # Implement custom auth logic
    if not self.validate_api_key(kwargs.get('api_key')):
        return self.handle_api_error("Unauthorized", "103", 401)
```

### Security Best Practices

1. **Always validate input parameters**
2. **Use sudo() carefully** - only when necessary
3. **Implement rate limiting** for public endpoints
4. **Log all API access** for audit trails
5. **Sanitize error messages** to avoid information leakage

## Helper Utilities

### Common Utility Functions

Create a `utils.py` file with common functions:

```python
from datetime import datetime, date
from typing import Any, List, TypeVar, Callable, Type
import dateutil.parser

T = TypeVar("T")

def from_str(x: Any) -> str:
    """Safe string conversion"""
    if not x:
        return ''
    assert isinstance(x, str)
    return x

def from_datetime(x: Any) -> datetime:
    """Safe datetime parsing"""
    if x:
        try:
            return dateutil.parser.parse(x.replace('Z', ''))
        except:
            return datetime.min
    return datetime.min

def from_list(f: Callable[[Any], T], x: Any) -> List[T]:
    """Safe list conversion with transformation"""
    assert isinstance(x, list)
    return [f(y) for y in x]

def to_class(c: Type[T], x: Any) -> dict:
    """Convert object to dictionary"""
    assert isinstance(x, c)
    return x.to_dict()
```

### Date/Time Utilities

```python
def _parse_date_range(date_from_str: str, date_to_str: str):
    """Parse date range from string format DDMMYYYY"""
    dt_from = datetime.strptime(date_from_str, "%d%m%Y").date()
    dt_to = datetime.strptime(date_to_str, "%d%m%Y").date()
    return dt_from, dt_to

def convert_to_str_time_2digit_format(float_time: float) -> str:
    """Convert float time to HH:MM format"""
    hours = int(float_time)
    minutes = int(round((float_time - hours) * 60))
    
    if minutes == 60:
        hours += 1
        minutes = 0
        
    return f"{hours:02d}:{minutes:02d}"
```

## Best Practices

### 1. Consistent Response Structure

Always return responses in the same format:

```python
{
    "ResponseCode": "000",
    "ResponseMessage": "Success",
    "ResponseMessageAR": "نجح",
    "Data": { /* actual data */ },
    "ErrorMessage": ""
}
```

### 2. Proper Logging

```python
# Log API access
self.logger.info(f"API called: {request.httprequest.url}")

# Log errors with context
self.logger.error(f"API error in {method_name}: {error_details}")
```

### 3. Input Validation

```python
def validate_required_params(self, params, required_fields):
    """Validate required parameters"""
    missing = [field for field in required_fields if not params.get(field)]
    if missing:
        raise ValueError(f"Missing required fields: {', '.join(missing)}")
```

### 4. Timezone Handling

```python
# Always set default timezone
current_user = request.env.user
if current_user and not current_user.tz:
    current_user.sudo().write({'tz': "Asia/Riyadh"})
```

### 5. CORS Configuration

```python
# Enable CORS for cross-origin requests
@http.route('/api/endpoint', cors="*")
```

## Implementation Examples

### Complete CRUD Controller Example

```python
from odoo import http
from odoo.http import request
from .base_controller import BaseController
from ..api.dto_entity import EntityDTO, EntityListDTO

class EntityController(BaseController):
    
    @property
    def entity_model(self):
        return self.request_context.env['your.entity'].sudo()
    
    @http.route('/api/entities', type='http', auth='public', 
                methods=['GET'], csrf=False, cors="*")
    def get_entities(self, search=None, limit=100, offset=0, **kwargs):
        """Get list of entities with optional search and pagination"""
        try:
            domain = []
            if search:
                domain = [('name', 'ilike', search)]
            
            entities = self.entity_model.search(domain, limit=int(limit), offset=int(offset))
            
            result = EntityListDTO(
                response_code="000",
                response_message="Success",
                entities=[self._entity_to_dto(entity) for entity in entities]
            )
            
            return request.make_json_response(result.to_dict(), status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/entity/<int:entity_id>', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_entity(self, entity_id, **kwargs):
        """Get single entity by ID"""
        try:
            entity = self.entity_model.browse(entity_id)
            if not entity.exists():
                return self.handle_api_error("Entity not found", "102", 404)
            
            result = self._entity_to_dto(entity)
            result.response_code = "000"
            result.response_message = "Success"
            
            return request.make_json_response(result.to_dict(), status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/entity', type='http', auth='user',
                methods=['POST'], csrf=False, cors="*")
    def create_entity(self, **kwargs):
        """Create new entity"""
        try:
            data = request.get_json_data()
            
            # Validate required fields
            required_fields = ['name']
            self.validate_required_params(data, required_fields)
            
            # Create entity
            entity = self.entity_model.create({
                'name': data['name'],
                'description': data.get('description', ''),
            })
            
            result = self._entity_to_dto(entity)
            result.response_code = "000"
            result.response_message = "Entity created successfully"
            
            return request.make_json_response(result.to_dict(), status=201)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    def _entity_to_dto(self, entity):
        """Convert entity record to DTO"""
        return EntityDTO(
            entity_id=str(entity.id),
            name=entity.name,
            description=entity.description or '',
        )
    
    def validate_required_params(self, data, required_fields):
        """Validate required parameters"""
        missing = [field for field in required_fields if not data.get(field)]
        if missing:
            raise ValueError(f"Missing required fields: {', '.join(missing)}")
```

### Portal Integration Example

```python
from odoo.addons.portal.controllers.portal import CustomerPortal

class CustomPortal(CustomerPortal):
    
    @http.route(['/portal/entities'], type='http', auth="user", website=True)
    def portal_entities(self, **kwargs):
        """Portal view for entities"""
        entities = request.env['your.entity'].search([
            ('create_uid', '=', request.env.user.id)
        ])
        
        return request.render('your_module.portal_entities', {
            'entities': entities,
            'page_name': 'entities',
        })
```

## Conclusion

This guide provides a comprehensive framework for developing RESTful APIs in Odoo 18. By following these patterns and best practices, developers can create consistent, maintainable, and scalable API endpoints that integrate seamlessly with Odoo's architecture.

### Key Takeaways:

1. **Inherit from BaseController** for common functionality
2. **Use DTOs** for consistent data transfer
3. **Implement proper error handling** with standardized responses
4. **Follow RESTful conventions** for routing
5. **Validate inputs** and handle edge cases
6. **Log appropriately** for debugging and monitoring
7. **Consider security** in all implementations

For additional examples and advanced patterns, refer to the `ams_ta` module implementation in your Odoo 18 installation.