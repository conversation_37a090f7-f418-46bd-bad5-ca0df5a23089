# Odoo Implementation Guide - Alert Dashboard System

## Overview

This guide provides step-by-step instructions for implementing the Alert Dashboard System as Odoo models, based on the TypeScript interfaces extracted from `/src/infrastructure/api`.

## Prerequisites

- Odoo 18.0 or later
- Python 3.11+
- PostgreSQL database
- Basic understanding of Odoo model development

## Module Structure

```
alert_dashboard/
├── __init__.py
├── __manifest__.py
├── models/
│   ├── __init__.py
│   ├── alert_building.py
│   ├── alert_zone.py
│   ├── alert_floor.py
│   ├── alert_room.py
│   ├── alert_door.py
│   ├── alert_event.py
│   ├── alert_marker.py
│   ├── alert_system.py
│   └── alert_system_metric.py
├── views/
│   ├── alert_building_views.xml
│   ├── alert_zone_views.xml
│   ├── alert_floor_views.xml
│   ├── alert_room_views.xml
│   ├── alert_door_views.xml
│   ├── alert_event_views.xml
│   ├── alert_marker_views.xml
│   └── alert_system_views.xml
├── security/
│   └── ir.model.access.csv
├── data/
│   ├── alert_system_data.xml
│   └── demo_data.xml
└── static/
    └── description/
        └── icon.png
```

## Step 1: Module Manifest

Create `__manifest__.py`:

```python
{
    'name': 'Alert Dashboard System',
    'version': '********.0',
    'category': 'Security',
    'summary': 'Comprehensive alert and monitoring system for buildings',
    'description': """
        Alert Dashboard System
        ======================
      
        This module provides a comprehensive alert and monitoring system for buildings,
        including geographic hierarchy management, event tracking, marker positioning,
        and system monitoring capabilities.
      
        Features:
        - Building, Zone, Floor, Room, Door hierarchy
        - Event management and alerting
        - Interis_active marker system
        - System monitoring and metrics
        - Real-time status tracking
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': ['base', 'web'],
    'data': [
        'security/ir.model.access.csv',
        'data/alert_system_data.xml',
        'views/alert_building_views.xml',
        'views/alert_zone_views.xml',
        'views/alert_floor_views.xml',
        'views/alert_room_views.xml',
        'views/alert_door_views.xml',
        'views/alert_event_views.xml',
        'views/alert_marker_views.xml',
        'views/alert_system_views.xml',
    ],
    'demo': [
        'data/demo_data.xml',
    ],
    'installable': True,
    'application': True,
    'auto_install': False,
}
```

## Step 2: Model Implementation

### Geographic Hierarchy Models

#### Building Model (`models/alert_building.py`)

```python
from odoo import models, fields, api
from odoo.exceptions import ValidationError

class AlertBuilding(models.Model):
    _name = 'alert.building'
    _description = 'Alert Building'
    _order = 'name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(string='Building Name', required=True, index=True)
    short_code = fields.Char(string='Short Code', required=True, size=10, index=True)
    address = fields.Text(string='Address')
    description = fields.Text(string='Description')
  
    # Computed Statistics
    total_floors = fields.Integer(string='Total Floors', compute='_compute_totals', store=True)
    total_rooms = fields.Integer(string='Total Rooms', compute='_compute_totals', store=True)
    total_doors = fields.Integer(string='Total Doors', compute='_compute_totals', store=True)
  
    # Status
    is_active = fields.Boolean(string='is_active', default=True)
  
    # Audit Fields
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now, readonly=True)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now, readonly=True)
  
    # Relationships
    zone_ids = fields.One2many('alert.zone', 'building_id', string='Zones')
    floor_ids = fields.One2many('alert.floor', 'building_id', string='Floors')
    event_ids = fields.One2many('alert.event', 'building_id', string='Events')
    marker_ids = fields.One2many('alert.marker', 'building_id', string='Markers')
  
    # Constraints
    _sql_constraints = [
        ('short_code_unique', 'UNIQUE(short_code)', 'Building short code must be unique!'),
        ('name_unique', 'UNIQUE(name)', 'Building name must be unique!'),
    ]
  
    @api.depends('zone_ids.floor_ids', 'zone_ids.floor_ids.room_ids', 'zone_ids.floor_ids.room_ids.door_ids')
    def _compute_totals(self):
        for building in self:
            floors = building.zone_ids.mapped('floor_ids')
            rooms = floors.mapped('room_ids')
            doors = rooms.mapped('door_ids')
          
            building.total_floors = len(floors)
            building.total_rooms = len(rooms)
            building.total_doors = len(doors)
  
    def write(self, vals):
        vals['updated_at'] = fields.Datetime.now()
        return super().write(vals)
  
    @api.constrains('short_code')
    def _check_short_code(self):
        for building in self:
            if not building.short_code.isalnum():
                raise ValidationError("Short code must contain only alphanumeric characters.")
```

#### Zone Model (`models/alert_zone.py`)

```python
from odoo import models, fields, api
from odoo.exceptions import ValidationError

class AlertZone(models.Model):
    _name = 'alert.zone'
    _description = 'Alert Zone'
    _order = 'building_id, name'
    _rec_name = 'display_name'

    # Basic Information
    name = fields.Char(string='Zone Name', required=True, index=True)
    zone_code = fields.Char(string='Zone Code', required=True, size=10, index=True)
    description = fields.Text(string='Description')
  
    # Foreign Keys
    building_id = fields.Many2one('alert.building', string='Building', required=True, ondelete='cascade')
  
    # Computed Statistics
    total_floors = fields.Integer(string='Total Floors', compute='_compute_totals', store=True)
    total_rooms = fields.Integer(string='Total Rooms', compute='_compute_totals', store=True)
    total_doors = fields.Integer(string='Total Doors', compute='_compute_totals', store=True)
  
    # Status
    is_active = fields.Boolean(string='is_active', default=True)
  
    # Audit Fields
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now, readonly=True)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now, readonly=True)
  
    # Computed Display
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
  
    # Relationships
    floor_ids = fields.One2many('alert.floor', 'zone_id', string='Floors')
    event_ids = fields.One2many('alert.event', 'zone_id', string='Events')
    marker_ids = fields.One2many('alert.marker', 'zone_id', string='Markers')
  
    # Constraints
    _sql_constraints = [
        ('zone_code_building_unique', 'UNIQUE(zone_code, building_id)', 
         'Zone code must be unique within the building!'),
    ]
  
    @api.depends('name', 'building_id.name')
    def _compute_display_name(self):
        for zone in self:
            zone.display_name = f"{zone.building_id.name} - {zone.name}"
  
    @api.depends('floor_ids', 'floor_ids.room_ids', 'floor_ids.room_ids.door_ids')
    def _compute_totals(self):
        for zone in self:
            rooms = zone.floor_ids.mapped('room_ids')
            doors = rooms.mapped('door_ids')
          
            zone.total_floors = len(zone.floor_ids)
            zone.total_rooms = len(rooms)
            zone.total_doors = len(doors)
  
    def write(self, vals):
        vals['updated_at'] = fields.Datetime.now()
        return super().write(vals)
```

#### Floor Model (`models/alert_floor.py`)

```python
from odoo import models, fields, api

class AlertFloor(models.Model):
    _name = 'alert.floor'
    _description = 'Alert Floor'
    _order = 'building_id, zone_id, level'
    _rec_name = 'display_name'

    # Basic Information
    name = fields.Char(string='Floor Name', required=True, index=True)
    level = fields.Integer(string='Level', required=True)
    floor_code = fields.Char(string='Floor Code', required=True, size=10, index=True)
    floor_plan_url = fields.Char(string='Floor Plan URL', default='/plans/floorPlan-1.png')
  
    # Foreign Keys
    building_id = fields.Many2one('alert.building', string='Building', required=True, ondelete='cascade')
    zone_id = fields.Many2one('alert.zone', string='Zone', required=True, ondelete='cascade')
  
    # Computed Statistics
    total_rooms = fields.Integer(string='Total Rooms', compute='_compute_totals', store=True)
    total_doors = fields.Integer(string='Total Doors', compute='_compute_totals', store=True)
  
    # Status
    is_active = fields.Boolean(string='is_active', default=True)
  
    # Audit Fields
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now, readonly=True)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now, readonly=True)
  
    # Computed Display
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
  
    # Relationships
    room_ids = fields.One2many('alert.room', 'floor_id', string='Rooms')
    event_ids = fields.One2many('alert.event', 'floor_id', string='Events')
    marker_ids = fields.One2many('alert.marker', 'floor_id', string='Markers')
  
    # Constraints
    _sql_constraints = [
        ('floor_code_building_unique', 'UNIQUE(floor_code, building_id)', 
         'Floor code must be unique within the building!'),
        ('level_zone_unique', 'UNIQUE(level, zone_id)', 
         'Floor level must be unique within the zone!'),
    ]
  
    @api.depends('name', 'zone_id.display_name')
    def _compute_display_name(self):
        for floor in self:
            floor.display_name = f"{floor.zone_id.display_name} - {floor.name}"
  
    @api.depends('room_ids', 'room_ids.door_ids')
    def _compute_totals(self):
        for floor in self:
            doors = floor.room_ids.mapped('door_ids')
            floor.total_rooms = len(floor.room_ids)
            floor.total_doors = len(doors)
  
    def write(self, vals):
        vals['updated_at'] = fields.Datetime.now()
        return super().write(vals)
```

#### Room Model (`models/alert_room.py`)

```python
from odoo import models, fields, api
from odoo.exceptions import ValidationError

class AlertRoom(models.Model):
    _name = 'alert.room'
    _description = 'Alert Room'
    _order = 'floor_id, name'
    _rec_name = 'display_name'

    # Basic Information
    name = fields.Char(string='Room Name', required=True, index=True)
    room_code = fields.Char(string='Room Code', required=True, size=20, index=True)
    room_type = fields.Selection([
        ('office', 'Office'),
        ('meeting', 'Meeting Room'),
        ('storage', 'Storage'),
        ('bathroom', 'Bathroom'),
        ('kitchen', 'Kitchen'),
        ('lobby', 'Lobby'),
        ('corridor', 'Corridor'),
        ('other', 'Other'),
    ], string='Room Type', required=True, default='office')
    capacity = fields.Integer(string='Capacity')
    area = fields.Float(string='Area (m²)', digits=(10, 2))
  
    # Foreign Keys
    floor_id = fields.Many2one('alert.floor', string='Floor', required=True, ondelete='cascade')
  
    # Related Fields (computed from floor)
    building_id = fields.Many2one('alert.building', string='Building', 
                                  related='floor_id.building_id', store=True, readonly=True)
    zone_id = fields.Many2one('alert.zone', string='Zone', 
                              related='floor_id.zone_id', store=True, readonly=True)
  
    # Computed Statistics
    total_doors = fields.Integer(string='Total Doors', compute='_compute_totals', store=True)
  
    # Status
    is_active = fields.Boolean(string='is_active', default=True)
  
    # Audit Fields
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now, readonly=True)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now, readonly=True)
  
    # Computed Display
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
  
    # Relationships
    door_ids = fields.One2many('alert.door', 'room_id', string='Doors')
  
    # Constraints
    _sql_constraints = [
        ('room_code_floor_unique', 'UNIQUE(room_code, floor_id)', 
         'Room code must be unique within the floor!'),
        ('capacity_positive', 'CHECK(capacity >= 0)', 'Capacity must be positive!'),
        ('area_positive', 'CHECK(area >= 0)', 'Area must be positive!'),
    ]
  
    @api.depends('name', 'floor_id.display_name')
    def _compute_display_name(self):
        for room in self:
            room.display_name = f"{room.floor_id.display_name} - {room.name}"
  
    @api.depends('door_ids')
    def _compute_totals(self):
        for room in self:
            room.total_doors = len(room.door_ids)
  
    def write(self, vals):
        vals['updated_at'] = fields.Datetime.now()
        return super().write(vals)
```

#### Door Model (`models/alert_door.py`)

```python
from odoo import models, fields, api

class AlertDoor(models.Model):
    _name = 'alert.door'
    _description = 'Alert Door'
    _order = 'room_id, name'
    _rec_name = 'display_name'

    # Basic Information
    name = fields.Char(string='Door Name', required=True, index=True)
    door_code = fields.Char(string='Door Code', required=True, size=20, index=True)
    door_type = fields.Selection([
        ('standard', 'Standard'),
        ('fire', 'Fire Door'),
        ('emergency', 'Emergency Exit'),
        ('security', 'Security Door'),
        ('automatic', 'Automatic Door'),
    ], string='Door Type', required=True, default='standard')
    access_level = fields.Selection([
        ('public', 'Public'),
        ('restricted', 'Restricted'),
        ('private', 'Private'),
        ('emergency', 'Emergency Only'),
    ], string='Access Level', required=True, default='public')
  
    # Status Information
    status = fields.Selection([
        ('closed', 'Closed'),
        ('open', 'Open'),
        ('locked', 'Locked'),
        ('unlocked', 'Unlocked'),
        ('fault', 'Fault'),
        ('maintenance', 'Maintenance'),
    ], string='Status', required=True, default='closed', index=True)
    last_status_change = fields.Datetime(string='Last Status Change', default=fields.Datetime.now)
  
    # Foreign Keys
    room_id = fields.Many2one('alert.room', string='Room', required=True, ondelete='cascade')
  
    # Related Fields (computed from room)
    floor_id = fields.Many2one('alert.floor', string='Floor', 
                               related='room_id.floor_id', store=True, readonly=True)
    building_id = fields.Many2one('alert.building', string='Building', 
                                  related='room_id.building_id', store=True, readonly=True)
    zone_id = fields.Many2one('alert.zone', string='Zone', 
                              related='room_id.zone_id', store=True, readonly=True)
  
    # Status
    is_active = fields.Boolean(string='is_active', default=True)
    is_alert = fields.Boolean(string='Alert Status', compute='_compute_is_alert', store=True)
  
    # Audit Fields
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now, readonly=True)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now, readonly=True)
  
    # Computed Display
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
  
    # Constraints
    _sql_constraints = [
        ('door_code_unique', 'UNIQUE(door_code)', 'Door code must be unique!'),
    ]
  
    @api.depends('name', 'room_id.display_name')
    def _compute_display_name(self):
        for door in self:
            door.display_name = f"{door.room_id.display_name} - {door.name}"
  
    @api.depends('status', 'door_type')
    def _compute_is_alert(self):
        alert_statuses = ['fault', 'maintenance']
        emergency_open_statuses = ['open', 'unlocked']
      
        for door in self:
            is_alert = door.status in alert_statuses
            if door.door_type == 'emergency' and door.status in emergency_open_statuses:
                is_alert = True
            door.is_alert = is_alert
  
    def write(self, vals):
        if 'status' in vals:
            vals['last_status_change'] = fields.Datetime.now()
        vals['updated_at'] = fields.Datetime.now()
        return super().write(vals)
```

## Step 3: Security Configuration

Create `security/ir.model.access.csv`:

```csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_alert_building_user,alert.building.user,model_alert_building,base.group_user,1,1,1,1
access_alert_zone_user,alert.zone.user,model_alert_zone,base.group_user,1,1,1,1
access_alert_floor_user,alert.floor.user,model_alert_floor,base.group_user,1,1,1,1
access_alert_room_user,alert.room.user,model_alert_room,base.group_user,1,1,1,1
access_alert_door_user,alert.door.user,model_alert_door,base.group_user,1,1,1,1
access_alert_event_user,alert.event.user,model_alert_event,base.group_user,1,1,1,1
access_alert_marker_user,alert.marker.user,model_alert_marker,base.group_user,1,1,1,1
access_alert_system_user,alert.system.user,model_alert_system,base.group_user,1,1,1,1
access_alert_system_metric_user,alert.system.metric.user,model_alert_system_metric,base.group_user,1,1,1,1
```

## Step 4: Data Migration Strategy

### From TypeScript to Odoo

1. **Export Current Data**: Extract data from your TypeScript application
2. **Transform Data**: Convert to Odoo-compatible format
3. **Import Data**: Use Odoo's data import tools

### Sample Migration Script

```python
# migration_script.py
import json
import csv
from datetime import datetime

def convert_typescript_to_odoo(typescript_data):
    """Convert TypeScript data structure to Odoo format"""
  
    # Building conversion
    buildings = []
    for building_data in typescript_data.get('buildings', []):
        buildings.append({
            'name': building_data['name'],
            'short_code': building_data['shortCode'],
            'address': building_data.get('address', ''),
            'description': building_data.get('description', ''),
            'is_active': True,
        })
  
    # Zone conversion
    zones = []
    for zone_data in typescript_data.get('zones', []):
        zones.append({
            'name': zone_data['name'],
            'zone_code': zone_data['zoneCode'],
            'description': zone_data.get('description', ''),
            'building_id/short_code': zone_data['buildingShortCode'],
            'is_active': True,
        })
  
    return {
        'buildings': buildings,
        'zones': zones,
        # Add other entities...
    }

def export_to_csv(data, filename):
    """Export data to CSV for Odoo import"""
    if data:
        with open(filename, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)

# Usage
with open('typescript_data.json', 'r') as f:
    ts_data = json.load(f)

odoo_data = convert_typescript_to_odoo(ts_data)

# Export to CSV files for import
export_to_csv(odoo_data['buildings'], 'buildings.csv')
export_to_csv(odoo_data['zones'], 'zones.csv')
```

## Step 5: Testing and Validation

### Unit Tests

Create test files in `tests/` directory:

```python
# tests/test_alert_building.py
from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError

class TestAlertBuilding(TransactionCase):
  
    def setUp(self):
        super().setUp()
        self.Building = self.env['alert.building']
  
    def test_building_creation(self):
        """Test building creation with valid data"""
        building = self.Building.create({
            'name': 'Test Building',
            'short_code': 'TB01',
            'address': '123 Test Street',
        })
        self.assertEqual(building.name, 'Test Building')
        self.assertEqual(building.short_code, 'TB01')
  
    def test_unique_constraints(self):
        """Test unique constraints"""
        self.Building.create({
            'name': 'Building 1',
            'short_code': 'B001',
        })
      
        with self.assertRaises(ValidationError):
            self.Building.create({
                'name': 'Building 2',
                'short_code': 'B001',  # Duplicate short_code
            })
```

## Step 6: Deployment Checklist

### Pre-deployment

- [ ] All models implemented and tested
- [ ] Security rules configured
- [ ] Data migration scripts prepared
- [ ] Views and menus created
- [ ] Unit tests passing

### Deployment

- [ ] Install module in test environment
- [ ] Run data migration
- [ ] Validate data integrity
- [ ] Test user workflows
- [ ] Performance testing

### Post-deployment

- [ ] Monitor system performance
- [ ] User training completed
- [ ] Documentation updated
- [ ] Backup procedures in place

## Maintenance and Updates

### Regular Tasks

1. **Data Backup**: Regular database backups
2. **Performance Monitoring**: Monitor query performance
3. **User Feedback**: Collect and address user feedback
4. **Security Updates**: Keep Odoo updated

### Future Enhancements

1. **API Integration**: REST API for external systems
2. **Mobile App**: Mobile interface for field operations
3. **Advanced Analytics**: Reporting and dashboard features
4. **IoT Integration**: Direct sensor data integration

This implementation guide provides a complete roadmap for converting your TypeScript-based Alert Dashboard System to a fully functional Odoo application.
