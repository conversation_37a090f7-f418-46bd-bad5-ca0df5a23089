# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class NebularFloor(models.Model):
    """Floor model for the Nebular Dashboard System"""
    
    # Private attributes
    _name = 'nebular.floor'
    _description = 'Nebular Floor'
    _inherit = ['mail.thread', 'mail.activity.schedule']
    _order = 'building_id, zone_id, floor_number, name'
    _rec_name = 'name'
    
    # Field declarations
    name = fields.Char(
        string='Floor Name',
        required=True,
        tracking=True,
        help='Name of the floor'
    )
    floor_number = fields.Integer(
        string='Floor Number',
        required=True,
        tracking=True,
        help='Floor number (can be negative for basement levels)'
    )
    description = fields.Text(
        string='Description',
        help='Detailed description of the floor'
    )
    
    # Floor plan fields
    floor_plan_image = fields.Binary(
        string='Floor Plan Image',
        help='Floor plan image for visualization'
    )
    floor_plan_filename = fields.Char(
        string='Floor Plan Filename',
        help='Filename of the floor plan image'
    )
    
    # Status and operational fields
    active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True,
        help='Whether this floor is active'
    )
    
    # Relationships
    building_id = fields.Many2one(
        comodel_name='nebular.building',
        string='Building',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Building this floor belongs to'
    )
    zone_id = fields.Many2one(
        comodel_name='nebular.zone',
        string='Zone',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Zone this floor belongs to'
    )
    room_ids = fields.One2many(
        comodel_name='nebular.room',
        inverse_name='floor_id',
        string='Rooms',
        help='Rooms on this floor'
    )
    door_ids = fields.One2many(
        comodel_name='nebular.door',
        inverse_name='floor_id',
        string='Doors',
        help='Doors on this floor'
    )
    device_ids = fields.One2many(
        comodel_name='nebular.device',
        inverse_name='floor_id',
        string='Devices',
        help='Devices on this floor'
    )
    event_ids = fields.One2many(
        comodel_name='nebular.event',
        inverse_name='floor_id',
        string='Events',
        help='Events related to this floor'
    )
    marker_ids = fields.One2many(
        comodel_name='nebular.marker',
        inverse_name='floor_id',
        string='Markers',
        help='Floor plan markers on this floor'
    )
    
    # Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Full display name including building and zone'
    )
    room_count = fields.Integer(
        string='Room Count',
        compute='_compute_counts',
        store=True,
        help='Number of rooms on this floor'
    )
    door_count = fields.Integer(
        string='Door Count',
        compute='_compute_counts',
        store=True,
        help='Number of doors on this floor'
    )
    device_count = fields.Integer(
        string='Device Count',
        compute='_compute_counts',
        store=True,
        help='Number of devices on this floor'
    )
    marker_count = fields.Integer(
        string='Marker Count',
        compute='_compute_counts',
        store=True,
        help='Number of markers on this floor'
    )
    event_count = fields.Integer(
        string='Event Count',
        compute='_compute_event_count',
        help='Number of recent events on this floor'
    )
    alert_count = fields.Integer(
        string='Alert Count',
        compute='_compute_alert_count',
        help='Number of active alerts on this floor'
    )
    has_alerts = fields.Boolean(
        string='Has Alerts',
        compute='_compute_alert_count',
        help='Whether this floor has active alerts'
    )
    has_floor_plan = fields.Boolean(
        string='Has Floor Plan',
        compute='_compute_has_floor_plan',
        help='Whether this floor has a floor plan image'
    )
    
    # Compute methods
    @api.depends('name', 'floor_number', 'zone_id.name', 'building_id.name')
    def _compute_display_name(self):
        """Compute display name with building and zone"""
        for record in self:
            parts = []
            if record.building_id:
                parts.append(record.building_id.name)
            if record.zone_id:
                parts.append(record.zone_id.name)
            if record.name:
                parts.append(f"Floor {record.floor_number} - {record.name}")
            else:
                parts.append(f"Floor {record.floor_number}")
            record.display_name = " - ".join(parts) if parts else ''
    
    @api.depends('room_ids', 'door_ids', 'device_ids', 'marker_ids')
    def _compute_counts(self):
        """Compute counts for related records"""
        for record in self:
            record.room_count = len(record.room_ids)
            record.door_count = len(record.door_ids)
            record.device_count = len(record.device_ids)
            record.marker_count = len(record.marker_ids)
    
    @api.depends('event_ids')
    def _compute_event_count(self):
        """Compute recent event count (last 30 days)"""
        for record in self:
            domain = [
                ('floor_id', '=', record.id),
                ('create_date', '>=', fields.Datetime.now() - fields.timedelta(days=30))
            ]
            record.event_count = self.env['nebular.event'].search_count(domain)
    
    @api.depends('marker_ids.is_alert', 'device_ids.is_alert')
    def _compute_alert_count(self):
        """Compute alert count from markers and devices"""
        for record in self:
            marker_alerts = len(record.marker_ids.filtered('is_alert'))
            device_alerts = len(record.device_ids.filtered('is_alert'))
            record.alert_count = marker_alerts + device_alerts
            record.has_alerts = record.alert_count > 0
    
    @api.depends('floor_plan_image')
    def _compute_has_floor_plan(self):
        """Check if floor has a floor plan image"""
        for record in self:
            record.has_floor_plan = bool(record.floor_plan_image)
    
    # Constraints
    @api.constrains('floor_number', 'building_id', 'zone_id')
    def _check_floor_number_unique_per_zone(self):
        """Ensure floor number is unique within zone"""
        for record in self:
            if record.floor_number is not False and record.zone_id:
                existing = self.search([
                    ('floor_number', '=', record.floor_number),
                    ('zone_id', '=', record.zone_id.id),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        f"Floor number '{record.floor_number}' already exists in zone '{record.zone_id.name}'."
                    )
    
    @api.constrains('zone_id', 'building_id')
    def _check_zone_building_consistency(self):
        """Ensure zone belongs to the same building"""
        for record in self:
            if record.zone_id and record.building_id:
                if record.zone_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Zone '{record.zone_id.name}' does not belong to building '{record.building_id.name}'."
                    )
    
    @api.constrains('name')
    def _check_name_not_empty(self):
        """Ensure floor name is not empty"""
        for record in self:
            if not record.name or not record.name.strip():
                raise ValidationError("Floor name cannot be empty.")
    
    # Onchange methods
    @api.onchange('building_id')
    def _onchange_building_id(self):
        """Filter zones based on selected building"""
        if self.building_id:
            domain = [('building_id', '=', self.building_id.id)]
            if self.zone_id and self.zone_id.building_id != self.building_id:
                self.zone_id = False
            return {'domain': {'zone_id': domain}}
        else:
            self.zone_id = False
            return {'domain': {'zone_id': []}}
    
    # CRUD overrides
    def create(self, vals_list):
        """Override create to add tracking message"""
        floors = super().create(vals_list)
        for floor in floors:
            floor.message_post(
                body=f"Floor '{floor.name}' (Level {floor.floor_number}) has been created in {floor.zone_id.name}.",
                message_type='notification'
            )
        return floors
    
    def write(self, vals):
        """Override write to add tracking for important changes"""
        result = super().write(vals)
        if 'active' in vals:
            for record in self:
                status = 'activated' if vals['active'] else 'deactivated'
                record.message_post(
                    body=f"Floor '{record.name}' has been {status}.",
                    message_type='notification'
                )
        return result
    
    # Action methods
    def action_view_rooms(self):
        """Action to view rooms on this floor"""
        self.ensure_one()
        return {
            'name': f'Rooms - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.room',
            'view_mode': 'list,form',
            'domain': [('floor_id', '=', self.id)],
            'context': {
                'default_floor_id': self.id,
                'default_zone_id': self.zone_id.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_view_devices(self):
        """Action to view devices on this floor"""
        self.ensure_one()
        return {
            'name': f'Devices - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.device',
            'view_mode': 'list,form',
            'domain': [('floor_id', '=', self.id)],
            'context': {
                'default_floor_id': self.id,
                'default_zone_id': self.zone_id.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_view_markers(self):
        """Action to view markers on this floor"""
        self.ensure_one()
        return {
            'name': f'Floor Plan Markers - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.marker',
            'view_mode': 'list,form',
            'domain': [('floor_id', '=', self.id)],
            'context': {
                'default_floor_id': self.id,
                'default_zone_id': self.zone_id.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_view_floor_plan(self):
        """Action to view floor plan with markers"""
        self.ensure_one()
        if not self.has_floor_plan:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': 'No floor plan image available for this floor.',
                    'type': 'warning',
                }
            }
        
        # Return action to open floor plan viewer
        return {
            'name': f'Floor Plan - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.floor',
            'res_id': self.id,
            'view_mode': 'form',
            'view_id': self.env.ref('nebular.nebular_floor_view_form_floor_plan').id,
            'target': 'new',
        }
    
    # Other business methods
    def get_floor_summary(self):
        """Get a summary of floor statistics"""
        self.ensure_one()
        return {
            'name': self.name,
            'floor_number': self.floor_number,
            'building': self.building_id.name,
            'zone': self.zone_id.name,
            'rooms': self.room_count,
            'doors': self.door_count,
            'devices': self.device_count,
            'markers': self.marker_count,
            'events': self.event_count,
            'alerts': self.alert_count,
            'has_alerts': self.has_alerts,
            'has_floor_plan': self.has_floor_plan,
        }