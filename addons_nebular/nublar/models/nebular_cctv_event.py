# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class NebularCctvEvent(models.Model):
    """CCTV Event specialized model for surveillance events"""
    
    # Private attributes
    _name = 'nebular.cctv.event'
    _description = 'Nebular CCTV Event'
    _inherit = ['nebular.event']
    _order = 'event_time desc, create_date desc'
    
    # CCTV-specific fields
    camera_id = fields.Char(
        string='Camera ID',
        required=True,
        help='Unique identifier of the CCTV camera'
    )
    camera_name = fields.Char(
        string='Camera Name',
        help='Descriptive name of the camera'
    )
    camera_location = fields.Char(
        string='Camera Location',
        help='Physical location description of the camera'
    )
    
    detection_type = fields.Selection([
        ('motion', 'Motion Detection'),
        ('person', 'Person Detection'),
        ('vehicle', 'Vehicle Detection'),
        ('face', 'Face Detection'),
        ('object', 'Object Detection'),
        ('intrusion', 'Intrusion Detection'),
        ('loitering', 'Loitering Detection'),
        ('crowd', 'Crowd Detection'),
        ('abandoned_object', 'Abandoned Object'),
        ('line_crossing', 'Line Crossing'),
        ('area_entry', 'Area Entry'),
        ('area_exit', 'Area Exit'),
        ('tampering', 'Camera Tampering'),
        ('video_loss', 'Video Loss'),
        ('other', 'Other'),
    ], string='Detection Type', help='Type of detection that triggered the event')
    
    confidence_level = fields.Float(
        string='Confidence Level (%)',
        help='AI confidence level for the detection'
    )
    
    # Video/Image details
    video_clip_path = fields.Char(
        string='Video Clip Path',
        help='Path to the recorded video clip'
    )
    snapshot_path = fields.Char(
        string='Snapshot Path',
        help='Path to the event snapshot image'
    )
    recording_duration = fields.Float(
        string='Recording Duration (seconds)',
        help='Duration of the recorded video clip'
    )
    
    # Detection details
    object_count = fields.Integer(
        string='Object Count',
        default=0,
        help='Number of objects detected'
    )
    person_count = fields.Integer(
        string='Person Count',
        default=0,
        help='Number of persons detected'
    )
    vehicle_count = fields.Integer(
        string='Vehicle Count',
        default=0,
        help='Number of vehicles detected'
    )
    
    # Analytics data
    motion_area_percentage = fields.Float(
        string='Motion Area (%)',
        help='Percentage of frame area with motion'
    )
    detection_zone = fields.Char(
        string='Detection Zone',
        help='Name or ID of the detection zone'
    )
    
    # Face recognition (if applicable)
    face_detected = fields.Boolean(
        string='Face Detected',
        default=False,
        help='Whether a face was detected'
    )
    face_recognized = fields.Boolean(
        string='Face Recognized',
        default=False,
        help='Whether the face was recognized'
    )
    recognized_person = fields.Char(
        string='Recognized Person',
        help='Name of the recognized person'
    )
    face_match_confidence = fields.Float(
        string='Face Match Confidence (%)',
        help='Confidence level of face recognition'
    )
    
    # License plate recognition (if applicable)
    license_plate_detected = fields.Boolean(
        string='License Plate Detected',
        default=False,
        help='Whether a license plate was detected'
    )
    license_plate_number = fields.Char(
        string='License Plate Number',
        help='Detected license plate number'
    )
    plate_confidence = fields.Float(
        string='Plate Confidence (%)',
        help='Confidence level of license plate recognition'
    )
    
    # Alert and response
    is_security_alert = fields.Boolean(
        string='Security Alert',
        default=False,
        help='Whether this event is a security alert'
    )
    alert_sent = fields.Boolean(
        string='Alert Sent',
        default=False,
        help='Whether an alert notification was sent'
    )
    response_required = fields.Boolean(
        string='Response Required',
        default=False,
        help='Whether this event requires security response'
    )
    
    # Investigation
    reviewed = fields.Boolean(
        string='Reviewed',
        default=False,
        help='Whether the event has been reviewed'
    )
    reviewed_by = fields.Many2one(
        comodel_name='res.users',
        string='Reviewed By',
        help='User who reviewed the event'
    )
    review_date = fields.Datetime(
        string='Review Date',
        help='When the event was reviewed'
    )
    review_notes = fields.Text(
        string='Review Notes',
        help='Notes from the review'
    )
    
    # Archive and retention
    archived = fields.Boolean(
        string='Archived',
        default=False,
        help='Whether the event has been archived'
    )
    retention_date = fields.Date(
        string='Retention Date',
        help='Date until which the video should be retained'
    )
    
    # Computed fields
    event_classification = fields.Selection([
        ('normal', 'Normal'),
        ('suspicious', 'Suspicious'),
        ('security_incident', 'Security Incident'),
        ('false_positive', 'False Positive'),
    ], string='Classification', compute='_compute_event_classification', store=True)
    
    detection_summary = fields.Char(
        string='Detection Summary',
        compute='_compute_detection_summary',
        store=True,
        help='Summary of what was detected'
    )
    
    # Default methods
    @api.model
    def default_get(self, fields_list):
        """Set default values for CCTV events"""
        defaults = super().default_get(fields_list)
        defaults.update({
            'event_type': 'cctv',
            'event_category': 'surveillance',
        })
        return defaults
    
    # Compute methods
    @api.depends('detection_type', 'is_security_alert', 'confidence_level', 'reviewed')
    def _compute_event_classification(self):
        """Compute event classification based on detection details"""
        for record in self:
            if record.reviewed and record.review_notes and 'false' in record.review_notes.lower():
                record.event_classification = 'false_positive'
            elif record.is_security_alert or record.detection_type in ['intrusion', 'tampering', 'abandoned_object']:
                record.event_classification = 'security_incident'
            elif record.detection_type in ['loitering', 'line_crossing', 'area_entry'] and record.confidence_level > 80:
                record.event_classification = 'suspicious'
            else:
                record.event_classification = 'normal'
    
    @api.depends('detection_type', 'person_count', 'vehicle_count', 'object_count', 'recognized_person', 'license_plate_number')
    def _compute_detection_summary(self):
        """Compute detection summary"""
        for record in self:
            parts = []
            
            if record.detection_type:
                parts.append(record.detection_type.replace('_', ' ').title())
            
            # Add counts
            counts = []
            if record.person_count > 0:
                counts.append(f"{record.person_count} person(s)")
            if record.vehicle_count > 0:
                counts.append(f"{record.vehicle_count} vehicle(s)")
            if record.object_count > 0:
                counts.append(f"{record.object_count} object(s)")
            
            if counts:
                parts.append(f"({', '.join(counts)})")
            
            # Add recognition info
            if record.recognized_person:
                parts.append(f"- {record.recognized_person}")
            if record.license_plate_number:
                parts.append(f"- Plate: {record.license_plate_number}")
            
            record.detection_summary = " ".join(parts) if parts else 'CCTV Event'
    
    # Constraints
    @api.constrains('confidence_level', 'face_match_confidence', 'plate_confidence')
    def _check_confidence_levels(self):
        """Validate confidence level percentages"""
        for record in self:
            for field_name, field_value in [
                ('confidence_level', record.confidence_level),
                ('face_match_confidence', record.face_match_confidence),
                ('plate_confidence', record.plate_confidence)
            ]:
                if field_value and (field_value < 0 or field_value > 100):
                    raise ValidationError(f"{field_name.replace('_', ' ').title()} must be between 0 and 100 percent.")
    
    @api.constrains('motion_area_percentage')
    def _check_motion_area_percentage(self):
        """Validate motion area percentage"""
        for record in self:
            if record.motion_area_percentage and (record.motion_area_percentage < 0 or record.motion_area_percentage > 100):
                raise ValidationError("Motion area percentage must be between 0 and 100 percent.")
    
    @api.constrains('recording_duration')
    def _check_recording_duration(self):
        """Validate recording duration"""
        for record in self:
            if record.recording_duration and record.recording_duration < 0:
                raise ValidationError("Recording duration cannot be negative.")
    
    @api.constrains('object_count', 'person_count', 'vehicle_count')
    def _check_counts(self):
        """Validate detection counts"""
        for record in self:
            for field_name, field_value in [
                ('object_count', record.object_count),
                ('person_count', record.person_count),
                ('vehicle_count', record.vehicle_count)
            ]:
                if field_value < 0:
                    raise ValidationError(f"{field_name.replace('_', ' ').title()} cannot be negative.")
    
    # Onchange methods
    @api.onchange('detection_type')
    def _onchange_detection_type(self):
        """Update fields based on detection type"""
        security_types = ['intrusion', 'tampering', 'abandoned_object', 'line_crossing']
        
        if self.detection_type in security_types:
            self.is_security_alert = True
            self.response_required = True
            self.is_alert = True
            self.severity = 'high'
            self.priority = '3'
        elif self.detection_type in ['loitering', 'crowd']:
            self.is_security_alert = True
            self.severity = 'medium'
            self.priority = '2'
        elif self.detection_type in ['video_loss', 'tampering']:
            self.is_alert = True
            self.severity = 'high'
            self.priority = '3'
        else:
            self.is_security_alert = False
            self.response_required = False
            self.severity = 'low'
            self.priority = '1'
    
    @api.onchange('face_detected')
    def _onchange_face_detected(self):
        """Clear face recognition fields if no face detected"""
        if not self.face_detected:
            self.face_recognized = False
            self.recognized_person = False
            self.face_match_confidence = 0.0
    
    @api.onchange('license_plate_detected')
    def _onchange_license_plate_detected(self):
        """Clear license plate fields if no plate detected"""
        if not self.license_plate_detected:
            self.license_plate_number = False
            self.plate_confidence = 0.0
    
    @api.onchange('reviewed')
    def _onchange_reviewed(self):
        """Update review fields when reviewed status changes"""
        if self.reviewed and not self.review_date:
            self.review_date = fields.Datetime.now()
            self.reviewed_by = self.env.user.id
    
    # Action methods
    def action_review_event(self):
        """Mark event as reviewed"""
        self.ensure_one()
        self.reviewed = True
        self.review_date = fields.Datetime.now()
        self.reviewed_by = self.env.user.id
        self.message_post(
            body=f"CCTV event '{self.name}' reviewed by {self.env.user.name}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Event '{self.name}' marked as reviewed.",
                'type': 'success',
            }
        }
    
    def action_send_alert(self):
        """Send security alert"""
        self.ensure_one()
        self.alert_sent = True
        self.is_security_alert = True
        self.message_post(
            body=f"Security alert sent for CCTV event '{self.name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Security alert sent for '{self.name}'.",
                'type': 'warning',
            }
        }
    
    def action_mark_false_positive(self):
        """Mark event as false positive"""
        self.ensure_one()
        self.event_classification = 'false_positive'
        self.reviewed = True
        self.review_date = fields.Datetime.now()
        self.reviewed_by = self.env.user.id
        self.is_security_alert = False
        self.response_required = False
        self.severity = 'low'
        if not self.review_notes:
            self.review_notes = "Marked as false positive"
        self.message_post(
            body=f"CCTV event '{self.name}' marked as false positive.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"'{self.name}' marked as false positive.",
                'type': 'info',
            }
        }
    
    def action_escalate_security(self):
        """Escalate to security incident"""
        self.ensure_one()
        self.event_classification = 'security_incident'
        self.is_security_alert = True
        self.response_required = True
        self.severity = 'high'
        self.priority = '4'
        self.message_post(
            body=f"CCTV event '{self.name}' escalated to security incident.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"'{self.name}' escalated to security incident.",
                'type': 'warning',
            }
        }
    
    def action_archive_event(self):
        """Archive the event"""
        self.ensure_one()
        self.archived = True
        self.state = 'closed'
        self.closed_time = fields.Datetime.now()
        self.is_active = False
        self.message_post(
            body=f"CCTV event '{self.name}' archived.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"'{self.name}' archived successfully.",
                'type': 'success',
            }
        }
    
    def action_download_video(self):
        """Download video clip"""
        self.ensure_one()
        if not self.video_clip_path:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': "No video clip available for this event.",
                    'type': 'warning',
                }
            }
        
        # In a real implementation, this would handle file download
        self.message_post(
            body=f"Video clip downloaded for CCTV event '{self.name}'.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Video clip download initiated for '{self.name}'.",
                'type': 'info',
            }
        }
    
    def action_view_snapshot(self):
        """View event snapshot"""
        self.ensure_one()
        if not self.snapshot_path:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': "No snapshot available for this event.",
                    'type': 'warning',
                }
            }
        
        # In a real implementation, this would open the image viewer
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Snapshot viewer opened for '{self.name}'.",
                'type': 'info',
            }
        }
    
    # Other business methods
    def get_cctv_event_summary(self):
        """Get CCTV event specific summary"""
        self.ensure_one()
        summary = self.get_event_summary()
        summary.update({
            'camera_id': self.camera_id,
            'camera_name': self.camera_name,
            'camera_location': self.camera_location,
            'detection_type': self.detection_type,
            'confidence_level': self.confidence_level,
            'video_clip_path': self.video_clip_path,
            'snapshot_path': self.snapshot_path,
            'recording_duration': self.recording_duration,
            'object_count': self.object_count,
            'person_count': self.person_count,
            'vehicle_count': self.vehicle_count,
            'motion_area_percentage': self.motion_area_percentage,
            'detection_zone': self.detection_zone,
            'face_detected': self.face_detected,
            'face_recognized': self.face_recognized,
            'recognized_person': self.recognized_person,
            'face_match_confidence': self.face_match_confidence,
            'license_plate_detected': self.license_plate_detected,
            'license_plate_number': self.license_plate_number,
            'plate_confidence': self.plate_confidence,
            'is_security_alert': self.is_security_alert,
            'alert_sent': self.alert_sent,
            'response_required': self.response_required,
            'reviewed': self.reviewed,
            'reviewed_by': self.reviewed_by.name if self.reviewed_by else None,
            'review_date': self.review_date,
            'event_classification': self.event_classification,
            'detection_summary': self.detection_summary,
        })
        return summary
    
    def generate_surveillance_report(self):
        """Generate surveillance report"""
        self.ensure_one()
        report_data = {
            'event_id': self.event_id,
            'event_time': self.event_time,
            'location': self.location_display,
            'camera': {
                'id': self.camera_id,
                'name': self.camera_name,
                'location': self.camera_location,
            },
            'detection': {
                'type': self.detection_type,
                'confidence': self.confidence_level,
                'zone': self.detection_zone,
                'summary': self.detection_summary,
            },
            'counts': {
                'objects': self.object_count,
                'persons': self.person_count,
                'vehicles': self.vehicle_count,
            },
            'analytics': {
                'motion_area': self.motion_area_percentage,
            },
            'recognition': {
                'face_detected': self.face_detected,
                'face_recognized': self.face_recognized,
                'recognized_person': self.recognized_person,
                'face_confidence': self.face_match_confidence,
                'plate_detected': self.license_plate_detected,
                'plate_number': self.license_plate_number,
                'plate_confidence': self.plate_confidence,
            },
            'media': {
                'video_path': self.video_clip_path,
                'snapshot_path': self.snapshot_path,
                'duration': self.recording_duration,
            },
            'security': {
                'classification': self.event_classification,
                'is_alert': self.is_security_alert,
                'alert_sent': self.alert_sent,
                'response_required': self.response_required,
            },
            'review': {
                'reviewed': self.reviewed,
                'reviewed_by': self.reviewed_by.name if self.reviewed_by else None,
                'review_date': self.review_date,
                'notes': self.review_notes,
            }
        }
        return report_data
    
    def analyze_detection_patterns(self):
        """Analyze detection patterns for this camera"""
        self.ensure_one()
        patterns = []
        
        # Check for frequent false positives
        recent_events = self.search([
            ('camera_id', '=', self.camera_id),
            ('event_time', '>=', fields.Datetime.now() - fields.timedelta(days=7)),
            ('id', '!=', self.id)
        ])
        
        false_positives = recent_events.filtered(lambda e: e.event_classification == 'false_positive')
        if len(false_positives) > len(recent_events) * 0.5:  # More than 50% false positives
            patterns.append('High false positive rate detected')
        
        # Check for detection clustering
        same_type_events = recent_events.filtered(lambda e: e.detection_type == self.detection_type)
        if len(same_type_events) > 10:
            patterns.append(f'High frequency of {self.detection_type} detections')
        
        # Check for low confidence detections
        low_confidence_events = recent_events.filtered(lambda e: e.confidence_level < 70)
        if len(low_confidence_events) > len(recent_events) * 0.3:  # More than 30% low confidence
            patterns.append('High rate of low confidence detections')
        
        return patterns