# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
import logging

_logger = logging.getLogger(__name__)


class NebularSystemAlert(models.Model):
    """System Alert Model for Nebular Dashboard System"""
    
    # 1. Private attributes
    _name = 'nebular.system.alert'
    _description = 'Nebular System Alert'
    _inherit = ['mail.thread', 'mail.activity.schedule']
    _order = 'created_at desc, severity desc, name'
    _rec_name = 'display_name'
    
    # 2. Default methods
    def _default_created_at(self):
        """Default creation timestamp"""
        return fields.Datetime.now()
    
    def _default_alert_id(self):
        """Generate unique alert ID"""
        return f"ALERT-{fields.Datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # 3. Selection methods
    def _selection_alert_type(self):
        """Selection for alert type"""
        return [
            ('threshold', 'Threshold Alert'),
            ('system', 'System Alert'),
            ('security', 'Security Alert'),
            ('device', 'Device Alert'),
            ('network', 'Network Alert'),
            ('application', 'Application Alert'),
            ('user', 'User Alert'),
            ('maintenance', 'Maintenance Alert'),
            ('custom', 'Custom Alert')
        ]
    
    def _selection_severity(self):
        """Selection for alert severity"""
        return [
            ('info', 'Info'),
            ('warning', 'Warning'),
            ('critical', 'Critical'),
            ('emergency', 'Emergency')
        ]
    
    def _selection_status(self):
        """Selection for alert status"""
        return [
            ('new', 'New'),
            ('acknowledged', 'Acknowledged'),
            ('investigating', 'Investigating'),
            ('resolved', 'Resolved'),
            ('closed', 'Closed'),
            ('false_positive', 'False Positive')
        ]
    
    def _selection_priority(self):
        """Selection for alert priority"""
        return [
            ('low', 'Low'),
            ('medium', 'Medium'),
            ('high', 'High'),
            ('urgent', 'Urgent')
        ]
    
    # 4. Field declarations
    name = fields.Char(
        string='Alert Name',
        required=True,
        tracking=True,
        help='Name of the alert'
    )
    alert_id = fields.Char(
        string='Alert ID',
        default=_default_alert_id,
        required=True,
        copy=False,
        help='Unique identifier for the alert'
    )
    description = fields.Text(
        string='Description',
        help='Detailed description of the alert'
    )
    alert_type = fields.Selection(
        selection='_selection_alert_type',
        string='Alert Type',
        required=True,
        default='system',
        tracking=True,
        help='Type of the alert'
    )
    severity = fields.Selection(
        selection='_selection_severity',
        string='Severity',
        required=True,
        default='warning',
        tracking=True,
        help='Severity level of the alert'
    )
    status = fields.Selection(
        selection='_selection_status',
        string='Status',
        default='new',
        tracking=True,
        help='Current status of the alert'
    )
    priority = fields.Selection(
        selection='_selection_priority',
        string='Priority',
        default='medium',
        tracking=True,
        help='Priority level of the alert'
    )
    is_active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True,
        help='Whether this alert is active'
    )
    is_acknowledged = fields.Boolean(
        string='Acknowledged',
        default=False,
        tracking=True,
        help='Whether this alert has been acknowledged'
    )
    is_resolved = fields.Boolean(
        string='Resolved',
        default=False,
        tracking=True,
        help='Whether this alert has been resolved'
    )
    
    # Timestamp fields
    created_at = fields.Datetime(
        string='Created At',
        default=_default_created_at,
        required=True,
        help='When the alert was created'
    )
    acknowledged_at = fields.Datetime(
        string='Acknowledged At',
        help='When the alert was acknowledged'
    )
    resolved_at = fields.Datetime(
        string='Resolved At',
        help='When the alert was resolved'
    )
    closed_at = fields.Datetime(
        string='Closed At',
        help='When the alert was closed'
    )
    
    # User fields
    acknowledged_by = fields.Many2one(
        'res.users',
        string='Acknowledged By',
        help='User who acknowledged the alert'
    )
    resolved_by = fields.Many2one(
        'res.users',
        string='Resolved By',
        help='User who resolved the alert'
    )
    assigned_to = fields.Many2one(
        'res.users',
        string='Assigned To',
        tracking=True,
        help='User assigned to handle this alert'
    )
    
    # Relationships
    metric_id = fields.Many2one(
        'nebular.system.metric',
        string='Related Metric',
        help='Metric that triggered this alert'
    )
    building_id = fields.Many2one(
        'nebular.building',
        string='Building',
        help='Building related to this alert'
    )
    zone_id = fields.Many2one(
        'nebular.zone',
        string='Zone',
        help='Zone related to this alert'
    )
    floor_id = fields.Many2one(
        'nebular.floor',
        string='Floor',
        help='Floor related to this alert'
    )
    room_id = fields.Many2one(
        'nebular.room',
        string='Room',
        help='Room related to this alert'
    )
    door_id = fields.Many2one(
        'nebular.door',
        string='Door',
        help='Door related to this alert'
    )
    device_id = fields.Many2one(
        'nebular.device',
        string='Device',
        help='Device related to this alert'
    )
    event_id = fields.Many2one(
        'nebular.event',
        string='Related Event',
        help='Event that triggered this alert'
    )
    
    # Additional data
    alert_data = fields.Text(
        string='Alert Data',
        help='Additional data related to the alert (JSON format)'
    )
    resolution_notes = fields.Text(
        string='Resolution Notes',
        help='Notes about how the alert was resolved'
    )
    
    # 5. Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Display name for the alert'
    )
    severity_color = fields.Char(
        string='Severity Color',
        compute='_compute_severity_color',
        help='Color based on severity'
    )
    status_color = fields.Char(
        string='Status Color',
        compute='_compute_status_color',
        help='Color based on status'
    )
    duration = fields.Char(
        string='Duration',
        compute='_compute_duration',
        help='Duration since alert creation'
    )
    location_display = fields.Char(
        string='Location',
        compute='_compute_location_display',
        help='Display location information'
    )
    age_hours = fields.Float(
        string='Age (Hours)',
        compute='_compute_age_hours',
        help='Age of the alert in hours'
    )
    response_time = fields.Float(
        string='Response Time (Hours)',
        compute='_compute_response_time',
        help='Time taken to acknowledge the alert'
    )
    resolution_time = fields.Float(
        string='Resolution Time (Hours)',
        compute='_compute_resolution_time',
        help='Time taken to resolve the alert'
    )
    
    # 6. Compute methods
    @api.depends('name', 'alert_id', 'severity', 'status')
    def _compute_display_name(self):
        """Compute display name"""
        for record in self:
            severity_name = dict(record._selection_severity()).get(record.severity, record.severity)
            status_name = dict(record._selection_status()).get(record.status, record.status)
            record.display_name = f"[{record.alert_id}] {severity_name} - {record.name} ({status_name})"
    
    @api.depends('severity')
    def _compute_severity_color(self):
        """Compute severity color"""
        for record in self:
            colors = {
                'info': '#17a2b8',      # Blue
                'warning': '#ffc107',   # Yellow
                'critical': '#dc3545',  # Red
                'emergency': '#6f42c1'  # Purple
            }
            record.severity_color = colors.get(record.severity, '#6c757d')
    
    @api.depends('status')
    def _compute_status_color(self):
        """Compute status color"""
        for record in self:
            colors = {
                'new': '#dc3545',           # Red
                'acknowledged': '#ffc107',  # Yellow
                'investigating': '#17a2b8', # Blue
                'resolved': '#28a745',      # Green
                'closed': '#6c757d',        # Gray
                'false_positive': '#6c757d' # Gray
            }
            record.status_color = colors.get(record.status, '#6c757d')
    
    @api.depends('created_at')
    def _compute_duration(self):
        """Compute duration since creation"""
        for record in self:
            if record.created_at:
                now = fields.Datetime.now()
                delta = now - record.created_at
                
                if delta.days > 0:
                    record.duration = f"{delta.days}d {delta.seconds // 3600}h"
                elif delta.seconds >= 3600:
                    record.duration = f"{delta.seconds // 3600}h {(delta.seconds % 3600) // 60}m"
                else:
                    record.duration = f"{delta.seconds // 60}m"
            else:
                record.duration = "Unknown"
    
    @api.depends('building_id', 'zone_id', 'floor_id', 'room_id', 'door_id', 'device_id')
    def _compute_location_display(self):
        """Compute location display"""
        for record in self:
            location_parts = []
            if record.building_id:
                location_parts.append(f"Building: {record.building_id.name}")
            if record.zone_id:
                location_parts.append(f"Zone: {record.zone_id.name}")
            if record.floor_id:
                location_parts.append(f"Floor: {record.floor_id.name}")
            if record.room_id:
                location_parts.append(f"Room: {record.room_id.name}")
            if record.door_id:
                location_parts.append(f"Door: {record.door_id.name}")
            if record.device_id:
                location_parts.append(f"Device: {record.device_id.name}")
            
            record.location_display = " | ".join(location_parts) if location_parts else "System Wide"
    
    @api.depends('created_at')
    def _compute_age_hours(self):
        """Compute age in hours"""
        for record in self:
            if record.created_at:
                now = fields.Datetime.now()
                delta = now - record.created_at
                record.age_hours = delta.total_seconds() / 3600
            else:
                record.age_hours = 0
    
    @api.depends('created_at', 'acknowledged_at')
    def _compute_response_time(self):
        """Compute response time"""
        for record in self:
            if record.created_at and record.acknowledged_at:
                delta = record.acknowledged_at - record.created_at
                record.response_time = delta.total_seconds() / 3600
            else:
                record.response_time = 0
    
    @api.depends('created_at', 'resolved_at')
    def _compute_resolution_time(self):
        """Compute resolution time"""
        for record in self:
            if record.created_at and record.resolved_at:
                delta = record.resolved_at - record.created_at
                record.resolution_time = delta.total_seconds() / 3600
            else:
                record.resolution_time = 0
    
    # 7. Constraints
    @api.constrains('alert_id')
    def _check_alert_id_unique(self):
        """Check alert ID is unique"""
        for record in self:
            if self.search_count([('alert_id', '=', record.alert_id), ('id', '!=', record.id)]) > 0:
                raise ValidationError(f"Alert ID '{record.alert_id}' already exists")
    
    @api.constrains('acknowledged_at', 'created_at')
    def _check_acknowledged_at(self):
        """Check acknowledged timestamp is after creation"""
        for record in self:
            if record.acknowledged_at and record.created_at:
                if record.acknowledged_at < record.created_at:
                    raise ValidationError("Acknowledged time cannot be before creation time")
    
    @api.constrains('resolved_at', 'created_at')
    def _check_resolved_at(self):
        """Check resolved timestamp is after creation"""
        for record in self:
            if record.resolved_at and record.created_at:
                if record.resolved_at < record.created_at:
                    raise ValidationError("Resolved time cannot be before creation time")
    
    # 8. Onchange methods
    @api.onchange('status')
    def _onchange_status(self):
        """Update flags when status changes"""
        if self.status == 'acknowledged':
            self.is_acknowledged = True
            if not self.acknowledged_at:
                self.acknowledged_at = fields.Datetime.now()
                self.acknowledged_by = self.env.user
        elif self.status in ['resolved', 'closed']:
            self.is_resolved = True
            if not self.resolved_at:
                self.resolved_at = fields.Datetime.now()
                self.resolved_by = self.env.user
        elif self.status == 'false_positive':
            self.is_active = False
            self.is_resolved = True
            if not self.resolved_at:
                self.resolved_at = fields.Datetime.now()
                self.resolved_by = self.env.user
    
    @api.onchange('severity')
    def _onchange_severity(self):
        """Update priority when severity changes"""
        if self.severity:
            severity_priority_map = {
                'info': 'low',
                'warning': 'medium',
                'critical': 'high',
                'emergency': 'urgent'
            }
            self.priority = severity_priority_map.get(self.severity, 'medium')
    
    # 9. CRUD overrides
    @api.model
    def create(self, vals):
        """Override create to set default values"""
        # Generate alert ID if not provided
        if not vals.get('alert_id'):
            vals['alert_id'] = self._default_alert_id()
        
        # Set default assigned user based on alert type
        if not vals.get('assigned_to'):
            alert_type = vals.get('alert_type', 'system')
            # You can implement logic to assign based on alert type
            # For now, we'll leave it unassigned
        
        record = super().create(vals)
        
        # Send notification for critical and emergency alerts
        if record.severity in ['critical', 'emergency']:
            record._send_alert_notification()
        
        return record
    
    def write(self, vals):
        """Override write to handle status changes"""
        # Handle status changes
        if 'status' in vals:
            status = vals['status']
            if status == 'acknowledged' and not self.is_acknowledged:
                vals.update({
                    'is_acknowledged': True,
                    'acknowledged_at': fields.Datetime.now(),
                    'acknowledged_by': self.env.user.id
                })
            elif status in ['resolved', 'closed'] and not self.is_resolved:
                vals.update({
                    'is_resolved': True,
                    'resolved_at': fields.Datetime.now(),
                    'resolved_by': self.env.user.id
                })
            elif status == 'false_positive':
                vals.update({
                    'is_active': False,
                    'is_resolved': True,
                    'resolved_at': fields.Datetime.now(),
                    'resolved_by': self.env.user.id
                })
        
        return super().write(vals)
    
    # 10. Action methods
    def action_acknowledge(self):
        """Acknowledge alert"""
        self.ensure_one()
        if self.is_acknowledged:
            raise UserError("Alert is already acknowledged")
        
        self.write({
            'status': 'acknowledged',
            'is_acknowledged': True,
            'acknowledged_at': fields.Datetime.now(),
            'acknowledged_by': self.env.user.id
        })
        
        self.message_post(body=f"Alert acknowledged by {self.env.user.name}")
    
    def action_investigate(self):
        """Start investigating alert"""
        self.ensure_one()
        self.status = 'investigating'
        if not self.assigned_to:
            self.assigned_to = self.env.user
        self.message_post(body=f"Investigation started by {self.env.user.name}")
    
    def action_resolve(self):
        """Resolve alert"""
        self.ensure_one()
        return {
            'name': 'Resolve Alert',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.system.alert',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_status': 'resolved',
                'show_resolution_form': True
            }
        }
    
    def action_close(self):
        """Close alert"""
        self.ensure_one()
        if not self.is_resolved:
            raise UserError("Alert must be resolved before closing")
        
        self.write({
            'status': 'closed',
            'closed_at': fields.Datetime.now(),
            'is_active': False
        })
        
        self.message_post(body=f"Alert closed by {self.env.user.name}")
    
    def action_reopen(self):
        """Reopen alert"""
        self.ensure_one()
        self.write({
            'status': 'new',
            'is_resolved': False,
            'is_active': True,
            'resolved_at': False,
            'resolved_by': False,
            'closed_at': False
        })
        
        self.message_post(body=f"Alert reopened by {self.env.user.name}")
    
    def action_mark_false_positive(self):
        """Mark alert as false positive"""
        self.ensure_one()
        self.write({
            'status': 'false_positive',
            'is_active': False,
            'is_resolved': True,
            'resolved_at': fields.Datetime.now(),
            'resolved_by': self.env.user.id
        })
        
        self.message_post(body=f"Alert marked as false positive by {self.env.user.name}")
    
    def action_assign_to_me(self):
        """Assign alert to current user"""
        self.ensure_one()
        self.assigned_to = self.env.user
        self.message_post(body=f"Alert assigned to {self.env.user.name}")
    
    # 11. Other business methods
    def _send_alert_notification(self):
        """Send alert notification"""
        self.ensure_one()
        
        # Create activity for assigned user or admin
        user_to_notify = self.assigned_to or self.env.ref('base.user_admin')
        
        self.activity_schedule(
            'mail.mail_activity_data_todo',
            summary=f"Critical Alert: {self.name}",
            note=f"Alert ID: {self.alert_id}\nSeverity: {self.severity}\nLocation: {self.location_display}\n\nDescription: {self.description}",
            user_id=user_to_notify.id
        )
        
        # Send email notification for emergency alerts
        if self.severity == 'emergency':
            self._send_emergency_email()
    
    def _send_emergency_email(self):
        """Send emergency email notification"""
        self.ensure_one()
        
        # Get admin users
        admin_users = self.env.ref('base.group_system').users
        
        for user in admin_users:
            if user.email:
                self.message_post(
                    body=f"Emergency Alert: {self.name}",
                    subject=f"EMERGENCY ALERT - {self.alert_id}",
                    partner_ids=[user.partner_id.id],
                    email_from=self.env.company.email or '<EMAIL>'
                )
    
    def get_alert_summary(self):
        """Get alert summary data"""
        self.ensure_one()
        return {
            'id': self.id,
            'alert_id': self.alert_id,
            'name': self.name,
            'type': self.alert_type,
            'severity': self.severity,
            'status': self.status,
            'priority': self.priority,
            'created_at': self.created_at,
            'age_hours': self.age_hours,
            'location': self.location_display,
            'assigned_to': self.assigned_to.name if self.assigned_to else None,
            'is_active': self.is_active,
            'is_acknowledged': self.is_acknowledged,
            'is_resolved': self.is_resolved
        }
    
    @api.model
    def get_active_alerts_count(self):
        """Get count of active alerts by severity"""
        domain = [('is_active', '=', True)]
        
        counts = {}
        for severity in ['info', 'warning', 'critical', 'emergency']:
            count = self.search_count(domain + [('severity', '=', severity)])
            counts[severity] = count
        
        counts['total'] = sum(counts.values())
        return counts
    
    @api.model
    def get_alerts_by_location(self, building_id=None):
        """Get alerts grouped by location"""
        domain = [('is_active', '=', True)]
        if building_id:
            domain.append(('building_id', '=', building_id))
        
        alerts = self.search(domain)
        
        location_groups = {}
        for alert in alerts:
            location = alert.location_display
            if location not in location_groups:
                location_groups[location] = []
            location_groups[location].append(alert.get_alert_summary())
        
        return location_groups
    
    @api.model
    def cleanup_old_alerts(self, days=30):
        """Clean up old resolved alerts"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        old_alerts = self.search([
            ('is_resolved', '=', True),
            ('status', 'in', ['resolved', 'closed', 'false_positive']),
            ('resolved_at', '<', cutoff_date)
        ])
        
        count = len(old_alerts)
        old_alerts.unlink()
        
        _logger.info(f"Cleaned up {count} old alerts older than {days} days")
        return count