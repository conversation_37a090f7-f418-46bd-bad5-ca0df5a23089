# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class NebularZone(models.Model):
    """Zone model for the Nebular Dashboard System"""
    
    # Private attributes
    _name = 'nebular.zone'
    _description = 'Nebular Zone'
    _inherit = ['mail.thread', 'mail.activity.schedule']
    _order = 'building_id, name'
    _rec_name = 'name'
    
    # Field declarations
    name = fields.Char(
        string='Zone Name',
        required=True,
        tracking=True,
        help='Name of the zone'
    )
    code = fields.Char(
        string='Zone Code',
        required=True,
        tracking=True,
        help='Unique code for the zone within the building'
    )
    description = fields.Text(
        string='Description',
        help='Detailed description of the zone'
    )
    
    # Status and operational fields
    active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True,
        help='Whether this zone is active'
    )
    
    # Relationships
    building_id = fields.Many2one(
        comodel_name='nebular.building',
        string='Building',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Building this zone belongs to'
    )
    floor_ids = fields.One2many(
        comodel_name='nebular.floor',
        inverse_name='zone_id',
        string='Floors',
        help='Floors within this zone'
    )
    room_ids = fields.One2many(
        comodel_name='nebular.room',
        inverse_name='zone_id',
        string='Rooms',
        help='Rooms within this zone'
    )
    door_ids = fields.One2many(
        comodel_name='nebular.door',
        inverse_name='zone_id',
        string='Doors',
        help='Doors within this zone'
    )
    device_ids = fields.One2many(
        comodel_name='nebular.device',
        inverse_name='zone_id',
        string='Devices',
        help='Devices within this zone'
    )
    event_ids = fields.One2many(
        comodel_name='nebular.event',
        inverse_name='zone_id',
        string='Events',
        help='Events related to this zone'
    )
    marker_ids = fields.One2many(
        comodel_name='nebular.marker',
        inverse_name='zone_id',
        string='Markers',
        help='Floor plan markers within this zone'
    )
    
    # Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Full display name including building'
    )
    floor_count = fields.Integer(
        string='Floor Count',
        compute='_compute_counts',
        store=True,
        help='Number of floors in this zone'
    )
    room_count = fields.Integer(
        string='Room Count',
        compute='_compute_counts',
        store=True,
        help='Number of rooms in this zone'
    )
    door_count = fields.Integer(
        string='Door Count',
        compute='_compute_counts',
        store=True,
        help='Number of doors in this zone'
    )
    device_count = fields.Integer(
        string='Device Count',
        compute='_compute_counts',
        store=True,
        help='Number of devices in this zone'
    )
    event_count = fields.Integer(
        string='Event Count',
        compute='_compute_event_count',
        help='Number of recent events in this zone'
    )
    alert_count = fields.Integer(
        string='Alert Count',
        compute='_compute_alert_count',
        help='Number of active alerts in this zone'
    )
    has_alerts = fields.Boolean(
        string='Has Alerts',
        compute='_compute_alert_count',
        help='Whether this zone has active alerts'
    )
    
    # Compute methods
    @api.depends('name', 'building_id.name')
    def _compute_display_name(self):
        """Compute display name with building"""
        for record in self:
            if record.building_id:
                record.display_name = f"{record.building_id.name} - {record.name}"
            else:
                record.display_name = record.name or ''
    
    @api.depends('floor_ids', 'room_ids', 'door_ids', 'device_ids')
    def _compute_counts(self):
        """Compute counts for related records"""
        for record in self:
            record.floor_count = len(record.floor_ids)
            record.room_count = len(record.room_ids)
            record.door_count = len(record.door_ids)
            record.device_count = len(record.device_ids)
    
    @api.depends('event_ids')
    def _compute_event_count(self):
        """Compute recent event count (last 30 days)"""
        for record in self:
            domain = [
                ('zone_id', '=', record.id),
                ('create_date', '>=', fields.Datetime.now() - fields.timedelta(days=30))
            ]
            record.event_count = self.env['nebular.event'].search_count(domain)
    
    @api.depends('marker_ids.is_alert', 'device_ids.is_alert')
    def _compute_alert_count(self):
        """Compute alert count from markers and devices"""
        for record in self:
            marker_alerts = len(record.marker_ids.filtered('is_alert'))
            device_alerts = len(record.device_ids.filtered('is_alert'))
            record.alert_count = marker_alerts + device_alerts
            record.has_alerts = record.alert_count > 0
    
    # Constraints
    @api.constrains('code', 'building_id')
    def _check_code_unique_per_building(self):
        """Ensure zone code is unique within building"""
        for record in self:
            if record.code and record.building_id:
                existing = self.search([
                    ('code', '=', record.code),
                    ('building_id', '=', record.building_id.id),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        f"Zone code '{record.code}' already exists in building '{record.building_id.name}'."
                    )
    
    @api.constrains('name')
    def _check_name_not_empty(self):
        """Ensure zone name is not empty"""
        for record in self:
            if not record.name or not record.name.strip():
                raise ValidationError("Zone name cannot be empty.")
    
    # CRUD overrides
    def create(self, vals_list):
        """Override create to add tracking message"""
        zones = super().create(vals_list)
        for zone in zones:
            zone.message_post(
                body=f"Zone '{zone.name}' has been created in building '{zone.building_id.name}'.",
                message_type='notification'
            )
        return zones
    
    def write(self, vals):
        """Override write to add tracking for important changes"""
        result = super().write(vals)
        if 'active' in vals:
            for record in self:
                status = 'activated' if vals['active'] else 'deactivated'
                record.message_post(
                    body=f"Zone '{record.name}' has been {status}.",
                    message_type='notification'
                )
        return result
    
    # Action methods
    def action_view_floors(self):
        """Action to view floors of this zone"""
        self.ensure_one()
        return {
            'name': f'Floors - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.floor',
            'view_mode': 'list,form',
            'domain': [('zone_id', '=', self.id)],
            'context': {
                'default_zone_id': self.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_view_rooms(self):
        """Action to view rooms of this zone"""
        self.ensure_one()
        return {
            'name': f'Rooms - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.room',
            'view_mode': 'list,form',
            'domain': [('zone_id', '=', self.id)],
            'context': {
                'default_zone_id': self.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_view_devices(self):
        """Action to view devices of this zone"""
        self.ensure_one()
        return {
            'name': f'Devices - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.device',
            'view_mode': 'list,form',
            'domain': [('zone_id', '=', self.id)],
            'context': {
                'default_zone_id': self.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_view_events(self):
        """Action to view events of this zone"""
        self.ensure_one()
        return {
            'name': f'Events - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.event',
            'view_mode': 'list,form',
            'domain': [('zone_id', '=', self.id)],
            'context': {
                'default_zone_id': self.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    # Other business methods
    def get_zone_summary(self):
        """Get a summary of zone statistics"""
        self.ensure_one()
        return {
            'name': self.name,
            'code': self.code,
            'building': self.building_id.name,
            'floors': self.floor_count,
            'rooms': self.room_count,
            'doors': self.door_count,
            'devices': self.device_count,
            'events': self.event_count,
            'alerts': self.alert_count,
            'has_alerts': self.has_alerts,
        }