# Task 03: Marker System Models

## Overview
Create the marker system models for interactive floor plan visualization. These models represent visual markers on floor plans that show device locations, status, and alerts.

## Models to Create

### 1. Marker Model (`nebular.marker`)
**File**: `models/nebular_marker.py`

**Key Fields**:
- `name` (Char, required) - Marker Name
- `title` (Char, required) - Title
- `subtitle` (Char) - Subtitle
- `description` (Text) - Description
- `public_address` (Text) - Public Address

**Marker Type**:
- `marker_type` (Selection, required) - Marker Type:
  - fire: Fire System
  - door: Door
  - gate: Gate
  - camera: Camera
  - people: People Counter
  - sensor: Sensor
  - emergency: Emergency
  - person: Person

**Position Fields**:
- `position_x` (Float, required, digits=(10,2)) - Position X
- `position_y` (Float, required, digits=(10,2)) - Position Y
- `position_x_percent` (Float, digits=(5,2)) - Position X (%)
- `position_y_percent` (Float, digits=(5,2)) - Position Y (%)

**Status & Alert**:
- `status` (Char, required) - Status
- `is_alert` (Boolean, default=False) - Is Alert
- `alert_timestamp` (Datetime) - Alert Timestamp

**Optional Fields**:
- `count` (Integer) - Count
- `source` (Char) - Source

**Location References**:
- `building_id` (Many2one, required) → `nebular.building`
- `zone_id` (Many2one, required) → `nebular.zone`
- `floor_id` (Many2one, required) → `nebular.floor`
- `zone_display` (Char) - Zone Display

**Computed Fields**:
- `display_name` (Char, computed) - Display Name
- `marker_color` (Char, computed) - Marker Color
- `marker_icon` (Char, computed) - Marker Icon

**Methods**:
- `_compute_display_name()` - Compute display name with type and title
- `_compute_marker_style()` - Compute color and icon based on marker type

## Style Mapping
The marker style computation should use this mapping:

```python
style_map = {
    'fire': {'color': '#ff4444', 'icon': '🔥'},
    'door': {'color': '#4CAF50', 'icon': '🚪'},
    'gate': {'color': '#FF9800', 'icon': '🚧'},
    'camera': {'color': '#2196F3', 'icon': '📹'},
    'people': {'color': '#9C27B0', 'icon': '👤'},
    'person': {'color': '#9C27B0', 'icon': '👤'},
    'sensor': {'color': '#607D8B', 'icon': '📡'},
    'emergency': {'color': '#F44336', 'icon': '🚨'},
}
```

## Views to Create

### List View
**Fields to Display**:
- `name` - Marker Name
- `marker_type` - Marker Type
- `title` - Title
- `status` - Status
- `is_alert` - Is Alert
- `floor_id` - Floor
- `building_id` - Building
- `position_x` - Position X
- `position_y` - Position Y

**Filters**:
- Alert markers (`is_alert = True`)
- By marker type
- By building
- By floor
- Active alerts only

### Form View
**Layout**:

**Main Tab - Basic Information**:
- `name` - Marker Name
- `title` - Title
- `subtitle` - Subtitle
- `marker_type` - Marker Type
- `description` - Description
- `public_address` - Public Address

**Position Tab**:
- `position_x` - Position X
- `position_y` - Position Y
- `position_x_percent` - Position X (%)
- `position_y_percent` - Position Y (%)

**Status Tab**:
- `status` - Status
- `is_alert` - Is Alert
- `alert_timestamp` - Alert Timestamp
- `count` - Count
- `source` - Source

**Location Tab**:
- `building_id` - Building
- `zone_id` - Zone
- `floor_id` - Floor
- `zone_display` - Zone Display

**Style Tab** (readonly):
- `marker_color` - Marker Color
- `marker_icon` - Marker Icon
- `display_name` - Display Name

### Search View
**Search Fields**:
- `name` - Marker Name
- `title` - Title
- `marker_type` - Marker Type
- `status` - Status
- `building_id` - Building
- `zone_id` - Zone
- `floor_id` - Floor

**Filters**:
- Alert Markers
- By Marker Type (group by)
- By Building (group by)
- By Floor (group by)
- By Status (group by)

**Group By Options**:
- Marker Type
- Building
- Floor
- Status
- Alert Status

## Integration with Device Model

### Relationship Considerations
- Markers can be linked to Device records
- Device model already contains similar position and type information
- Consider if markers should reference devices or be separate entities
- Markers might be more UI-focused while devices are more data-focused

### Potential Enhancements
- Add `device_id` (Many2one) → `nebular.device` for linking
- Sync position data between markers and devices
- Use markers for floor plan visualization, devices for data management

## Floor Plan Integration

### Position Handling
- Support both absolute (x, y) and percentage-based positioning
- Validate position coordinates are within reasonable ranges
- Consider floor plan dimensions for position validation

### Real-time Updates
- Markers should reflect real-time status changes
- Alert status should trigger visual updates
- Consider WebSocket integration for live updates

## Reference
Model definitions from: `/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_nebular/_docs/02-odoo-model-mapping.md` (Lines 800-950)

## Implementation Notes
- Use proper field ordering in model definition
- Add indexes on frequently queried fields (marker_type, is_alert, floor_id)
- Implement position validation constraints
- Consider caching computed style fields for performance
- Add proper ondelete cascade for location references
- Use `_rec_name = 'title'` for better display in relations