# Task 02: Event System Models

## Overview
Create the event system models for handling various system events (Fire, Access Control, CCTV, Gate, PA, Presence). These models follow a main event pattern with specialized event types.

## Models to Create

### 1. Main Event Model (`nebular.event`)
**File**: `models/nebular_event.py`

**Key Fields**:
- `building_id` (Many2one, required) → `nebular.building`
- `building_code` (Char, required) - Building Code
- `zone_id` (Many2one, required) → `nebular.zone`
- `zone_code` (Char, required) - Zone Code  
- `floor_id` (Many2one, required) → `nebular.floor`
- `floor_code` (Char, required) - Floor Code
- `system_id` (Char, required) - System ID (fire, access, cctv, gate, pa, presence)
- `system_name` (Char, required) - System Name
- `event_type` (Char, required) - Event Type
- `datetime` (Datetime, required) - Event DateTime
- `message` (Text) - Message
- `state` (Char) - State
- `command` (Char) - Command
- `severity` (Integer, default=0) - Severity
- `res_model` (Char) - Linked Model
- `res_id` (Integer) - Linked Record ID
- `data_json` (Json, computed) - Data JSON for API

**Methods**:
- `_compute_data_json()` - Compute JSON data based on system_id
- `create_from_json()` - Create event from JSON API data

### 2. Fire Event Model (`nebular.fire.event`)
**File**: `models/nebular_fire_event.py`

**Key Fields**:
- `event_id` (Many2one, required) → `nebular.event`
- `panel_code` (Char) - Panel Code
- `panel_name` (Char) - Panel Name
- `zone` (Char) - Zone
- `loop` (Char) - Loop
- `node_id` (Integer) - Node ID
- `node_code` (Char) - Node Code
- `address` (Char) - Address

### 3. Access Event Model (`nebular.access.event`)
**File**: `models/nebular_access_event.py`

**Key Fields**:
- `event_id` (Many2one, required) → `nebular.event`
- `controller_id` (Integer) - Controller ID
- `controller_code` (Char) - Controller Code
- `controller_name` (Char) - Controller Name
- `reader_id` (Integer) - Reader ID
- `reader_code` (Char) - Reader Code
- `card_id` (Integer) - Card ID
- `card_code` (Char) - Card Code
- `user_id` (Integer) - User ID
- `user_code` (Char) - User Code
- `door_id` (Char) - Door ID
- `door_name` (Char) - Door Name
- `result` (Char) - Result
- `reason` (Char) - Reason
- `held_open_seconds` (Integer) - Held Open Seconds

### 4. CCTV Event Model (`nebular.cctv.event`)
**File**: `models/nebular_cctv_event.py`

**Key Fields**:
- `event_id` (Many2one, required) → `nebular.event`
- `camera_id` (Integer) - Camera ID
- `camera_code` (Char) - Camera Code
- `camera_name` (Char) - Camera Name
- `location` (Char) - Location
- `vendor` (Char) - Vendor
- `model` (Char) - Model
- `ip` (Char) - IP Address
- `channel` (Integer) - Channel
- `analytic_type` (Char) - Analytic Type
- `count` (Integer) - Count
- `face_id` (Char) - Face ID
- `uri` (Char) - URI
- `snapshot_url` (Char) - Snapshot URL
- `recording` (Boolean) - Recording

### 5. Gate Event Model (`nebular.gate.event`)
**File**: `models/nebular_gate_event.py`

**Key Fields**:
- `event_id` (Many2one, required) → `nebular.event`
- `gate_id` (Integer) - Gate ID
- `gate_code` (Char) - Gate Code
- `name` (Char) - Name
- `location` (Char) - Location
- `status` (Char) - Status
- `vehicle_plate` (Char) - Vehicle Plate
- `trigger` (Char) - Trigger
- `anpr_conf` (Float) - ANPR Confidence

### 6. PA Event Model (`nebular.pa.event`)
**File**: `models/nebular_pa_event.py`

**Key Fields**:
- `event_id` (Many2one, required) → `nebular.event`
- `zone_id` (Integer) - Zone ID
- `zone_code` (Char) - Zone Code
- `zone_name` (Char) - Zone Name
- `location` (Char) - Location
- `volume` (Integer) - Volume
- `message` (Text) - Message
- `announcement_id` (Char) - Announcement ID
- `script` (Char) - Script
- `duration_sec` (Integer) - Duration (seconds)

### 7. Presence Event Model (`nebular.presence.event`)
**File**: `models/nebular_presence_event.py`

**Key Fields**:
- `event_id` (Many2one, required) → `nebular.event`
- `sensor_id` (Integer) - Sensor ID
- `sensor_code` (Char) - Sensor Code
- `name` (Char) - Name
- `location` (Char) - Location
- `vendor` (Char) - Vendor
- `model` (Char) - Model
- `type` (Char) - Type (pir/microwave/ultrasonic/camera)
- `count` (Integer) - Count
- `occupancy` (Boolean) - Occupancy

## Views to Create

### List Views
- Main Event list: datetime, system_name, event_type, message, severity, building_id
- Fire Event list: panel_name, zone, node_code, address, event_id.datetime
- Access Event list: door_name, user_code, result, reason, event_id.datetime
- CCTV Event list: camera_name, analytic_type, count, recording, event_id.datetime
- Gate Event list: name, vehicle_plate, status, trigger, event_id.datetime
- PA Event list: zone_name, volume, duration_sec, event_id.datetime
- Presence Event list: name, type, count, occupancy, event_id.datetime

### Form Views
- Main Event form: basic info, system details, linked record info
- Fire Event form: panel details, zone/loop info, node details
- Access Event form: controller/reader info, card/user details, door info
- CCTV Event form: camera details, analytics info, recording details
- Gate Event form: gate info, vehicle details, trigger info
- PA Event form: zone info, announcement details, script info
- Presence Event form: sensor details, occupancy info

### Search Views
- Main Event search: system_id, event_type, datetime, severity, building_id
- Fire Event search: panel_code, zone, node_code
- Access Event search: door_name, user_code, result
- CCTV Event search: camera_name, analytic_type, recording
- Gate Event search: gate_code, vehicle_plate, status
- PA Event search: zone_name, announcement_id
- Presence Event search: sensor_code, type, occupancy

## Special Features

### JSON API Integration
- `data_json` computed field aggregates system-specific data
- `create_from_json()` method handles API event creation
- Automatic creation of specialized event records

### Event Relationships
- All specialized events link to main event via `event_id`
- Main event provides common fields (datetime, location, severity)
- Specialized events provide system-specific details

## Reference
Model definitions from: `/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_nebular/_docs/02-odoo-model-mapping.md` (Lines 400-800)

## Implementation Notes
- Use `_order = 'datetime desc'` for chronological ordering
- Implement proper cascade deletion (ondelete='cascade')
- Add indexes on frequently searched fields (system_id, event_type, datetime)
- Consider partitioning for large event volumes
- Implement proper JSON handling for API integration