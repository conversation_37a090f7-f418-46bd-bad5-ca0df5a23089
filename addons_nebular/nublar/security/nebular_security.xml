<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Building Access Rules -->
        <record id="nebular_building_rule_admin" model="ir.rule">
            <field name="name">Nebular Building: Admin Access</field>
            <field name="model_id" ref="model_nebular_building"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_building_rule_manager" model="ir.rule">
            <field name="name">Nebular Building: Manager Access</field>
            <field name="model_id" ref="model_nebular_building"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_building_rule_user" model="ir.rule">
            <field name="name">Nebular Building: User Access</field>
            <field name="model_id" ref="model_nebular_building"/>
            <field name="domain_force">[('is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user')), (4, ref('nebular_group_operator')), (4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_building_rule_portal" model="ir.rule">
            <field name="name">Nebular Building: Portal Access</field>
            <field name="model_id" ref="model_nebular_building"/>
            <field name="domain_force">[('is_active', '=', True), ('is_public', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Zone Access Rules -->
        <record id="nebular_zone_rule_admin" model="ir.rule">
            <field name="name">Nebular Zone: Admin Access</field>
            <field name="model_id" ref="model_nebular_zone"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_zone_rule_user" model="ir.rule">
            <field name="name">Nebular Zone: User Access</field>
            <field name="model_id" ref="model_nebular_zone"/>
            <field name="domain_force">[('is_active', '=', True), ('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user')), (4, ref('nebular_group_operator')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Room Access Rules -->
        <record id="nebular_room_rule_admin" model="ir.rule">
            <field name="name">Nebular Room: Admin Access</field>
            <field name="model_id" ref="model_nebular_room"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_room_rule_user" model="ir.rule">
            <field name="name">Nebular Room: User Access</field>
            <field name="model_id" ref="model_nebular_room"/>
            <field name="domain_force">[('active', '=', True), ('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user')), (4, ref('nebular_group_operator')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Door Access Rules -->
        <record id="nebular_door_rule_admin" model="ir.rule">
            <field name="name">Nebular Door: Admin Access</field>
            <field name="model_id" ref="model_nebular_door"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_door_rule_security" model="ir.rule">
            <field name="name">Nebular Door: Security Access</field>
            <field name="model_id" ref="model_nebular_door"/>
            <field name="domain_force">[('active', '=', True), ('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_security')), (4, ref('nebular_group_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_door_rule_user" model="ir.rule">
            <field name="name">Nebular Door: User Access</field>
            <field name="model_id" ref="model_nebular_door"/>
            <field name="domain_force">[('active', '=', True), ('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user')), (4, ref('nebular_group_operator')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Device Access Rules -->
        <record id="nebular_device_rule_admin" model="ir.rule">
            <field name="name">Nebular Device: Admin Access</field>
            <field name="model_id" ref="model_nebular_device"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_device_rule_maintenance" model="ir.rule">
            <field name="name">Nebular Device: Maintenance Access</field>
            <field name="model_id" ref="model_nebular_device"/>
            <field name="domain_force">[('is_active', '=', True), ('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_maintenance')), (4, ref('nebular_group_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_device_rule_user" model="ir.rule">
            <field name="name">Nebular Device: User Access</field>
            <field name="model_id" ref="model_nebular_device"/>
            <field name="domain_force">[('is_active', '=', True), ('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user')), (4, ref('nebular_group_operator')), (4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Event Access Rules -->
        <record id="nebular_event_rule_admin" model="ir.rule">
            <field name="name">Nebular Event: Admin Access</field>
            <field name="model_id" ref="model_nebular_event"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_event_rule_operator" model="ir.rule">
            <field name="name">Nebular Event: Operator Access</field>
            <field name="model_id" ref="model_nebular_event"/>
            <field name="domain_force">[('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_operator')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_event_rule_user" model="ir.rule">
            <field name="name">Nebular Event: User Access</field>
            <field name="model_id" ref="model_nebular_event"/>
            <field name="domain_force">[('building_id.is_active', '=', True), ('severity', 'in', ['info', 'warning'])]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Fire Event Access Rules -->
        <record id="nebular_fire_event_rule_fire_safety" model="ir.rule">
            <field name="name">Nebular Fire Event: Fire Safety Access</field>
            <field name="model_id" ref="model_nebular_fire_event"/>
            <field name="domain_force">[('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_fire_event_rule_operator" model="ir.rule">
            <field name="name">Nebular Fire Event: Operator Access</field>
            <field name="model_id" ref="model_nebular_fire_event"/>
            <field name="domain_force">[('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_operator'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Access Event Access Rules -->
        <record id="nebular_access_event_rule_security" model="ir.rule">
            <field name="name">Nebular Access Event: Security Access</field>
            <field name="model_id" ref="model_nebular_access_event"/>
            <field name="domain_force">[('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_security')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_access_event_rule_operator" model="ir.rule">
            <field name="name">Nebular Access Event: Operator Access</field>
            <field name="model_id" ref="model_nebular_access_event"/>
            <field name="domain_force">[('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_operator'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- CCTV Event Access Rules -->
        <record id="nebular_cctv_event_rule_security" model="ir.rule">
            <field name="name">Nebular CCTV Event: Security Access</field>
            <field name="model_id" ref="model_nebular_cctv_event"/>
            <field name="domain_force">[('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_security')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- System Metric Access Rules -->
        <record id="nebular_system_metric_rule_admin" model="ir.rule">
            <field name="name">Nebular System Metric: Admin Access</field>
            <field name="model_id" ref="model_nebular_system_metric"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_system_metric_rule_maintenance" model="ir.rule">
            <field name="name">Nebular System Metric: Maintenance Access</field>
            <field name="model_id" ref="model_nebular_system_metric"/>
            <field name="domain_force">[('is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_maintenance')), (4, ref('nebular_group_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_system_metric_rule_operator" model="ir.rule">
            <field name="name">Nebular System Metric: Operator Access</field>
            <field name="model_id" ref="model_nebular_system_metric"/>
            <field name="domain_force">[('is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_operator'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </field>
        
        <!-- System Alert Access Rules -->
        <record id="nebular_system_alert_rule_admin" model="ir.rule">
            <field name="name">Nebular System Alert: Admin Access</field>
            <field name="model_id" ref="model_nebular_system_alert"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_system_alert_rule_assigned" model="ir.rule">
            <field name="name">Nebular System Alert: Assigned User Access</field>
            <field name="model_id" ref="model_nebular_system_alert"/>
            <field name="domain_force">['|', ('assigned_to', '=', user.id), ('assigned_to', '=', False)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_operator')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="nebular_system_alert_rule_user" model="ir.rule">
            <field name="name">Nebular System Alert: User Access</field>
            <field name="model_id" ref="model_nebular_system_alert"/>
            <field name="domain_force">[('severity', 'in', ['info', 'warning']), ('is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Floor Plan Access Rules -->
        <record id="nebular_floor_plan_rule_admin" model="ir.rule">
            <field name="name">Nebular Floor Plan: Admin Access</field>
            <field name="model_id" ref="model_nebular_floor_plan"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_floor_plan_rule_published" model="ir.rule">
            <field name="name">Nebular Floor Plan: Published Access</field>
            <field name="model_id" ref="model_nebular_floor_plan"/>
            <field name="domain_force">[('is_published', '=', True), ('is_active', '=', True), ('building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user')), (4, ref('nebular_group_operator')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance')), (4, ref('nebular_group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Marker Access Rules -->
        <record id="nebular_marker_rule_admin" model="ir.rule">
            <field name="name">Nebular Marker: Admin Access</field>
            <field name="model_id" ref="model_nebular_marker"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="nebular_marker_rule_active" model="ir.rule">
            <field name="name">Nebular Marker: Active Access</field>
            <field name="model_id" ref="model_nebular_marker"/>
            <field name="domain_force">[('is_active', '=', True), ('floor_plan_id.is_published', '=', True), ('floor_plan_id.building_id.is_active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('nebular_group_user')), (4, ref('nebular_group_operator')), (4, ref('nebular_group_manager')), (4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance')), (4, ref('nebular_group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
    </data>
</odoo>