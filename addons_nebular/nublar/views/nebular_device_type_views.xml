<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Device Type Form View -->
    <record id="nebular_device_type_view_form" model="ir.ui.view">
        <field name="name">nebular.device.type.form</field>
        <field name="model">nebular.device.type</field>
        <field name="arch" type="xml">
            <form string="Device Type">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            class="btn-primary" invisible="active"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            class="btn-secondary" invisible="not active"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-microchip">
                            <field name="device_count" widget="statinfo" string="Devices"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Device Type Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="category"/>
                            <field name="manufacturer"/>
                            <field name="model"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group name="capabilities">
                            <field name="has_battery"/>
                            <field name="has_wifi"/>
                            <field name="has_bluetooth"/>
                            <field name="has_ethernet"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="specifications">
                            <field name="power_consumption"/>
                            <field name="operating_voltage"/>
                            <field name="communication_protocol"/>
                            <field name="data_format"/>
                        </group>
                        <group name="environmental">
                            <field name="operating_temp_min"/>
                            <field name="operating_temp_max"/>
                            <field name="operating_humidity_min"/>
                            <field name="operating_humidity_max"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Device type description..."/>
                    </group>
                    
                    <notebook>
                        <page string="Configuration Schema" name="configuration">
                            <group>
                                <field name="config_schema" widget="ace" options="{'mode': 'json'}"/>
                            </group>
                        </page>
                        <page string="Devices" name="devices">
                            <field name="device_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="building_id"/>
                                    <field name="room_id"/>
                                    <field name="location"/>
                                    <field name="status"/>
                                    <field name="is_online"/>
                                    <field name="last_seen"/>
                                </list>
                            </field>
                        </page>
                        <page string="Technical Specifications" name="technical">
                            <group>
                                <group name="dimensions">
                                    <field name="dimensions"/>
                                    <field name="weight"/>
                                    <field name="mounting_type"/>
                                </group>
                                <group name="certifications">
                                    <field name="certifications"/>
                                    <field name="ip_rating"/>
                                    <field name="safety_standards"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Device Type List View -->
    <record id="nebular_device_type_view_list" model="ir.ui.view">
        <field name="name">nebular.device.type.list</field>
        <field name="model">nebular.device.type</field>
        <field name="arch" type="xml">
            <list string="Device Types" default_order="category, name">
                <field name="name"/>
                <field name="category"/>
                <field name="manufacturer"/>
                <field name="model"/>
                <field name="power_consumption"/>
                <field name="communication_protocol"/>
                <field name="has_battery" widget="boolean_toggle"/>
                <field name="has_wifi" widget="boolean_toggle"/>
                <field name="device_count"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Device Type Kanban View -->
    <record id="nebular_device_type_view_kanban" model="ir.ui.view">
        <field name="name">nebular.device.type.kanban</field>
        <field name="model">nebular.device.type</field>
        <field name="arch" type="xml">
            <kanban default_group_by="category" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="category"/>
                <field name="manufacturer"/>
                <field name="model"/>
                <field name="power_consumption"/>
                <field name="has_battery"/>
                <field name="has_wifi"/>
                <field name="has_bluetooth"/>
                <field name="has_ethernet"/>
                <field name="device_count"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="manufacturer"/> - <field name="model"/>
                                        </small>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div t-if="record.power_consumption.raw_value">
                                        <i class="fa fa-bolt"/> <field name="power_consumption"/>W
                                    </div>
                                    <div class="o_kanban_tags_section">
                                        <span t-if="record.has_battery.raw_value" 
                                              class="badge badge-info">Battery</span>
                                        <span t-if="record.has_wifi.raw_value" 
                                              class="badge badge-success">WiFi</span>
                                        <span t-if="record.has_bluetooth.raw_value" 
                                              class="badge badge-primary">Bluetooth</span>
                                        <span t-if="record.has_ethernet.raw_value" 
                                              class="badge badge-secondary">Ethernet</span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-microchip" title="Devices"/> 
                                            <field name="device_count"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Device Type Search View -->
    <record id="nebular_device_type_view_search" model="ir.ui.view">
        <field name="name">nebular.device.type.search</field>
        <field name="model">nebular.device.type</field>
        <field name="arch" type="xml">
            <search string="Device Types">
                <field name="name" string="Device Type" 
                       filter_domain="['|', '|', ('name', 'ilike', self), ('manufacturer', 'ilike', self), ('model', 'ilike', self)]"/>
                <field name="category"/>
                <field name="manufacturer"/>
                <field name="model"/>
                <field name="communication_protocol"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Sensors" name="sensors" domain="[('category', '=', 'sensor')]"/>
                <filter string="Actuators" name="actuators" domain="[('category', '=', 'actuator')]"/>
                <filter string="Controllers" name="controllers" domain="[('category', '=', 'controller')]"/>
                <filter string="Cameras" name="cameras" domain="[('category', '=', 'camera')]"/>
                <filter string="Access Control" name="access_control" domain="[('category', '=', 'access_control')]"/>
                <filter string="Environmental" name="environmental" domain="[('category', '=', 'environmental')]"/>
                <filter string="Safety" name="safety" domain="[('category', '=', 'safety')]"/>
                <filter string="Communication" name="communication" domain="[('category', '=', 'communication')]"/>
                
                <separator/>
                <filter string="Battery Powered" name="battery" domain="[('has_battery', '=', True)]"/>
                <filter string="WiFi Enabled" name="wifi" domain="[('has_wifi', '=', True)]"/>
                <filter string="Bluetooth Enabled" name="bluetooth" domain="[('has_bluetooth', '=', True)]"/>
                <filter string="Ethernet Enabled" name="ethernet" domain="[('has_ethernet', '=', True)]"/>
                
                <separator/>
                <filter string="Has Devices" name="has_devices" domain="[('device_count', '>', 0)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Category" name="group_category" 
                            context="{'group_by': 'category'}"/>
                    <filter string="Manufacturer" name="group_manufacturer" 
                            context="{'group_by': 'manufacturer'}"/>
                    <filter string="Communication Protocol" name="group_protocol" 
                            context="{'group_by': 'communication_protocol'}"/>
                    <filter string="Creation Date" name="group_create_date" 
                            context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Device Type Action -->
    <record id="nebular_device_type_action" model="ir.actions.act_window">
        <field name="name">Device Types</field>
        <field name="res_model">nebular.device.type</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first device type!
            </p>
            <p>
                Device types define the specifications and capabilities
                of different IoT devices in your system.
            </p>
        </field>
    </record>
</odoo>