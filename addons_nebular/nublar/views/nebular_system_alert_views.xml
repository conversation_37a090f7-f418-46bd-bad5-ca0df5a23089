<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- System Alert Form View -->
    <record id="nebular_system_alert_view_form" model="ir.ui.view">
        <field name="name">nebular.system.alert.form</field>
        <field name="model">nebular.system.alert</field>
        <field name="arch" type="xml">
            <form string="System Alert">
                <header>
                    <button name="action_acknowledge" type="object" string="Acknowledge" 
                            class="btn-primary" invisible="acknowledged"/>
                    <button name="action_resolve" type="object" string="Resolve" 
                            class="btn-success" invisible="resolved"/>
                    <button name="action_escalate" type="object" string="Escalate" 
                            class="btn-warning" invisible="resolved"/>
                    <button name="action_snooze" type="object" string="Snooze" 
                            class="btn-secondary" invisible="resolved"/>
                    <field name="status" widget="statusbar" statusbar_visible="new,acknowledged,resolved"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Critical" bg_color="bg-danger" 
                            invisible="severity != 'critical'"/>
                    <widget name="web_ribbon" title="High" bg_color="bg-warning" 
                            invisible="severity != 'high'"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <h3>
                            <field name="alert_type" readonly="1"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="timestamp" readonly="1"/>
                            <field name="severity" readonly="1"/>
                            <field name="source" readonly="1"/>
                            <field name="acknowledged"/>
                            <field name="resolved"/>
                        </group>
                        <group name="location">
                            <field name="building_id" readonly="1"/>
                            <field name="floor_id" readonly="1"/>
                            <field name="zone_id" readonly="1"/>
                            <field name="room_id" readonly="1"/>
                            <field name="device_id" readonly="1"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="handling">
                            <field name="acknowledged_by"/>
                            <field name="acknowledged_at"/>
                            <field name="resolved_by"/>
                            <field name="resolved_at"/>
                        </group>
                        <group name="escalation">
                            <field name="escalated"/>
                            <field name="escalated_at"/>
                            <field name="escalation_level"/>
                            <field name="assigned_to"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="snooze">
                            <field name="snoozed"/>
                            <field name="snoozed_until"/>
                            <field name="snooze_count"/>
                        </group>
                        <group name="relationships">
                            <field name="event_id"/>
                            <field name="metric_id"/>
                            <field name="parent_alert_id"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" readonly="1"/>
                    </group>
                    
                    <notebook>
                        <page string="Alert Data" name="alert_data">
                            <group>
                                <field name="alert_data" widget="ace" options="{'mode': 'json'}" readonly="1"/>
                            </group>
                        </page>
                        <page string="Metadata" name="metadata">
                            <group>
                                <field name="metadata" widget="ace" options="{'mode': 'json'}" readonly="1"/>
                            </group>
                        </page>
                        <page string="Child Alerts" name="child_alerts">
                            <field name="child_alert_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="alert_type"/>
                                    <field name="severity"/>
                                    <field name="timestamp"/>
                                    <field name="acknowledged"/>
                                    <field name="resolved"/>
                                    <field name="status"/>
                                </list>
                            </field>
                        </page>
                        <page string="Response Actions" name="actions">
                            <group>
                                <field name="response_actions" placeholder="Document response actions taken..."/>
                            </group>
                        </page>
                        <page string="Notification History" name="notifications">
                            <group>
                                <field name="notification_sent"/>
                                <field name="notification_channels"/>
                                <field name="notification_count"/>
                                <field name="last_notification_sent"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- System Alert List View -->
    <record id="nebular_system_alert_view_list" model="ir.ui.view">
        <field name="name">nebular.system.alert.list</field>
        <field name="model">nebular.system.alert</field>
        <field name="arch" type="xml">
            <list string="System Alerts" default_order="timestamp desc" decoration-danger="severity == 'critical'" 
                  decoration-warning="severity == 'high'" decoration-muted="resolved">
                <field name="timestamp"/>
                <field name="name"/>
                <field name="alert_type"/>
                <field name="severity" widget="badge" 
                       decoration-danger="severity == 'critical'"
                       decoration-warning="severity == 'high'"
                       decoration-info="severity == 'medium'"
                       decoration-success="severity == 'low'"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                <field name="acknowledged" widget="boolean_toggle"/>
                <field name="resolved" widget="boolean_toggle"/>
                <field name="status" widget="badge"/>
                <field name="assigned_to"/>
                <field name="escalation_level"/>
            </list>
        </field>
    </record>

    <!-- System Alert Kanban View -->
    <record id="nebular_system_alert_view_kanban" model="ir.ui.view">
        <field name="name">nebular.system.alert.kanban</field>
        <field name="model">nebular.system.alert</field>
        <field name="arch" type="xml">
            <kanban default_group_by="status" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="alert_type"/>
                <field name="severity"/>
                <field name="timestamp"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                <field name="acknowledged"/>
                <field name="resolved"/>
                <field name="status"/>
                <field name="assigned_to"/>
                <field name="escalation_level"/>
                <field name="snoozed"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="alert_type"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_record_title">
                                        <span t-att-class="'badge ' + (record.severity.raw_value == 'critical' ? 'badge-danger' : 
                                                          record.severity.raw_value == 'high' ? 'badge-warning' : 
                                                          record.severity.raw_value == 'medium' ? 'badge-info' : 'badge-success')">
                                            <field name="severity"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-clock-o"/> <field name="timestamp"/>
                                    </div>
                                    <div t-if="record.building_id.raw_value">
                                        <i class="fa fa-building"/> <field name="building_id"/>
                                    </div>
                                    <div t-if="record.room_id.raw_value">
                                        <i class="fa fa-home"/> <field name="room_id"/>
                                    </div>
                                    <div t-if="record.device_id.raw_value">
                                        <i class="fa fa-microchip"/> <field name="device_id"/>
                                    </div>
                                    <div t-if="record.assigned_to.raw_value">
                                        <i class="fa fa-user"/> <field name="assigned_to"/>
                                    </div>
                                    <div class="o_kanban_tags_section">
                                        <span t-if="record.acknowledged.raw_value" 
                                              class="badge badge-info">Acknowledged</span>
                                        <span t-if="record.resolved.raw_value" 
                                              class="badge badge-success">Resolved</span>
                                        <span t-if="record.snoozed.raw_value" 
                                              class="badge badge-secondary">Snoozed</span>
                                        <span t-if="record.escalation_level.raw_value > 0" 
                                              class="badge badge-warning">
                                            Level <field name="escalation_level"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="source"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- System Alert Search View -->
    <record id="nebular_system_alert_view_search" model="ir.ui.view">
        <field name="name">nebular.system.alert.search</field>
        <field name="model">nebular.system.alert</field>
        <field name="arch" type="xml">
            <search string="System Alerts">
                <field name="name" string="Alert" 
                       filter_domain="['|', ('name', 'ilike', self), ('description', 'ilike', self)]"/>
                <field name="alert_type"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                <field name="assigned_to"/>
                
                <separator/>
                <filter string="Unacknowledged" name="unacknowledged" domain="[('acknowledged', '=', False)]"/>
                <filter string="Acknowledged" name="acknowledged" domain="[('acknowledged', '=', True)]"/>
                <filter string="Unresolved" name="unresolved" domain="[('resolved', '=', False)]"/>
                <filter string="Resolved" name="resolved" domain="[('resolved', '=', True)]"/>
                
                <separator/>
                <filter string="Critical" name="critical" domain="[('severity', '=', 'critical')]"/>
                <filter string="High" name="high" domain="[('severity', '=', 'high')]"/>
                <filter string="Medium" name="medium" domain="[('severity', '=', 'medium')]"/>
                <filter string="Low" name="low" domain="[('severity', '=', 'low')]"/>
                
                <separator/>
                <filter string="System Alerts" name="system" domain="[('alert_type', '=', 'system')]"/>
                <filter string="Security Alerts" name="security" domain="[('alert_type', '=', 'security')]"/>
                <filter string="Performance Alerts" name="performance" domain="[('alert_type', '=', 'performance')]"/>
                <filter string="Threshold Alerts" name="threshold" domain="[('alert_type', '=', 'threshold')]"/>
                <filter string="Device Alerts" name="device" domain="[('alert_type', '=', 'device')]"/>
                <filter string="Network Alerts" name="network" domain="[('alert_type', '=', 'network')]"/>
                <filter string="Environmental Alerts" name="environmental" domain="[('alert_type', '=', 'environmental')]"/>
                <filter string="Maintenance Alerts" name="maintenance" domain="[('alert_type', '=', 'maintenance')]"/>
                <filter string="Configuration Alerts" name="configuration" domain="[('alert_type', '=', 'configuration')]"/>
                <filter string="Integration Alerts" name="integration" domain="[('alert_type', '=', 'integration')]"/>
                <filter string="Backup Alerts" name="backup" domain="[('alert_type', '=', 'backup')]"/>
                <filter string="Update Alerts" name="update" domain="[('alert_type', '=', 'update')]"/>
                <filter string="License Alerts" name="license" domain="[('alert_type', '=', 'license')]"/>
                <filter string="Compliance Alerts" name="compliance" domain="[('alert_type', '=', 'compliance')]"/>
                <filter string="Custom Alerts" name="custom" domain="[('alert_type', '=', 'custom')]"/>
                
                <separator/>
                <filter string="Escalated" name="escalated" domain="[('escalated', '=', True)]"/>
                <filter string="Snoozed" name="snoozed" domain="[('snoozed', '=', True)]"/>
                <filter string="Assigned to Me" name="assigned_to_me" domain="[('assigned_to', '=', uid)]"/>
                <filter string="Unassigned" name="unassigned" domain="[('assigned_to', '=', False)]"/>
                
                <separator/>
                <filter string="Today" name="today" domain="[('timestamp', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('timestamp', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter string="Last 7 Days" name="last_week" domain="[('timestamp', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="Last 30 Days" name="last_month" domain="[('timestamp', '>=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Alert Type" name="group_alert_type" 
                            context="{'group_by': 'alert_type'}"/>
                    <filter string="Severity" name="group_severity" 
                            context="{'group_by': 'severity'}"/>
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'status'}"/>
                    <filter string="Source" name="group_source" 
                            context="{'group_by': 'source'}"/>
                    <filter string="Building" name="group_building" 
                            context="{'group_by': 'building_id'}"/>
                    <filter string="Assigned To" name="group_assigned_to" 
                            context="{'group_by': 'assigned_to'}"/>
                    <filter string="Escalation Level" name="group_escalation_level" 
                            context="{'group_by': 'escalation_level'}"/>
                    <filter string="Date" name="group_date" 
                            context="{'group_by': 'timestamp:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- System Alert Action -->
    <record id="nebular_system_alert_action" model="ir.actions.act_window">
        <field name="name">System Alerts</field>
        <field name="res_model">nebular.system.alert</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_unresolved': 1,
            'search_default_group_status': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No system alerts found!
            </p>
            <p>
                System alerts are automatically generated when
                system conditions require attention or intervention.
            </p>
        </field>
    </record>
</odoo>