<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Room Form View -->
    <record id="nebular_room_view_form" model="ir.ui.view">
        <field name="name">nebular.room.form</field>
        <field name="model">nebular.room</field>
        <field name="arch" type="xml">
            <form string="Room">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            class="btn-primary" invisible="active"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            class="btn-secondary" invisible="not active"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,inactive"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-microchip">
                            <field name="device_count" widget="statinfo" string="Devices"/>
                        </button>
                        <button name="action_view_doors" type="object" class="oe_stat_button" icon="fa-door-open">
                            <field name="door_count" widget="statinfo" string="Doors"/>
                        </button>
                        <button name="action_view_events" type="object" class="oe_stat_button" icon="fa-bell">
                            <field name="event_count" widget="statinfo" string="Events"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Room Name"/>
                        </h1>
                        <h3>
                            <field name="display_name" readonly="1"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="building_id" required="1"/>
                            <field name="floor_id" domain="[('building_id', '=', building_id)]"/>
                            <field name="zone_id" domain="[('floor_id', '=', floor_id)]"/>
                            <field name="room_number"/>
                            <field name="room_type"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group name="physical_info">
                            <field name="area"/>
                            <field name="max_occupancy"/>
                            <field name="current_occupancy"/>
                            <field name="occupancy_percentage" widget="percentage"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="coordinates">
                            <field name="x_coordinate"/>
                            <field name="y_coordinate"/>
                            <field name="width"/>
                            <field name="height"/>
                        </group>
                        <group name="environmental">
                            <field name="temperature"/>
                            <field name="humidity"/>
                            <field name="air_quality"/>
                            <field name="light_level"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="security">
                            <field name="security_level"/>
                            <field name="access_control_required"/>
                            <field name="camera_coverage"/>
                        </group>
                        <group name="safety">
                            <field name="fire_safety_equipment"/>
                            <field name="emergency_exit"/>
                            <field name="accessibility_features"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Room description..."/>
                    </group>
                    
                    <notebook>
                        <page string="Devices" name="devices">
                            <field name="device_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="device_type_id"/>
                                    <field name="location"/>
                                    <field name="status"/>
                                    <field name="last_seen"/>
                                    <field name="battery_level" widget="percentage"/>
                                </list>
                            </field>
                        </page>
                        <page string="Doors" name="doors">
                            <field name="door_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="door_type"/>
                                    <field name="status"/>
                                    <field name="access_level"/>
                                    <field name="last_activity"/>
                                </list>
                            </field>
                        </page>
                        <page string="Recent Events" name="events">
                            <field name="event_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="severity"/>
                                    <field name="timestamp"/>
                                    <field name="status"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Room List View -->
    <record id="nebular_room_view_list" model="ir.ui.view">
        <field name="name">nebular.room.list</field>
        <field name="model">nebular.room</field>
        <field name="arch" type="xml">
            <list string="Rooms" default_order="building_id, floor_id, zone_id, room_number">
                <field name="name"/>
                <field name="room_number"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="zone_id"/>
                <field name="room_type"/>
                <field name="area"/>
                <field name="max_occupancy"/>
                <field name="current_occupancy"/>
                <field name="occupancy_percentage" widget="percentage"/>
                <field name="temperature" widget="float"/>
                <field name="security_level"/>
                <field name="device_count"/>
                <field name="door_count"/>
                <field name="state" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-muted="state == 'inactive'"
                       decoration-info="state == 'draft'"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Room Kanban View -->
    <record id="nebular_room_view_kanban" model="ir.ui.view">
        <field name="name">nebular.room.kanban</field>
        <field name="model">nebular.room</field>
        <field name="arch" type="xml">
            <kanban default_group_by="room_type" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="room_number"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="zone_id"/>
                <field name="room_type"/>
                <field name="area"/>
                <field name="max_occupancy"/>
                <field name="current_occupancy"/>
                <field name="occupancy_percentage"/>
                <field name="temperature"/>
                <field name="humidity"/>
                <field name="security_level"/>
                <field name="device_count"/>
                <field name="door_count"/>
                <field name="state"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="room_number"/> - <field name="building_id"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <span t-if="record.state.raw_value == 'active'" 
                                              class="badge badge-success">Active</span>
                                        <span t-if="record.state.raw_value == 'inactive'" 
                                              class="badge badge-secondary">Inactive</span>
                                        <span t-if="record.state.raw_value == 'draft'" 
                                              class="badge badge-info">Draft</span>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-map-marker"/> <field name="floor_id"/> - <field name="zone_id"/>
                                    </div>
                                    <div t-if="record.area.raw_value">
                                        <i class="fa fa-expand"/> <field name="area"/> m²
                                    </div>
                                    <div t-if="record.max_occupancy.raw_value">
                                        <i class="fa fa-users"/> 
                                        <field name="current_occupancy"/>/<field name="max_occupancy"/>
                                        (<field name="occupancy_percentage"/>%)
                                    </div>
                                    <div t-if="record.temperature.raw_value">
                                        <i class="fa fa-thermometer-half"/> <field name="temperature"/>°C
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-microchip" title="Devices"/> 
                                            <field name="device_count"/>
                                        </span>
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-door-open" title="Doors"/> 
                                            <field name="door_count"/>
                                        </span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-if="record.security_level.raw_value == 'critical'" 
                                              class="badge badge-danger">Critical</span>
                                        <span t-if="record.security_level.raw_value == 'high'" 
                                              class="badge badge-warning">High</span>
                                        <span t-if="record.security_level.raw_value == 'medium'" 
                                              class="badge badge-info">Medium</span>
                                        <span t-if="record.security_level.raw_value == 'low'" 
                                              class="badge badge-secondary">Low</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Room Search View -->
    <record id="nebular_room_view_search" model="ir.ui.view">
        <field name="name">nebular.room.search</field>
        <field name="model">nebular.room</field>
        <field name="arch" type="xml">
            <search string="Rooms">
                <field name="name" string="Room" 
                       filter_domain="['|', ('name', 'ilike', self), ('room_number', 'ilike', self)]"/>
                <field name="room_number"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="zone_id"/>
                <field name="room_type"/>
                <field name="security_level"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active State" name="active_state" domain="[('state', '=', 'active')]"/>
                <filter string="Inactive State" name="inactive_state" domain="[('state', '=', 'inactive')]"/>
                
                <separator/>
                <filter string="Office" name="office" domain="[('room_type', '=', 'office')]"/>
                <filter string="Meeting Room" name="meeting" domain="[('room_type', '=', 'meeting')]"/>
                <filter string="Conference Room" name="conference" domain="[('room_type', '=', 'conference')]"/>
                <filter string="Workspace" name="workspace" domain="[('room_type', '=', 'workspace')]"/>
                <filter string="Storage" name="storage" domain="[('room_type', '=', 'storage')]"/>
                <filter string="Technical" name="technical" domain="[('room_type', '=', 'technical')]"/>
                <filter string="Restroom" name="restroom" domain="[('room_type', '=', 'restroom')]"/>
                <filter string="Kitchen" name="kitchen" domain="[('room_type', '=', 'kitchen')]"/>
                <filter string="Reception" name="reception" domain="[('room_type', '=', 'reception')]"/>
                <filter string="Server Room" name="server" domain="[('room_type', '=', 'server')]"/>
                
                <separator/>
                <filter string="Low Security" name="low_security" domain="[('security_level', '=', 'low')]"/>
                <filter string="Medium Security" name="medium_security" domain="[('security_level', '=', 'medium')]"/>
                <filter string="High Security" name="high_security" domain="[('security_level', '=', 'high')]"/>
                <filter string="Critical Security" name="critical_security" domain="[('security_level', '=', 'critical')]"/>
                
                <separator/>
                <filter string="Access Control Required" name="access_control" 
                        domain="[('access_control_required', '=', True)]"/>
                <filter string="Camera Coverage" name="camera_coverage" 
                        domain="[('camera_coverage', '=', True)]"/>
                <filter string="Fire Safety Equipment" name="fire_safety" 
                        domain="[('fire_safety_equipment', '=', True)]"/>
                <filter string="Emergency Exit" name="emergency_exit" 
                        domain="[('emergency_exit', '=', True)]"/>
                <filter string="Accessibility Features" name="accessibility" 
                        domain="[('accessibility_features', '=', True)]"/>
                
                <separator/>
                <filter string="Over Capacity" name="over_capacity" 
                        domain="[('current_occupancy', '>', 'max_occupancy')]"/>
                <filter string="High Temperature" name="high_temp" 
                        domain="[('temperature', '>', 25)]"/>
                <filter string="Low Temperature" name="low_temp" 
                        domain="[('temperature', '<', 18)]"/>
                <filter string="High Humidity" name="high_humidity" 
                        domain="[('humidity', '>', 70)]"/>
                
                <separator/>
                <filter string="Has Devices" name="has_devices" domain="[('device_count', '>', 0)]"/>
                <filter string="Has Doors" name="has_doors" domain="[('door_count', '>', 0)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Building" name="group_building" 
                            context="{'group_by': 'building_id'}"/>
                    <filter string="Floor" name="group_floor" 
                            context="{'group_by': 'floor_id'}"/>
                    <filter string="Zone" name="group_zone" 
                            context="{'group_by': 'zone_id'}"/>
                    <filter string="Room Type" name="group_room_type" 
                            context="{'group_by': 'room_type'}"/>
                    <filter string="Security Level" name="group_security" 
                            context="{'group_by': 'security_level'}"/>
                    <filter string="State" name="group_state" 
                            context="{'group_by': 'state'}"/>
                    <filter string="Creation Date" name="group_create_date" 
                            context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Room Action -->
    <record id="nebular_room_action" model="ir.actions.act_window">
        <field name="name">Rooms</field>
        <field name="res_model">nebular.room</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1,
            'search_default_active_state': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first room!
            </p>
            <p>
                Rooms are the basic units of space within your building structure.
                They can contain devices, doors, and generate events.
            </p>
        </field>
    </record>
</odoo>