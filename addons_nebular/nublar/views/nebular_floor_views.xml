<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Floor Form View -->
    <record id="nebular_floor_view_form" model="ir.ui.view">
        <field name="name">nebular.floor.form</field>
        <field name="model">nebular.floor</field>
        <field name="arch" type="xml">
            <form string="Floor">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            class="btn-primary" invisible="active"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            class="btn-secondary" invisible="not active"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,inactive"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_zones" type="object" class="oe_stat_button" icon="fa-map-marker">
                            <field name="zone_count" widget="statinfo" string="Zones"/>
                        </button>
                        <button name="action_view_rooms" type="object" class="oe_stat_button" icon="fa-home">
                            <field name="room_count" widget="statinfo" string="Rooms"/>
                        </button>
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-microchip">
                            <field name="device_count" widget="statinfo" string="Devices"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Floor Name"/>
                        </h1>
                        <h3>
                            <field name="display_name" readonly="1"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="building_id" required="1"/>
                            <field name="floor_number"/>
                            <field name="floor_type"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group name="physical_info">
                            <field name="area"/>
                            <field name="height"/>
                            <field name="max_occupancy"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Floor description..."/>
                    </group>
                    
                    <notebook>
                        <page string="Zones" name="zones">
                            <field name="zone_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="zone_type"/>
                                    <field name="area"/>
                                    <field name="max_occupancy"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Rooms" name="rooms">
                            <field name="room_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="room_number"/>
                                    <field name="room_type"/>
                                    <field name="zone_id"/>
                                    <field name="area"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Devices" name="devices">
                            <field name="device_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="device_type_id"/>
                                    <field name="location"/>
                                    <field name="status"/>
                                    <field name="last_seen"/>
                                </list>
                            </field>
                        </page>
                        <page string="Floor Plans" name="floor_plans">
                            <field name="floor_plan_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="version"/>
                                    <field name="is_published"/>
                                    <field name="marker_count"/>
                                    <field name="create_date"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Floor List View -->
    <record id="nebular_floor_view_list" model="ir.ui.view">
        <field name="name">nebular.floor.list</field>
        <field name="model">nebular.floor</field>
        <field name="arch" type="xml">
            <list string="Floors" default_order="building_id, floor_number">
                <field name="name"/>
                <field name="building_id"/>
                <field name="floor_number"/>
                <field name="floor_type"/>
                <field name="area"/>
                <field name="max_occupancy"/>
                <field name="zone_count"/>
                <field name="room_count"/>
                <field name="device_count"/>
                <field name="state" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-muted="state == 'inactive'"
                       decoration-info="state == 'draft'"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Floor Kanban View -->
    <record id="nebular_floor_view_kanban" model="ir.ui.view">
        <field name="name">nebular.floor.kanban</field>
        <field name="model">nebular.floor</field>
        <field name="arch" type="xml">
            <kanban default_group_by="building_id" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="building_id"/>
                <field name="floor_number"/>
                <field name="floor_type"/>
                <field name="area"/>
                <field name="zone_count"/>
                <field name="room_count"/>
                <field name="device_count"/>
                <field name="state"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            Floor <field name="floor_number"/> - <field name="floor_type"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <span t-if="record.state.raw_value == 'active'" 
                                              class="badge badge-success">Active</span>
                                        <span t-if="record.state.raw_value == 'inactive'" 
                                              class="badge badge-secondary">Inactive</span>
                                        <span t-if="record.state.raw_value == 'draft'" 
                                              class="badge badge-info">Draft</span>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-building"/> <field name="building_id"/>
                                    </div>
                                    <div t-if="record.area.raw_value">
                                        <i class="fa fa-expand"/> <field name="area"/> m²
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-map-marker" title="Zones"/> 
                                            <field name="zone_count"/>
                                        </span>
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-home" title="Rooms"/> 
                                            <field name="room_count"/>
                                        </span>
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-microchip" title="Devices"/> 
                                            <field name="device_count"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Floor Search View -->
    <record id="nebular_floor_view_search" model="ir.ui.view">
        <field name="name">nebular.floor.search</field>
        <field name="model">nebular.floor</field>
        <field name="arch" type="xml">
            <search string="Floors">
                <field name="name" string="Floor" 
                       filter_domain="[('name', 'ilike', self)]"/>
                <field name="building_id"/>
                <field name="floor_number"/>
                <field name="floor_type"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active State" name="active_state" domain="[('state', '=', 'active')]"/>
                <filter string="Inactive State" name="inactive_state" domain="[('state', '=', 'inactive')]"/>
                
                <separator/>
                <filter string="Ground Floor" name="ground" domain="[('floor_type', '=', 'ground')]"/>
                <filter string="Upper Floor" name="upper" domain="[('floor_type', '=', 'upper')]"/>
                <filter string="Basement" name="basement" domain="[('floor_type', '=', 'basement')]"/>
                <filter string="Mezzanine" name="mezzanine" domain="[('floor_type', '=', 'mezzanine')]"/>
                <filter string="Rooftop" name="rooftop" domain="[('floor_type', '=', 'rooftop')]"/>
                
                <separator/>
                <filter string="Has Zones" name="has_zones" domain="[('zone_count', '>', 0)]"/>
                <filter string="Has Rooms" name="has_rooms" domain="[('room_count', '>', 0)]"/>
                <filter string="Has Devices" name="has_devices" domain="[('device_count', '>', 0)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Building" name="group_building" 
                            context="{'group_by': 'building_id'}"/>
                    <filter string="Floor Type" name="group_floor_type" 
                            context="{'group_by': 'floor_type'}"/>
                    <filter string="State" name="group_state" 
                            context="{'group_by': 'state'}"/>
                    <filter string="Creation Date" name="group_create_date" 
                            context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Floor Action -->
    <record id="nebular_floor_action" model="ir.actions.act_window">
        <field name="name">Floors</field>
        <field name="res_model">nebular.floor</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1,
            'search_default_active_state': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first floor!
            </p>
            <p>
                Floors represent the different levels within a building.
                Each floor can contain zones, rooms, and devices.
            </p>
        </field>
    </record>
</odoo>