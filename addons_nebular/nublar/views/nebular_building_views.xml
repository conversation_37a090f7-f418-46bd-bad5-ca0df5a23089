<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Building Form View -->
    <record id="nebular_building_view_form" model="ir.ui.view">
        <field name="name">nebular.building.form</field>
        <field name="model">nebular.building</field>
        <field name="arch" type="xml">
            <form string="Building">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            class="btn-primary" invisible="active"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            class="btn-secondary" invisible="not active"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,inactive"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_floors" type="object" class="oe_stat_button" icon="fa-building">
                            <field name="floor_count" widget="statinfo" string="Floors"/>
                        </button>
                        <button name="action_view_zones" type="object" class="oe_stat_button" icon="fa-map-marker">
                            <field name="zone_count" widget="statinfo" string="Zones"/>
                        </button>
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-microchip">
                            <field name="device_count" widget="statinfo" string="Devices"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <field name="image" widget="image" class="oe_avatar" 
                           options="{'preview_image': 'image', 'size': [90, 90]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Building Name"/>
                        </h1>
                        <h3>
                            <field name="code" placeholder="Building Code"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="description"/>
                            <field name="building_type"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group name="location_info">
                            <field name="address"/>
                            <field name="city"/>
                            <field name="country_id"/>
                            <field name="postal_code"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="physical_info">
                            <field name="total_floors"/>
                            <field name="total_area"/>
                            <field name="construction_year"/>
                        </group>
                        <group name="contact_info">
                            <field name="manager_id"/>
                            <field name="phone"/>
                            <field name="email"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Floors" name="floors">
                            <field name="floor_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="floor_number"/>
                                    <field name="floor_type"/>
                                    <field name="area"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Zones" name="zones">
                            <field name="zone_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="zone_type"/>
                                    <field name="floor_id"/>
                                    <field name="area"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Devices" name="devices">
                            <field name="device_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="device_type_id"/>
                                    <field name="location"/>
                                    <field name="status"/>
                                    <field name="last_seen"/>
                                </list>
                            </field>
                        </page>
                        <page string="Technical Details" name="technical">
                            <group>
                                <group name="coordinates">
                                    <field name="latitude"/>
                                    <field name="longitude"/>
                                </group>
                                <group name="timestamps">
                                    <field name="create_date" readonly="1"/>
                                    <field name="write_date" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Building List View -->
    <record id="nebular_building_view_list" model="ir.ui.view">
        <field name="name">nebular.building.list</field>
        <field name="model">nebular.building</field>
        <field name="arch" type="xml">
            <list string="Buildings" default_order="name">
                <field name="image" widget="image" options="{'size': [40, 40]}"/>
                <field name="name"/>
                <field name="code"/>
                <field name="building_type"/>
                <field name="city"/>
                <field name="country_id"/>
                <field name="manager_id"/>
                <field name="total_floors"/>
                <field name="floor_count"/>
                <field name="zone_count"/>
                <field name="device_count"/>
                <field name="state" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-muted="state == 'inactive'"
                       decoration-info="state == 'draft'"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Building Kanban View -->
    <record id="nebular_building_view_kanban" model="ir.ui.view">
        <field name="name">nebular.building.kanban</field>
        <field name="model">nebular.building</field>
        <field name="arch" type="xml">
            <kanban default_group_by="building_type" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="code"/>
                <field name="building_type"/>
                <field name="city"/>
                <field name="manager_id"/>
                <field name="floor_count"/>
                <field name="zone_count"/>
                <field name="device_count"/>
                <field name="state"/>
                <field name="image"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_image">
                                <img t-att-src="kanban_image('nebular.building', 'image', record.id.raw_value)" 
                                     alt="Building" class="o_image_64_contain"/>
                            </div>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="code"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <span t-if="record.state.raw_value == 'active'" 
                                              class="badge badge-success">Active</span>
                                        <span t-if="record.state.raw_value == 'inactive'" 
                                              class="badge badge-secondary">Inactive</span>
                                        <span t-if="record.state.raw_value == 'draft'" 
                                              class="badge badge-info">Draft</span>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <field name="city"/>
                                    <br/>
                                    <small class="text-muted">
                                        Manager: <field name="manager_id"/>
                                    </small>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-building-o" title="Floors"/> 
                                            <field name="floor_count"/>
                                        </span>
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-map-marker" title="Zones"/> 
                                            <field name="zone_count"/>
                                        </span>
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-microchip" title="Devices"/> 
                                            <field name="device_count"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Building Search View -->
    <record id="nebular_building_view_search" model="ir.ui.view">
        <field name="name">nebular.building.search</field>
        <field name="model">nebular.building</field>
        <field name="arch" type="xml">
            <search string="Buildings">
                <field name="name" string="Building" 
                       filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <field name="city"/>
                <field name="country_id"/>
                <field name="manager_id"/>
                <field name="building_type"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active State" name="active_state" domain="[('state', '=', 'active')]"/>
                <filter string="Inactive State" name="inactive_state" domain="[('state', '=', 'inactive')]"/>
                
                <separator/>
                <filter string="Residential" name="residential" domain="[('building_type', '=', 'residential')]"/>
                <filter string="Commercial" name="commercial" domain="[('building_type', '=', 'commercial')]"/>
                <filter string="Industrial" name="industrial" domain="[('building_type', '=', 'industrial')]"/>
                <filter string="Mixed Use" name="mixed_use" domain="[('building_type', '=', 'mixed_use')]"/>
                
                <separator/>
                <filter string="My Buildings" name="my_buildings" 
                        domain="[('manager_id', '=', uid)]"/>
                
                <separator/>
                <filter string="Created Today" name="created_today" 
                        domain="[('create_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter string="Created This Week" name="created_week" 
                        domain="[('create_date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Building Type" name="group_building_type" 
                            context="{'group_by': 'building_type'}"/>
                    <filter string="City" name="group_city" 
                            context="{'group_by': 'city'}"/>
                    <filter string="Country" name="group_country" 
                            context="{'group_by': 'country_id'}"/>
                    <filter string="Manager" name="group_manager" 
                            context="{'group_by': 'manager_id'}"/>
                    <filter string="State" name="group_state" 
                            context="{'group_by': 'state'}"/>
                    <filter string="Creation Date" name="group_create_date" 
                            context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Building Action -->
    <record id="nebular_building_action" model="ir.actions.act_window">
        <field name="name">Buildings</field>
        <field name="res_model">nebular.building</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1,
            'search_default_active_state': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first building!
            </p>
            <p>
                Buildings are the top-level containers in your Nebular system.
                Each building can contain multiple floors, zones, and devices.
            </p>
        </field>
    </record>
</odoo>