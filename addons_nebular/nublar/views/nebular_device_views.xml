<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Device Form View -->
    <record id="nebular_device_view_form" model="ir.ui.view">
        <field name="name">nebular.device.form</field>
        <field name="model">nebular.device</field>
        <field name="arch" type="xml">
            <form string="Device">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            class="btn-primary" invisible="status == 'active'"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            class="btn-secondary" invisible="status != 'active'"/>
                    <button name="action_reset" type="object" string="Reset" 
                            class="btn-warning" invisible="status == 'offline'"/>
                    <field name="status" widget="statusbar" statusbar_visible="offline,online,active,inactive,error"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_events" type="object" class="oe_stat_button" icon="fa-bell">
                            <field name="event_count" widget="statinfo" string="Events"/>
                        </button>
                        <button name="action_view_readings" type="object" class="oe_stat_button" icon="fa-line-chart">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="reading_count"/>
                                </span>
                                <span class="o_stat_text">Readings</span>
                            </div>
                        </button>
                        <button name="action_test_connection" type="object" class="oe_stat_button" icon="fa-wifi">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="signal_strength"/>%
                                </span>
                                <span class="o_stat_text">Signal</span>
                            </div>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Device Name"/>
                        </h1>
                        <h3>
                            <field name="display_name" readonly="1"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="device_type_id" required="1"/>
                            <field name="building_id" required="1"/>
                            <field name="floor_id" domain="[('building_id', '=', building_id)]"/>
                            <field name="room_id" domain="[('floor_id', '=', floor_id)]"/>
                            <field name="location"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group name="status_info">
                            <field name="is_online" widget="boolean_toggle"/>
                            <field name="last_seen"/>
                            <field name="battery_level" widget="percentage"/>
                            <field name="firmware_version"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="coordinates">
                            <field name="x_coordinate"/>
                            <field name="y_coordinate"/>
                            <field name="z_coordinate"/>
                        </group>
                        <group name="connectivity">
                            <field name="ip_address"/>
                            <field name="mac_address"/>
                            <field name="signal_strength" widget="percentage"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="hardware">
                            <field name="manufacturer"/>
                            <field name="model"/>
                            <field name="serial_number"/>
                        </group>
                        <group name="maintenance">
                            <field name="installation_date"/>
                            <field name="last_maintenance"/>
                            <field name="next_maintenance"/>
                            <field name="warranty_expiry"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Device description..."/>
                    </group>
                    
                    <notebook>
                        <page string="Configuration" name="configuration">
                            <group>
                                <group name="settings">
                                    <field name="config_data" widget="ace" options="{'mode': 'json'}"/>
                                </group>
                                <group name="thresholds">
                                    <field name="alert_threshold_min"/>
                                    <field name="alert_threshold_max"/>
                                    <field name="critical_threshold_min"/>
                                    <field name="critical_threshold_max"/>
                                </group>
                            </group>
                        </page>
                        <page string="Recent Events" name="events">
                            <field name="event_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="severity"/>
                                    <field name="timestamp"/>
                                    <field name="status"/>
                                    <field name="value"/>
                                </list>
                            </field>
                        </page>
                        <page string="Sensor Readings" name="readings" invisible="device_type_id and device_type_id.category != 'sensor'">
                            <group>
                                <group name="current_readings">
                                    <field name="current_value"/>
                                    <field name="current_unit"/>
                                    <field name="last_reading_time"/>
                                </group>
                                <group name="reading_stats">
                                    <field name="min_value_24h"/>
                                    <field name="max_value_24h"/>
                                    <field name="avg_value_24h"/>
                                </group>
                            </group>
                        </page>
                        <page string="Technical Details" name="technical">
                            <group>
                                <group name="power">
                                    <field name="power_consumption"/>
                                    <field name="power_source"/>
                                    <field name="backup_battery"/>
                                </group>
                                <group name="communication">
                                    <field name="communication_protocol"/>
                                    <field name="data_format"/>
                                    <field name="reporting_interval"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Device List View -->
    <record id="nebular_device_view_list" model="ir.ui.view">
        <field name="name">nebular.device.list</field>
        <field name="model">nebular.device</field>
        <field name="arch" type="xml">
            <list string="Devices" default_order="building_id, floor_id, room_id, name">
                <field name="name"/>
                <field name="device_type_id"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <field name="location"/>
                <field name="status" widget="badge" 
                       decoration-success="status == 'active'"
                       decoration-info="status == 'online'"
                       decoration-muted="status == 'inactive'"
                       decoration-warning="status == 'offline'"
                       decoration-danger="status == 'error'"/>
                <field name="is_online" widget="boolean_toggle"/>
                <field name="battery_level" widget="percentage"/>
                <field name="signal_strength" widget="percentage"/>
                <field name="last_seen"/>
                <field name="current_value"/>
                <field name="current_unit"/>
                <field name="event_count"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Device Kanban View -->
    <record id="nebular_device_view_kanban" model="ir.ui.view">
        <field name="name">nebular.device.kanban</field>
        <field name="model">nebular.device</field>
        <field name="arch" type="xml">
            <kanban default_group_by="device_type_id" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="device_type_id"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <field name="location"/>
                <field name="status"/>
                <field name="is_online"/>
                <field name="battery_level"/>
                <field name="signal_strength"/>
                <field name="last_seen"/>
                <field name="current_value"/>
                <field name="current_unit"/>
                <field name="event_count"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="device_type_id"/> - <field name="location"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <span t-if="record.status.raw_value == 'active'" 
                                              class="badge badge-success">Active</span>
                                        <span t-if="record.status.raw_value == 'online'" 
                                              class="badge badge-info">Online</span>
                                        <span t-if="record.status.raw_value == 'inactive'" 
                                              class="badge badge-secondary">Inactive</span>
                                        <span t-if="record.status.raw_value == 'offline'" 
                                              class="badge badge-warning">Offline</span>
                                        <span t-if="record.status.raw_value == 'error'" 
                                              class="badge badge-danger">Error</span>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-map-marker"/> <field name="building_id"/> - <field name="room_id"/>
                                    </div>
                                    <div t-if="record.current_value.raw_value">
                                        <i class="fa fa-tachometer"/> 
                                        <field name="current_value"/> <field name="current_unit"/>
                                    </div>
                                    <div class="row">
                                        <div class="col-6" t-if="record.battery_level.raw_value">
                                            <i class="fa fa-battery-half"/> <field name="battery_level"/>%
                                        </div>
                                        <div class="col-6" t-if="record.signal_strength.raw_value">
                                            <i class="fa fa-wifi"/> <field name="signal_strength"/>%
                                        </div>
                                    </div>
                                    <div t-if="record.last_seen.raw_value">
                                        <i class="fa fa-clock-o"/> Last: <field name="last_seen"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-bell" title="Events"/> 
                                            <field name="event_count"/>
                                        </span>
                                        <span t-if="record.is_online.raw_value" 
                                              class="o_kanban_inline_block">
                                            <i class="fa fa-circle text-success" title="Online"/>
                                        </span>
                                        <span t-if="!record.is_online.raw_value" 
                                              class="o_kanban_inline_block">
                                            <i class="fa fa-circle text-danger" title="Offline"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Device Search View -->
    <record id="nebular_device_view_search" model="ir.ui.view">
        <field name="name">nebular.device.search</field>
        <field name="model">nebular.device</field>
        <field name="arch" type="xml">
            <search string="Devices">
                <field name="name" string="Device" 
                       filter_domain="['|', '|', ('name', 'ilike', self), ('serial_number', 'ilike', self), ('location', 'ilike', self)]"/>
                <field name="device_type_id"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <field name="location"/>
                <field name="manufacturer"/>
                <field name="model"/>
                <field name="serial_number"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Online" name="online" domain="[('is_online', '=', True)]"/>
                <filter string="Offline" name="offline" domain="[('is_online', '=', False)]"/>
                
                <separator/>
                <filter string="Active Status" name="active_status" domain="[('status', '=', 'active')]"/>
                <filter string="Online Status" name="online_status" domain="[('status', '=', 'online')]"/>
                <filter string="Inactive Status" name="inactive_status" domain="[('status', '=', 'inactive')]"/>
                <filter string="Offline Status" name="offline_status" domain="[('status', '=', 'offline')]"/>
                <filter string="Error Status" name="error_status" domain="[('status', '=', 'error')]"/>
                
                <separator/>
                <filter string="Sensors" name="sensors" domain="[('device_type_id.category', '=', 'sensor')]"/>
                <filter string="Actuators" name="actuators" domain="[('device_type_id.category', '=', 'actuator')]"/>
                <filter string="Controllers" name="controllers" domain="[('device_type_id.category', '=', 'controller')]"/>
                <filter string="Cameras" name="cameras" domain="[('device_type_id.category', '=', 'camera')]"/>
                <filter string="Access Control" name="access_control" domain="[('device_type_id.category', '=', 'access_control')]"/>
                <filter string="Environmental" name="environmental" domain="[('device_type_id.category', '=', 'environmental')]"/>
                <filter string="Safety" name="safety" domain="[('device_type_id.category', '=', 'safety')]"/>
                <filter string="Communication" name="communication" domain="[('device_type_id.category', '=', 'communication')]"/>
                
                <separator/>
                <filter string="Low Battery" name="low_battery" domain="[('battery_level', '&lt;', 20)]"/>
                <filter string="Weak Signal" name="weak_signal" domain="[('signal_strength', '&lt;', 30)]"/>
                <filter string="Recent Activity" name="recent_activity" 
                        domain="[('last_seen', '>=', (context_today() - datetime.timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'))]"/>
                <filter string="Maintenance Due" name="maintenance_due" 
                        domain="[('next_maintenance', '&lt;=', context_today())]"/>
                <filter string="Warranty Expired" name="warranty_expired" 
                        domain="[('warranty_expiry', '&lt;', context_today())]"/>
                
                <separator/>
                <filter string="Has Events" name="has_events" domain="[('event_count', '>', 0)]"/>
                <filter string="Has Current Reading" name="has_reading" domain="[('current_value', '!=', False)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Device Type" name="group_device_type" 
                            context="{'group_by': 'device_type_id'}"/>
                    <filter string="Building" name="group_building" 
                            context="{'group_by': 'building_id'}"/>
                    <filter string="Floor" name="group_floor" 
                            context="{'group_by': 'floor_id'}"/>
                    <filter string="Room" name="group_room" 
                            context="{'group_by': 'room_id'}"/>
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'status'}"/>
                    <filter string="Manufacturer" name="group_manufacturer" 
                            context="{'group_by': 'manufacturer'}"/>
                    <filter string="Installation Date" name="group_installation" 
                            context="{'group_by': 'installation_date:month'}"/>
                    <filter string="Last Seen" name="group_last_seen" 
                            context="{'group_by': 'last_seen:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Device Action -->
    <record id="nebular_device_action" model="ir.actions.act_window">
        <field name="name">Devices</field>
        <field name="res_model">nebular.device</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1,
            'search_default_online': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first device!
            </p>
            <p>
                Devices are the IoT sensors, actuators, and controllers
                that make up your smart building system.
            </p>
        </field>
    </record>
</odoo>