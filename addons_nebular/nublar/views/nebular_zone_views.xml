<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Zone Form View -->
    <record id="nebular_zone_view_form" model="ir.ui.view">
        <field name="name">nebular.zone.form</field>
        <field name="model">nebular.zone</field>
        <field name="arch" type="xml">
            <form string="Zone">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            class="btn-primary" invisible="active"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            class="btn-secondary" invisible="not active"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,inactive"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_rooms" type="object" class="oe_stat_button" icon="fa-home">
                            <field name="room_count" widget="statinfo" string="Rooms"/>
                        </button>
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-microchip">
                            <field name="device_count" widget="statinfo" string="Devices"/>
                        </button>
                        <button name="action_view_events" type="object" class="oe_stat_button" icon="fa-bell">
                            <field name="event_count" widget="statinfo" string="Events"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Zone Name"/>
                        </h1>
                        <h3>
                            <field name="display_name" readonly="1"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="building_id" required="1"/>
                            <field name="floor_id" domain="[('building_id', '=', building_id)]"/>
                            <field name="zone_type"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group name="physical_info">
                            <field name="area"/>
                            <field name="max_occupancy"/>
                            <field name="current_occupancy"/>
                            <field name="occupancy_percentage" widget="percentage"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="coordinates">
                            <field name="x_coordinate"/>
                            <field name="y_coordinate"/>
                            <field name="width"/>
                            <field name="height"/>
                        </group>
                        <group name="security">
                            <field name="security_level"/>
                            <field name="access_control_required"/>
                            <field name="emergency_exit"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Zone description..."/>
                    </group>
                    
                    <notebook>
                        <page string="Rooms" name="rooms">
                            <field name="room_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="room_number"/>
                                    <field name="room_type"/>
                                    <field name="area"/>
                                    <field name="max_occupancy"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Devices" name="devices">
                            <field name="device_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="device_type_id"/>
                                    <field name="location"/>
                                    <field name="status"/>
                                    <field name="last_seen"/>
                                </list>
                            </field>
                        </page>
                        <page string="Recent Events" name="events">
                            <field name="event_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="severity"/>
                                    <field name="timestamp"/>
                                    <field name="status"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Zone List View -->
    <record id="nebular_zone_view_list" model="ir.ui.view">
        <field name="name">nebular.zone.list</field>
        <field name="model">nebular.zone</field>
        <field name="arch" type="xml">
            <list string="Zones" default_order="building_id, floor_id, name">
                <field name="name"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="zone_type"/>
                <field name="area"/>
                <field name="max_occupancy"/>
                <field name="current_occupancy"/>
                <field name="occupancy_percentage" widget="percentage"/>
                <field name="security_level"/>
                <field name="room_count"/>
                <field name="device_count"/>
                <field name="state" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-muted="state == 'inactive'"
                       decoration-info="state == 'draft'"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Zone Kanban View -->
    <record id="nebular_zone_view_kanban" model="ir.ui.view">
        <field name="name">nebular.zone.kanban</field>
        <field name="model">nebular.zone</field>
        <field name="arch" type="xml">
            <kanban default_group_by="zone_type" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="zone_type"/>
                <field name="area"/>
                <field name="max_occupancy"/>
                <field name="current_occupancy"/>
                <field name="occupancy_percentage"/>
                <field name="security_level"/>
                <field name="room_count"/>
                <field name="device_count"/>
                <field name="state"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="building_id"/> - <field name="floor_id"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <span t-if="record.state.raw_value == 'active'" 
                                              class="badge badge-success">Active</span>
                                        <span t-if="record.state.raw_value == 'inactive'" 
                                              class="badge badge-secondary">Inactive</span>
                                        <span t-if="record.state.raw_value == 'draft'" 
                                              class="badge badge-info">Draft</span>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-shield"/> Security: <field name="security_level"/>
                                    </div>
                                    <div t-if="record.area.raw_value">
                                        <i class="fa fa-expand"/> <field name="area"/> m²
                                    </div>
                                    <div t-if="record.max_occupancy.raw_value">
                                        <i class="fa fa-users"/> 
                                        <field name="current_occupancy"/>/<field name="max_occupancy"/>
                                        (<field name="occupancy_percentage"/>%)
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-home" title="Rooms"/> 
                                            <field name="room_count"/>
                                        </span>
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-microchip" title="Devices"/> 
                                            <field name="device_count"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Zone Search View -->
    <record id="nebular_zone_view_search" model="ir.ui.view">
        <field name="name">nebular.zone.search</field>
        <field name="model">nebular.zone</field>
        <field name="arch" type="xml">
            <search string="Zones">
                <field name="name" string="Zone" 
                       filter_domain="[('name', 'ilike', self)]"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="zone_type"/>
                <field name="security_level"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active State" name="active_state" domain="[('state', '=', 'active')]"/>
                <filter string="Inactive State" name="inactive_state" domain="[('state', '=', 'inactive')]"/>
                
                <separator/>
                <filter string="Office" name="office" domain="[('zone_type', '=', 'office')]"/>
                <filter string="Meeting Room" name="meeting" domain="[('zone_type', '=', 'meeting')]"/>
                <filter string="Common Area" name="common" domain="[('zone_type', '=', 'common')]"/>
                <filter string="Storage" name="storage" domain="[('zone_type', '=', 'storage')]"/>
                <filter string="Technical" name="technical" domain="[('zone_type', '=', 'technical')]"/>
                <filter string="Emergency" name="emergency" domain="[('zone_type', '=', 'emergency')]"/>
                
                <separator/>
                <filter string="Low Security" name="low_security" domain="[('security_level', '=', 'low')]"/>
                <filter string="Medium Security" name="medium_security" domain="[('security_level', '=', 'medium')]"/>
                <filter string="High Security" name="high_security" domain="[('security_level', '=', 'high')]"/>
                <filter string="Critical Security" name="critical_security" domain="[('security_level', '=', 'critical')]"/>
                
                <separator/>
                <filter string="Access Control Required" name="access_control" 
                        domain="[('access_control_required', '=', True)]"/>
                <filter string="Emergency Exit" name="emergency_exit" 
                        domain="[('emergency_exit', '=', True)]"/>
                <filter string="Over Capacity" name="over_capacity" 
                        domain="[('current_occupancy', '>', 'max_occupancy')]"/>
                
                <separator/>
                <filter string="Has Rooms" name="has_rooms" domain="[('room_count', '>', 0)]"/>
                <filter string="Has Devices" name="has_devices" domain="[('device_count', '>', 0)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Building" name="group_building" 
                            context="{'group_by': 'building_id'}"/>
                    <filter string="Floor" name="group_floor" 
                            context="{'group_by': 'floor_id'}"/>
                    <filter string="Zone Type" name="group_zone_type" 
                            context="{'group_by': 'zone_type'}"/>
                    <filter string="Security Level" name="group_security" 
                            context="{'group_by': 'security_level'}"/>
                    <filter string="State" name="group_state" 
                            context="{'group_by': 'state'}"/>
                    <filter string="Creation Date" name="group_create_date" 
                            context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Zone Action -->
    <record id="nebular_zone_action" model="ir.actions.act_window">
        <field name="name">Zones</field>
        <field name="res_model">nebular.zone</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1,
            'search_default_active_state': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first zone!
            </p>
            <p>
                Zones are logical divisions within floors that help organize
                spaces by function, security level, or other criteria.
            </p>
        </field>
    </record>
</odoo>