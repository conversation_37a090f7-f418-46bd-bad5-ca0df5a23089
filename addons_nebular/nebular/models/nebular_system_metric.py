# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
import logging

_logger = logging.getLogger(__name__)


class NebularSystemMetric(models.Model):
    """System Metric Model for Nebular Dashboard System"""
    
    # 1. Private attributes
    _name = 'nebular.system.metric'
    _description = 'Nebular System Metric'
    _inherit = ['mail.thread', 'mail.activity.schedule']
    _order = 'timestamp desc, metric_type, name'
    _rec_name = 'display_name'
    
    # 2. Default methods
    def _default_timestamp(self):
        """Default timestamp"""
        return fields.Datetime.now()
    
    def _default_value(self):
        """Default metric value"""
        return 0.0
    
    # 3. Selection methods
    def _selection_metric_type(self):
        """Selection for metric type"""
        return [
            ('system', 'System Performance'),
            ('network', 'Network'),
            ('database', 'Database'),
            ('application', 'Application'),
            ('security', 'Security'),
            ('user', 'User Activity'),
            ('device', 'Device Status'),
            ('event', 'Event Statistics'),
            ('custom', 'Custom Metric')
        ]
    
    def _selection_unit(self):
        """Selection for metric unit"""
        return [
            ('count', 'Count'),
            ('percentage', 'Percentage (%)'),
            ('bytes', 'Bytes'),
            ('kb', 'Kilobytes (KB)'),
            ('mb', 'Megabytes (MB)'),
            ('gb', 'Gigabytes (GB)'),
            ('seconds', 'Seconds'),
            ('minutes', 'Minutes'),
            ('hours', 'Hours'),
            ('requests', 'Requests'),
            ('users', 'Users'),
            ('events', 'Events'),
            ('custom', 'Custom Unit')
        ]
    
    def _selection_status(self):
        """Selection for metric status"""
        return [
            ('normal', 'Normal'),
            ('warning', 'Warning'),
            ('critical', 'Critical'),
            ('unknown', 'Unknown')
        ]
    
    # 4. Field declarations
    name = fields.Char(
        string='Metric Name',
        required=True,
        tracking=True,
        help='Name of the metric'
    )
    description = fields.Text(
        string='Description',
        help='Description of the metric'
    )
    metric_type = fields.Selection(
        selection='_selection_metric_type',
        string='Metric Type',
        required=True,
        default='system',
        tracking=True,
        help='Type of the metric'
    )
    metric_key = fields.Char(
        string='Metric Key',
        required=True,
        help='Unique key for the metric'
    )
    value = fields.Float(
        string='Value',
        default=_default_value,
        required=True,
        help='Current value of the metric'
    )
    unit = fields.Selection(
        selection='_selection_unit',
        string='Unit',
        default='count',
        help='Unit of measurement'
    )
    custom_unit = fields.Char(
        string='Custom Unit',
        help='Custom unit when unit is set to custom'
    )
    timestamp = fields.Datetime(
        string='Timestamp',
        default=_default_timestamp,
        required=True,
        help='When the metric was recorded'
    )
    status = fields.Selection(
        selection='_selection_status',
        string='Status',
        default='normal',
        tracking=True,
        help='Current status of the metric'
    )
    is_active = fields.Boolean(
        string='Active',
        default=True,
        help='Whether this metric is actively monitored'
    )
    
    # Threshold fields
    warning_threshold = fields.Float(
        string='Warning Threshold',
        help='Value at which to trigger warning status'
    )
    critical_threshold = fields.Float(
        string='Critical Threshold',
        help='Value at which to trigger critical status'
    )
    threshold_operator = fields.Selection(
        [('>', 'Greater Than'), ('<', 'Less Than'), ('=', 'Equal To')],
        string='Threshold Operator',
        default='>',
        help='Operator for threshold comparison'
    )
    
    # Relationships
    building_id = fields.Many2one(
        'nebular.building',
        string='Building',
        help='Building this metric relates to'
    )
    zone_id = fields.Many2one(
        'nebular.zone',
        string='Zone',
        help='Zone this metric relates to'
    )
    floor_id = fields.Many2one(
        'nebular.floor',
        string='Floor',
        help='Floor this metric relates to'
    )
    room_id = fields.Many2one(
        'nebular.room',
        string='Room',
        help='Room this metric relates to'
    )
    device_id = fields.Many2one(
        'nebular.device',
        string='Device',
        help='Device this metric relates to'
    )
    alert_ids = fields.One2many(
        'nebular.system.alert',
        'metric_id',
        string='Alerts',
        help='Alerts generated by this metric'
    )
    
    # 5. Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Display name for the metric'
    )
    value_display = fields.Char(
        string='Value Display',
        compute='_compute_value_display',
        help='Formatted value with unit'
    )
    status_color = fields.Char(
        string='Status Color',
        compute='_compute_status_color',
        help='Color based on status'
    )
    has_active_alerts = fields.Boolean(
        string='Has Active Alerts',
        compute='_compute_has_active_alerts',
        help='Whether this metric has active alerts'
    )
    location_display = fields.Char(
        string='Location',
        compute='_compute_location_display',
        help='Display location information'
    )
    
    # 6. Compute methods
    @api.depends('name', 'metric_type', 'metric_key')
    def _compute_display_name(self):
        """Compute display name"""
        for record in self:
            type_name = dict(record._selection_metric_type()).get(record.metric_type, record.metric_type)
            record.display_name = f"{type_name}: {record.name} ({record.metric_key})"
    
    @api.depends('value', 'unit', 'custom_unit')
    def _compute_value_display(self):
        """Compute value display"""
        for record in self:
            unit_display = record.custom_unit if record.unit == 'custom' else dict(record._selection_unit()).get(record.unit, '')
            if record.unit == 'percentage':
                record.value_display = f"{record.value:.1f}%"
            elif unit_display:
                record.value_display = f"{record.value:.2f} {unit_display}"
            else:
                record.value_display = f"{record.value:.2f}"
    
    @api.depends('status')
    def _compute_status_color(self):
        """Compute status color"""
        for record in self:
            colors = {
                'normal': '#28a745',    # Green
                'warning': '#ffc107',   # Yellow
                'critical': '#dc3545',  # Red
                'unknown': '#6c757d'    # Gray
            }
            record.status_color = colors.get(record.status, '#6c757d')
    
    @api.depends('alert_ids.is_active')
    def _compute_has_active_alerts(self):
        """Compute has active alerts"""
        for record in self:
            record.has_active_alerts = any(alert.is_active for alert in record.alert_ids)
    
    @api.depends('building_id', 'zone_id', 'floor_id', 'room_id', 'device_id')
    def _compute_location_display(self):
        """Compute location display"""
        for record in self:
            location_parts = []
            if record.building_id:
                location_parts.append(f"Building: {record.building_id.name}")
            if record.zone_id:
                location_parts.append(f"Zone: {record.zone_id.name}")
            if record.floor_id:
                location_parts.append(f"Floor: {record.floor_id.name}")
            if record.room_id:
                location_parts.append(f"Room: {record.room_id.name}")
            if record.device_id:
                location_parts.append(f"Device: {record.device_id.name}")
            
            record.location_display = " | ".join(location_parts) if location_parts else "System Wide"
    
    # 7. Constraints
    @api.constrains('metric_key')
    def _check_metric_key_unique(self):
        """Check metric key is unique per type and location"""
        for record in self:
            domain = [
                ('metric_key', '=', record.metric_key),
                ('metric_type', '=', record.metric_type),
                ('id', '!=', record.id)
            ]
            
            # Add location constraints
            if record.building_id:
                domain.append(('building_id', '=', record.building_id.id))
            if record.device_id:
                domain.append(('device_id', '=', record.device_id.id))
            
            if self.search_count(domain) > 0:
                raise ValidationError(f"Metric key '{record.metric_key}' already exists for this type and location")
    
    @api.constrains('warning_threshold', 'critical_threshold', 'threshold_operator')
    def _check_thresholds(self):
        """Check threshold consistency"""
        for record in self:
            if record.warning_threshold and record.critical_threshold:
                if record.threshold_operator == '>':
                    if record.critical_threshold <= record.warning_threshold:
                        raise ValidationError("Critical threshold must be greater than warning threshold")
                elif record.threshold_operator == '<':
                    if record.critical_threshold >= record.warning_threshold:
                        raise ValidationError("Critical threshold must be less than warning threshold")
    
    # 8. Onchange methods
    @api.onchange('metric_type')
    def _onchange_metric_type(self):
        """Update default values when metric type changes"""
        if self.metric_type:
            type_defaults = {
                'system': {'unit': 'percentage'},
                'network': {'unit': 'mb'},
                'database': {'unit': 'count'},
                'application': {'unit': 'requests'},
                'security': {'unit': 'events'},
                'user': {'unit': 'users'},
                'device': {'unit': 'count'},
                'event': {'unit': 'events'}
            }
            
            defaults = type_defaults.get(self.metric_type, {})
            for field, value in defaults.items():
                setattr(self, field, value)
    
    @api.onchange('unit')
    def _onchange_unit(self):
        """Clear custom unit when unit is not custom"""
        if self.unit != 'custom':
            self.custom_unit = False
    
    # 9. CRUD overrides
    @api.model
    def create(self, vals):
        """Override create to set default values"""
        # Generate metric key if not provided
        if not vals.get('metric_key'):
            name = vals.get('name', 'metric')
            metric_type = vals.get('metric_type', 'system')
            vals['metric_key'] = f"{metric_type}_{name.lower().replace(' ', '_')}"
        
        record = super().create(vals)
        
        # Check thresholds and update status
        record._check_and_update_status()
        
        return record
    
    def write(self, vals):
        """Override write to handle status updates"""
        result = super().write(vals)
        
        # Check thresholds and update status if value changed
        if 'value' in vals:
            self._check_and_update_status()
        
        return result
    
    # 10. Action methods
    def action_view_alerts(self):
        """View alerts for this metric"""
        self.ensure_one()
        return {
            'name': f'Alerts - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.system.alert',
            'view_mode': 'list,form',
            'domain': [('metric_id', '=', self.id)],
            'context': {'default_metric_id': self.id}
        }
    
    def action_create_alert(self):
        """Create alert for this metric"""
        self.ensure_one()
        return {
            'name': f'Create Alert - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.system.alert',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_metric_id': self.id,
                'default_name': f"Alert for {self.name}",
                'default_alert_type': 'threshold',
                'default_severity': self.status if self.status != 'normal' else 'warning'
            }
        }
    
    def action_reset_status(self):
        """Reset metric status to normal"""
        self.ensure_one()
        self.status = 'normal'
        self.message_post(body="Metric status reset to normal")
    
    def action_toggle_active(self):
        """Toggle metric active status"""
        self.ensure_one()
        self.is_active = not self.is_active
        status = "activated" if self.is_active else "deactivated"
        self.message_post(body=f"Metric {status}")
    
    # 11. Other business methods
    def update_value(self, new_value, timestamp=None):
        """Update metric value"""
        self.ensure_one()
        old_value = self.value
        self.write({
            'value': new_value,
            'timestamp': timestamp or fields.Datetime.now()
        })
        
        # Log value change
        self.message_post(
            body=f"Value updated from {old_value:.2f} to {new_value:.2f}"
        )
        
        return self.status
    
    def _check_and_update_status(self):
        """Check thresholds and update status"""
        for record in self:
            if not record.is_active:
                continue
            
            old_status = record.status
            new_status = 'normal'
            
            if record.critical_threshold:
                if record._check_threshold(record.value, record.critical_threshold, record.threshold_operator):
                    new_status = 'critical'
            
            if new_status == 'normal' and record.warning_threshold:
                if record._check_threshold(record.value, record.warning_threshold, record.threshold_operator):
                    new_status = 'warning'
            
            if new_status != old_status:
                record.status = new_status
                record.message_post(
                    body=f"Status changed from {old_status} to {new_status} (value: {record.value_display})"
                )
                
                # Create alert if status is warning or critical
                if new_status in ['warning', 'critical']:
                    record._create_threshold_alert(new_status)
    
    def _check_threshold(self, value, threshold, operator):
        """Check if value meets threshold condition"""
        if operator == '>':
            return value > threshold
        elif operator == '<':
            return value < threshold
        elif operator == '=':
            return value == threshold
        return False
    
    def _create_threshold_alert(self, severity):
        """Create threshold alert"""
        self.ensure_one()
        alert_data = {
            'name': f"Threshold Alert - {self.name}",
            'alert_type': 'threshold',
            'severity': severity,
            'description': f"Metric '{self.name}' has reached {severity} threshold. Current value: {self.value_display}",
            'metric_id': self.id,
            'building_id': self.building_id.id if self.building_id else None,
            'zone_id': self.zone_id.id if self.zone_id else None,
            'floor_id': self.floor_id.id if self.floor_id else None,
            'room_id': self.room_id.id if self.room_id else None,
            'device_id': self.device_id.id if self.device_id else None
        }
        
        return self.env['nebular.system.alert'].create(alert_data)
    
    def get_historical_data(self, hours=24):
        """Get historical data for this metric"""
        self.ensure_one()
        start_time = datetime.now() - timedelta(hours=hours)
        
        domain = [
            ('metric_key', '=', self.metric_key),
            ('metric_type', '=', self.metric_type),
            ('timestamp', '>=', start_time)
        ]
        
        # Add location filters
        if self.building_id:
            domain.append(('building_id', '=', self.building_id.id))
        if self.device_id:
            domain.append(('device_id', '=', self.device_id.id))
        
        historical_records = self.search(domain, order='timestamp asc')
        
        return [{
            'timestamp': record.timestamp,
            'value': record.value,
            'status': record.status
        } for record in historical_records]
    
    @api.model
    def record_metric(self, metric_key, value, metric_type='system', **kwargs):
        """Record a new metric value"""
        # Find existing metric or create new one
        domain = [
            ('metric_key', '=', metric_key),
            ('metric_type', '=', metric_type)
        ]
        
        # Add location filters from kwargs
        for field in ['building_id', 'zone_id', 'floor_id', 'room_id', 'device_id']:
            if field in kwargs:
                domain.append((field, '=', kwargs[field]))
        
        metric = self.search(domain, limit=1)
        
        if metric:
            metric.update_value(value)
        else:
            # Create new metric
            metric_data = {
                'name': kwargs.get('name', metric_key.replace('_', ' ').title()),
                'metric_key': metric_key,
                'metric_type': metric_type,
                'value': value,
                **kwargs
            }
            metric = self.create(metric_data)
        
        return metric