# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import timedelta


class NebularBuilding(models.Model):
    """Building model for the Nebular Dashboard System"""
    
    # Private attributes
    _name = 'nebular.building'
    _description = 'Nebular Building'
    _inherit = ['mail.thread']
    _order = 'name'
    _rec_name = 'name'
    
    # Field declarations
    name = fields.Char(
        string='Building Name',
        required=True,
        tracking=True,
        help='Name of the building'
    )
    code = fields.Char(
        string='Building Code',
        required=True,
        tracking=True,
        help='Unique code for the building'
    )
    description = fields.Text(
        string='Description',
        help='Detailed description of the building'
    )
    address = fields.Text(
        string='Address',
        help='Physical address of the building'
    )
    
    # Status and operational fields
    is_active = fields.Boolean(
        string='is_active',
        default=True,
        tracking=True,
        help='Whether this building is is_active'
    )
    
    # Relationships
    zone_ids = fields.One2many(
        comodel_name='nebular.zone',
        inverse_name='building_id',
        string='Zones',
        help='Zones within this building'
    )
    floor_ids = fields.One2many(
        comodel_name='nebular.floor',
        inverse_name='building_id',
        string='Floors',
        help='Floors within this building'
    )
    room_ids = fields.One2many(
        comodel_name='nebular.room',
        inverse_name='building_id',
        string='Rooms',
        help='Rooms within this building'
    )
    door_ids = fields.One2many(
        comodel_name='nebular.door',
        inverse_name='building_id',
        string='Doors',
        help='Doors within this building'
    )
    device_ids = fields.One2many(
        comodel_name='nebular.device',
        inverse_name='building_id',
        string='Devices',
        help='Devices within this building'
    )
    event_ids = fields.One2many(
        comodel_name='nebular.event',
        inverse_name='building_id',
        string='Events',
        help='Events related to this building'
    )
    marker_ids = fields.One2many(
        comodel_name='nebular.marker',
        inverse_name='building_id',
        string='Markers',
        help='Floor plan markers within this building'
    )
    
    # Computed fields
    zone_count = fields.Integer(
        string='Zone Count',
        compute='_compute_counts',
        store=True,
        help='Number of zones in this building'
    )
    floor_count = fields.Integer(
        string='Floor Count',
        compute='_compute_counts',
        store=True,
        help='Number of floors in this building'
    )
    room_count = fields.Integer(
        string='Room Count',
        compute='_compute_counts',
        store=True,
        help='Number of rooms in this building'
    )
    door_count = fields.Integer(
        string='Door Count',
        compute='_compute_counts',
        store=True,
        help='Number of doors in this building'
    )
    device_count = fields.Integer(
        string='Device Count',
        compute='_compute_counts',
        store=True,
        help='Number of devices in this building'
    )
    event_count = fields.Integer(
        string='Event Count',
        compute='_compute_event_count',
        help='Number of recent events in this building'
    )
    has_alerts = fields.Boolean(
        string='Has Alerts',
        compute='_compute_has_alerts',
        help='Whether this building has is_active alerts'
    )
    
    # Compute methods
    @api.depends('zone_ids', 'floor_ids', 'room_ids', 'door_ids', 'device_ids')
    def _compute_counts(self):
        """Compute counts for related records"""
        for record in self:
            record.zone_count = len(record.zone_ids)
            record.floor_count = len(record.floor_ids)
            record.room_count = len(record.room_ids)
            record.door_count = len(record.door_ids)
            record.device_count = len(record.device_ids)
    
    @api.depends('event_ids')
    def _compute_event_count(self):
        """Compute recent event count (last 30 days)"""
        for record in self:
            domain = [
                ('building_id', '=', record.id),
                ('create_date', '>=', fields.Datetime.now() - timedelta(days=30))
            ]
            record.event_count = self.env['nebular.event'].search_count(domain)
    
    @api.depends('marker_ids.has_alerts', 'device_ids.is_alert')
    def _compute_has_alerts(self):
        """Compute has_alerts from markers and devices"""
        for record in self:
            marker_alerts = record.marker_ids.filtered('has_alerts')
            device_alerts = record.device_ids.filtered('is_alert')
            record.has_alerts = bool(marker_alerts or device_alerts)
    
    # Constraints
    @api.constrains('code')
    def _check_code_unique(self):
        """Ensure building code is unique"""
        for record in self:
            if record.code:
                existing = self.search([
                    ('code', '=', record.code),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(f"Building code '{record.code}' already exists.")
    
    @api.constrains('name')
    def _check_name_not_empty(self):
        """Ensure building name is not empty"""
        for record in self:
            if not record.name or not record.name.strip():
                raise ValidationError("Building name cannot be empty.")
    
    # CRUD overrides
    def create(self, vals_list):
        """Override create to add tracking message"""
        buildings = super().create(vals_list)
        for building in buildings:
            building.message_post(
                body=f"Building '{building.name}' has been created.",
                message_type='notification'
            )
        return buildings
    
    def write(self, vals):
        """Override write to add tracking for important changes"""
        result = super().write(vals)
        if 'is_active' in vals:
            for record in self:
                status = 'activated' if vals['is_active'] else 'deactivated'
                record.message_post(
                    body=f"Building '{record.name}' has been {status}.",
                    message_type='notification'
                )
        return result
    
    # Action methods
    def action_activate(self):
        """Activate the building"""
        self.ensure_one()
        self.is_active = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Building '{self.name}' has been activated.",
                'type': 'success',
            }
        }
    
    def action_deactivate(self):
        """Deactivate the building"""
        self.ensure_one()
        self.is_active = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Building '{self.name}' has been deactivated.",
                'type': 'warning',
            }
        }
    
    def action_view_zones(self):
        """Action to view zones of this building"""
        self.ensure_one()
        return {
            'name': f'Zones - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.zone',
            'view_mode': 'list,form',
            'domain': [('building_id', '=', self.id)],
            'context': {'default_building_id': self.id},
        }
    
    def action_view_floors(self):
        """Action to view floors of this building"""
        self.ensure_one()
        return {
            'name': f'Floors - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.floor',
            'view_mode': 'list,form',
            'domain': [('building_id', '=', self.id)],
            'context': {'default_building_id': self.id},
        }
    
    def action_view_rooms(self):
        """Action to view rooms of this building"""
        self.ensure_one()
        return {
            'name': f'Rooms - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.room',
            'view_mode': 'list,form',
            'domain': [('building_id', '=', self.id)],
            'context': {'default_building_id': self.id},
        }
    
    def action_view_devices(self):
        """Action to view devices of this building"""
        self.ensure_one()
        return {
            'name': f'Devices - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.device',
            'view_mode': 'list,form',
            'domain': [('building_id', '=', self.id)],
            'context': {'default_building_id': self.id},
        }
    
    def action_view_events(self):
        """Action to view events of this building"""
        self.ensure_one()
        return {
            'name': f'Events - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.event',
            'view_mode': 'list,form',
            'domain': [('building_id', '=', self.id)],
            'context': {'default_building_id': self.id},
        }
    
    def action_view_alerts(self):
        """Action to view is_active alerts in this building"""
        self.ensure_one()
        marker_ids = self.marker_ids.filtered('has_alerts').ids
        device_ids = self.device_ids.filtered('is_alert').ids
        
        if marker_ids and device_ids:
            # Show both markers and devices with alerts
            return {
                'name': f'Alerts - {self.name}',
                'type': 'ir.actions.act_window',
                'res_model': 'nebular.marker',
                'view_mode': 'list,form',
                'domain': [('id', 'in', marker_ids)],
            }
        elif marker_ids:
            return {
                'name': f'Marker Alerts - {self.name}',
                'type': 'ir.actions.act_window',
                'res_model': 'nebular.marker',
                'view_mode': 'list,form',
                'domain': [('id', 'in', marker_ids)],
            }
        elif device_ids:
            return {
                'name': f'Device Alerts - {self.name}',
                'type': 'ir.actions.act_window',
                'res_model': 'nebular.device',
                'view_mode': 'list,form',
                'domain': [('id', 'in', device_ids)],
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': 'No is_active alerts found in this building.',
                    'type': 'info',
                }
            }
    
    # Other business methods
    def get_building_summary(self):
        """Get a summary of building statistics"""
        self.ensure_one()
        return {
            'name': self.name,
            'code': self.code,
            'zones': self.zone_count,
            'floors': self.floor_count,
            'rooms': self.room_count,
            'doors': self.door_count,
            'devices': self.device_count,
            'events': self.event_count,
            'has_alerts': self.has_alerts,
        }