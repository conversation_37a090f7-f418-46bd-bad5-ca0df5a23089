# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class NebularConfig(models.TransientModel):
    """Configuration Settings for Nebular Dashboard System"""
    
    # 1. Private attributes
    _name = 'nebular.config'
    _description = 'Nebular Dashboard Configuration'
    _inherit = 'res.config.settings'
    
    # 2. Field declarations
    
    # General Settings
    nebular_company_name = fields.Char(
        string='Company Name',
        config_parameter='nebular.company_name',
        help='Company name displayed in the dashboard'
    )
    nebular_system_name = fields.Char(
        string='System Name',
        config_parameter='nebular.system_name',
        default='Nebular Dashboard',
        help='Name of the Nebular system'
    )
    nebular_system_version = fields.Char(
        string='System Version',
        config_parameter='nebular.system_version',
        default='1.0.0',
        help='Version of the Nebular system'
    )
    nebular_timezone = fields.Selection(
        selection=[
            ('UTC', 'UTC'),
            ('US/Eastern', 'US/Eastern'),
            ('US/Central', 'US/Central'),
            ('US/Mountain', 'US/Mountain'),
            ('US/Pacific', 'US/Pacific'),
            ('Europe/London', 'Europe/London'),
            ('Europe/Paris', 'Europe/Paris'),
            ('Europe/Berlin', 'Europe/Berlin'),
            ('Asia/Tokyo', 'Asia/Tokyo'),
            ('Asia/Shanghai', 'Asia/Shanghai'),
            ('Asia/Dubai', 'Asia/Dubai'),
            ('Australia/Sydney', 'Australia/Sydney')
        ],
        string='System Timezone',
        config_parameter='nebular.timezone',
        default='UTC',
        help='Default timezone for the system'
    )
    nebular_language = fields.Selection(
        selection=[
            ('en_US', 'English (US)'),
            ('en_GB', 'English (UK)'),
            ('fr_FR', 'French'),
            ('de_DE', 'German'),
            ('es_ES', 'Spanish'),
            ('it_IT', 'Italian'),
            ('pt_PT', 'Portuguese'),
            ('zh_CN', 'Chinese (Simplified)'),
            ('ja_JP', 'Japanese'),
            ('ar_SA', 'Arabic')
        ],
        string='Default Language',
        config_parameter='nebular.language',
        default='en_US',
        help='Default language for the system'
    )
    
    # Dashboard Settings
    nebular_dashboard_refresh_interval = fields.Integer(
        string='Dashboard Refresh Interval (seconds)',
        config_parameter='nebular.dashboard_refresh_interval',
        default=30,
        help='How often the dashboard refreshes automatically'
    )
    nebular_max_events_display = fields.Integer(
        string='Max Events to Display',
        config_parameter='nebular.max_events_display',
        default=100,
        help='Maximum number of events to display in lists'
    )
    nebular_enable_real_time = fields.Boolean(
        string='Enable Real-time Updates',
        config_parameter='nebular.enable_real_time',
        default=True,
        help='Enable real-time updates via WebSocket'
    )
    nebular_enable_notifications = fields.Boolean(
        string='Enable Notifications',
        config_parameter='nebular.enable_notifications',
        default=True,
        help='Enable system notifications'
    )
    
    # Security Settings
    nebular_enable_audit_log = fields.Boolean(
        string='Enable Audit Log',
        config_parameter='nebular.enable_audit_log',
        default=True,
        help='Enable audit logging for security events'
    )
    nebular_session_timeout = fields.Integer(
        string='Session Timeout (minutes)',
        config_parameter='nebular.session_timeout',
        default=480,
        help='User session timeout in minutes'
    )
    nebular_max_login_attempts = fields.Integer(
        string='Max Login Attempts',
        config_parameter='nebular.max_login_attempts',
        default=5,
        help='Maximum failed login attempts before lockout'
    )
    nebular_lockout_duration = fields.Integer(
        string='Lockout Duration (minutes)',
        config_parameter='nebular.lockout_duration',
        default=30,
        help='Duration of account lockout in minutes'
    )
    
    # System Monitoring Settings
    nebular_enable_system_monitoring = fields.Boolean(
        string='Enable System Monitoring',
        config_parameter='nebular.enable_system_monitoring',
        default=True,
        help='Enable system performance monitoring'
    )

    nebular_event_retention_days = fields.Integer(
        string='Event Retention (days)',
        config_parameter='nebular.event_retention_days',
        default=180,
        help='Number of days to retain events'
    )
    
    # Integration Settings
    nebular_enable_api = fields.Boolean(
        string='Enable REST API',
        config_parameter='nebular.enable_api',
        default=True,
        help='Enable REST API for external integrations'
    )
    nebular_api_rate_limit = fields.Integer(
        string='API Rate Limit (requests/minute)',
        config_parameter='nebular.api_rate_limit',
        default=1000,
        help='Rate limit for API requests per minute'
    )
    nebular_enable_webhook = fields.Boolean(
        string='Enable Webhooks',
        config_parameter='nebular.enable_webhook',
        default=False,
        help='Enable webhook notifications for events'
    )
    nebular_webhook_url = fields.Char(
        string='Webhook URL',
        config_parameter='nebular.webhook_url',
        help='URL for webhook notifications'
    )
    nebular_webhook_secret = fields.Char(
        string='Webhook Secret',
        config_parameter='nebular.webhook_secret',
        help='Secret key for webhook authentication'
    )
    
    # Device Integration Settings
    nebular_enable_device_auto_discovery = fields.Boolean(
        string='Enable Device Auto-Discovery',
        config_parameter='nebular.enable_device_auto_discovery',
        default=False,
        help='Automatically discover and register new devices'
    )
    nebular_device_heartbeat_interval = fields.Integer(
        string='Device Heartbeat Interval (seconds)',
        config_parameter='nebular.device_heartbeat_interval',
        default=60,
        help='Expected heartbeat interval from devices'
    )
    nebular_device_offline_threshold = fields.Integer(
        string='Device Offline Threshold (minutes)',
        config_parameter='nebular.device_offline_threshold',
        default=5,
        help='Time before a device is considered offline'
    )
    
    # Backup and Maintenance Settings
    nebular_enable_auto_backup = fields.Boolean(
        string='Enable Auto Backup',
        config_parameter='nebular.enable_auto_backup',
        default=True,
        help='Enable automatic database backups'
    )
    nebular_backup_frequency = fields.Selection(
        selection=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly')
        ],
        string='Backup Frequency',
        config_parameter='nebular.backup_frequency',
        default='daily',
        help='Frequency of automatic backups'
    )
    nebular_backup_retention = fields.Integer(
        string='Backup Retention (days)',
        config_parameter='nebular.backup_retention',
        default=30,
        help='Number of days to retain backups'
    )
    nebular_enable_maintenance_mode = fields.Boolean(
        string='Maintenance Mode',
        config_parameter='nebular.enable_maintenance_mode',
        default=False,
        help='Enable maintenance mode (restricts access)'
    )
    
    # 3. Selection methods
    def _selection_timezone(self):
        """Selection for timezone"""
        return [
            ('UTC', 'UTC'),
            ('US/Eastern', 'US/Eastern'),
            ('US/Central', 'US/Central'),
            ('US/Mountain', 'US/Mountain'),
            ('US/Pacific', 'US/Pacific'),
            ('Europe/London', 'Europe/London'),
            ('Europe/Paris', 'Europe/Paris'),
            ('Europe/Berlin', 'Europe/Berlin'),
            ('Asia/Tokyo', 'Asia/Tokyo'),
            ('Asia/Shanghai', 'Asia/Shanghai'),
            ('Asia/Dubai', 'Asia/Dubai'),
            ('Australia/Sydney', 'Australia/Sydney')
        ]
    
    def _selection_language(self):
        """Selection for language"""
        return [
            ('en_US', 'English (US)'),
            ('en_GB', 'English (UK)'),
            ('fr_FR', 'French'),
            ('de_DE', 'German'),
            ('es_ES', 'Spanish'),
            ('it_IT', 'Italian'),
            ('pt_PT', 'Portuguese'),
            ('zh_CN', 'Chinese (Simplified)'),
            ('ja_JP', 'Japanese'),
            ('ar_SA', 'Arabic')
        ]
    
    # 4. Constraints
    @api.constrains('nebular_dashboard_refresh_interval')
    def _check_refresh_interval(self):
        """Check refresh interval is reasonable"""
        for record in self:
            if record.nebular_dashboard_refresh_interval and record.nebular_dashboard_refresh_interval < 5:
                raise ValidationError("Dashboard refresh interval must be at least 5 seconds")
            if record.nebular_dashboard_refresh_interval and record.nebular_dashboard_refresh_interval > 3600:
                raise ValidationError("Dashboard refresh interval cannot exceed 1 hour")
    
    @api.constrains('nebular_session_timeout')
    def _check_session_timeout(self):
        """Check session timeout is reasonable"""
        for record in self:
            if record.nebular_session_timeout and record.nebular_session_timeout < 5:
                raise ValidationError("Session timeout must be at least 5 minutes")
            if record.nebular_session_timeout and record.nebular_session_timeout > 1440:
                raise ValidationError("Session timeout cannot exceed 24 hours")
    
    @api.constrains('nebular_max_login_attempts')
    def _check_max_login_attempts(self):
        """Check max login attempts is reasonable"""
        for record in self:
            if record.nebular_max_login_attempts and record.nebular_max_login_attempts < 3:
                raise ValidationError("Maximum login attempts must be at least 3")
            if record.nebular_max_login_attempts and record.nebular_max_login_attempts > 20:
                raise ValidationError("Maximum login attempts cannot exceed 20")
    
    @api.constrains('nebular_api_rate_limit')
    def _check_api_rate_limit(self):
        """Validate API rate limit"""
        if self.nebular_api_rate_limit <= 0:
            raise ValidationError("API rate limit must be greater than 0")

    # 5. Onchange methods
    @api.onchange('nebular_enable_maintenance_mode')
    def _onchange_maintenance_mode(self):
        """Warning when enabling maintenance mode"""
        if self.nebular_enable_maintenance_mode:
            return {
                'warning': {
                    'title': 'Maintenance Mode',
                    'message': 'Enabling maintenance mode will restrict access to the system for all users except administrators.'
                }
            }
    
    # 6. Action methods
    def action_activate(self):
        """Activate configuration settings"""
        self.ensure_one()
        # This would typically activate certain system features
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Configuration settings have been activated.',
                'type': 'success',
            }
        }
    
    def action_deactivate(self):
        """Deactivate configuration settings"""
        self.ensure_one()
        # This would typically deactivate certain system features
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Configuration settings have been deactivated.',
                'type': 'warning',
            }
        }
    
    def action_test_webhook(self):
        """Test webhook configuration"""
        self.ensure_one()
        if not self.nebular_webhook_url:
            raise UserError("Please configure webhook URL first")
        
        # Test webhook implementation would go here
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Webhook test completed successfully.',
                'type': 'success',
            }
        }
    
    def action_cleanup_old_data(self):
        """Clean up old data based on retention settings"""
        self.ensure_one()
        
        cleanup_results = []
        
        # Clean up old events (replaces both metrics and alerts)
        if self.nebular_event_retention_days:
            event_model = self.env['nebular.event']
            if hasattr(event_model, 'cleanup_old_events'):
                count = event_model.cleanup_old_events(self.nebular_event_retention_days)
                cleanup_results.append(f"Cleaned up {count} old events")
        
        message = '\n'.join(cleanup_results) if cleanup_results else 'No data to clean up'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Data Cleanup',
                'message': message,
                'type': 'success',
                'sticky': True
            }
        }
    
    def action_reset_to_defaults(self):
        """Reset all settings to default values"""
        self.ensure_one()
        
        # Get all config parameters for nebular
        config_params = self.env['ir.config_parameter'].search([
            ('key', 'like', 'nebular.%')
        ])
        
        # Delete all nebular config parameters to reset to defaults
        config_params.unlink()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Settings Reset',
                'message': 'All settings have been reset to default values',
                'type': 'success',
                'sticky': False
            }
        }
    
    # 7. Other business methods
    @api.model
    def get_system_info(self):
        """Get system information"""
        config = self.env['ir.config_parameter'].sudo()
        
        return {
            'company_name': config.get_param('nebular.company_name', 'Nebular Company'),
            'system_name': config.get_param('nebular.system_name', 'Nebular Dashboard'),
            'system_version': config.get_param('nebular.system_version', '1.0.0'),
            'timezone': config.get_param('nebular.timezone', 'UTC'),
            'language': config.get_param('nebular.language', 'en_US'),
            'real_time_enabled': config.get_param('nebular.enable_real_time', 'True') == 'True',
            'notifications_enabled': config.get_param('nebular.enable_notifications', 'True') == 'True',
            'api_enabled': config.get_param('nebular.enable_api', 'True') == 'True',
            'maintenance_mode': config.get_param('nebular.enable_maintenance_mode', 'False') == 'True'
        }
    
    @api.model
    def is_maintenance_mode(self):
        """Check if system is in maintenance mode"""
        return self.env['ir.config_parameter'].sudo().get_param('nebular.enable_maintenance_mode', 'False') == 'True'
    
    @api.model
    def get_dashboard_settings(self):
        """Get dashboard-specific settings"""
        config = self.env['ir.config_parameter'].sudo()
        
        return {
            'refresh_interval': int(config.get_param('nebular.dashboard_refresh_interval', '30')),
            'max_events_display': int(config.get_param('nebular.max_events_display', '100')),
            'real_time_enabled': config.get_param('nebular.enable_real_time', 'True') == 'True',
            'notifications_enabled': config.get_param('nebular.enable_notifications', 'True') == 'True'
        }
    
    @api.model
    def get_security_settings(self):
        """Get security-specific settings"""
        config = self.env['ir.config_parameter'].sudo()
        
        return {
            'audit_log_enabled': config.get_param('nebular.enable_audit_log', 'True') == 'True',
            'session_timeout': int(config.get_param('nebular.session_timeout', '480')),
            'max_login_attempts': int(config.get_param('nebular.max_login_attempts', '5')),
            'lockout_duration': int(config.get_param('nebular.lockout_duration', '30'))
        }
    
    @api.model
    def validate_system_health(self):
        """Validate system health and configuration"""
        issues = []
        config = self.env['ir.config_parameter'].sudo()
        
        # Check critical settings
        if not config.get_param('nebular.company_name'):
            issues.append("Company name is not configured")
        
        if config.get_param('nebular.enable_webhook', 'False') == 'True':
            if not config.get_param('nebular.webhook_url'):
                issues.append("Webhooks are enabled but no URL configured")
        
        # Check retention settings
        event_retention = int(config.get_param('nebular.event_retention_days', '180'))
        if event_retention < 7:
            issues.append("Event retention period is too short (minimum 7 days recommended)")
        
        return {
            'healthy': len(issues) == 0,
            'issues': issues,
            'total_issues': len(issues)
        }