# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class NebularDeviceType(models.Model):
    """Device Type Model for Nebular Dashboard System"""
    
    _name = 'nebular.device.type'
    _description = 'Nebular Device Type'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'
    
    # Basic Information
    name = fields.Char(
        string='Device Type Name',
        required=True,
        tracking=True,
        help='Name of the device type'
    )
    
    code = fields.Char(
        string='Type Code',
        required=True,
        tracking=True,
        help='Unique code for the device type'
    )
    
    description = fields.Text(
        string='Description',
        help='Detailed description of the device type'
    )
    
    category = fields.Selection([
        ('sensor', 'Sensor'),
        ('actuator', 'Actuator'),
        ('controller', 'Controller'),
        ('camera', 'Camera'),
        ('access_control', 'Access Control'),
        ('environmental', 'Environmental'),
        ('safety', 'Safety'),
        ('communication', 'Communication'),
        ('security', 'Security'),
        ('fire_safety', 'Fire Safety'),
        ('monitoring', 'Monitoring'),
        ('other', 'Other')
    ], string='Category', required=True, default='other', tracking=True)
    
    # Technical Specifications
    manufacturer = fields.Char(
        string='Manufacturer',
        help='Device manufacturer'
    )
    
    model_number = fields.Char(
        string='Model Number',
        help='Manufacturer model number'
    )
    
    specifications = fields.Text(
        string='Technical Specifications',
        help='Technical specifications and features'
    )
    
    # Configuration
    default_config = fields.Text(
        string='Default Configuration',
        help='Default configuration parameters for this device type'
    )
    
    supported_protocols = fields.Char(
        string='Supported Protocols',
        help='Communication protocols supported by this device type'
    )
    
    # Status and Relationships
    is_active = fields.Boolean(
        string='is_active',
        default=True,
        tracking=True,
        help='Whether this device type is is_active'
    )
    
    device_ids = fields.One2many(
        comodel_name='nebular.device',
        inverse_name='device_type_id',
        string='Devices',
        help='Devices of this type'
    )
    
    device_count = fields.Integer(
        string='Device Count',
        compute='_compute_device_count',
        store=True,
        help='Number of devices of this type'
    )
    
    # Computed Fields
    @api.depends('device_ids')
    def _compute_device_count(self):
        """Compute the number of devices for this type"""
        for record in self:
            record.device_count = len(record.device_ids)
    
    # Constraints
    @api.constrains('code')
    def _check_code_unique(self):
        """Ensure device type code is unique"""
        for record in self:
            if record.code:
                existing = self.search([
                    ('code', '=', record.code),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        f"Device type code '{record.code}' already exists. "
                        "Please use a unique code."
                    )
    
    # Actions
    def action_activate(self):
        """Activate the device type"""
        self.ensure_one()
        self.is_active = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Device type '{self.name}' has been activated.",
                'type': 'success',
            }
        }
    
    def action_deactivate(self):
        """Deactivate the device type"""
        self.ensure_one()
        self.is_active = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Device type '{self.name}' has been deactivated.",
                'type': 'warning',
            }
        }
    
    def action_view_devices(self):
        """Action to view devices of this type"""
        self.ensure_one()
        return {
            'name': f'Devices - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.device',
            'view_mode': 'list,form',
            'domain': [('device_type_id', '=', self.id)],
            'context': {'default_device_type_id': self.id},
        }
    
    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"[{record.code}] {record.name}" if record.code else record.name
            result.append((record.id, name))
        return result