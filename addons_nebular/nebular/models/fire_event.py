# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class NebularFireEvent(models.Model):
    """Fire Event specialized model for fire safety events"""
    
    # Private attributes
    _name = 'nebular.fire.event'
    _description = 'Nebular Fire Event'
    _inherit = ['nebular.event']
    _order = 'event_time desc, create_date desc'
    
    # Additional fields from class diagram
    alarm_type = fields.Selection([
        ('smoke', 'Smoke Alarm'),
        ('heat', 'Heat Alarm'),
        ('flame', 'Flame Alarm'),
        ('gas', 'Gas Alarm'),
        ('manual', 'Manual Alarm'),
        ('other', 'Other'),
    ], string='Alarm Type', help='Type of fire alarm as shown in class diagram')
    
    sensor_status = fields.Selection([
        ('normal', 'Normal'),
        ('alarm', 'Alarm'),
        ('fault', 'Fault'),
        ('maintenance', 'Maintenance'),
        ('disabled', 'Disabled'),
    ], string='Sensor Status', help='Status of the fire sensor')
    
    # Fire-specific fields
    fire_zone = fields.Char(
        string='Fire Zone',
        help='Fire detection zone identifier'
    )
    detector_type = fields.Selection([
        ('smoke', 'Smoke Detector'),
        ('heat', 'Heat Detector'),
        ('flame', 'Flame Detector'),
        ('gas', 'Gas Detector'),
        ('beam', 'Beam Detector'),
        ('aspirating', 'Aspirating Detector'),
        ('manual', 'Manual Call Point'),
        ('other', 'Other'),
    ], string='Detector Type', help='Type of fire detector')
    
    alarm_level = fields.Selection([
        ('pre_alarm', 'Pre-Alarm'),
        ('alarm', 'Alarm'),
        ('fire', 'Fire'),
        ('fault', 'Fault'),
        ('test', 'Test'),
        ('isolate', 'Isolate'),
    ], string='Alarm Level', help='Fire alarm level')
    
    smoke_level = fields.Float(
        string='Smoke Level (%)',
        help='Smoke density percentage'
    )
    temperature = fields.Float(
        string='Temperature (°C)',
        help='Temperature reading at detection'
    )
    
    # Fire safety response
    evacuation_required = fields.Boolean(
        string='Evacuation Required',
        default=False,
        help='Whether evacuation is required'
    )
    evacuation_started = fields.Boolean(
        string='Evacuation Started',
        default=False,
        help='Whether evacuation has been initiated'
    )
    fire_brigade_notified = fields.Boolean(
        string='Fire Brigade Notified',
        default=False,
        help='Whether fire brigade has been notified'
    )
    
    # Response times
    evacuation_time = fields.Datetime(
        string='Evacuation Time',
        help='When evacuation was initiated'
    )
    fire_brigade_arrival = fields.Datetime(
        string='Fire Brigade Arrival',
        help='When fire brigade arrived'
    )
    all_clear_time = fields.Datetime(
        string='All Clear Time',
        help='When all clear was given'
    )
    
    # Fire safety equipment
    sprinkler_activated = fields.Boolean(
        string='Sprinkler Activated',
        default=False,
        help='Whether sprinkler system was activated'
    )
    fire_doors_closed = fields.Boolean(
        string='Fire Doors Closed',
        default=False,
        help='Whether fire doors were automatically closed'
    )
    ventilation_stopped = fields.Boolean(
        string='Ventilation Stopped',
        default=False,
        help='Whether ventilation system was stopped'
    )
    
    # Investigation
    false_alarm = fields.Boolean(
        string='False Alarm',
        default=False,
        help='Whether this was determined to be a false alarm'
    )
    cause_of_alarm = fields.Text(
        string='Cause of Alarm',
        help='Description of what caused the alarm'
    )
    investigation_notes = fields.Text(
        string='Investigation Notes',
        help='Notes from the investigation'
    )
    
    # Computed fields
    response_effectiveness = fields.Selection([
        ('excellent', 'Excellent'),
        ('good', 'Good'),
        ('fair', 'Fair'),
        ('poor', 'Poor'),
    ], string='Response Effectiveness', compute='_compute_response_effectiveness', store=True)
    
    total_response_time = fields.Float(
        string='Total Response Time (minutes)',
        compute='_compute_total_response_time',
        help='Total time from alarm to all clear'
    )
    
    # Default methods
    @api.model
    def default_get(self, fields_list):
        """Set default values for fire events"""
        defaults = super().default_get(fields_list)
        defaults.update({
            'event_type': 'fire',
            'event_category': 'safety',
            'is_alert': True,
            'severity': 'high',
            'priority': '4',
        })
        return defaults
    
    # Compute methods
    @api.depends('evacuation_time', 'fire_brigade_arrival', 'all_clear_time', 'response_time')
    def _compute_response_effectiveness(self):
        """Compute response effectiveness based on response times"""
        for record in self:
            if record.false_alarm:
                record.response_effectiveness = 'good'
            elif record.response_time <= 2:  # 2 minutes
                record.response_effectiveness = 'excellent'
            elif record.response_time <= 5:  # 5 minutes
                record.response_effectiveness = 'good'
            elif record.response_time <= 10:  # 10 minutes
                record.response_effectiveness = 'fair'
            else:
                record.response_effectiveness = 'poor'
    
    @api.depends('event_time', 'all_clear_time')
    def _compute_total_response_time(self):
        """Compute total response time"""
        for record in self:
            if record.event_time and record.all_clear_time:
                delta = record.all_clear_time - record.event_time
                record.total_response_time = delta.total_seconds() / 60.0
            else:
                record.total_response_time = 0.0
    
    # Constraints
    @api.constrains('smoke_level')
    def _check_smoke_level(self):
        """Validate smoke level percentage"""
        for record in self:
            if record.smoke_level and (record.smoke_level < 0 or record.smoke_level > 100):
                raise ValidationError("Smoke level must be between 0 and 100 percent.")
    
    @api.constrains('temperature')
    def _check_temperature(self):
        """Validate temperature reading"""
        for record in self:
            if record.temperature and (record.temperature < -50 or record.temperature > 200):
                raise ValidationError("Temperature must be between -50°C and 200°C.")
    
    @api.constrains('evacuation_time', 'fire_brigade_arrival', 'all_clear_time')
    def _check_fire_time_sequence(self):
        """Ensure fire event times are in logical sequence"""
        for record in self:
            times = []
            if record.event_time:
                times.append(('event_time', record.event_time))
            if record.evacuation_time:
                times.append(('evacuation_time', record.evacuation_time))
            if record.fire_brigade_arrival:
                times.append(('fire_brigade_arrival', record.fire_brigade_arrival))
            if record.all_clear_time:
                times.append(('all_clear_time', record.all_clear_time))
            
            # Sort by time and check sequence
            times.sort(key=lambda x: x[1])
            
            # Evacuation should not be before event
            if record.evacuation_time and record.event_time:
                if record.evacuation_time < record.event_time:
                    raise ValidationError("Evacuation time cannot be before event time.")
            
            # All clear should be after event
            if record.all_clear_time and record.event_time:
                if record.all_clear_time < record.event_time:
                    raise ValidationError("All clear time cannot be before event time.")
    
    # Onchange methods
    @api.onchange('alarm_level')
    def _onchange_alarm_level(self):
        """Update fields based on alarm level"""
        if self.alarm_level in ['alarm', 'fire']:
            self.evacuation_required = True
            self.is_alert = True
            self.severity = 'critical' if self.alarm_level == 'fire' else 'high'
        elif self.alarm_level == 'pre_alarm':
            self.evacuation_required = False
            self.severity = 'medium'
        elif self.alarm_level in ['fault', 'test', 'isolate']:
            self.evacuation_required = False
            self.is_alert = False
            self.severity = 'low'
    
    @api.onchange('detector_type')
    def _onchange_detector_type(self):
        """Set appropriate fields based on detector type"""
        if self.detector_type == 'smoke':
            self.smoke_level = 0.0
        elif self.detector_type in ['heat', 'flame']:
            self.temperature = 0.0
    
    @api.onchange('evacuation_required', 'evacuation_started')
    def _onchange_evacuation(self):
        """Update evacuation time when evacuation is started"""
        if self.evacuation_started and not self.evacuation_time:
            self.evacuation_time = fields.Datetime.now()
    
    # Action methods
    def action_start_evacuation(self):
        """Start evacuation procedure"""
        self.ensure_one()
        self.evacuation_started = True
        self.evacuation_time = fields.Datetime.now()
        self.message_post(
            body=f"Evacuation started for fire event '{self.name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Evacuation started for '{self.name}'.",
                'type': 'warning',
            }
        }
    
    def action_notify_fire_brigade(self):
        """Notify fire brigade"""
        self.ensure_one()
        self.fire_brigade_notified = True
        self.message_post(
            body=f"Fire brigade notified for fire event '{self.name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Fire brigade notified for '{self.name}'.",
                'type': 'success',
            }
        }
    
    def action_fire_brigade_arrived(self):
        """Mark fire brigade arrival"""
        self.ensure_one()
        self.fire_brigade_arrival = fields.Datetime.now()
        self.message_post(
            body=f"Fire brigade arrived for fire event '{self.name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Fire brigade arrived for '{self.name}'.",
                'type': 'info',
            }
        }
    
    def action_all_clear(self):
        """Give all clear signal"""
        self.ensure_one()
        self.all_clear_time = fields.Datetime.now()
        self.state = 'resolved'
        self.resolved_time = fields.Datetime.now()
        self.resolved_user_id = self.env.user.id
        self.is_active = False
        self.message_post(
            body=f"All clear given for fire event '{self.name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"All clear given for '{self.name}'.",
                'type': 'success',
            }
        }
    
    def action_mark_false_alarm(self):
        """Mark as false alarm"""
        self.ensure_one()
        self.false_alarm = True
        self.state = 'resolved'
        self.resolved_time = fields.Datetime.now()
        self.resolved_user_id = self.env.user.id
        self.is_active = False
        self.message_post(
            body=f"Fire event '{self.name}' marked as false alarm.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"'{self.name}' marked as false alarm.",
                'type': 'info',
            }
        }
    
    def action_activate_sprinkler(self):
        """Activate sprinkler system"""
        self.ensure_one()
        self.sprinkler_activated = True
        self.message_post(
            body=f"Sprinkler system activated for fire event '{self.name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Sprinkler system activated for '{self.name}'.",
                'type': 'warning',
            }
        }
    
    def action_close_fire_doors(self):
        """Close fire doors"""
        self.ensure_one()
        self.fire_doors_closed = True
        self.message_post(
            body=f"Fire doors closed for fire event '{self.name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Fire doors closed for '{self.name}'.",
                'type': 'info',
            }
        }
    
    def action_stop_ventilation(self):
        """Stop ventilation system"""
        self.ensure_one()
        self.ventilation_stopped = True
        self.message_post(
            body=f"Ventilation stopped for fire event '{self.name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Ventilation stopped for '{self.name}'.",
                'type': 'info',
            }
        }
    
    # Other business methods
    def get_fire_event_summary(self):
        """Get fire event specific summary"""
        self.ensure_one()
        summary = self.get_event_summary()
        summary.update({
            'fire_zone': self.fire_zone,
            'detector_type': self.detector_type,
            'alarm_level': self.alarm_level,
            'smoke_level': self.smoke_level,
            'temperature': self.temperature,
            'evacuation_required': self.evacuation_required,
            'evacuation_started': self.evacuation_started,
            'fire_brigade_notified': self.fire_brigade_notified,
            'evacuation_time': self.evacuation_time,
            'fire_brigade_arrival': self.fire_brigade_arrival,
            'all_clear_time': self.all_clear_time,
            'sprinkler_activated': self.sprinkler_activated,
            'fire_doors_closed': self.fire_doors_closed,
            'ventilation_stopped': self.ventilation_stopped,
            'false_alarm': self.false_alarm,
            'cause_of_alarm': self.cause_of_alarm,
            'response_effectiveness': self.response_effectiveness,
            'total_response_time': self.total_response_time,
        })
        return summary
    
    def generate_fire_report(self):
        """Generate fire incident report"""
        self.ensure_one()
        report_data = {
            'incident_id': self.event_code,
            'incident_time': self.event_time,
            'location': self.location_display,
            'detector_info': {
                'type': self.detector_type,
                'zone': self.fire_zone,
                'alarm_level': self.alarm_level,
            },
            'readings': {
                'smoke_level': self.smoke_level,
                'temperature': self.temperature,
            },
            'response': {
                'evacuation_required': self.evacuation_required,
                'evacuation_started': self.evacuation_started,
                'evacuation_time': self.evacuation_time,
                'fire_brigade_notified': self.fire_brigade_notified,
                'fire_brigade_arrival': self.fire_brigade_arrival,
                'all_clear_time': self.all_clear_time,
            },
            'systems': {
                'sprinkler_activated': self.sprinkler_activated,
                'fire_doors_closed': self.fire_doors_closed,
                'ventilation_stopped': self.ventilation_stopped,
            },
            'investigation': {
                'false_alarm': self.false_alarm,
                'cause': self.cause_of_alarm,
                'notes': self.investigation_notes,
            },
            'performance': {
                'response_time': self.response_time,
                'total_response_time': self.total_response_time,
                'effectiveness': self.response_effectiveness,
            }
        }
        return report_data