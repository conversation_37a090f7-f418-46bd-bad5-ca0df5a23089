# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class NebularPAEvent(models.Model):
    """PA (Public Address) Event specialized model for PA system events"""
    
    # Private attributes
    _name = 'nebular.pa.event'
    _description = 'Nebular PA Event'
    _inherit = ['nebular.event']
    _order = 'event_time desc, create_date desc'
    
    # PA-specific fields from class diagram
    zone_id = fields.Integer(
        string='PA Zone ID',
        help='PA system zone identifier'
    )
    
    zone_code = fields.Char(
        string='Zone Code',
        required=True,
        help='PA zone code identifier'
    )
    
    zone_name = fields.Char(
        string='Zone Name',
        help='Descriptive name of the PA zone'
    )
    
    location = fields.Char(
        string='Location',
        help='Physical location description of the PA zone'
    )
    
    volume = fields.Integer(
        string='Volume Level',
        help='Volume level (0-100)'
    )
    
    pa_message = fields.Text(
        string='PA Message',
        help='The message content for PA announcement'
    )
    
    announcement_id = fields.Char(
        string='Announcement ID',
        help='Unique identifier for the announcement'
    )
    
    script = fields.Char(
        string='Script',
        help='Script or template used for the announcement'
    )
    
    duration_sec = fields.Integer(
        string='Duration (seconds)',
        help='Duration of the announcement in seconds'
    )
    
    # Additional useful fields
    announcement_type = fields.Selection([
        ('emergency', 'Emergency'),
        ('evacuation', 'Evacuation'),
        ('general', 'General Announcement'),
        ('music', 'Background Music'),
        ('paging', 'Paging'),
        ('test', 'Test Announcement'),
        ('scheduled', 'Scheduled Announcement'),
    ], string='Announcement Type', help='Type of PA announcement')
    
    priority = fields.Selection([
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('emergency', 'Emergency'),
    ], string='Priority', default='normal', help='Priority level of the announcement')
    
    language = fields.Selection([
        ('en', 'English'),
        ('ar', 'Arabic'),
        ('fr', 'French'),
        ('es', 'Spanish'),
        ('multi', 'Multi-language'),
    ], string='Language', default='en', help='Language of the announcement')
    
    status = fields.Selection([
        ('scheduled', 'Scheduled'),
        ('playing', 'Playing'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('failed', 'Failed'),
    ], string='Status', default='scheduled', help='Status of the PA announcement')
    
    repeat_count = fields.Integer(
        string='Repeat Count',
        default=1,
        help='Number of times to repeat the announcement'
    )
    
    # Constraints and validations
    @api.constrains('volume')
    def _check_volume_range(self):
        """Validate volume is between 0 and 100"""
        for record in self:
            if record.volume and (record.volume < 0 or record.volume > 100):
                raise ValidationError("Volume level must be between 0 and 100")
    
    @api.constrains('duration_sec')
    def _check_duration_positive(self):
        """Validate duration is positive"""
        for record in self:
            if record.duration_sec and record.duration_sec <= 0:
                raise ValidationError("Duration must be a positive number")
    
    @api.constrains('repeat_count')
    def _check_repeat_count_positive(self):
        """Validate repeat count is positive"""
        for record in self:
            if record.repeat_count and record.repeat_count <= 0:
                raise ValidationError("Repeat count must be a positive number")
    
    # Methods
    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"[{record.zone_code}] {record.zone_name or 'PA Event'}"
            if record.announcement_type:
                name += f" - {record.announcement_type.title()}"
            result.append((record.id, name))
        return result
    
    @api.model
    def create_pa_event(self, pa_data):
        """Create PA event from external system data"""
        event_vals = {
            'event_type': 'pa',
            'system_id': 'pa',
            'system_name': 'Public Address System',
            'building_id': pa_data.get('building_id'),
            'zone_id': pa_data.get('zone_id'),
            'floor_id': pa_data.get('floor_id'),
            'event_time': pa_data.get('datetime', fields.Datetime.now()),
            'message': pa_data.get('message', ''),
            'severity': pa_data.get('severity', 1),
        }
        
        pa_vals = {
            'zone_id': pa_data.get('pa_zone_id'),
            'zone_code': pa_data.get('zone_code'),
            'zone_name': pa_data.get('zone_name'),
            'location': pa_data.get('location'),
            'volume': pa_data.get('volume'),
            'message': pa_data.get('pa_message'),
            'announcement_id': pa_data.get('announcement_id'),
            'script': pa_data.get('script'),
            'duration_sec': pa_data.get('duration_sec'),
            'announcement_type': pa_data.get('announcement_type'),
            'priority': pa_data.get('priority', 'normal'),
            'language': pa_data.get('language', 'en'),
            'status': pa_data.get('status', 'scheduled'),
            'repeat_count': pa_data.get('repeat_count', 1),
        }
        
        # Merge the dictionaries
        vals = {**event_vals, **pa_vals}
        return self.create(vals)
    
    def action_play_announcement(self):
        """Action to play the announcement"""
        self.ensure_one()
        if self.status == 'scheduled':
            self.status = 'playing'
            # Here you would integrate with the actual PA system
            # For now, we just update the status
            return True
        return False
    
    def action_cancel_announcement(self):
        """Action to cancel the announcement"""
        self.ensure_one()
        if self.status in ['scheduled', 'playing']:
            self.status = 'cancelled'
            return True
        return False
    
    def action_complete_announcement(self):
        """Action to mark announcement as completed"""
        self.ensure_one()
        if self.status == 'playing':
            self.status = 'completed'
            return True
        return False