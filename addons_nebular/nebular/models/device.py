# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import timedelta
import json


class NebularDevice(models.Model):
    """Device model for the Nebular Dashboard System"""
    
    # Private attributes
    _name = 'nebular.device'
    _description = 'Nebular Device'
    _inherit = ['mail.thread', 'mail.activity.schedule']
    _order = 'building_id, zone_id, floor_id, room_id, name'
    _rec_name = 'name'
    
    # Field declarations
    name = fields.Char(
        string='Device Name',
        required=True,
        tracking=True,
        help='Name or identifier of the device'
    )
    device_id = fields.Char(
        string='Device ID',
        required=True,
        tracking=True,
        help='Unique device identifier'
    )
    description = fields.Text(
        string='Description',
        help='Detailed description of the device'
    )
    
    # Device specifications
    device_type = fields.Selection([
        ('camera', 'CCTV Camera'),
        ('card_reader', 'Card Reader'),
        ('sensor', 'Sensor'),
        ('fire_detector', 'Fire Detector'),
        ('smoke_detector', 'Smoke Detector'),
        ('motion_detector', 'Motion Detector'),
        ('temperature_sensor', 'Temperature Sensor'),
        ('humidity_sensor', 'Humidity Sensor'),
        ('access_control', 'Access Control'),
        ('intercom', 'Intercom'),
        ('speaker', 'PA Speaker'),
        ('microphone', 'Microphone'),
        ('alarm', 'Alarm'),
        ('beacon', 'Beacon'),
        ('gateway', 'Gateway'),
        ('controller', 'Controller'),
        ('other', 'Other'),
    ], string='Device Type', required=True, tracking=True, help='Type of device')
    
    device_category = fields.Selection([
        ('security', 'Security'),
        ('fire_safety', 'Fire Safety'),
        ('access_control', 'Access Control'),
        ('surveillance', 'Surveillance'),
        ('environmental', 'Environmental'),
        ('communication', 'Communication'),
        ('automation', 'Automation'),
        ('monitoring', 'Monitoring'),
        ('other', 'Other'),
    ], string='Category', compute='_compute_device_category', store=True, help='Device category')
    
    manufacturer = fields.Char(
        string='Manufacturer',
        help='Device manufacturer'
    )
    model = fields.Char(
        string='Model',
        help='Device model'
    )
    serial_number = fields.Char(
        string='Serial Number',
        help='Device serial number'
    )
    firmware_version = fields.Char(
        string='Firmware Version',
        help='Current firmware version'
    )
    
    # Network and connectivity
    ip_address = fields.Char(
        string='IP Address',
        help='Device IP address'
    )
    mac_address = fields.Char(
        string='MAC Address',
        help='Device MAC address'
    )
    port = fields.Integer(
        string='Port',
        help='Network port'
    )
    protocol = fields.Selection([
        ('http', 'HTTP'),
        ('https', 'HTTPS'),
        ('tcp', 'TCP'),
        ('udp', 'UDP'),
        ('mqtt', 'MQTT'),
        ('websocket', 'WebSocket'),
        ('modbus', 'Modbus'),
        ('bacnet', 'BACnet'),
        ('other', 'Other'),
    ], string='Protocol', help='Communication protocol')
    
    # Status and operational fields
    is_active = fields.Boolean(
        string='is_active',
        default=True,
        tracking=True,
        help='Whether this device is is_active'
    )
    is_online = fields.Boolean(
        string='Online',
        default=False,
        help='Whether the device is currently online'
    )
    is_alert = fields.Boolean(
        string='Alert Status',
        default=False,
        help='Whether there is an is_active alert for this device'
    )
    last_seen = fields.Datetime(
        string='Last Seen',
        help='Last time the device was seen online'
    )
    
    # Configuration and settings
    configuration = fields.Text(
        string='Configuration',
        help='Device configuration in JSON format'
    )
    settings = fields.Text(
        string='Settings',
        help='Device settings in JSON format'
    )
    
    # Relationships
    device_type_id = fields.Many2one(
        comodel_name='nebular.device.type',
        string='Device Type',
        required=True,
        ondelete='restrict',
        tracking=True,
        help='Type of this device'
    )
    building_id = fields.Many2one(
        comodel_name='nebular.building',
        string='Building',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Building this device is located in'
    )
    zone_id = fields.Many2one(
        comodel_name='nebular.zone',
        string='Zone',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Zone this device is located in'
    )
    floor_id = fields.Many2one(
        comodel_name='nebular.floor',
        string='Floor',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Floor this device is located on'
    )
    room_id = fields.Many2one(
        comodel_name='nebular.room',
        string='Room',
        ondelete='cascade',
        tracking=True,
        help='Room this device is located in (optional for corridor devices)'
    )
    door_id = fields.Many2one(
        comodel_name='nebular.door',
        string='Door',
        ondelete='cascade',
        help='Door this device is associated with (for access control devices)'
    )
    system_id = fields.Many2one(
        comodel_name='nebular.system',
        string='System',
        ondelete='cascade',
        tracking=True,
        help='System this device belongs to'
    )
    event_ids = fields.One2many(
        comodel_name='nebular.event',
        inverse_name='device_id',
        string='Events',
        help='Events generated by this device'
    )
    
    # Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Full display name including location hierarchy'
    )
    event_count = fields.Integer(
        string='Event Count',
        compute='_compute_event_count',
        help='Number of recent events from this device'
    )
    status_display = fields.Char(
        string='Status',
        compute='_compute_status_display',
        help='Current device status display'
    )
    connection_status = fields.Char(
        string='Connection Status',
        compute='_compute_connection_status',
        help='Connection status description'
    )
    uptime_display = fields.Char(
        string='Uptime',
        compute='_compute_uptime_display',
        help='Device uptime display'
    )
    
    # Compute methods
    @api.depends('device_type')
    def _compute_device_category(self):
        """Compute device category based on device type"""
        category_mapping = {
            'camera': 'surveillance',
            'card_reader': 'access_control',
            'sensor': 'monitoring',
            'fire_detector': 'fire_safety',
            'smoke_detector': 'fire_safety',
            'motion_detector': 'security',
            'temperature_sensor': 'environmental',
            'humidity_sensor': 'environmental',
            'access_control': 'access_control',
            'intercom': 'communication',
            'speaker': 'communication',
            'microphone': 'communication',
            'alarm': 'security',
            'beacon': 'monitoring',
            'gateway': 'automation',
            'controller': 'automation',
        }
        
        for record in self:
            record.device_category = category_mapping.get(record.device_type, 'other')
    
    @api.depends('name', 'device_id', 'room_id.name', 'door_id.name', 'floor_id.name', 'zone_id.name', 'building_id.name')
    def _compute_display_name(self):
        """Compute display name with location hierarchy"""
        for record in self:
            parts = []
            if record.building_id:
                parts.append(record.building_id.name)
            if record.zone_id:
                parts.append(record.zone_id.name)
            if record.floor_id:
                parts.append(f"Floor {record.floor_id.floor_number}")
            if record.room_id:
                parts.append(record.room_id.name)
            elif record.door_id:
                parts.append(f"Door {record.door_id.name}")
            
            device_name = f"{record.name} ({record.device_id})"
            parts.append(device_name)
            
            record.display_name = " - ".join(parts) if parts else ''
    
    @api.depends('event_ids')
    def _compute_event_count(self):
        """Compute recent event count (last 30 days)"""
        for record in self:
            domain = [
                ('device_id', '=', record.id),
                ('create_date', '>=', fields.Datetime.now() - timedelta(days=30))
            ]
            record.event_count = self.env['nebular.event'].search_count(domain)
    
    @api.depends('is_online', 'is_alert', 'is_active')
    def _compute_status_display(self):
        """Compute status display"""
        for record in self:
            if not record.is_active:
                record.status_display = 'Inis_active'
            elif record.is_alert:
                record.status_display = 'Alert'
            elif record.is_online:
                record.status_display = 'Online'
            else:
                record.status_display = 'Offline'
    
    @api.depends('is_online', 'last_seen', 'ip_address', 'protocol')
    def _compute_connection_status(self):
        """Compute connection status"""
        for record in self:
            if record.is_online:
                if record.ip_address and record.protocol:
                    record.connection_status = f"Connected via {record.protocol.upper()} ({record.ip_address})"
                else:
                    record.connection_status = "Connected"
            else:
                if record.last_seen:
                    record.connection_status = f"Disconnected (Last seen: {record.last_seen})"
                else:
                    record.connection_status = "Never connected"
    
    @api.depends('last_seen', 'is_online')
    def _compute_uptime_display(self):
        """Compute uptime display"""
        for record in self:
            if record.is_online and record.last_seen:
                delta = fields.Datetime.now() - record.last_seen
                days = delta.days
                hours, remainder = divmod(delta.seconds, 3600)
                minutes, _ = divmod(remainder, 60)
                
                if days > 0:
                    record.uptime_display = f"{days}d {hours}h {minutes}m"
                elif hours > 0:
                    record.uptime_display = f"{hours}h {minutes}m"
                else:
                    record.uptime_display = f"{minutes}m"
            else:
                record.uptime_display = "N/A"
    
    # Constraints
    @api.constrains('device_id')
    def _check_device_id_unique(self):
        """Ensure device ID is unique"""
        for record in self:
            if record.device_id:
                existing = self.search([
                    ('device_id', '=', record.device_id),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        f"Device ID '{record.device_id}' already exists."
                    )
    
    @api.constrains('zone_id', 'building_id', 'floor_id', 'room_id', 'door_id')
    def _check_hierarchy_consistency(self):
        """Ensure all relationships are consistent with hierarchy"""
        for record in self:
            if record.zone_id and record.building_id:
                if record.zone_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Zone '{record.zone_id.name}' does not belong to building '{record.building_id.name}'."
                    )
            if record.floor_id and record.building_id:
                if record.floor_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Floor '{record.floor_id.name}' does not belong to building '{record.building_id.name}'."
                    )
            if record.floor_id and record.zone_id:
                if record.floor_id.zone_id != record.zone_id:
                    raise ValidationError(
                        f"Floor '{record.floor_id.name}' does not belong to zone '{record.zone_id.name}'."
                    )
            if record.room_id:
                if record.room_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Room '{record.room_id.name}' does not belong to building '{record.building_id.name}'."
                    )
                if record.room_id.zone_id != record.zone_id:
                    raise ValidationError(
                        f"Room '{record.room_id.name}' does not belong to zone '{record.zone_id.name}'."
                    )
                if record.room_id.floor_id != record.floor_id:
                    raise ValidationError(
                        f"Room '{record.room_id.name}' is not on floor '{record.floor_id.name}'."
                    )
            if record.door_id:
                if record.door_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Door '{record.door_id.name}' does not belong to building '{record.building_id.name}'."
                    )
                if record.door_id.zone_id != record.zone_id:
                    raise ValidationError(
                        f"Door '{record.door_id.name}' does not belong to zone '{record.zone_id.name}'."
                    )
                if record.door_id.floor_id != record.floor_id:
                    raise ValidationError(
                        f"Door '{record.door_id.name}' is not on floor '{record.floor_id.name}'."
                    )
    
    @api.constrains('ip_address')
    def _check_ip_address_format(self):
        """Validate IP address format"""
        import re
        ip_pattern = re.compile(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
        
        for record in self:
            if record.ip_address and not ip_pattern.match(record.ip_address):
                raise ValidationError(f"Invalid IP address format: {record.ip_address}")
    
    @api.constrains('port')
    def _check_port_range(self):
        """Ensure port is in valid range"""
        for record in self:
            if record.port is not False and (record.port < 1 or record.port > 65535):
                raise ValidationError("Port must be between 1 and 65535.")
    
    @api.constrains('configuration', 'settings')
    def _check_json_format(self):
        """Validate JSON format for configuration and settings"""
        for record in self:
            if record.configuration:
                try:
                    json.loads(record.configuration)
                except json.JSONDecodeError:
                    raise ValidationError("Configuration must be valid JSON format.")
            if record.settings:
                try:
                    json.loads(record.settings)
                except json.JSONDecodeError:
                    raise ValidationError("Settings must be valid JSON format.")
    
    @api.constrains('name')
    def _check_name_not_empty(self):
        """Ensure device name is not empty"""
        for record in self:
            if not record.name or not record.name.strip():
                raise ValidationError("Device name cannot be empty.")
    
    # Onchange methods
    @api.onchange('building_id')
    def _onchange_building_id(self):
        """Filter zones, floors, rooms, and doors based on selected building"""
        if self.building_id:
            zone_domain = [('building_id', '=', self.building_id.id)]
            floor_domain = [('building_id', '=', self.building_id.id)]
            room_domain = [('building_id', '=', self.building_id.id)]
            door_domain = [('building_id', '=', self.building_id.id)]
            
            if self.zone_id and self.zone_id.building_id != self.building_id:
                self.zone_id = False
            if self.floor_id and self.floor_id.building_id != self.building_id:
                self.floor_id = False
            if self.room_id and self.room_id.building_id != self.building_id:
                self.room_id = False
            if self.door_id and self.door_id.building_id != self.building_id:
                self.door_id = False
                
            return {
                'domain': {
                    'zone_id': zone_domain,
                    'floor_id': floor_domain,
                    'room_id': room_domain,
                    'door_id': door_domain
                }
            }
        else:
            self.zone_id = False
            self.floor_id = False
            self.room_id = False
            self.door_id = False
            return {
                'domain': {
                    'zone_id': [],
                    'floor_id': [],
                    'room_id': [],
                    'door_id': []
                }
            }
    
    @api.onchange('zone_id')
    def _onchange_zone_id(self):
        """Filter floors, rooms, and doors based on selected zone"""
        if self.zone_id:
            floor_domain = [('zone_id', '=', self.zone_id.id)]
            room_domain = [('zone_id', '=', self.zone_id.id)]
            door_domain = [('zone_id', '=', self.zone_id.id)]
            
            if self.floor_id and self.floor_id.zone_id != self.zone_id:
                self.floor_id = False
            if self.room_id and self.room_id.zone_id != self.zone_id:
                self.room_id = False
            if self.door_id and self.door_id.zone_id != self.zone_id:
                self.door_id = False
                
            return {
                'domain': {
                    'floor_id': floor_domain,
                    'room_id': room_domain,
                    'door_id': door_domain
                }
            }
        else:
            return {
                'domain': {
                    'floor_id': [],
                    'room_id': [],
                    'door_id': []
                }
            }
    
    @api.onchange('floor_id')
    def _onchange_floor_id(self):
        """Filter rooms and doors based on selected floor"""
        if self.floor_id:
            room_domain = [('floor_id', '=', self.floor_id.id)]
            door_domain = [('floor_id', '=', self.floor_id.id)]
            
            if self.room_id and self.room_id.floor_id != self.floor_id:
                self.room_id = False
            if self.door_id and self.door_id.floor_id != self.floor_id:
                self.door_id = False
                
            return {
                'domain': {
                    'room_id': room_domain,
                    'door_id': door_domain
                }
            }
        else:
            return {
                'domain': {
                    'room_id': [],
                    'door_id': []
                }
            }
    
    # CRUD overrides
    def create(self, vals_list):
        """Override create to add tracking message"""
        devices = super().create(vals_list)
        for device in devices:
            location_parts = []
            if device.room_id:
                location_parts.append(f"room {device.room_id.name}")
            elif device.door_id:
                location_parts.append(f"door {device.door_id.name}")
            location_parts.append(f"floor {device.floor_id.name}")
            location = " on ".join(location_parts)
            
            device.message_post(
                body=f"Device '{device.name}' ({device.device_type}) has been created at {location}.",
                message_type='notification'
            )
        return devices
    
    def write(self, vals):
        """Override write to add tracking for important changes"""
        result = super().write(vals)
        if 'is_online' in vals:
            for record in self:
                status = 'online' if vals['is_online'] else 'offline'
                record.message_post(
                    body=f"Device '{record.name}' is now {status}.",
                    message_type='notification'
                )
        if 'is_alert' in vals:
            for record in self:
                if vals['is_alert']:
                    record.message_post(
                        body=f"Alert activated for device '{record.name}'.",
                        message_type='notification'
                    )
                else:
                    record.message_post(
                        body=f"Alert cleared for device '{record.name}'.",
                        message_type='notification'
                    )
        return result
    
    # Action methods
    def action_activate(self):
        """Activate the device"""
        self.ensure_one()
        self.is_active = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Device '{self.name}' has been activated.",
                'type': 'success',
            }
        }
    
    def action_deactivate(self):
        """Deactivate the device"""
        self.ensure_one()
        self.is_active = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Device '{self.name}' has been deactivated.",
                'type': 'warning',
            }
        }
    
    def action_sync(self):
        """Synchronize device data"""
        self.ensure_one()
        # This would typically integrate with actual device sync functionality
        self.last_seen = fields.Datetime.now()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Device '{self.name}' synchronized successfully.",
                'type': 'info',
            }
        }
    
    def action_ping(self):
        """Ping the device to check connectivity"""
        self.ensure_one()
        # This would typically integrate with actual device ping functionality
        self.last_seen = fields.Datetime.now()
        self.is_online = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Device '{self.name}' pinged successfully.",
                'type': 'success',
            }
        }
    
    def action_reset(self):
        """Reset the device"""
        self.ensure_one()
        # This would typically integrate with actual device reset functionality
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Reset command sent to device '{self.name}'.",
                'type': 'info',
            }
        }
    
    def action_view_events(self):
        """Action to view events from this device"""
        self.ensure_one()
        return {
            'name': f'Events - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.event',
            'view_mode': 'list,form',
            'domain': [('device_id', '=', self.id)],
            'context': {
                'default_device_id': self.id,
                'default_room_id': self.room_id.id if self.room_id else False,
                'default_door_id': self.door_id.id if self.door_id else False,
                'default_floor_id': self.floor_id.id,
                'default_zone_id': self.zone_id.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    # Other business methods
    def get_device_summary(self):
        """Get a summary of device statistics"""
        self.ensure_one()
        return {
            'name': self.name,
            'device_id': self.device_id,
            'device_type': self.device_type,
            'device_category': self.device_category,
            'manufacturer': self.manufacturer,
            'model': self.model,
            'building': self.building_id.name,
            'zone': self.zone_id.name,
            'floor': self.floor_id.name,
            'room': self.room_id.name if self.room_id else None,
            'door': self.door_id.name if self.door_id else None,
            'is_online': self.is_online,
            'is_alert': self.is_alert,
            'last_seen': self.last_seen,
            'ip_address': self.ip_address,
            'protocol': self.protocol,
            'events': self.event_count,
            'status': self.status_display,
            'connection_status': self.connection_status,
            'uptime': self.uptime_display,
        }
    
    def update_configuration(self, config_dict):
        """Update device configuration"""
        self.ensure_one()
        self.configuration = json.dumps(config_dict, indent=2)
        self.message_post(
            body=f"Configuration updated for device '{self.name}'.",
            message_type='notification'
        )
    
    def update_settings(self, settings_dict):
        """Update device settings"""
        self.ensure_one()
        self.settings = json.dumps(settings_dict, indent=2)
        self.message_post(
            body=f"Settings updated for device '{self.name}'.",
            message_type='notification'
        )