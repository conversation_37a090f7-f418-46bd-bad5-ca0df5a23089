# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import timedelta
import json


class NebularEvent(models.Model):
    """Main Event model for the Nebular Dashboard System"""
    
    # Private attributes
    _name = 'nebular.event'
    _description = 'Nebular Event'
    _inherit = ['mail.thread']
    _order = 'event_time desc, create_date desc'
    _rec_name = 'name'
    
    # Field declarations
    name = fields.Char(
        string='Event Name',
        required=True,
        tracking=True,
        help='Name or title of the event'
    )
    event_code = fields.Char(
        string='Event Code',
        required=True,
        tracking=True,
        help='Unique event identifier from the system'
    )
    description = fields.Text(
        string='Description',
        help='Detailed description of the event'
    )
    
    # Event classification
    event_type = fields.Selection([
        ('fire', 'Fire Event'),
        ('access', 'Access Event'),
        ('cctv', 'CCTV Event'),
        ('gate', 'Gate Event'),
        ('pa', 'PA Event'),
        ('presence', 'Presence Event'),
        ('system', 'System Event'),
        ('alarm', 'Alarm Event'),
        ('maintenance', 'Maintenance Event'),
        ('other', 'Other'),
    ], string='Event Type', required=True, tracking=True, help='Type of event')
    
    event_category = fields.Selection([
        ('security', 'Security'),
        ('safety', 'Safety'),
        ('access_control', 'Access Control'),
        ('surveillance', 'Surveillance'),
        ('communication', 'Communication'),
        ('system', 'System'),
        ('maintenance', 'Maintenance'),
        ('other', 'Other'),
    ], string='Category', compute='_compute_event_category', store=True, help='Event category')
    
    severity = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ], string='Severity', default='medium', tracking=True, help='Event severity level')
    
    priority = fields.Selection([
        ('0', 'Very Low'),
        ('1', 'Low'),
        ('2', 'Normal'),
        ('3', 'High'),
        ('4', 'Very High'),
    ], string='Priority', default='2', tracking=True, help='Event priority')
    
    # Status and operational fields
    state = fields.Selection([
        ('new', 'New'),
        ('acknowledged', 'Acknowledged'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
        ('cancelled', 'Cancelled'),
    ], string='State', default='new', tracking=True, help='Current state of the event')
    
    is_alert = fields.Boolean(
        string='Is Alert',
        default=False,
        tracking=True,
        help='Whether this event is an alert that requires attention'
    )
    is_active = fields.Boolean(
        string='Active',
        default=True,
        help='Whether this event is currently active'
    )
    
    # Timing fields
    event_time = fields.Datetime(
        string='Event Time',
        required=True,
        default=fields.Datetime.now,
        tracking=True,
        help='When the event occurred'
    )
    acknowledged_time = fields.Datetime(
        string='Acknowledged Time',
        help='When the event was acknowledged'
    )
    resolved_time = fields.Datetime(
        string='Resolved Time',
        help='When the event was resolved'
    )
    closed_time = fields.Datetime(
        string='Closed Time',
        help='When the event was closed'
    )
    
    # Metadata
    metadata = fields.Text(
        string='Metadata',
        help='Additional metadata in JSON format'
    )
    
    # Relationships
    building_id = fields.Many2one(
        comodel_name='nebular.building',
        string='Building',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Building where the event occurred'
    )
    zone_id = fields.Many2one(
        comodel_name='nebular.zone',
        string='Zone',
        ondelete='cascade',
        tracking=True,
        help='Zone where the event occurred'
    )
    floor_id = fields.Many2one(
        comodel_name='nebular.floor',
        string='Floor',
        ondelete='cascade',
        tracking=True,
        help='Floor where the event occurred'
    )
    room_id = fields.Many2one(
        comodel_name='nebular.room',
        string='Room',
        ondelete='cascade',
        help='Room where the event occurred'
    )
    door_id = fields.Many2one(
        comodel_name='nebular.door',
        string='Door',
        ondelete='cascade',
        help='Door related to the event'
    )
    device_id = fields.Many2one(
        comodel_name='nebular.device',
        string='Device',
        ondelete='cascade',
        help='Device that generated the event'
    )
    system_id = fields.Many2one(
        comodel_name='nebular.system',
        string='System',
        ondelete='cascade',
        tracking=True,
        help='System that generated the event'
    )
    
    # User assignment
    assigned_user_id = fields.Many2one(
        comodel_name='res.users',
        string='Assigned To',
        tracking=True,
        help='User assigned to handle this event'
    )
    acknowledged_user_id = fields.Many2one(
        comodel_name='res.users',
        string='Acknowledged By',
        help='User who acknowledged the event'
    )
    resolved_user_id = fields.Many2one(
        comodel_name='res.users',
        string='Resolved By',
        help='User who resolved the event'
    )
    
    marker_id = fields.Many2one(
        'nebular.marker',
        string='Related Marker',
        help='Marker related to this event'
    )
    
    # Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Full display name with location and time'
    )
    location_display = fields.Char(
        string='Location',
        compute='_compute_location_display',
        store=True,
        help='Event location display'
    )
    duration = fields.Float(
        string='Duration (hours)',
        compute='_compute_duration',
        help='Duration from event time to resolution'
    )
    response_time = fields.Float(
        string='Response Time (minutes)',
        compute='_compute_response_time',
        help='Time from event to acknowledgment'
    )
    age_display = fields.Char(
        string='Age',
        compute='_compute_age_display',
        help='How long ago the event occurred'
    )
    status_color = fields.Integer(
        string='Status Color',
        compute='_compute_status_color',
        help='Color code for status display'
    )
    
    # Compute methods
    @api.depends('event_type')
    def _compute_event_category(self):
        """Compute event category based on event type"""
        category_mapping = {
            'fire': 'safety',
            'access': 'access_control',
            'cctv': 'surveillance',
            'gate': 'access_control',
            'pa': 'communication',
            'presence': 'surveillance',
            'system': 'system',
            'alarm': 'security',
            'maintenance': 'maintenance',
        }
        
        for record in self:
            record.event_category = category_mapping.get(record.event_type, 'other')
    
    @api.depends('name', 'event_type', 'event_time', 'building_id.name', 'zone_id.name')
    def _compute_display_name(self):
        """Compute display name with event info and location"""
        for record in self:
            parts = [record.name]
            if record.event_type:
                parts.append(f"({record.event_type.title()})")
            if record.building_id:
                parts.append(f"- {record.building_id.name}")
            if record.zone_id:
                parts.append(f"/ {record.zone_id.name}")
            if record.event_time:
                parts.append(f"at {record.event_time.strftime('%Y-%m-%d %H:%M')}")
            
            record.display_name = " ".join(parts) if parts else ''
    
    @api.depends('building_id.name', 'zone_id.name', 'floor_id.name', 'room_id.name', 'door_id.name')
    def _compute_location_display(self):
        """Compute location display"""
        for record in self:
            parts = []
            if record.building_id:
                parts.append(record.building_id.name)
            if record.zone_id:
                parts.append(record.zone_id.name)
            if record.floor_id:
                parts.append(f"Floor {record.floor_id.floor_number}")
            if record.room_id:
                parts.append(record.room_id.name)
            elif record.door_id:
                parts.append(f"Door {record.door_id.name}")
            
            record.location_display = " - ".join(parts) if parts else 'Unknown Location'
    
    @api.depends('event_time', 'resolved_time', 'closed_time')
    def _compute_duration(self):
        """Compute event duration"""
        for record in self:
            if record.event_time:
                end_time = record.resolved_time or record.closed_time or fields.Datetime.now()
                delta = end_time - record.event_time
                record.duration = delta.total_seconds() / 3600.0  # Convert to hours
            else:
                record.duration = 0.0
    
    @api.depends('event_time', 'acknowledged_time')
    def _compute_response_time(self):
        """Compute response time"""
        for record in self:
            if record.event_time and record.acknowledged_time:
                delta = record.acknowledged_time - record.event_time
                record.response_time = delta.total_seconds() / 60.0  # Convert to minutes
            else:
                record.response_time = 0.0
    
    @api.depends('event_time')
    def _compute_age_display(self):
        """Compute age display"""
        for record in self:
            if record.event_time:
                delta = fields.Datetime.now() - record.event_time
                days = delta.days
                hours, remainder = divmod(delta.seconds, 3600)
                minutes, _ = divmod(remainder, 60)
                
                if days > 0:
                    record.age_display = f"{days}d {hours}h ago"
                elif hours > 0:
                    record.age_display = f"{hours}h {minutes}m ago"
                else:
                    record.age_display = f"{minutes}m ago"
            else:
                record.age_display = "Unknown"
    
    @api.depends('state', 'severity', 'is_alert')
    def _compute_status_color(self):
        """Compute status color for UI display"""
        color_mapping = {
            'new': 1,  # Red
            'acknowledged': 3,  # Yellow
            'in_progress': 4,  # Blue
            'resolved': 10,  # Green
            'closed': 8,  # Gray
            'cancelled': 7,  # Light gray
        }
        
        for record in self:
            base_color = color_mapping.get(record.state, 0)
            
            # Adjust color based on severity and alert status
            if record.is_alert and record.severity == 'critical':
                record.status_color = 1  # Red for critical alerts
            elif record.is_alert and record.severity == 'high':
                record.status_color = 2  # Orange for high alerts
            else:
                record.status_color = base_color
    
    # Constraints
    @api.constrains('event_code')
    def _check_event_code_unique(self):
        """Ensure event code is unique"""
        for record in self:
            if record.event_code:
                existing = self.search([
                    ('event_code', '=', record.event_code),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        f"Event Code '{record.event_code}' already exists."
                    )
    
    @api.constrains('event_time', 'acknowledged_time', 'resolved_time', 'closed_time')
    def _check_time_sequence(self):
        """Ensure time fields are in logical sequence"""
        for record in self:
            times = [
                ('event_time', record.event_time),
                ('acknowledged_time', record.acknowledged_time),
                ('resolved_time', record.resolved_time),
                ('closed_time', record.closed_time),
            ]
            
            # Filter out None values and sort by time
            valid_times = [(name, time) for name, time in times if time]
            valid_times.sort(key=lambda x: x[1])
            
            # Check if the sequence matches expected order
            expected_order = ['event_time', 'acknowledged_time', 'resolved_time', 'closed_time']
            actual_order = [name for name, _ in valid_times]
            
            for i, name in enumerate(actual_order):
                expected_index = expected_order.index(name)
                if i > 0:
                    prev_name = actual_order[i-1]
                    prev_expected_index = expected_order.index(prev_name)
                    if expected_index < prev_expected_index:
                        raise ValidationError(
                            f"Time sequence error: {name} cannot be before {prev_name}."
                        )
    
    @api.constrains('metadata')
    def _check_json_format(self):
        """Validate JSON format for metadata"""
        for record in self:
            if record.metadata:
                try:
                    json.loads(record.metadata)
                except json.JSONDecodeError:
                    raise ValidationError("Metadata must be valid JSON format.")
    
    @api.constrains('name')
    def _check_name_not_empty(self):
        """Ensure event name is not empty"""
        for record in self:
            if not record.name or not record.name.strip():
                raise ValidationError("Event name cannot be empty.")
    
    # Onchange methods
    @api.onchange('building_id')
    def _onchange_building_id(self):
        """Filter zones, floors, rooms, doors, and devices based on selected building"""
        if self.building_id:
            zone_domain = [('building_id', '=', self.building_id.id)]
            floor_domain = [('building_id', '=', self.building_id.id)]
            room_domain = [('building_id', '=', self.building_id.id)]
            door_domain = [('building_id', '=', self.building_id.id)]
            device_domain = [('building_id', '=', self.building_id.id)]
            
            return {
                'domain': {
                    'zone_id': zone_domain,
                    'floor_id': floor_domain,
                    'room_id': room_domain,
                    'door_id': door_domain,
                    'device_id': device_domain
                }
            }
        else:
            return {
                'domain': {
                    'zone_id': [],
                    'floor_id': [],
                    'room_id': [],
                    'door_id': [],
                    'device_id': []
                }
            }
    
    @api.onchange('state')
    def _onchange_state(self):
        """Update timestamps when state changes"""
        if self.state == 'acknowledged' and not self.acknowledged_time:
            self.acknowledged_time = fields.Datetime.now()
            self.acknowledged_user_id = self.env.user.id
        elif self.state == 'resolved' and not self.resolved_time:
            self.resolved_time = fields.Datetime.now()
            self.resolved_user_id = self.env.user.id
        elif self.state == 'closed' and not self.closed_time:
            self.closed_time = fields.Datetime.now()
    
    # CRUD overrides
    def create(self, vals_list):
        """Override create to add tracking message"""
        events = super().create(vals_list)
        for event in events:
            event.message_post(
                body=f"Event '{event.name}' ({event.event_type}) created at {event.location_display}.",
                message_type='notification'
            )
        return events
    
    def write(self, vals):
        """Override write to add tracking for important changes"""
        result = super().write(vals)
        if 'state' in vals:
            for record in self:
                record.message_post(
                    body=f"Event state changed to '{record.state}'.",
                    message_type='notification'
                )
        if 'assigned_user_id' in vals:
            for record in self:
                if record.assigned_user_id:
                    record.message_post(
                        body=f"Event assigned to {record.assigned_user_id.name}.",
                        message_type='notification'
                    )
        return result
    
    # Action methods
    def action_acknowledge(self):
        """Acknowledge the event"""
        self.ensure_one()
        self.state = 'acknowledged'
        self.acknowledged_time = fields.Datetime.now()
        self.acknowledged_user_id = self.env.user.id
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Event '{self.name}' has been acknowledged.",
                'type': 'success',
            }
        }
    
    def action_start_progress(self):
        """Start working on the event"""
        self.ensure_one()
        self.state = 'in_progress'
        if not self.assigned_user_id:
            self.assigned_user_id = self.env.user.id
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Started working on event '{self.name}'.",
                'type': 'info',
            }
        }
    
    def action_resolve(self):
        """Resolve the event"""
        self.ensure_one()
        self.state = 'resolved'
        self.resolved_time = fields.Datetime.now()
        self.resolved_user_id = self.env.user.id
        self.is_active = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Event '{self.name}' has been resolved.",
                'type': 'success',
            }
        }
    
    def action_close(self):
        """Close the event"""
        self.ensure_one()
        self.state = 'closed'
        self.closed_time = fields.Datetime.now()
        self.is_active = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Event '{self.name}' has been closed.",
                'type': 'success',
            }
        }
    
    def action_cancel(self):
        """Cancel the event"""
        self.ensure_one()
        self.state = 'cancelled'
        self.is_active = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Event '{self.name}' has been cancelled.",
                'type': 'info',
            }
        }
    
    def action_assign_to_me(self):
        """Assign event to current user"""
        self.ensure_one()
        self.assigned_user_id = self.env.user.id
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Event '{self.name}' assigned to you.",
                'type': 'success',
            }
        }
    
    def action_reopen(self):
        """Reopen a closed or resolved event"""
        self.ensure_one()
        if self.state in ['closed', 'resolved']:
            self.state = 'in_progress'
            self.resolved_time = False
            self.closed_time = False
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': f"Event '{self.name}' has been reopened.",
                    'type': 'info',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': f"Event '{self.name}' cannot be reopened from current state.",
                    'type': 'warning',
                }
            }
    
    def action_view_related_events(self):
        """View events related to the same location or device"""
        self.ensure_one()
        domain = []
        
        # Build domain based on available location/device information
        if self.device_id:
            domain.append(('device_id', '=', self.device_id.id))
        elif self.door_id:
            domain.append(('door_id', '=', self.door_id.id))
        elif self.room_id:
            domain.append(('room_id', '=', self.room_id.id))
        elif self.floor_id:
            domain.append(('floor_id', '=', self.floor_id.id))
        elif self.zone_id:
            domain.append(('zone_id', '=', self.zone_id.id))
        else:
            domain.append(('building_id', '=', self.building_id.id))
        
        # Exclude current event
        domain.append(('id', '!=', self.id))
        
        return {
            'name': f'Related Events - {self.location_display}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.event',
            'view_mode': 'list,form',
            'domain': domain,
            'context': {
                'default_building_id': self.building_id.id,
                'default_zone_id': self.zone_id.id if self.zone_id else False,
                'default_floor_id': self.floor_id.id if self.floor_id else False,
                'default_room_id': self.room_id.id if self.room_id else False,
                'default_door_id': self.door_id.id if self.door_id else False,
                'default_device_id': self.device_id.id if self.device_id else False,
            },
        }
    
    # Other business methods
    def get_event_summary(self):
        """Get a summary of event information"""
        self.ensure_one()
        return {
            'name': self.name,
            'event_code': self.event_code,
            'event_type': self.event_type,
            'event_category': self.event_category,
            'severity': self.severity,
            'priority': self.priority,
            'state': self.state,
            'is_alert': self.is_alert,
            'is_active': self.is_active,
            'event_time': self.event_time,
            'location': self.location_display,
            'building': self.building_id.name,
            'zone': self.zone_id.name if self.zone_id else None,
            'floor': self.floor_id.name if self.floor_id else None,
            'room': self.room_id.name if self.room_id else None,
            'door': self.door_id.name if self.door_id else None,
            'device': self.device_id.name if self.device_id else None,
            'assigned_to': self.assigned_user_id.name if self.assigned_user_id else None,
            'duration': self.duration,
            'response_time': self.response_time,
            'age': self.age_display,
        }
    
    def update_metadata(self, metadata_dict):
        """Update event metadata"""
        self.ensure_one()
        self.metadata = json.dumps(metadata_dict, indent=2)
        self.message_post(
            body=f"Metadata updated for event '{self.name}'.",
            message_type='notification'
        )