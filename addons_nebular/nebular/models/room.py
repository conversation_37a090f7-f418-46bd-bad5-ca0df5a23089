# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import timedelta


class NebularRoom(models.Model):
    """Room model for the Nebular Dashboard System"""
    
    # Private attributes
    _name = 'nebular.room'
    _description = 'Nebular Room'
    _inherit = ['mail.thread']
    _order = 'building_id, zone_id, floor_id, name'
    _rec_name = 'name'
    
    # Field declarations
    name = fields.Char(
        string='Room Name',
        required=True,
        tracking=True,
        help='Name of the room'
    )
    room_number = fields.Char(
        string='Room Number',
        tracking=True,
        help='Room number or identifier'
    )
    description = fields.Text(
        string='Description',
        help='Detailed description of the room'
    )
    
    # Room specifications
    room_type = fields.Selection([
        ('office', 'Office'),
        ('meeting', 'Meeting Room'),
        ('conference', 'Conference Room'),
        ('storage', 'Storage'),
        ('server', 'Server Room'),
        ('utility', 'Utility Room'),
        ('restroom', 'Restroom'),
        ('kitchen', 'Kitchen'),
        ('lobby', 'Lobby'),
        ('corridor', 'Corridor'),
        ('stairwell', 'Stairwell'),
        ('elevator', 'Elevator'),
        ('emergency', 'Emergency Exit'),
        ('other', 'Other'),
    ], string='Room Type', default='office', tracking=True, help='Type of room')
    
    capacity = fields.Integer(
        string='Capacity',
        help='Maximum occupancy capacity of the room'
    )
    area = fields.Float(
        string='Area (m²)',
        digits=(10, 2),
        help='Floor area of the room in square meters'
    )
    
    # Status and operational fields
    is_active = fields.Boolean(
        string='is_active',
        default=True,
        tracking=True,
        help='Whether this room is is_active'
    )
    is_occupied = fields.Boolean(
        string='Currently Occupied',
        default=False,
        help='Whether the room is currently occupied'
    )
    
    # Relationships
    building_id = fields.Many2one(
        comodel_name='nebular.building',
        string='Building',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Building this room belongs to'
    )
    zone_id = fields.Many2one(
        comodel_name='nebular.zone',
        string='Zone',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Zone this room belongs to'
    )
    floor_id = fields.Many2one(
        comodel_name='nebular.floor',
        string='Floor',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Floor this room is on'
    )
    door_ids = fields.One2many(
        comodel_name='nebular.door',
        inverse_name='room_id',
        string='Doors',
        help='Doors providing access to this room'
    )
    device_ids = fields.One2many(
        comodel_name='nebular.device',
        inverse_name='room_id',
        string='Devices',
        help='Devices installed in this room'
    )
    event_ids = fields.One2many(
        comodel_name='nebular.event',
        inverse_name='room_id',
        string='Events',
        help='Events related to this room'
    )
    
    # Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Full display name including building, zone, and floor'
    )
    door_count = fields.Integer(
        string='Door Count',
        compute='_compute_counts',
        store=True,
        help='Number of doors providing access to this room'
    )
    device_count = fields.Integer(
        string='Device Count',
        compute='_compute_counts',
        store=True,
        help='Number of devices in this room'
    )
    event_count = fields.Integer(
        string='Event Count',
        compute='_compute_event_count',
        help='Number of recent events in this room'
    )
    has_alerts = fields.Boolean(
        string='Has Alerts',
        compute='_compute_has_alerts',
        store=True,
        help='Whether this room has is_active alerts'
    )
    occupancy_status = fields.Char(
        string='Occupancy Status',
        compute='_compute_occupancy_status',
        help='Current occupancy status description'
    )
    
    # Compute methods
    @api.depends('name', 'room_number', 'floor_id.name', 'zone_id.name', 'building_id.name')
    def _compute_display_name(self):
        """Compute display name with building, zone, and floor"""
        for record in self:
            parts = []
            if record.building_id:
                parts.append(record.building_id.name)
            if record.zone_id:
                parts.append(record.zone_id.name)
            if record.floor_id:
                parts.append(f"Floor {record.floor_id.floor_number}")
            
            room_name = record.name
            if record.room_number:
                room_name = f"{record.room_number} - {room_name}"
            parts.append(room_name)
            
            record.display_name = " - ".join(parts) if parts else ''
    
    @api.depends('door_ids', 'device_ids')
    def _compute_counts(self):
        """Compute counts for related records"""
        for record in self:
            record.door_count = len(record.door_ids)
            record.device_count = len(record.device_ids)
    
    @api.depends('event_ids')
    def _compute_event_count(self):
        """Compute recent event count (last 30 days)"""
        for record in self:
            domain = [
                ('room_id', '=', record.id),
                ('create_date', '>=', fields.Datetime.now() - timedelta(days=30))
            ]
            record.event_count = self.env['nebular.event'].search_count(domain)
    
    @api.depends('device_ids.is_alert', 'door_ids.is_alert')
    def _compute_has_alerts(self):
        """Compute has_alerts from devices and doors"""
        for record in self:
            device_alerts = record.device_ids.filtered('is_alert')
            door_alerts = record.door_ids.filtered('is_alert')
            record.has_alerts = bool(device_alerts or door_alerts)
    
    @api.depends('is_occupied', 'capacity')
    def _compute_occupancy_status(self):
        """Compute occupancy status description"""
        for record in self:
            if record.is_occupied:
                if record.capacity:
                    record.occupancy_status = f"Occupied (Capacity: {record.capacity})"
                else:
                    record.occupancy_status = "Occupied"
            else:
                if record.capacity:
                    record.occupancy_status = f"Available (Capacity: {record.capacity})"
                else:
                    record.occupancy_status = "Available"
    
    # Constraints
    @api.constrains('room_number', 'floor_id')
    def _check_room_number_unique_per_floor(self):
        """Ensure room number is unique within floor"""
        for record in self:
            if record.room_number and record.floor_id:
                existing = self.search([
                    ('room_number', '=', record.room_number),
                    ('floor_id', '=', record.floor_id.id),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        f"Room number '{record.room_number}' already exists on floor '{record.floor_id.name}'."
                    )
    
    @api.constrains('zone_id', 'building_id', 'floor_id')
    def _check_hierarchy_consistency(self):
        """Ensure zone and floor belong to the same building"""
        for record in self:
            if record.zone_id and record.building_id:
                if record.zone_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Zone '{record.zone_id.name}' does not belong to building '{record.building_id.name}'."
                    )
            if record.floor_id and record.building_id:
                if record.floor_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Floor '{record.floor_id.name}' does not belong to building '{record.building_id.name}'."
                    )
            if record.floor_id and record.zone_id:
                if record.floor_id.zone_id != record.zone_id:
                    raise ValidationError(
                        f"Floor '{record.floor_id.name}' does not belong to zone '{record.zone_id.name}'."
                    )
    
    @api.constrains('capacity')
    def _check_capacity_positive(self):
        """Ensure capacity is positive if specified"""
        for record in self:
            if record.capacity is not False and record.capacity < 0:
                raise ValidationError("Room capacity must be positive.")
    
    @api.constrains('area')
    def _check_area_positive(self):
        """Ensure area is positive if specified"""
        for record in self:
            if record.area is not False and record.area <= 0:
                raise ValidationError("Room area must be positive.")
    
    @api.constrains('name')
    def _check_name_not_empty(self):
        """Ensure room name is not empty"""
        for record in self:
            if not record.name or not record.name.strip():
                raise ValidationError("Room name cannot be empty.")
    
    # Onchange methods
    @api.onchange('building_id')
    def _onchange_building_id(self):
        """Filter zones and floors based on selected building"""
        if self.building_id:
            zone_domain = [('building_id', '=', self.building_id.id)]
            floor_domain = [('building_id', '=', self.building_id.id)]
            
            if self.zone_id and self.zone_id.building_id != self.building_id:
                self.zone_id = False
            if self.floor_id and self.floor_id.building_id != self.building_id:
                self.floor_id = False
                
            return {
                'domain': {
                    'zone_id': zone_domain,
                    'floor_id': floor_domain
                }
            }
        else:
            self.zone_id = False
            self.floor_id = False
            return {
                'domain': {
                    'zone_id': [],
                    'floor_id': []
                }
            }
    
    @api.onchange('zone_id')
    def _onchange_zone_id(self):
        """Filter floors based on selected zone"""
        if self.zone_id:
            floor_domain = [('zone_id', '=', self.zone_id.id)]
            if self.floor_id and self.floor_id.zone_id != self.zone_id:
                self.floor_id = False
            return {'domain': {'floor_id': floor_domain}}
        else:
            return {'domain': {'floor_id': []}}
    
    # CRUD overrides
    def create(self, vals_list):
        """Override create to add tracking message"""
        rooms = super().create(vals_list)
        for room in rooms:
            room.message_post(
                body=f"Room '{room.name}' has been created on {room.floor_id.name}.",
                message_type='notification'
            )
        return rooms
    
    def write(self, vals):
        """Override write to add tracking for important changes"""
        result = super().write(vals)
        if 'is_active' in vals:
            for record in self:
                status = 'activated' if vals['is_active'] else 'deactivated'
                record.message_post(
                    body=f"Room '{record.name}' has been {status}.",
                    message_type='notification'
                )
        if 'is_occupied' in vals:
            for record in self:
                status = 'occupied' if vals['is_occupied'] else 'vacated'
                record.message_post(
                    body=f"Room '{record.name}' has been {status}.",
                    message_type='notification'
                )
        return result
    
    # Action methods
    def action_activate(self):
        """Activate the room"""
        self.ensure_one()
        self.is_active = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Room '{self.name}' has been activated.",
                'type': 'success',
            }
        }
    
    def action_deactivate(self):
        """Deactivate the room"""
        self.ensure_one()
        self.is_active = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Room '{self.name}' has been deactivated.",
                'type': 'warning',
            }
        }
    
    def action_view_doors(self):
        """Action to view doors of this room"""
        self.ensure_one()
        return {
            'name': f'Doors - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.door',
            'view_mode': 'list,form',
            'domain': [('room_id', '=', self.id)],
            'context': {
                'default_room_id': self.id,
                'default_floor_id': self.floor_id.id,
                'default_zone_id': self.zone_id.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_view_devices(self):
        """Action to view devices in this room"""
        self.ensure_one()
        return {
            'name': f'Devices - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.device',
            'view_mode': 'list,form',
            'domain': [('room_id', '=', self.id)],
            'context': {
                'default_room_id': self.id,
                'default_floor_id': self.floor_id.id,
                'default_zone_id': self.zone_id.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_toggle_occupancy(self):
        """Toggle room occupancy status"""
        self.ensure_one()
        self.is_occupied = not self.is_occupied
        status = 'occupied' if self.is_occupied else 'available'
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Room '{self.name}' is now {status}.",
                'type': 'success',
            }
        }
    
    # Other business methods
    def get_room_summary(self):
        """Get a summary of room statistics"""
        self.ensure_one()
        return {
            'name': self.name,
            'room_number': self.room_number,
            'room_type': self.room_type,
            'building': self.building_id.name,
            'zone': self.zone_id.name,
            'floor': self.floor_id.name,
            'capacity': self.capacity,
            'area': self.area,
            'is_occupied': self.is_occupied,
            'doors': self.door_count,
            'devices': self.device_count,
            'events': self.event_count,
            'has_alerts': self.has_alerts,
        }