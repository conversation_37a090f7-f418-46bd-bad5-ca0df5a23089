# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class NebularPresenceEvent(models.Model):
    """Presence Event specialized model for presence detection events"""
    
    # Private attributes
    _name = 'nebular.presence.event'
    _description = 'Nebular Presence Event'
    _inherit = ['nebular.event']
    _order = 'event_time desc, create_date desc'
    
    # Presence-specific fields from class diagram
    sensor_id = fields.Integer(
        string='Sensor ID',
        help='Unique identifier of the presence sensor'
    )
    
    sensor_code = fields.Char(
        string='Sensor Code',
        required=True,
        help='Unique code identifier of the sensor'
    )
    
    sensor_name = fields.Char(
        string='Sensor Name',
        help='Descriptive name of the presence sensor'
    )
    
    location = fields.Char(
        string='Location',
        help='Physical location description of the sensor'
    )
    
    vendor = fields.Char(
        string='Vendor',
        help='Manufacturer/vendor of the sensor'
    )
    
    model = fields.Char(
        string='Model',
        help='Model number/name of the sensor'
    )
    
    type = fields.Selection([
        ('pir', 'PIR (Passive Infrared)'),
        ('microwave', 'Microwave'),
        ('ultrasonic', 'Ultrasonic'),
        ('camera', 'Camera-based'),
        ('dual', 'Dual Technology'),
        ('beam', 'Beam Break'),
        ('pressure', 'Pressure Mat'),
    ], string='Sensor Type', help='Type of presence detection technology')
    
    count = fields.Integer(
        string='Detection Count',
        help='Number of detections or people count'
    )
    
    occupancy = fields.Boolean(
        string='Occupancy Status',
        help='Current occupancy status (True = occupied, False = vacant)'
    )
    
    # Additional useful fields
    sensitivity = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('auto', 'Auto'),
    ], string='Sensitivity Level', default='medium', help='Sensor sensitivity level')
    
    detection_range = fields.Float(
        string='Detection Range (m)',
        help='Detection range in meters'
    )
    
    confidence_level = fields.Float(
        string='Confidence Level (%)',
        digits=(5, 2),
        help='Confidence level of the detection (0-100%)'
    )
    
    motion_type = fields.Selection([
        ('entry', 'Entry'),
        ('exit', 'Exit'),
        ('movement', 'Movement'),
        ('stationary', 'Stationary'),
        ('loitering', 'Loitering'),
    ], string='Motion Type', help='Type of motion detected')
    
    duration_seconds = fields.Integer(
        string='Duration (seconds)',
        help='Duration of presence detection in seconds'
    )
    
    temperature = fields.Float(
        string='Temperature (°C)',
        help='Temperature reading if sensor supports it'
    )
    
    battery_level = fields.Float(
        string='Battery Level (%)',
        help='Battery level for wireless sensors'
    )
    
    signal_strength = fields.Integer(
        string='Signal Strength (dBm)',
        help='Signal strength for wireless sensors'
    )
    
    # Constraints and validations
    @api.constrains('confidence_level')
    def _check_confidence_level(self):
        """Validate confidence level is between 0 and 100"""
        for record in self:
            if record.confidence_level and (record.confidence_level < 0 or record.confidence_level > 100):
                raise ValidationError("Confidence level must be between 0.00 and 100.00")
    
    @api.constrains('battery_level')
    def _check_battery_level(self):
        """Validate battery level is between 0 and 100"""
        for record in self:
            if record.battery_level and (record.battery_level < 0 or record.battery_level > 100):
                raise ValidationError("Battery level must be between 0.00 and 100.00")
    
    @api.constrains('detection_range')
    def _check_detection_range_positive(self):
        """Validate detection range is positive"""
        for record in self:
            if record.detection_range and record.detection_range <= 0:
                raise ValidationError("Detection range must be a positive number")
    
    @api.constrains('count')
    def _check_count_non_negative(self):
        """Validate count is non-negative"""
        for record in self:
            if record.count and record.count < 0:
                raise ValidationError("Detection count cannot be negative")
    
    @api.constrains('sensor_code')
    def _check_sensor_code_unique(self):
        """Ensure sensor code is unique within the same building"""
        for record in self:
            if record.sensor_code and record.building_id:
                existing = self.search([
                    ('sensor_code', '=', record.sensor_code),
                    ('building_id', '=', record.building_id.id),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(f"Sensor code '{record.sensor_code}' already exists in building '{record.building_id.name}'")
    
    # Methods
    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"[{record.sensor_code}] {record.sensor_name or 'Presence Event'}"
            if record.occupancy is not None:
                status = "Occupied" if record.occupancy else "Vacant"
                name += f" - {status}"
            result.append((record.id, name))
        return result
    
    @api.model
    def create_presence_event(self, presence_data):
        """Create presence event from external system data"""
        event_vals = {
            'event_type': 'presence',
            'system_id': 'presence',
            'system_name': 'Presence Detection System',
            'building_id': presence_data.get('building_id'),
            'zone_id': presence_data.get('zone_id'),
            'floor_id': presence_data.get('floor_id'),
            'event_time': presence_data.get('datetime', fields.Datetime.now()),
            'message': presence_data.get('message', ''),
            'severity': presence_data.get('severity', 1),
        }
        
        presence_vals = {
            'sensor_id': presence_data.get('sensor_id'),
            'sensor_code': presence_data.get('sensor_code'),
            'sensor_name': presence_data.get('name'),
            'location': presence_data.get('location'),
            'vendor': presence_data.get('vendor'),
            'model': presence_data.get('model'),
            'type': presence_data.get('type'),
            'count': presence_data.get('count'),
            'occupancy': presence_data.get('occupancy'),
            'sensitivity': presence_data.get('sensitivity', 'medium'),
            'detection_range': presence_data.get('detection_range'),
            'confidence_level': presence_data.get('confidence_level'),
            'motion_type': presence_data.get('motion_type'),
            'duration_seconds': presence_data.get('duration_seconds'),
            'temperature': presence_data.get('temperature'),
            'battery_level': presence_data.get('battery_level'),
            'signal_strength': presence_data.get('signal_strength'),
        }
        
        # Merge the dictionaries
        vals = {**event_vals, **presence_vals}
        return self.create(vals)
    
    def action_acknowledge_presence(self):
        """Action to acknowledge presence detection"""
        self.ensure_one()
        self.message_post(
            body=f"Presence event acknowledged by {self.env.user.name}",
            message_type='notification'
        )
        return True
    
    @api.model
    def get_occupancy_summary(self, building_id=None, zone_id=None, floor_id=None):
        """Get occupancy summary for specified area"""
        domain = [('occupancy', '=', True)]
        
        if building_id:
            domain.append(('building_id', '=', building_id))
        if zone_id:
            domain.append(('zone_id', '=', zone_id))
        if floor_id:
            domain.append(('floor_id', '=', floor_id))
        
        # Get latest presence events per sensor
        latest_events = self.search(domain, order='sensor_code, event_time desc')
        
        # Group by sensor and get the latest event for each
        sensor_occupancy = {}
        for event in latest_events:
            if event.sensor_code not in sensor_occupancy:
                sensor_occupancy[event.sensor_code] = event
        
        occupied_count = sum(1 for event in sensor_occupancy.values() if event.occupancy)
        total_sensors = len(sensor_occupancy)
        
        return {
            'occupied_sensors': occupied_count,
            'total_sensors': total_sensors,
            'occupancy_rate': (occupied_count / total_sensors * 100) if total_sensors > 0 else 0,
            'sensor_details': sensor_occupancy
        }