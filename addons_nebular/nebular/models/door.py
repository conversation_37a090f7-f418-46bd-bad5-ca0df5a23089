# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import timedelta


class NebularDoor(models.Model):
    """Door model for the Nebular Dashboard System"""
    
    # Private attributes
    _name = 'nebular.door'
    _description = 'Nebular Door'
    _inherit = ['mail.thread']
    _order = 'building_id, zone_id, floor_id, room_id, name'
    _rec_name = 'name'
    
    # Field declarations
    name = fields.Char(
        string='Door Name',
        required=True,
        tracking=True,
        help='Name or identifier of the door'
    )
    door_number = fields.Char(
        string='Door Number',
        tracking=True,
        help='Door number or code'
    )
    description = fields.Text(
        string='Description',
        help='Detailed description of the door'
    )
    
    # Door specifications
    door_type = fields.Selection([
        ('entry', 'Entry Door'),
        ('exit', 'Exit Door'),
        ('emergency', 'Emergency Exit'),
        ('fire', 'Fire Door'),
        ('security', 'Security Door'),
        ('automatic', 'Automatic Door'),
        ('revolving', 'Revolving Door'),
        ('sliding', 'Sliding Door'),
        ('swing', 'Swing Door'),
        ('double', 'Double Door'),
        ('other', 'Other'),
    ], string='Door Type', default='swing', tracking=True, help='Type of door')
    
    access_level = fields.Selection([
        ('public', 'Public Access'),
        ('restricted', 'Restricted Access'),
        ('authorized', 'Authorized Personnel Only'),
        ('emergency', 'Emergency Only'),
        ('maintenance', 'Maintenance Only'),
    ], string='Access Level', default='public', tracking=True, help='Access level required')
    
    # Status and operational fields
    is_active = fields.Boolean(
        string='is_active',
        default=True,
        tracking=True,
        help='Whether this door is is_active'
    )
    is_locked = fields.Boolean(
        string='Locked',
        default=False,
        tracking=True,
        help='Whether the door is currently locked'
    )
    is_open = fields.Boolean(
        string='Open',
        default=False,
        help='Whether the door is currently open'
    )
    is_alert = fields.Boolean(
        string='Alert Status',
        default=False,
        help='Whether there is an is_active alert for this door'
    )
    
    # Relationships
    building_id = fields.Many2one(
        comodel_name='nebular.building',
        string='Building',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Building this door belongs to'
    )
    zone_id = fields.Many2one(
        comodel_name='nebular.zone',
        string='Zone',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Zone this door belongs to'
    )
    floor_id = fields.Many2one(
        comodel_name='nebular.floor',
        string='Floor',
        required=True,
        ondelete='cascade',
        tracking=True,
        help='Floor this door is on'
    )
    room_id = fields.Many2one(
        comodel_name='nebular.room',
        string='Room',
        ondelete='cascade',
        tracking=True,
        help='Room this door provides access to (optional for external doors)'
    )
    device_ids = fields.One2many(
        comodel_name='nebular.device',
        inverse_name='door_id',
        string='Devices',
        help='Devices associated with this door (card readers, sensors, etc.)'
    )
    event_ids = fields.One2many(
        comodel_name='nebular.event',
        inverse_name='door_id',
        string='Events',
        help='Events related to this door'
    )
    
    # Computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Full display name including location hierarchy'
    )
    device_count = fields.Integer(
        string='Device Count',
        compute='_compute_device_count',
        store=True,
        help='Number of devices associated with this door'
    )
    event_count = fields.Integer(
        string='Event Count',
        compute='_compute_event_count',
        help='Number of recent events for this door'
    )
    access_event_count = fields.Integer(
        string='Access Event Count',
        compute='_compute_access_event_count',
        help='Number of recent access events'
    )
    last_access_time = fields.Datetime(
        string='Last Access',
        compute='_compute_last_access_time',
        help='Time of last access event'
    )
    status_display = fields.Char(
        string='Status',
        compute='_compute_status_display',
        help='Current door status display'
    )
    security_status = fields.Char(
        string='Security Status',
        compute='_compute_security_status',
        help='Security status description'
    )
    
    # Compute methods
    @api.depends('name', 'door_number', 'room_id.name', 'floor_id.name', 'zone_id.name', 'building_id.name')
    def _compute_display_name(self):
        """Compute display name with location hierarchy"""
        for record in self:
            parts = []
            if record.building_id:
                parts.append(record.building_id.name)
            if record.zone_id:
                parts.append(record.zone_id.name)
            if record.floor_id:
                parts.append(f"Floor {record.floor_id.floor_number}")
            if record.room_id:
                parts.append(record.room_id.name)
            
            door_name = record.name
            if record.door_number:
                door_name = f"{record.door_number} - {door_name}"
            parts.append(door_name)
            
            record.display_name = " - ".join(parts) if parts else ''
    
    @api.depends('device_ids')
    def _compute_device_count(self):
        """Compute device count"""
        for record in self:
            record.device_count = len(record.device_ids)
    
    @api.depends('event_ids')
    def _compute_event_count(self):
        """Compute recent event count (last 30 days)"""
        for record in self:
            domain = [
                ('door_id', '=', record.id),
                ('create_date', '>=', fields.Datetime.now() - timedelta(days=30))
            ]
            record.event_count = self.env['nebular.event'].search_count(domain)
    
    @api.depends('event_ids')
    def _compute_access_event_count(self):
        """Compute recent access event count (last 30 days)"""
        for record in self:
            domain = [
                ('door_id', '=', record.id),
                ('event_type', '=', 'access'),
                ('create_date', '>=', fields.Datetime.now() - timedelta(days=30))
            ]
            record.access_event_count = self.env['nebular.access.event'].search_count(domain)
    
    @api.depends('event_ids')
    def _compute_last_access_time(self):
        """Compute last access time"""
        for record in self:
            last_event = self.env['nebular.access.event'].search([
                ('door_id', '=', record.id)
            ], order='create_date desc', limit=1)
            record.last_access_time = last_event.create_date if last_event else False
    
    @api.depends('is_open', 'is_locked', 'is_alert', 'is_active')
    def _compute_status_display(self):
        """Compute status display"""
        for record in self:
            if not record.is_active:
                record.status_display = 'Inis_active'
            elif record.is_alert:
                record.status_display = 'Alert'
            elif record.is_open:
                record.status_display = 'Open'
            elif record.is_locked:
                record.status_display = 'Locked'
            else:
                record.status_display = 'Closed'
    
    @api.depends('access_level', 'is_locked', 'is_alert')
    def _compute_security_status(self):
        """Compute security status"""
        for record in self:
            if record.is_alert:
                record.security_status = 'Security Alert'
            elif record.access_level == 'emergency':
                record.security_status = 'Emergency Access Only'
            elif record.access_level == 'authorized':
                if record.is_locked:
                    record.security_status = 'Secured - Authorized Access'
                else:
                    record.security_status = 'Authorized Access'
            elif record.access_level == 'restricted':
                if record.is_locked:
                    record.security_status = 'Secured - Restricted Access'
                else:
                    record.security_status = 'Restricted Access'
            else:
                if record.is_locked:
                    record.security_status = 'Locked'
                else:
                    record.security_status = 'Public Access'
    
    # Constraints
    @api.constrains('door_number', 'floor_id')
    def _check_door_number_unique_per_floor(self):
        """Ensure door number is unique within floor"""
        for record in self:
            if record.door_number and record.floor_id:
                existing = self.search([
                    ('door_number', '=', record.door_number),
                    ('floor_id', '=', record.floor_id.id),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        f"Door number '{record.door_number}' already exists on floor '{record.floor_id.name}'."
                    )
    
    @api.constrains('zone_id', 'building_id', 'floor_id', 'room_id')
    def _check_hierarchy_consistency(self):
        """Ensure all relationships are consistent with hierarchy"""
        for record in self:
            if record.zone_id and record.building_id:
                if record.zone_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Zone '{record.zone_id.name}' does not belong to building '{record.building_id.name}'."
                    )
            if record.floor_id and record.building_id:
                if record.floor_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Floor '{record.floor_id.name}' does not belong to building '{record.building_id.name}'."
                    )
            if record.floor_id and record.zone_id:
                if record.floor_id.zone_id != record.zone_id:
                    raise ValidationError(
                        f"Floor '{record.floor_id.name}' does not belong to zone '{record.zone_id.name}'."
                    )
            if record.room_id:
                if record.room_id.building_id != record.building_id:
                    raise ValidationError(
                        f"Room '{record.room_id.name}' does not belong to building '{record.building_id.name}'."
                    )
                if record.room_id.zone_id != record.zone_id:
                    raise ValidationError(
                        f"Room '{record.room_id.name}' does not belong to zone '{record.zone_id.name}'."
                    )
                if record.room_id.floor_id != record.floor_id:
                    raise ValidationError(
                        f"Room '{record.room_id.name}' is not on floor '{record.floor_id.name}'."
                    )
    
    @api.constrains('name')
    def _check_name_not_empty(self):
        """Ensure door name is not empty"""
        for record in self:
            if not record.name or not record.name.strip():
                raise ValidationError("Door name cannot be empty.")
    
    # Onchange methods
    @api.onchange('building_id')
    def _onchange_building_id(self):
        """Filter zones, floors, and rooms based on selected building"""
        if self.building_id:
            zone_domain = [('building_id', '=', self.building_id.id)]
            floor_domain = [('building_id', '=', self.building_id.id)]
            room_domain = [('building_id', '=', self.building_id.id)]
            
            if self.zone_id and self.zone_id.building_id != self.building_id:
                self.zone_id = False
            if self.floor_id and self.floor_id.building_id != self.building_id:
                self.floor_id = False
            if self.room_id and self.room_id.building_id != self.building_id:
                self.room_id = False
                
            return {
                'domain': {
                    'zone_id': zone_domain,
                    'floor_id': floor_domain,
                    'room_id': room_domain
                }
            }
        else:
            self.zone_id = False
            self.floor_id = False
            self.room_id = False
            return {
                'domain': {
                    'zone_id': [],
                    'floor_id': [],
                    'room_id': []
                }
            }
    
    @api.onchange('zone_id')
    def _onchange_zone_id(self):
        """Filter floors and rooms based on selected zone"""
        if self.zone_id:
            floor_domain = [('zone_id', '=', self.zone_id.id)]
            room_domain = [('zone_id', '=', self.zone_id.id)]
            
            if self.floor_id and self.floor_id.zone_id != self.zone_id:
                self.floor_id = False
            if self.room_id and self.room_id.zone_id != self.zone_id:
                self.room_id = False
                
            return {
                'domain': {
                    'floor_id': floor_domain,
                    'room_id': room_domain
                }
            }
        else:
            return {
                'domain': {
                    'floor_id': [],
                    'room_id': []
                }
            }
    
    @api.onchange('floor_id')
    def _onchange_floor_id(self):
        """Filter rooms based on selected floor"""
        if self.floor_id:
            room_domain = [('floor_id', '=', self.floor_id.id)]
            if self.room_id and self.room_id.floor_id != self.floor_id:
                self.room_id = False
            return {'domain': {'room_id': room_domain}}
        else:
            return {'domain': {'room_id': []}}
    
    # CRUD overrides
    def create(self, vals_list):
        """Override create to add tracking message"""
        doors = super().create(vals_list)
        for door in doors:
            location = door.room_id.name if door.room_id else door.floor_id.name
            door.message_post(
                body=f"Door '{door.name}' has been created at {location}.",
                message_type='notification'
            )
        return doors
    
    def write(self, vals):
        """Override write to add tracking for important changes"""
        result = super().write(vals)
        if 'is_locked' in vals:
            for record in self:
                status = 'locked' if vals['is_locked'] else 'unlocked'
                record.message_post(
                    body=f"Door '{record.name}' has been {status}.",
                    message_type='notification'
                )
        if 'is_alert' in vals:
            for record in self:
                if vals['is_alert']:
                    record.message_post(
                        body=f"Alert activated for door '{record.name}'.",
                        message_type='notification'
                    )
                else:
                    record.message_post(
                        body=f"Alert cleared for door '{record.name}'.",
                        message_type='notification'
                    )
        return result
    
    # Action methods
    def action_lock(self):
        """Lock the door"""
        self.ensure_one()
        self.is_locked = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Door '{self.name}' has been locked.",
                'type': 'success',
            }
        }
    
    def action_unlock(self):
        """Unlock the door"""
        self.ensure_one()
        self.is_locked = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Door '{self.name}' has been unlocked.",
                'type': 'success',
            }
        }
    
    def action_toggle_lock(self):
        """Toggle door lock status"""
        self.ensure_one()
        self.is_locked = not self.is_locked
        status = 'locked' if self.is_locked else 'unlocked'
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Door '{self.name}' has been {status}.",
                'type': 'success',
            }
        }
    
    def action_open(self):
        """Open the door"""
        self.ensure_one()
        self.is_open = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Door '{self.name}' has been opened.",
                'type': 'success',
            }
        }
    
    def action_close(self):
        """Close the door"""
        self.ensure_one()
        self.is_open = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Door '{self.name}' has been closed.",
                'type': 'success',
            }
        }
    
    def action_view_devices(self):
        """Action to view devices associated with this door"""
        self.ensure_one()
        return {
            'name': f'Devices - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.device',
            'view_mode': 'list,form',
            'domain': [('door_id', '=', self.id)],
            'context': {
                'default_door_id': self.id,
                'default_room_id': self.room_id.id if self.room_id else False,
                'default_floor_id': self.floor_id.id,
                'default_zone_id': self.zone_id.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    def action_view_events(self):
        """Action to view events for this door"""
        self.ensure_one()
        return {
            'name': f'Events - {self.display_name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.event',
            'view_mode': 'list,form',
            'domain': [('door_id', '=', self.id)],
            'context': {
                'default_door_id': self.id,
                'default_room_id': self.room_id.id if self.room_id else False,
                'default_floor_id': self.floor_id.id,
                'default_zone_id': self.zone_id.id,
                'default_building_id': self.building_id.id,
            },
        }
    
    # Other business methods
    def get_door_summary(self):
        """Get a summary of door statistics"""
        self.ensure_one()
        return {
            'name': self.name,
            'door_number': self.door_number,
            'door_type': self.door_type,
            'access_level': self.access_level,
            'building': self.building_id.name,
            'zone': self.zone_id.name,
            'floor': self.floor_id.name,
            'room': self.room_id.name if self.room_id else None,
            'is_locked': self.is_locked,
            'is_open': self.is_open,
            'is_alert': self.is_alert,
            'devices': self.device_count,
            'events': self.event_count,
            'access_events': self.access_event_count,
            'last_access': self.last_access_time,
            'status': self.status_display,
            'security_status': self.security_status,
        }