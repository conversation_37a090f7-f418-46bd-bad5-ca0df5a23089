# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class NebularGateEvent(models.Model):
    """Gate Event specialized model for gate control events"""
    
    # Private attributes
    _name = 'nebular.gate.event'
    _description = 'Nebular Gate Event'
    _inherit = ['nebular.event']
    _order = 'event_time desc, create_date desc'
    
    # Gate-specific fields from class diagram
    gate_id = fields.Integer(
        string='Gate ID',
        help='Unique identifier of the gate'
    )
    
    gate_code = fields.Char(
        string='Gate Code',
        required=True,
        help='Unique code identifier of the gate'
    )
    
    gate_name = fields.Char(
        string='Gate Name',
        help='Descriptive name of the gate'
    )
    
    location = fields.Char(
        string='Location',
        help='Physical location description of the gate'
    )
    
    status = fields.Selection([
        ('open', 'Open'),
        ('closed', 'Closed'),
        ('opening', 'Opening'),
        ('closing', 'Closing'),
        ('fault', 'Fault'),
        ('maintenance', 'Maintenance'),
        ('blocked', 'Blocked'),
    ], string='Gate Status', help='Current status of the gate')
    
    vehicle_plate = fields.Char(
        string='Vehicle Plate',
        help='License plate number of the vehicle'
    )
    
    trigger = fields.Selection([
        ('manual', 'Manual'),
        ('remote', 'Remote Control'),
        ('card', 'Access Card'),
        ('anpr', 'ANPR (Automatic Number Plate Recognition)'),
        ('sensor', 'Sensor'),
        ('timer', 'Timer'),
        ('emergency', 'Emergency'),
    ], string='Trigger', help='What triggered the gate event')
    
    anpr_conf = fields.Float(
        string='ANPR Confidence',
        digits=(5, 2),
        help='ANPR confidence level (0.00 to 100.00)'
    )
    
    # Additional useful fields
    gate_type = fields.Selection([
        ('barrier', 'Barrier Gate'),
        ('sliding', 'Sliding Gate'),
        ('swing', 'Swing Gate'),
        ('bollard', 'Bollard'),
        ('turnstile', 'Turnstile'),
    ], string='Gate Type', help='Type of gate')
    
    direction = fields.Selection([
        ('entry', 'Entry'),
        ('exit', 'Exit'),
        ('both', 'Both'),
    ], string='Direction', help='Direction of movement')
    
    # Constraints and validations
    @api.constrains('anpr_conf')
    def _check_anpr_confidence(self):
        """Validate ANPR confidence is between 0 and 100"""
        for record in self:
            if record.anpr_conf and (record.anpr_conf < 0 or record.anpr_conf > 100):
                raise ValidationError("ANPR confidence must be between 0.00 and 100.00")
    
    @api.constrains('gate_code')
    def _check_gate_code_unique(self):
        """Ensure gate code is unique within the same building"""
        for record in self:
            if record.gate_code and record.building_id:
                existing = self.search([
                    ('gate_code', '=', record.gate_code),
                    ('building_id', '=', record.building_id.id),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(f"Gate code '{record.gate_code}' already exists in building '{record.building_id.name}'")
    
    # Methods
    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"[{record.gate_code}] {record.gate_name or 'Gate Event'}"
            if record.vehicle_plate:
                name += f" - {record.vehicle_plate}"
            result.append((record.id, name))
        return result
    
    @api.model
    def create_gate_event(self, gate_data):
        """Create gate event from external system data"""
        event_vals = {
            'event_type': 'gate',
            'system_id': 'gate',
            'system_name': 'Gate Control System',
            'building_id': gate_data.get('building_id'),
            'zone_id': gate_data.get('zone_id'),
            'floor_id': gate_data.get('floor_id'),
            'event_time': gate_data.get('datetime', fields.Datetime.now()),
            'message': gate_data.get('message', ''),
            'severity': gate_data.get('severity', 1),
        }
        
        gate_vals = {
            'gate_id': gate_data.get('gate_id'),
            'gate_code': gate_data.get('gate_code'),
            'gate_name': gate_data.get('name'),
            'location': gate_data.get('location'),
            'status': gate_data.get('status'),
            'vehicle_plate': gate_data.get('vehicle_plate'),
            'trigger': gate_data.get('trigger'),
            'anpr_conf': gate_data.get('anpr_conf'),
            'gate_type': gate_data.get('gate_type'),
            'direction': gate_data.get('direction'),
        }
        
        # Merge the dictionaries
        vals = {**event_vals, **gate_vals}
        return self.create(vals)