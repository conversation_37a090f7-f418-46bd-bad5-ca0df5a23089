# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta

class NebularAccessEvent(models.Model):
    """Access Event specialized model for access control events"""
    
    # Private attributes
    _name = 'nebular.access.event'
    _description = 'Nebular Access Event'
    _inherit = ['nebular.event']
    _order = 'event_time desc, create_date desc'
    
    # Access-specific fields
    card_number = fields.Char(
        string='Card Number',
        help='Access card number used'
    )
    card_holder_name = fields.Char(
        string='Card Holder Name',
        help='Name of the card holder'
    )
    employee_id = fields.Char(
        string='Employee ID',
        help='Employee identification number'
    )
    
    access_type = fields.Selection([
        ('card', 'Card Access'),
        ('pin', 'PIN Access'),
        ('biometric', 'Biometric Access'),
        ('manual', 'Manual Access'),
        ('remote', 'Remote Access'),
        ('emergency', 'Emergency Access'),
        ('maintenance', 'Maintenance Access'),
        ('visitor', 'Visitor Access'),
    ], string='Access Type', help='Type of access method used')
    
    access_result = fields.Selection([
        ('granted', 'Access Granted'),
        ('denied', 'Access Denied'),
        ('timeout', 'Access Timeout'),
        ('forced', 'Forced Entry'),
        ('tailgating', 'Tailgating Detected'),
        ('duress', 'Duress Code Used'),
    ], string='Access Result', help='Result of the access attempt')
    
    access_direction = fields.Selection([
        ('entry', 'Entry'),
        ('exit', 'Exit'),
        ('unknown', 'Unknown'),
    ], string='Direction', help='Direction of access (entry or exit)')
    
    # Access control details
    access_level_required = fields.Char(
        string='Required Access Level',
        help='Access level required for this door/area'
    )
    access_level_granted = fields.Char(
        string='Granted Access Level',
        help='Access level of the card/user'
    )
    
    time_zone_is_active = fields.Boolean(
        string='Time Zone is_active',
        help='Whether the time zone was is_active during access'
    )
    anti_passback_violation = fields.Boolean(
        string='Anti-Passback Violation',
        default=False,
        help='Whether anti-passback rule was violated'
    )
    
    # Biometric details (if applicable)
    biometric_type = fields.Selection([
        ('fingerprint', 'Fingerprint'),
        ('face', 'Face Recognition'),
        ('iris', 'Iris Scan'),
        ('palm', 'Palm Print'),
        ('voice', 'Voice Recognition'),
    ], string='Biometric Type', help='Type of biometric authentication')
    
    biometric_quality = fields.Float(
        string='Biometric Quality (%)',
        help='Quality score of biometric reading'
    )
    biometric_match_score = fields.Float(
        string='Match Score (%)',
        help='Biometric match confidence score'
    )
    
    # Security flags
    is_security_violation = fields.Boolean(
        string='Security Violation',
        default=False,
        help='Whether this event represents a security violation'
    )
    is_unauthorized_access = fields.Boolean(
        string='Unauthorized Access',
        default=False,
        help='Whether this was an unauthorized access attempt'
    )
    requires_investigation = fields.Boolean(
        string='Requires Investigation',
        default=False,
        help='Whether this event requires security investigation'
    )
    
    # Investigation details
    investigation_status = fields.Selection([
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('closed', 'Closed'),
    ], string='Investigation Status', default='pending', help='Status of security investigation')
    
    investigation_notes = fields.Text(
        string='Investigation Notes',
        help='Notes from security investigation'
    )
    investigated_by = fields.Many2one(
        comodel_name='res.users',
        string='Investigated By',
        help='Security officer who investigated this event'
    )
    investigation_date = fields.Datetime(
        string='Investigation Date',
        help='When the investigation was completed'
    )
    
    # Visitor management (if applicable)
    visitor_name = fields.Char(
        string='Visitor Name',
        help='Name of visitor (for visitor access)'
    )
    visitor_company = fields.Char(
        string='Visitor Company',
        help='Company of visitor'
    )
    host_employee = fields.Char(
        string='Host Employee',
        help='Employee hosting the visitor'
    )
    visit_purpose = fields.Text(
        string='Visit Purpose',
        help='Purpose of the visit'
    )
    
    # Computed fields
    security_risk_level = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ], string='Security Risk Level', compute='_compute_security_risk_level', store=True)
    
    access_summary = fields.Char(
        string='Access Summary',
        compute='_compute_access_summary',
        store=True,
        help='Summary of access event'
    )
    
    # Default methods
    @api.model
    def default_get(self, fields_list):
        """Set default values for access events"""
        defaults = super().default_get(fields_list)
        defaults.update({
            'event_type': 'access',
            'event_category': 'access_control',
        })
        return defaults
    
    # Compute methods
    @api.depends('access_result', 'is_security_violation', 'is_unauthorized_access', 'anti_passback_violation')
    def _compute_security_risk_level(self):
        """Compute security risk level based on access details"""
        for record in self:
            if record.access_result == 'duress':
                record.security_risk_level = 'critical'
            elif record.access_result in ['forced', 'tailgating'] or record.is_unauthorized_access:
                record.security_risk_level = 'high'
            elif record.access_result == 'denied' or record.anti_passback_violation or record.is_security_violation:
                record.security_risk_level = 'medium'
            else:
                record.security_risk_level = 'low'
    
    @api.depends('access_type', 'access_result', 'access_direction', 'card_holder_name', 'visitor_name')
    def _compute_access_summary(self):
        """Compute access summary"""
        for record in self:
            parts = []
            
            # Add person name
            person = record.card_holder_name or record.visitor_name or 'Unknown'
            parts.append(person)
            
            # Add access type and result
            if record.access_type and record.access_result:
                parts.append(f"({record.access_type} - {record.access_result})")
            
            # Add direction
            if record.access_direction:
                parts.append(f"- {record.access_direction.title()}")
            
            record.access_summary = " ".join(parts) if parts else 'Access Event'
    
    # Constraints
    @api.constrains('biometric_quality', 'biometric_match_score')
    def _check_biometric_scores(self):
        """Validate biometric scores"""
        for record in self:
            if record.biometric_quality and (record.biometric_quality < 0 or record.biometric_quality > 100):
                raise ValidationError("Biometric quality must be between 0 and 100 percent.")
            if record.biometric_match_score and (record.biometric_match_score < 0 or record.biometric_match_score > 100):
                raise ValidationError("Biometric match score must be between 0 and 100 percent.")
    
    @api.constrains('card_number')
    def _check_card_number_format(self):
        """Validate card number format"""
        for record in self:
            if record.card_number and len(record.card_number.strip()) == 0:
                raise ValidationError("Card number cannot be empty.")
    
    # Onchange methods
    @api.onchange('access_result')
    def _onchange_access_result(self):
        """Update security flags based on access result"""
        if self.access_result == 'denied':
            self.is_alert = True
            self.severity = 'medium'
            self.requires_investigation = True
        elif self.access_result in ['forced', 'tailgating']:
            self.is_alert = True
            self.is_security_violation = True
            self.is_unauthorized_access = True
            self.severity = 'high'
            self.priority = '4'
            self.requires_investigation = True
        elif self.access_result == 'duress':
            self.is_alert = True
            self.is_security_violation = True
            self.severity = 'critical'
            self.priority = '4'
            self.requires_investigation = True
        elif self.access_result == 'granted':
            self.is_alert = False
            self.severity = 'low'
            self.requires_investigation = False
    
    @api.onchange('access_type')
    def _onchange_access_type(self):
        """Update fields based on access type"""
        if self.access_type == 'visitor':
            # Clear employee-related fields for visitors
            self.card_number = False
            self.employee_id = False
        elif self.access_type in ['card', 'pin']:
            # Clear visitor-related fields for employees
            self.visitor_name = False
            self.visitor_company = False
            self.host_employee = False
            self.visit_purpose = False
        elif self.access_type == 'biometric':
            # Set default biometric type
            if not self.biometric_type:
                self.biometric_type = 'fingerprint'
    
    @api.onchange('requires_investigation')
    def _onchange_requires_investigation(self):
        """Update investigation status when investigation is required"""
        if self.requires_investigation and self.investigation_status == 'pending':
            self.investigation_status = 'pending'
        elif not self.requires_investigation:
            self.investigation_status = 'closed'
    
    # Action methods
    def action_grant_access(self):
        """Manually grant access (for manual override)"""
        self.ensure_one()
        self.access_result = 'granted'
        self.access_type = 'manual'
        self.message_post(
            body=f"Access manually granted for '{self.card_holder_name or self.visitor_name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Access granted for '{self.name}'.",
                'type': 'success',
            }
        }
    
    def action_deny_access(self):
        """Manually deny access"""
        self.ensure_one()
        self.access_result = 'denied'
        self.is_alert = True
        self.message_post(
            body=f"Access manually denied for '{self.card_holder_name or self.visitor_name}' at {self.location_display}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Access denied for '{self.name}'.",
                'type': 'warning',
            }
        }
    
    def action_start_investigation(self):
        """Start security investigation"""
        self.ensure_one()
        self.investigation_status = 'in_progress'
        self.investigated_by = self.env.user.id
        self.message_post(
            body=f"Security investigation started for access event '{self.name}' by {self.env.user.name}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Investigation started for '{self.name}'.",
                'type': 'info',
            }
        }
    
    def action_complete_investigation(self):
        """Complete security investigation"""
        self.ensure_one()
        self.investigation_status = 'completed'
        self.investigation_date = fields.Datetime.now()
        if not self.investigated_by:
            self.investigated_by = self.env.user.id
        self.message_post(
            body=f"Security investigation completed for access event '{self.name}' by {self.env.user.name}.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Investigation completed for '{self.name}'.",
                'type': 'success',
            }
        }
    
    def action_close_investigation(self):
        """Close security investigation"""
        self.ensure_one()
        self.investigation_status = 'closed'
        if not self.investigation_date:
            self.investigation_date = fields.Datetime.now()
        if not self.investigated_by:
            self.investigated_by = self.env.user.id
        self.state = 'closed'
        self.closed_time = fields.Datetime.now()
        self.is_active = False
        self.message_post(
            body=f"Security investigation closed for access event '{self.name}'.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Investigation closed for '{self.name}'.",
                'type': 'success',
            }
        }
    
    def action_mark_security_violation(self):
        """Mark as security violation"""
        self.ensure_one()
        self.is_security_violation = True
        self.requires_investigation = True
        self.is_alert = True
        self.severity = 'high'
        self.message_post(
            body=f"Access event '{self.name}' marked as security violation.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"'{self.name}' marked as security violation.",
                'type': 'warning',
            }
        }
    
    def action_clear_security_violation(self):
        """Clear security violation flag"""
        self.ensure_one()
        self.is_security_violation = False
        self.is_unauthorized_access = False
        self.requires_investigation = False
        self.severity = 'low'
        self.message_post(
            body=f"Security violation cleared for access event '{self.name}'.",
            message_type='notification'
        )
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Security violation cleared for '{self.name}'.",
                'type': 'success',
            }
        }
    
    # Other business methods
    def get_access_event_summary(self):
        """Get access event specific summary"""
        self.ensure_one()
        summary = self.get_event_summary()
        summary.update({
            'card_number': self.card_number,
            'card_holder_name': self.card_holder_name,
            'employee_id': self.employee_id,
            'access_type': self.access_type,
            'access_result': self.access_result,
            'access_direction': self.access_direction,
            'access_level_required': self.access_level_required,
            'access_level_granted': self.access_level_granted,
            'time_zone_is_active': self.time_zone_is_active,
            'anti_passback_violation': self.anti_passback_violation,
            'biometric_type': self.biometric_type,
            'biometric_quality': self.biometric_quality,
            'biometric_match_score': self.biometric_match_score,
            'is_security_violation': self.is_security_violation,
            'is_unauthorized_access': self.is_unauthorized_access,
            'requires_investigation': self.requires_investigation,
            'investigation_status': self.investigation_status,
            'visitor_name': self.visitor_name,
            'visitor_company': self.visitor_company,
            'host_employee': self.host_employee,
            'visit_purpose': self.visit_purpose,
            'security_risk_level': self.security_risk_level,
            'access_summary': self.access_summary,
        })
        return summary
    
    def generate_access_report(self):
        """Generate access control report"""
        self.ensure_one()
        report_data = {
            'event_code': self.event_code,
            'event_time': self.event_time,
            'location': self.location_display,
            'person': {
                'card_number': self.card_number,
                'card_holder_name': self.card_holder_name,
                'employee_id': self.employee_id,
                'visitor_name': self.visitor_name,
                'visitor_company': self.visitor_company,
                'host_employee': self.host_employee,
            },
            'access_details': {
                'type': self.access_type,
                'result': self.access_result,
                'direction': self.access_direction,
                'level_required': self.access_level_required,
                'level_granted': self.access_level_granted,
                'time_zone_is_active': self.time_zone_is_active,
            },
            'biometric': {
                'type': self.biometric_type,
                'quality': self.biometric_quality,
                'match_score': self.biometric_match_score,
            },
            'security': {
                'risk_level': self.security_risk_level,
                'is_violation': self.is_security_violation,
                'is_unauthorized': self.is_unauthorized_access,
                'anti_passback_violation': self.anti_passback_violation,
                'requires_investigation': self.requires_investigation,
            },
            'investigation': {
                'status': self.investigation_status,
                'investigated_by': self.investigated_by.name if self.investigated_by else None,
                'investigation_date': self.investigation_date,
                'notes': self.investigation_notes,
            }
        }
        return report_data
    
    def check_access_patterns(self):
        """Check for unusual access patterns"""
        self.ensure_one()
        patterns = []
        
        # Check for multiple failed attempts
        if self.card_number:
            recent_failures = self.search([
                ('card_number', '=', self.card_number),
                ('access_result', '=', 'denied'),
                ('event_time', '>=', fields.Datetime.now() - timedelta(hours=1)),
                ('id', '!=', self.id)
            ])
            if len(recent_failures) >= 3:
                patterns.append('Multiple failed access attempts detected')
        
        # Check for unusual time access
        if self.event_time:
            hour = self.event_time.hour
            if hour < 6 or hour > 22:  # Outside normal hours
                patterns.append('Access attempt outside normal hours')
        
        # Check for rapid successive access
        if self.card_number:
            recent_access = self.search([
                ('card_number', '=', self.card_number),
                ('access_result', '=', 'granted'),
                ('event_time', '>=', fields.Datetime.now() - timedelta(minutes=5)),
                ('id', '!=', self.id)
            ])
            if recent_access:
                patterns.append('Rapid successive access attempts')
        
        return patterns