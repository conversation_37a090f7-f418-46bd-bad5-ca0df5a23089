<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Config Form View -->
    <record id="nebular_config_view_form" model="ir.ui.view">
        <field name="name">nebular.config.form</field>
        <field name="model">nebular.config</field>
        <field name="arch" type="xml">
            <form string="Nebular Configuration">
                <header>
                    <button name="action_activate" type="object" string="Activate Settings" 
                            class="btn-primary"/>
                    <button name="action_deactivate" type="object" string="Deactivate Settings" 
                            class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>Nebular Dashboard Configuration</h1>
                    </div>
                    <notebook>
                        <page string="General Settings">
                            <group>
                                <group string="System Information">
                                    <field name="nebular_company_name"/>
                                    <field name="nebular_system_name"/>
                                    <field name="nebular_system_version"/>
                                    <field name="nebular_timezone"/>
                                    <field name="nebular_language"/>
                                </group>
                            </group>
                        </page>
                        <page string="Dashboard Settings">
                            <group>
                                <group string="Display Settings">
                                    <field name="nebular_dashboard_refresh_interval"/>
                                    <field name="nebular_max_events_display"/>
                                    <field name="nebular_enable_real_time"/>
                                    <field name="nebular_enable_notifications"/>
                                </group>
                            </group>
                        </page>
                        <page string="Security Settings">
                            <group>
                                <group string="Authentication">
                                    <field name="nebular_enable_audit_log"/>
                                    <field name="nebular_session_timeout"/>
                                    <field name="nebular_max_login_attempts"/>
                                    <field name="nebular_lockout_duration"/>
                                </group>
                            </group>
                        </page>
                        <page string="System Monitoring">
                            <group>
                                <group string="Monitoring Settings">
                                    <field name="nebular_enable_system_monitoring"/>
                                    <field name="nebular_event_retention_days"/>
                                </group>
                            </group>
                        </page>
                        <page string="Integration Settings">
                            <group>
                                <group string="API Settings">
                                    <field name="nebular_enable_api"/>
                                    <field name="nebular_api_rate_limit"/>
                                </group>
                                <group string="Webhook Settings">
                                    <field name="nebular_enable_webhook"/>
                                    <field name="nebular_webhook_url"/>
                                    <field name="nebular_webhook_secret" password="True"/>
                                </group>
                            </group>
                        </page>
                        <page string="Device Settings">
                            <group>
                                <group string="Device Integration">
                                    <field name="nebular_enable_device_auto_discovery"/>
                                    <field name="nebular_device_heartbeat_interval"/>
                                    <field name="nebular_device_offline_threshold"/>
                                </group>
                            </group>
                        </page>
                        <page string="Backup &amp; Maintenance">
                            <group>
                                <group string="Backup Settings">
                                    <field name="nebular_enable_auto_backup"/>
                                    <field name="nebular_backup_frequency"/>
                                    <field name="nebular_backup_retention"/>
                                </group>
                                <group string="Maintenance">
                                    <field name="nebular_enable_maintenance_mode"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button string="Save" type="object" name="execute" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                    <button string="Reset to Defaults" type="object" name="action_reset_to_defaults" class="btn-link"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Config Action -->
    <record id="nebular_config_action" model="ir.actions.act_window">
        <field name="name">Nebular Configuration</field>
        <field name="res_model">nebular.config</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Configure your Nebular Dashboard System!
            </p>
            <p>
                Configure system settings, security parameters, alerts, integrations, 
                and other important settings for your Nebular Dashboard.
            </p>
        </field>
    </record>
</odoo>