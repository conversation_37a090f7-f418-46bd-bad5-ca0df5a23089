<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Device Type List View -->
    <record id="nebular_device_type_view_list" model="ir.ui.view">
        <field name="name">nebular.device.type.list</field>
        <field name="model">nebular.device.type</field>
        <field name="arch" type="xml">
            <list string="Device Types">
                <field name="name"/>
                <field name="code"/>
                <field name="category"/>
                <field name="manufacturer"/>
                <field name="device_count"/>
                <field name="is_active"/>
            </list>
        </field>
    </record>

    <!-- Device Type Form View -->
    <record id="nebular_device_type_view_form" model="ir.ui.view">
        <field name="name">nebular.device.type.form</field>
        <field name="model">nebular.device.type</field>
        <field name="arch" type="xml">
            <form string="Device Type">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            invisible="is_active" class="btn-primary"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            invisible="not is_active" class="btn-secondary"/>
                    <button name="action_view_devices" type="object" string="View Devices" 
                            invisible="device_count == 0" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-microchip">
                            <field name="device_count" widget="statinfo" string="Devices"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="category"/>
                            <field name="is_active"/>
                        </group>
                        <group>
                            <field name="manufacturer"/>
                            <field name="model_number"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Detailed description of the device type..."/>
                        </page>
                        <page string="Technical Specifications" name="specifications">
                            <group>
                                <field name="specifications" placeholder="Technical specifications and features..."/>
                                <field name="default_config" placeholder="Default configuration parameters..."/>
                                <field name="supported_protocols" placeholder="Communication protocols supported..."/>
                            </group>
                        </page>
                        <page string="Devices" name="devices" invisible="device_count == 0">
                            <field name="device_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="device_id"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Device Type Search View -->
    <record id="nebular_device_type_view_search" model="ir.ui.view">
        <field name="name">nebular.device.type.search</field>
        <field name="model">nebular.device.type</field>
        <field name="arch" type="xml">
            <search string="Device Types">
                <field name="name"/>
                <field name="code"/>
                <field name="category"/>
                <field name="manufacturer"/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                    <filter string="Manufacturer" name="group_manufacturer" context="{'group_by': 'manufacturer'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'is_active'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Device Type Action -->
    <record id="nebular_device_type_action" model="ir.actions.act_window">
        <field name="name">Device Types</field>
        <field name="res_model">nebular.device.type</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first device type!
            </p>
            <p>
                Device types define the categories and specifications of devices in your system.
            </p>
        </field>
    </record>

    <!-- Device Type Menu -->
    <menuitem id="nebular_device_type_menu"
              name="Device Types"
              parent="nebular.nebular_configuration_menu"
              action="nebular_device_type_action"
              sequence="10"/>
</odoo>