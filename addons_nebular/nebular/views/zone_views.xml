<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Zone Form View -->
    <record id="nebular_zone_view_form" model="ir.ui.view">
        <field name="name">nebular.zone.form</field>
        <field name="model">nebular.zone</field>
        <field name="arch" type="xml">
            <form string="Zone">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            invisible="is_active" class="btn-primary"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            invisible="not is_active" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Zone Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="building_id"/>
                            <field name="is_active"/>
                        </group>
                        <group>
                            <field name="description"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Rooms">
                            <field name="room_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="room_number"/>
                                    <field name="room_type"/>
                                    <field name="area"/>
                                    <field name="capacity"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Statistics">
                            <group>
                                <group>
                                    <field name="floor_count"/>
                                    <field name="room_count"/>
                                    <field name="door_count"/>
                                </group>
                                <group>
                                    <field name="device_count"/>
                                    <field name="event_count"/>
                                    <field name="marker_count"/>
                                </group>
                            </group>
                        </page>
                        <page string="Systems &amp; Control">
                            <group>
                                <group>
                                    <field name="system_ids" widget="many2many_tags"/>
                                    <field name="device_ids" widget="many2many_tags"/>
                                </group>
                                <group>
                                    <field name="marker_ids" widget="many2many_tags"/>
                                    <field name="event_ids" widget="many2many_tags"/>
                                </group>
                            </group>
                        </page>




                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Zone List View -->
    <record id="nebular_zone_view_list" model="ir.ui.view">
        <field name="name">nebular.zone.list</field>
        <field name="model">nebular.zone</field>
        <field name="arch" type="xml">
            <list string="Zones">
                <field name="name"/>
                <field name="code"/>
                <field name="building_id"/>
                <field name="floor_count"/>
                <field name="room_count"/>
                <field name="is_active"/>
            </list>
        </field>
    </record>

    <!-- Zone Kanban View -->
    <record id="nebular_zone_view_kanban" model="ir.ui.view">
        <field name="name">nebular.zone.kanban</field>
        <field name="model">nebular.zone</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="code"/>
                <field name="building_id"/>
                <field name="room_count"/>
                <field name="is_active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="code"/> - <field name="building_id"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_manage_button_section">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>Floors: <field name="floor_count"/></span><br/>
                                            <span>Rooms: <field name="room_count"/></span>
                                        </div>
                                        <div class="col-6">
                                            <span>Doors: <field name="door_count"/></span><br/>
                                            <span>Devices: <field name="device_count"/></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="is_active" widget="boolean_toggle"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Zone Search View -->
    <record id="nebular_zone_view_search" model="ir.ui.view">
        <field name="name">nebular.zone.search</field>
        <field name="model">nebular.zone</field>
        <field name="arch" type="xml">
            <search string="Zones">
                <field name="name"/>
                <field name="code"/>
                <field name="building_id"/>
                <field name="description"/>
                <separator/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Building" name="group_by_building" context="{'group_by': 'building_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Zone Action -->
    <record id="nebular_zone_action" model="ir.actions.act_window">
        <field name="name">Zones</field>
        <field name="res_model">nebular.zone</field>
        <field name="view_mode">list,form,kanban</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first zone!
            </p>
            <p>
                Zones are logical groupings of rooms within floors that share common systems or characteristics.
                They help organize and control building automation systems efficiently.
            </p>
        </field>
    </record>
</odoo>