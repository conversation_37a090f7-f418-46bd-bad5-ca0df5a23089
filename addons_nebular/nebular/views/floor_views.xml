<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Floor List View -->
    <record id="nebular_floor_view_list" model="ir.ui.view">
        <field name="name">nebular.floor.list</field>
        <field name="model">nebular.floor</field>
        <field name="arch" type="xml">
            <list string="Floors">
                <field name="name"/>
                <field name="code"/>
                <field name="floor_number"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="room_count"/>
                <field name="door_count"/>
                <field name="device_count"/>
                <field name="is_active"/>
            </list>
        </field>
    </record>

    <!-- Floor Form View -->
    <record id="nebular_floor_view_form" model="ir.ui.view">
        <field name="name">nebular.floor.form</field>
        <field name="model">nebular.floor</field>
        <field name="arch" type="xml">
            <form string="Floor">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            invisible="is_active" class="btn-primary"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            invisible="not is_active" class="btn-secondary"/>
                    <button name="action_view_rooms" type="object" string="View Rooms" 
                            invisible="room_count == 0" class="btn-secondary"/>
                    <button name="action_view_devices" type="object" string="View Devices" 
                            invisible="device_count == 0" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_rooms" type="object" class="oe_stat_button" icon="fa-home">
                            <field name="room_count" widget="statinfo" string="Rooms"/>
                        </button>
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-cog">
                            <field name="device_count" widget="statinfo" string="Devices"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_number"/>
                            <field name="is_active"/>
                        </group>
                        <group>
                            <field name="floor_number"/>
                            <field name="description"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Detailed description of the floor..."/>
                        </page>
                        <page string="Floor Plan">
                            <group>
                                <field name="floor_plan_image"/>
                                <field name="floor_plan_filename"/>
                                <field name="has_floor_plan"/>
                            </group>
                        </page>
                        <page string="Statistics">
                            <group>
                                <field name="event_count"/>
                                <field name="has_alerts"/>
                                <field name="marker_count"/>
                            </group>
                        </page>
                        <page string="Rooms" name="rooms" invisible="room_count == 0">
                            <field name="room_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="room_number"/>
                                    <field name="room_type"/>
                                    <field name="capacity"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Doors" name="doors" invisible="door_count == 0">
                            <field name="door_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="door_number"/>
                                    <field name="door_type"/>
                                    <field name="is_active"/>
                                    <field name="is_locked"/>
                                </list>
                            </field>
                        </page>
                        <page string="Devices" name="devices" invisible="device_count == 0">
                            <field name="device_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="device_type_id"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Floor Search View -->
    <record id="nebular_floor_view_search" model="ir.ui.view">
        <field name="name">nebular.floor.search</field>
        <field name="model">nebular.floor</field>
        <field name="arch" type="xml">
            <search string="Floors">
                <field name="name"/>
                <field name="floor_number"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <filter string="Ground Floor" name="ground_floor" domain="[('floor_number', '=', 0)]"/>
                <filter string="Upper Floors" name="upper_floors" domain="[('floor_number', '>', 0)]"/>
                <filter string="Basement" name="basement" domain="[('floor_number', '&lt;', 0)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Zone" name="group_zone" context="{'group_by': 'zone_id'}"/>
                    <filter string="Floor Number" name="group_floor_number" context="{'group_by': 'floor_number'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Floor Action -->
    <record id="nebular_floor_action" model="ir.actions.act_window">
        <field name="name">Floors</field>
        <field name="res_model">nebular.floor</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first floor!
            </p>
            <p>
                Floors represent different levels in your building and contain rooms, doors, and devices.
            </p>
        </field>
    </record>

</odoo>