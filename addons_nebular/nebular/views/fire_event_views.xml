<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Fire Event List View -->
    <record id="nebular_fire_event_view_list" model="ir.ui.view">
        <field name="name">nebular.fire.event.list</field>
        <field name="model">nebular.fire.event</field>
        <field name="arch" type="xml">
            <list string="Fire Events">
                <field name="name"/>
                <field name="event_code"/>
                <field name="alarm_type"/>
                <field name="fire_zone"/>
                <field name="detector_type"/>
                <field name="sensor_status"/>
                <field name="severity"/>
                <field name="state"/>
                <field name="building_id"/>
                <field name="event_time"/>
                <field name="is_alert" optional="hide"/>
            </list>
        </field>
    </record>

    <!-- Fire Event Form View -->
    <record id="nebular_fire_event_view_form" model="ir.ui.view">
        <field name="name">nebular.fire.event.form</field>
        <field name="model">nebular.fire.event</field>
        <field name="arch" type="xml">
            <form string="Fire Event">
                <header>
                    <button name="action_acknowledge" type="object" string="Acknowledge" 
                            invisible="state == 'acknowledged'" class="btn-primary"/>
                    <button name="action_resolve" type="object" string="Resolve" 
                            invisible="state == 'resolved'" class="btn-success"/>
                    <button name="action_close" type="object" string="Close" 
                            invisible="state == 'closed'" class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="new,acknowledged,resolved,closed"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="event_code"/>
                            <field name="alarm_type"/>
                            <field name="fire_zone"/>
                            <field name="detector_type"/>
                            <field name="sensor_status"/>
                            <field name="severity"/>
                            <field name="priority"/>
                        </group>
                        <group>
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                            <field name="room_id"/>
                            <field name="device_id"/>
                            <field name="system_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Fire Details" name="fire_details">
                            <group>
                                <group>

                                    <field name="smoke_level"/>
                                    <field name="temperature"/>

                                </group>
                                <group>
                                    <field name="evacuation_required"/>
                                    <field name="sprinkler_activated"/>
                                </group>
                            </group>
                            <group string="Description">
                                <field name="description" nolabel="1" placeholder="Fire event description..."/>
                            </group>
                        </page>
                        <page string="Event Details" name="event_details">
                            <group>
                                <group>
                                    <field name="event_time"/>
                                    <field name="acknowledged_time"/>
                                    <field name="resolved_time"/>
                                    <field name="closed_time"/>
                                </group>
                                <group>
                                    <field name="acknowledged_user_id"/>
                                    <field name="resolved_user_id"/>
                                    <field name="assigned_user_id"/>
                                    <field name="is_alert"/>
                                </group>
                            </group>
                        </page>
                        <page string="Response" name="response">
                            <group>
                                <field name="response_time"/>
                                <field name="duration"/>
                                <field name="age_display"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Fire Event Search View -->
    <record id="nebular_fire_event_view_search" model="ir.ui.view">
        <field name="name">nebular.fire.event.search</field>
        <field name="model">nebular.fire.event</field>
        <field name="arch" type="xml">
            <search string="Fire Events">
                <field name="name"/>
                <field name="event_code"/>
                <field name="alarm_type"/>
                <field name="fire_zone"/>
                <field name="detector_type"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>

                <filter string="New" name="new" domain="[('state', '=', 'new')]"/>
                <filter string="Acknowledged" name="acknowledged" domain="[('state', '=', 'acknowledged')]"/>
                <filter string="Resolved" name="resolved" domain="[('state', '=', 'resolved')]"/>
                <filter string="Critical" name="critical" domain="[('severity', '=', 'critical')]"/>
                <filter string="High" name="high" domain="[('severity', '=', 'high')]"/>
                <filter string="Evacuation Required" name="evacuation" domain="[('evacuation_required', '=', True)]"/>
                <filter string="Today" name="today" domain="[('event_time', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('event_time', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Alarm Type" name="group_alarm_type" context="{'group_by': 'alarm_type'}"/>
                    <filter string="Fire Zone" name="group_fire_zone" context="{'group_by': 'fire_zone'}"/>
                    <filter string="Detector Type" name="group_detector_type" context="{'group_by': 'detector_type'}"/>
                    <filter string="Severity" name="group_severity" context="{'group_by': 'severity'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Event Date" name="group_event_date" context="{'group_by': 'event_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Fire Event Action -->
    <record id="nebular_fire_event_action" model="ir.actions.act_window">
        <field name="name">Fire Events</field>
        <field name="res_model">nebular.fire.event</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_new': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No fire events found!
            </p>
            <p>
                Fire events are generated by fire detection systems to notify about fire-related incidents.
            </p>
        </field>
    </record>

</odoo>