<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- PA Event List View -->
    <record id="nebular_pa_event_view_list" model="ir.ui.view">
        <field name="name">nebular.pa.event.list</field>
        <field name="model">nebular.pa.event</field>
        <field name="arch" type="xml">
            <list string="PA Events" default_order="event_time desc">
                <field name="event_time"/>
                <field name="event_code"/>
                <field name="zone_code"/>
                <field name="zone_name"/>
                <field name="announcement_type" />
                <field name="status" />
                <field name="priority" />
                <field name="volume"/>
                <field name="duration_sec"/>
                <field name="building_id"/>
                <field name="severity" decoration-danger="severity >= 3" decoration-warning="severity == 2"/>
                <field name="source_event_code"/>
                <field name="source_state"/>
            </list>
        </field>
    </record>

    <!-- PA Event Form View -->
    <record id="nebular_pa_event_view_form" model="ir.ui.view">
        <field name="name">nebular.pa.event.form</field>
        <field name="model">nebular.pa.event</field>
        <field name="arch" type="xml">
            <form string="PA Event">
                <header>
                    <field name="status" widget="statusbar" />
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <!-- Add action buttons here if needed -->
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="zone_code" placeholder="Zone Code"/>
                        </h1>
                        <h2>
                            <field name="zone_name" placeholder="Zone Name"/>
                        </h2>
                    </div>
                    <group>
                        <group name="announcement_info" string="Announcement Information">
                            <field name="announcement_type"/>
                            <field name="priority"/>
                            <field name="language"/>
                            <field name="announcement_id"/>
                        </group>
                        <group name="event_info" string="Event Information">
                            <field name="event_time"/>
                            <field name="event_code"/>
                            <field name="event_type"/>
                            <field name="severity"/>
                             <field name="source_event_code"/>
                <field name="source_state"/>
                            <field name="zone_id"/>
                        </group>
                    </group>
                    <group>
                        <group name="location_info" string="Location">
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                            <field name="location"/>
                        </group>
                        <group name="audio_settings" string="Audio Settings">
                            <field name="volume"/>
                            <field name="duration_sec"/>
                            <field name="repeat_count"/>
                            <field name="script"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Message" name="message">
                            <group>
                                <field name="message" widget="text" nolabel="1"/>
                            </group>
                        </page>
                        <page string="Details" name="details">
                            <group>
                                <field name="description" widget="text"/>
                            </group>
                        </page>
                        <page string="System Information" name="system_info">
                            <group>
                                <field name="system_id"/>
                                <field name="create_date"/>
                                <field name="write_date"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- PA Event Search View -->
    <record id="nebular_pa_event_view_search" model="ir.ui.view">
        <field name="name">nebular.pa.event.search</field>
        <field name="model">nebular.pa.event</field>
        <field name="arch" type="xml">
            <search string="Search PA Events">
                <field name="event_code"/>
                <field name="zone_code"/>
                <field name="zone_name"/>
                <field name="announcement_id"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="floor_id"/>
                <field name="pa_message"/>
                <separator/>
                <filter string="Emergency" name="emergency" domain="[('announcement_type', '=', 'emergency')]"/>
                <filter string="Evacuation" name="evacuation" domain="[('announcement_type', '=', 'evacuation')]"/>
                <filter string="General" name="general" domain="[('announcement_type', '=', 'general')]"/>
                <filter string="Test" name="test" domain="[('announcement_type', '=', 'test')]"/>

                <filter string="Today" name="today" domain="[('event_time', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('event_time', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="this_week" domain="[('event_time', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('event_time', '&lt;', (context_today() + datetime.timedelta(days=7-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" domain="[('event_time', '>=', datetime.datetime.now().strftime('%Y-%m-01'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Announcement Type" name="group_announcement_type" context="{'group_by': 'announcement_type'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                    <filter string="Priority" name="group_priority" context="{'group_by': 'priority'}"/>
                    <filter string="Language" name="group_language" context="{'group_by': 'language'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Zone" name="group_zone" context="{'group_by': 'zone_id'}"/>
                    <filter string="Floor" name="group_floor" context="{'group_by': 'floor_id'}"/>
                    <filter string="Event Date" name="group_event_date" context="{'group_by': 'event_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- PA Event Action -->
    <record id="nebular_pa_event_action" model="ir.actions.act_window">
        <field name="name">PA Events</field>
        <field name="res_model">nebular.pa.event</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_today': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No PA events found!
            </p>
            <p>
                PA events are generated for public address announcements, emergency broadcasts, and system notifications.
            </p>
        </field>
    </record>

</odoo>