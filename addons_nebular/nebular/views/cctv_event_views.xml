<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- CCTV Event List View -->
    <record id="nebular_cctv_event_view_list" model="ir.ui.view">
        <field name="name">nebular.cctv.event.list</field>
        <field name="model">nebular.cctv.event</field>
        <field name="arch" type="xml">
            <list string="CCTV Events">
                <field name="name"/>
                <field name="event_code"/>
                <field name="camera_code"/>
                <field name="camera_name"/>
                <field name="detection_type"/>
                <field name="camera_status"/>
                <field name="confidence_level"/>
                <field name="severity"/>
                <field name="state"/>
                <field name="building_id"/>
                <field name="event_time"/>
                <field name="is_alert" optional="hide"/>
            </list>
        </field>
    </record>

    <!-- CCTV Event Form View -->
    <record id="nebular_cctv_event_view_form" model="ir.ui.view">
        <field name="name">nebular.cctv.event.form</field>
        <field name="model">nebular.cctv.event</field>
        <field name="arch" type="xml">
            <form string="CCTV Event">
                <header>
                    <button name="action_acknowledge" type="object" string="Acknowledge" 
                            invisible="state == 'acknowledged'" class="btn-primary"/>
                    <button name="action_resolve" type="object" string="Resolve" 
                            invisible="state == 'resolved'" class="btn-success"/>
                    <button name="action_close" type="object" string="Close" 
                            invisible="state == 'closed'" class="btn-secondary"/>
                    <button name="action_review_event" type="object" string="Review Event" 
                            class="btn-info"/>
                    <button name="action_download_video" type="object" string="Download Video" 
                            invisible="not video_clip_path" class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="new,acknowledged,resolved,closed"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="event_code"/>
                            <field name="camera_code"/>
                            <field name="camera_name"/>
                            <field name="camera_location"/>
                            <field name="camera_status"/>
                            <field name="detection_type"/>
                            <field name="confidence_level"/>
                            <field name="severity"/>
                            <field name="priority"/>
                        </group>
                        <group>
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                            <field name="room_id"/>
                            <field name="device_id"/>
                            <field name="system_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Detection Details" name="detection_details">
                            <group>
                                <group string="Object Detection">
                                    <field name="object_count"/>
                                    <field name="person_count"/>
                                    <field name="vehicle_count"/>
                                    <field name="motion_area_percentage"/>
                                    <field name="detection_zone"/>
                                </group>
                                <group string="Recognition">
                                    <field name="face_detected"/>
                                    <field name="face_recognized"/>
                                    <field name="recognized_person"/>
                                    <field name="face_match_confidence"/>
                                    <field name="license_plate_detected"/>
                                    <field name="license_plate_number"/>
                                    <field name="plate_confidence"/>
                                </group>
                            </group>
                        </page>
                        <page string="Media" name="media">
                            <group>
                                <group string="Video/Image">
                                    <field name="video_clip_path"/>
                                    <field name="snapshot_path"/>
                                    <field name="recording_duration"/>
                                </group>
                                <group string="Security">
                                    <field name="is_security_alert"/>
                                    <field name="alert_sent"/>
                                    <field name="response_required"/>
                                    <field name="event_classification"/>
                                </group>
                            </group>
                        </page>
                        <page string="Review" name="review">
                            <group>
                                <group>
                                    <field name="reviewed"/>
                                    <field name="reviewed_by"/>
                                    <field name="review_date"/>
                                    <field name="archived"/>
                                    <field name="retention_date"/>
                                </group>
                            </group>
                            <group string="Review Notes">
                                <field name="review_notes" nolabel="1" placeholder="Review notes..."/>
                            </group>
                        </page>
                        <page string="Event Details" name="event_details">
                            <group>
                                <group>
                                    <field name="event_time"/>
                                    <field name="acknowledged_time"/>
                                    <field name="resolved_time"/>
                                    <field name="closed_time"/>
                                </group>
                                <group>
                                    <field name="acknowledged_user_id"/>
                                    <field name="resolved_user_id"/>
                                    <field name="assigned_user_id"/>
                                    <field name="is_alert"/>
                                </group>
                            </group>
                            <group string="Description">
                                <field name="description" nolabel="1" placeholder="CCTV event description..."/>
                            </group>
                        </page>
                        <page string="Response" name="response">
                            <group>
                                <field name="response_time"/>
                                <field name="duration"/>
                                <field name="age_display"/>
                                <field name="detection_summary"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- CCTV Event Search View -->
    <record id="nebular_cctv_event_view_search" model="ir.ui.view">
        <field name="name">nebular.cctv.event.search</field>
        <field name="model">nebular.cctv.event</field>
        <field name="arch" type="xml">
            <search string="CCTV Events">
                <field name="name"/>
                <field name="event_code"/>
                <field name="camera_code"/>
                <field name="camera_name"/>
                <field name="detection_type"/>
                <field name="recognized_person"/>
                <field name="license_plate_number"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>

                <filter string="New" name="new" domain="[('state', '=', 'new')]"/>
                <filter string="Acknowledged" name="acknowledged" domain="[('state', '=', 'acknowledged')]"/>
                <filter string="Resolved" name="resolved" domain="[('state', '=', 'resolved')]"/>
                <filter string="Security Alert" name="security_alert" domain="[('is_security_alert', '=', True)]"/>
                <filter string="Face Detected" name="face_detected" domain="[('face_detected', '=', True)]"/>
                <filter string="Face Recognized" name="face_recognized" domain="[('face_recognized', '=', True)]"/>
                <filter string="License Plate Detected" name="plate_detected" domain="[('license_plate_detected', '=', True)]"/>
                <filter string="Not Reviewed" name="not_reviewed" domain="[('reviewed', '=', False)]"/>
                <filter string="Critical" name="critical" domain="[('severity', '=', 'critical')]"/>
                <filter string="High" name="high" domain="[('severity', '=', 'high')]"/>
                <filter string="Today" name="today" domain="[('event_time', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('event_time', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Detection Type" name="group_detection_type" context="{'group_by': 'detection_type'}"/>
                    <filter string="Camera" name="group_camera" context="{'group_by': 'camera_code'}"/>
                    <filter string="Camera Status" name="group_camera_status" context="{'group_by': 'camera_status'}"/>
                    <filter string="Classification" name="group_classification" context="{'group_by': 'event_classification'}"/>
                    <filter string="Severity" name="group_severity" context="{'group_by': 'severity'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Event Date" name="group_event_date" context="{'group_by': 'event_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- CCTV Event Action -->
    <record id="nebular_cctv_event_action" model="ir.actions.act_window">
        <field name="name">CCTV Events</field>
        <field name="res_model">nebular.cctv.event</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_new': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No CCTV events found!
            </p>
            <p>
                CCTV events are generated by surveillance cameras to notify about detected activities and incidents.
            </p>
        </field>
    </record>

</odoo>