<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Access Event List View -->
    <record id="nebular_access_event_view_list" model="ir.ui.view">
        <field name="name">nebular.access.event.list</field>
        <field name="model">nebular.access.event</field>
        <field name="arch" type="xml">
            <list string="Access Events">
                <field name="name"/>
                <field name="event_code"/>
                <field name="user_code"/>
                <field name="card_number"/>
                <field name="access_type"/>
                <field name="access_result"/>
                <field name="access_point"/>
                <field name="severity"/>
                 <field name="source_event_code"/>
                            <field name="source_state"/>
                <field name="state"/>
                <field name="building_id"/>
                <field name="event_time"/>
                <field name="is_alert" optional="hide"/>
            </list>
        </field>
    </record>

    <!-- Access Event Form View -->
    <record id="nebular_access_event_view_form" model="ir.ui.view">
        <field name="name">nebular.access.event.form</field>
        <field name="model">nebular.access.event</field>
        <field name="arch" type="xml">
            <form string="Access Event">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="new,acknowledged,resolved,closed"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="event_code"/>
                            <field name="user_code"/>
                            <field name="card_number"/>
                            <field name="access_type"/>
                            <field name="access_result"/>
                            <field name="access_point"/>
                             <field name="source_event_code"/>
                            <field name="source_state"/>
                            <field name="severity"/>
                            <field name="priority"/>
                        </group>
                        <group>
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                            <field name="room_id"/>
                            <field name="door_id"/>
                            <field name="device_id"/>
                            <field name="employee_code"/>
                        </group>
                    </group>
                    <notebook>

                        <page string="Event Details" name="event_details">
                            <group>
                                <group>
                                                                    <field name="message" widget="text"/>

                                    <field name="event_time"/>
                                    <field name="acknowledged_time"/>
                                    <field name="resolved_time"/>
                                    <field name="closed_time"/>
                                </group>
                                <group>
                                    <field name="acknowledged_user_id"/>
                                    <field name="resolved_user_id"/>
                                    <field name="assigned_user_id"/>
                                    <field name="is_alert"/>
                                </group>
                            </group>
                        </page>
                        <page string="Response" name="response">
                            <group>
                                <field name="response_time"/>
                                <field name="duration"/>
                                <field name="age_display"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Access Event Search View -->
    <record id="nebular_access_event_view_search" model="ir.ui.view">
        <field name="name">nebular.access.event.search</field>
        <field name="model">nebular.access.event</field>
        <field name="arch" type="xml">
            <search string="Access Events">
                <field name="name"/>
                <field name="event_code"/>
                <field name="user_code"/>
                <field name="card_number"/>
                <field name="access_type"/>
                <field name="access_point"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <field name="door_id"/>

                <filter string="New" name="new" domain="[('state', '=', 'new')]"/>
                <filter string="Acknowledged" name="acknowledged" domain="[('state', '=', 'acknowledged')]"/>
                <filter string="Resolved" name="resolved" domain="[('state', '=', 'resolved')]"/>
                 <filter string="Critical" name="critical" domain="[('severity', '=', 'critical')]"/>
                <filter string="High" name="high" domain="[('severity', '=', 'high')]"/>
                <filter string="Today" name="today" domain="[('event_time', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('event_time', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Access Type" name="group_access_type" context="{'group_by': 'access_type'}"/>
                    <filter string="Access Result" name="group_access_result" context="{'group_by': 'access_result'}"/>
                    <filter string="Severity" name="group_severity" context="{'group_by': 'severity'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Door" name="group_door" context="{'group_by': 'door_id'}"/>
                    <filter string="Event Date" name="group_event_date" context="{'group_by': 'event_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Access Event Action -->
    <record id="nebular_access_event_action" model="ir.actions.act_window">
        <field name="name">Access Events</field>
        <field name="res_model">nebular.access.event</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_new': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No access events found!
            </p>
            <p>
                Access events are generated by access control systems to track entry and exit activities.
            </p>
        </field>
    </record>

</odoo>