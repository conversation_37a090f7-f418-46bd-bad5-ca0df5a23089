<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Presence Event List View -->
    <record id="nebular_presence_event_view_list" model="ir.ui.view">
        <field name="name">nebular.presence.event.list</field>
        <field name="model">nebular.presence.event</field>
        <field name="arch" type="xml">
            <list string="Presence Events" default_order="event_time desc">
                <field name="event_time"/>
                <field name="event_code"/>
                <field name="sensor_code"/>
                <field name="name"/>
                <field name="type"/>
                <field name="occupancy" decoration-success="occupancy == True" decoration-muted="occupancy == False"/>
                <field name="count"/>
                <field name="motion_type"/>
                <field name="confidence_level"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="floor_id"/>
                <field name="system_id"/>
                <field name="source_event_code"/>
                <field name="source_state"/>
                <field name="severity" decoration-danger="severity >= 3" decoration-warning="severity == 2"/>
            </list>
        </field>
    </record>

    <!-- Presence Event Form View -->
    <record id="nebular_presence_event_view_form" model="ir.ui.view">
        <field name="name">nebular.presence.event.form</field>
        <field name="model">nebular.presence.event</field>
        <field name="arch" type="xml">
            <form string="Presence Event">
                <header>
                    <button name="action_acknowledge_presence" string="Acknowledge" type="object" class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <!-- Add action buttons here if needed -->
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="sensor_code" placeholder="Sensor Code"/>
                        </h1>
                        <h2>
                            <field name="name" placeholder="Sensor Name"/>
                        </h2>
                    </div>
                    <group>
                        <group name="sensor_info" string="Sensor Information">
                            <field name="sensor_id"/>
                            <field name="type"/>
                            <field name="vendor"/>
                            <field name="model"/>
                            <field name="sensitivity"/>
                            <field name="severity"/>
                        </group>
                        <group name="detection_info" string="Detection Information">
                            <field name="occupancy"/>
                            <field name="count"/>
                            <field name="motion_type"/>
                            <field name="confidence_level"/>
                            <field name="duration_seconds"/>
                        </group>
                    </group>
                    <group>
                        <group name="location_info" string="Location">
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                            <field name="system_id"/>
                <field name="source_event_code"/>
                            <field name="source_state"/>
                            <field name="location"/>
                        </group>
                        <group name="technical_info" string="Technical Information">
                            <field name="detection_range"/>
                            <field name="temperature"/>
                            <field name="battery_level"/>
                            <field name="signal_strength"/>
                        </group>
                    </group>
                    <group>
                        <group name="event_info" string="Event Information">
                            <field name="event_time"/>
                            <field name="event_code"/>
                            <field name="event_type"/>
                            <field name="severity"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Details" name="details">
                            <group>
                                <field name="description" widget="text"/>
                                <field name="message" widget="text"/>
                            </group>
                        </page>
                        <page string="System Information" name="system_info">
                            <group>
                                <field name="system_id"/>
                                <field name="create_date"/>
                                <field name="write_date"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
               <chatter/>
            </form>
        </field>
    </record>

    <!-- Presence Event Search View -->
    <record id="nebular_presence_event_view_search" model="ir.ui.view">
        <field name="name">nebular.presence.event.search</field>
        <field name="model">nebular.presence.event</field>
        <field name="arch" type="xml">
            <search string="Search Presence Events">
                <field name="event_code"/>
                <field name="sensor_code"/>
                <field name="name"/>
                <field name="vendor"/>
                <field name="model"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="floor_id"/>
                <separator/>
                <filter string="Occupied" name="occupied" domain="[('occupancy', '=', True)]"/>
                <filter string="Vacant" name="vacant" domain="[('occupancy', '=', False)]"/>
                <separator/>
                <filter string="PIR Sensors" name="type_pir" domain="[('type', '=', 'pir')]"/>
                <filter string="Microwave Sensors" name="type_microwave" domain="[('type', '=', 'microwave')]"/>
                <filter string="Ultrasonic Sensors" name="type_ultrasonic" domain="[('type', '=', 'ultrasonic')]"/>
                <filter string="Camera Sensors" name="type_camera" domain="[('type', '=', 'camera')]"/>
                <separator/>
                <filter string="Entry" name="motion_entry" domain="[('motion_type', '=', 'entry')]"/>
                <filter string="Exit" name="motion_exit" domain="[('motion_type', '=', 'exit')]"/>
                <filter string="Movement" name="motion_movement" domain="[('motion_type', '=', 'movement')]"/>
                <filter string="Loitering" name="motion_loitering" domain="[('motion_type', '=', 'loitering')]"/>
                <separator/>
                <filter string="High Confidence" name="high_confidence" domain="[('confidence_level', '>=', 80)]"/>
                <filter string="Medium Confidence" name="medium_confidence" domain="[('confidence_level', '>=', 60), ('confidence_level', '&lt;', 80)]"/>
                <filter string="Low Confidence" name="low_confidence" domain="[('confidence_level', '&lt;', 60)]"/>
                <separator/>
                <filter string="Low Battery" name="low_battery" domain="[('battery_level', '&lt;', 20)]"/>
                <filter string="Weak Signal" name="weak_signal" domain="[('signal_strength', '&lt;', -70)]"/>
                <separator/>
                <filter string="Today" name="today" domain="[('event_time', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('event_time', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="this_week" domain="[('event_time', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('event_time', '&lt;', (context_today() + datetime.timedelta(days=7-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" domain="[('event_time', '>=', datetime.datetime.now().strftime('%Y-%m-01'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Occupancy" name="group_occupancy" context="{'group_by': 'occupancy'}"/>
                    <filter string="Sensor Type" name="group_type" context="{'group_by': 'type'}"/>
                    <filter string="Motion Type" name="group_motion_type" context="{'group_by': 'motion_type'}"/>
                    <filter string="Vendor" name="group_vendor" context="{'group_by': 'vendor'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Zone" name="group_zone" context="{'group_by': 'zone_id'}"/>
                    <filter string="Floor" name="group_floor" context="{'group_by': 'floor_id'}"/>
                    <filter string="Event Date" name="group_event_date" context="{'group_by': 'event_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Presence Event Action -->
    <record id="nebular_presence_event_action" model="ir.actions.act_window">
        <field name="name">Presence Events</field>
        <field name="res_model">nebular.presence.event</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_today': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No presence events found!
            </p>
            <p>
                Presence events are generated by motion sensors, occupancy detectors, and other presence detection systems.
            </p>
        </field>
    </record>

    <!-- Presence Event Kanban View -->
    <record id="nebular_presence_event_view_kanban" model="ir.ui.view">
        <field name="name">nebular.presence.event.kanban</field>
        <field name="model">nebular.presence.event</field>
        <field name="arch" type="xml">
            <kanban default_group_by="occupancy">
                <field name="sensor_code"/>
                <field name="name"/>
                <field name="occupancy"/>
                <field name="count"/>
                <field name="confidence_level"/>
                <field name="event_time"/>
                <field name="building_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="sensor_code"/>
                                    </strong>
                                    <br/>
                                    <span class="o_kanban_record_subtitle">
                                        <field name="name"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_top_right">
                                    <span t-if="record.occupancy.raw_value" class="badge badge-success">Occupied</span>
                                    <span t-else="" class="badge badge-secondary">Vacant</span>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Count:</strong> <field name="count"/>
                                    </div>
                                    <div class="col-6">
                                        <strong>Confidence:</strong> <field name="confidence_level"/>%
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <strong>Building:</strong> <field name="building_id"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>