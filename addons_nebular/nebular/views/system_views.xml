<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- System Form View -->
    <record id="nebular_system_view_form" model="ir.ui.view">
        <field name="name">nebular.system.form</field>
        <field name="model">nebular.system</field>
        <field name="arch" type="xml">
            <form string="System">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            invisible="is_active" class="btn-primary"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            invisible="not is_active" class="btn-secondary"/>
                    <button name="action_start_maintenance" type="object" string="Start Maintenance" 
                            class="btn-warning"/>
                    <button name="action_complete_maintenance" type="object" string="Complete Maintenance" 
                            class="btn-success"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="System Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="system_type"/>
                            <field name="manufacturer"/>
                            <field name="model"/>
                            <field name="building_id"/>
                        </group>
                        <group>
                            <field name="status"/>
                            <field name="health_status"/>
                            <field name="last_communication"/>
                            <field name="is_active"/>
                            <field name="icon_name"/>
                            <field name="icon_color"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="System description..."/>
                        </page>
                        <page string="Events">
                            <field name="event_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="is_active"/>
                                    <field name="event_time"/>
                                </list>
                            </field>
                        </page>
                        <page string="Portal Settings">
                            <group>
                                <field name="icon_name"/>
                                <field name="icon_color"/>
                            </group>
                        </page>
                        <page string="Statistics">
                            <group>
                                <field name="event_count"/>
                                <field name="display_name"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- System List View -->
    <record id="nebular_system_view_list" model="ir.ui.view">
        <field name="name">nebular.system.list</field>
        <field name="model">nebular.system</field>
        <field name="arch" type="xml">
            <list string="Systems">
                <field name="name"/>
                <field name="code"/>
                <field name="manufacturer"/>
                <field name="model"/>
                <field name="status"/>
                <field name="is_active"/>
            </list>
        </field>
    </record>

    <!-- System Kanban View -->
    <record id="nebular_system_view_kanban" model="ir.ui.view">
        <field name="name">nebular.system.kanban</field>
        <field name="model">nebular.system</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="code"/>
                <field name="system_type"/>
                <field name="building_id"/>
                <field name="manufacturer"/>
                <field name="model"/>
                <field name="status"/>
                <field name="health_status"/>
                <field name="last_communication"/>
                <field name="is_active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="code"/> - <field name="system_type"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_manage_button_section">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>Building: <field name="building_id"/></span>
                                        </div>
                                        <div class="col-6">
                                            <span>Manufacturer: <field name="manufacturer"/></span>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <span>Model: <field name="model"/></span>
                                        </div>
                                        <div class="col-6">
                                            <span>Last Comm: <field name="last_communication"/></span>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <span t-if="record.status.raw_value == 'online'" class="badge badge-success">Online</span>
                                            <span t-if="record.status.raw_value == 'offline'" class="badge badge-danger">Offline</span>
                                            <span t-if="record.status.raw_value == 'maintenance'" class="badge badge-warning">Maintenance</span>
                                            <span t-if="record.status.raw_value == 'error'" class="badge badge-danger">Error</span>
                                        </div>
                                        <div class="col-6">
                                            <span t-if="record.health_status.raw_value == 'healthy'" class="badge badge-success">Healthy</span>
                                            <span t-if="record.health_status.raw_value == 'warning'" class="badge badge-warning">Warning</span>
                                            <span t-if="record.health_status.raw_value == 'critical'" class="badge badge-danger">Critical</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="is_active" widget="boolean_toggle"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- System Search View -->
    <record id="nebular_system_view_search" model="ir.ui.view">
        <field name="name">nebular.system.search</field>
        <field name="model">nebular.system</field>
        <field name="arch" type="xml">
            <search string="Systems">
                <field name="name"/>
                <field name="code"/>
                <field name="system_type"/>
                <field name="building_id"/>
                <field name="manufacturer"/>
                <field name="model"/>
                <separator/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <separator/>
                <filter string="Online" name="online" domain="[('status', '=', 'online')]"/>
                <filter string="Offline" name="offline" domain="[('status', '=', 'offline')]"/>
                <filter string="Maintenance" name="maintenance" domain="[('status', '=', 'maintenance')]"/>
                <filter string="Error" name="error" domain="[('status', '=', 'error')]"/>
                <separator/>
                <filter string="Healthy" name="healthy" domain="[('health_status', '=', 'healthy')]"/>
                <filter string="Warning" name="warning" domain="[('health_status', '=', 'warning')]"/>
                <filter string="Critical" name="critical" domain="[('health_status', '=', 'critical')]"/>
                <separator/>
                <filter string="HVAC" name="hvac" domain="[('system_type', '=', 'hvac')]"/>
                <filter string="Lighting" name="lighting" domain="[('system_type', '=', 'lighting')]"/>
                <filter string="Security" name="security" domain="[('system_type', '=', 'security')]"/>
                <filter string="Fire Safety" name="fire_safety" domain="[('system_type', '=', 'fire_safety')]"/>
                <filter string="Access Control" name="access_control" domain="[('system_type', '=', 'access_control')]"/>
                <filter string="Energy Management" name="energy" domain="[('system_type', '=', 'energy')]"/>
                <separator/>
                <filter string="High Security" name="high_security" domain="[('security_level', '=', 'high')]"/>
                <filter string="Critical Security" name="critical_security" domain="[('security_level', '=', 'critical')]"/>
                <filter string="SSL Enabled" name="ssl_enabled" domain="[('ssl_enabled', '=', True)]"/>
                <filter string="Encryption Enabled" name="encryption" domain="[('encryption_enabled', '=', True)]"/>
                <separator/>
                <filter string="Supports Automation" name="automation" domain="[('supports_automation', '=', True)]"/>
                <filter string="Supports Monitoring" name="monitoring" domain="[('supports_monitoring', '=', True)]"/>
                <filter string="Supports Remote Control" name="remote_control" domain="[('supports_remote_control', '=', True)]"/>
                <filter string="Cloud Connected" name="cloud" domain="[('cloud_connectivity', '=', True)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Building" name="group_by_building" context="{'group_by': 'building_id'}"/>
                    <filter string="System Type" name="group_by_type" context="{'group_by': 'system_type'}"/>
                    <filter string="Manufacturer" name="group_by_manufacturer" context="{'group_by': 'manufacturer'}"/>
                    <filter string="Status" name="group_by_status" context="{'group_by': 'status'}"/>
                    <filter string="Health Status" name="group_by_health" context="{'group_by': 'health_status'}"/>
                    <filter string="Security Level" name="group_by_security" context="{'group_by': 'security_level'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- System Action -->
    <record id="nebular_system_action" model="ir.actions.act_window">
        <field name="name">Systems</field>
        <field name="res_model">nebular.system</field>
        <field name="view_mode">list,form,kanban</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first system!
            </p>
            <p>
                Systems represent the various building automation and control systems
                that manage different aspects of your smart building infrastructure.
            </p>
        </field>
    </record>
</odoo>