<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Device List View -->
    <record id="nebular_device_view_list" model="ir.ui.view">
        <field name="name">nebular.device.list</field>
        <field name="model">nebular.device</field>
        <field name="arch" type="xml">
            <list string="Devices">
                <field name="name"/>
                <field name="device_id"/>
                <field name="device_category"/>
                <field name="device_type_id"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="is_active"/>
                <field name="is_alert"/>
            </list>
        </field>
    </record>

    <!-- Device Form View -->
    <record id="nebular_device_view_form" model="ir.ui.view">
        <field name="name">nebular.device.form</field>
        <field name="model">nebular.device</field>
        <field name="arch" type="xml">
            <form string="Device">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            invisible="is_active" class="btn-primary"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            invisible="not is_active" class="btn-secondary"/>
                    <button name="action_sync" type="object" string="Sync" class="btn-secondary"/>
                    <button name="action_reset" type="object" string="Reset" class="btn-secondary"/>
                    <button name="action_view_events" type="object" string="View Events" 
                            invisible="event_count == 0" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_events" type="object" class="oe_stat_button" icon="fa-calendar">
                            <field name="event_count" widget="statinfo" string="Events"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="device_id"/>
                            <field name="device_type_id"/>
                            <field name="device_category" readonly="1"/>
                            <field name="is_active"/>
                            <field name="is_alert"/>
                        </group>
                        <group>
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                            <field name="room_id"/>
                            <field name="door_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Detailed description of the device..."/>
                        </page>
                        <page string="Technical Details" name="technical">
                            <group>
                                <group>
                                    <field name="manufacturer"/>
                                    <field name="model"/>
                                    <field name="serial_number"/>
                                    <field name="firmware_version"/>
                                </group>
                                <group>
                                    <field name="ip_address"/>
                                    <field name="mac_address"/>
                                    <field name="port"/>
                                    <field name="protocol"/>
                                </group>
                            </group>
                        </page>
                        <page string="Configuration" name="configuration">
                            <group>
                                <field name="configuration" placeholder="Device configuration parameters..."/>
                                <field name="settings" placeholder="Device settings..."/>
                            </group>
                        </page>
                        <page string="Status" name="status">
                            <group>
                                <group>
                                    <field name="is_online"/>
                                    <field name="last_seen"/>
                                    <field name="status_display" readonly="1"/>
                                </group>
                                <group>
                                    <field name="connection_status" readonly="1"/>
                                    <field name="uptime_display" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Events" name="events" invisible="event_count == 0">
                            <field name="event_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="severity"/>
                                    <field name="state"/>
                                    <field name="event_time"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Device Search View -->
    <record id="nebular_device_view_search" model="ir.ui.view">
        <field name="name">nebular.device.search</field>
        <field name="model">nebular.device</field>
        <field name="arch" type="xml">
            <search string="Devices">
                <field name="name"/>
                <field name="device_id"/>
                <field name="device_type_id"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <filter string="Alerts" name="alerts" domain="[('is_alert', '=', True)]"/>
                <filter string="Online" name="online" domain="[('is_online', '=', True)]"/>
                <filter string="Offline" name="offline" domain="[('is_online', '=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Device Type" name="group_device_type" context="{'group_by': 'device_type_id'}"/>
                    <filter string="Category" name="group_category" context="{'group_by': 'device_category'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Floor" name="group_floor" context="{'group_by': 'floor_id'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'status_display'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Device Action -->
    <record id="nebular_device_action" model="ir.actions.act_window">
        <field name="name">Devices</field>
        <field name="res_model">nebular.device</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first device!
            </p>
            <p>
                Devices are the physical components of your system that generate events and data.
            </p>
        </field>
    </record>

</odoo>