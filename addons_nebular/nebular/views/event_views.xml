<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Event List View -->
    <record id="nebular_event_view_list" model="ir.ui.view">
        <field name="name">nebular.event.list</field>
        <field name="model">nebular.event</field>
        <field name="arch" type="xml">
            <list string="Events">
                <field name="name"/>
                <field name="event_code"/>
                <field name="event_type"/>
                <field name="event_category"/>
                <field name="severity"/>
                <field name="state"/>
                <field name="device_id"/>
                <field name="building_id"/>
                <field name="event_time"/>
                <field name="is_alert"/>
            </list>
        </field>
    </record>

    <!-- Event Form View -->
    <record id="nebular_event_view_form" model="ir.ui.view">
        <field name="name">nebular.event.form</field>
        <field name="model">nebular.event</field>
        <field name="arch" type="xml">
            <form string="Event">
                <header>
                    <button name="action_acknowledge" type="object" string="Acknowledge" 
                            invisible="state == 'acknowledged'" class="btn-primary"/>
                    <button name="action_resolve" type="object" string="Resolve" 
                            invisible="state == 'resolved'" class="btn-success"/>

                    <button name="action_close" type="object" string="Close" 
                            invisible="state == 'closed'" class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="new,acknowledged,resolved,closed"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="event_code"/>
                            <field name="event_type"/>
                            <field name="event_category" readonly="1"/>
                            <field name="severity"/>
                            <field name="priority"/>
                        </group>
                        <group>
                            <field name="device_id"/>
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                            <field name="room_id"/>
                            <field name="door_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Event Details" name="details">
                            <group>
                                <group>
                                    <field name="event_time"/>
                                    <field name="acknowledged_time"/>
                                    <field name="resolved_time"/>
                                    <field name="closed_time"/>
                                </group>
                                <group>
                                    <field name="acknowledged_user_id"/>
                                    <field name="resolved_user_id"/>
                                    <field name="assigned_user_id"/>
                                    <field name="is_alert"/>
                                </group>
                            </group>
                            <group string="Description">
                                <field name="description" nolabel="1" placeholder="Event description..."/>
                            </group>
                        </page>
                        <page string="Metadata" name="metadata">
                            <group>
                                <field name="metadata" placeholder="Event metadata..."/>
                            </group>
                        </page>
                        <page string="Response" name="response">
                            <group>
                                <field name="response_time"/>
                                <field name="duration"/>
                                <field name="age_display"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Event Search View -->
    <record id="nebular_event_view_search" model="ir.ui.view">
        <field name="name">nebular.event.search</field>
        <field name="model">nebular.event</field>
        <field name="arch" type="xml">
            <search string="Events">
                <field name="name"/>
                <field name="event_code"/>
                <field name="event_type"/>
                <field name="device_id"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <filter string="New" name="new" domain="[('state', '=', 'new')]"/>
                <filter string="Acknowledged" name="acknowledged" domain="[('state', '=', 'acknowledged')]"/>
                <filter string="Resolved" name="resolved" domain="[('state', '=', 'resolved')]"/>
                <filter string="Closed" name="closed" domain="[('state', '=', 'closed')]"/>
                <filter string="Critical" name="critical" domain="[('severity', '=', 'critical')]"/>
                <filter string="High" name="high" domain="[('severity', '=', 'high')]"/>
                <filter string="Medium" name="medium" domain="[('severity', '=', 'medium')]"/>
                <filter string="Low" name="low" domain="[('severity', '=', 'low')]"/>
                <filter string="Today" name="today" domain="[('event_time', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('event_time', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Event Type" name="group_event_type" context="{'group_by': 'event_type'}"/>
                    <filter string="Category" name="group_category" context="{'group_by': 'event_category'}"/>
                    <filter string="Severity" name="group_severity" context="{'group_by': 'severity'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Device" name="group_device" context="{'group_by': 'device_id'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Floor" name="group_floor" context="{'group_by': 'floor_id'}"/>
                    <filter string="Event Date" name="group_event_date" context="{'group_by': 'event_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Event Action -->
    <record id="nebular_event_action" model="ir.actions.act_window">
        <field name="name">Events</field>
        <field name="res_model">nebular.event</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_new': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No events found!
            </p>
            <p>
                Events are generated by devices and systems to notify about various conditions and activities.
            </p>
        </field>
    </record>

</odoo>