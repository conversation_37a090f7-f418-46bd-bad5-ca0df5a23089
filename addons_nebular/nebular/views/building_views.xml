<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Building Form View -->
    <record id="nebular_building_view_form" model="ir.ui.view">
        <field name="name">nebular.building.form</field>
        <field name="model">nebular.building</field>
        <field name="arch" type="xml">
            <form string="Building">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            invisible="is_active" class="btn-primary"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            invisible="not is_active" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Building Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="is_active"/>
                        </group>
                        <group>
                            <field name="zone_count"/>
                            <field name="floor_count"/>
                            <field name="room_count"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Details">
                            <group>
                                <field name="description" widget="text"/>
                                <field name="address" widget="text"/>
                            </group>
                        </page>
                        <page string="Structure">
                            <group>
                                <field name="zone_ids" nolabel="1">
                                    <list>
                                        <field name="name"/>
                                        <field name="code"/>
                                        <field name="is_active"/>
                                    </list>
                                </field>
                            </group>
                        </page>
                        <page string="Floors">
                            <field name="floor_ids" nolabel="1">
                                <list>
                                    <field name="name"/>
                                    <field name="floor_number"/>
                                    <field name="zone_id"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Rooms">
                            <field name="room_ids" nolabel="1">
                                <list>
                                    <field name="name"/>
                                    <field name="room_number"/>
                                    <field name="room_type"/>
                                    <field name="floor_id"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Building List View -->
    <record id="nebular_building_view_list" model="ir.ui.view">
        <field name="name">nebular.building.list</field>
        <field name="model">nebular.building</field>
        <field name="arch" type="xml">
            <list string="Buildings">
                <field name="name"/>
                <field name="code"/>
                <field name="zone_count"/>
                <field name="floor_count"/>
                <field name="room_count"/>
                <field name="is_active"/>
            </list>
        </field>
    </record>

    <!-- Building Kanban View -->
    <record id="nebular_building_view_kanban" model="ir.ui.view">
        <field name="name">nebular.building.kanban</field>
        <field name="model">nebular.building</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="code"/>
                <field name="zone_count"/>
                <field name="floor_count"/>
                <field name="is_active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            Code: <field name="code"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_manage_button_section">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>Zones: <field name="zone_count"/></span>
                                        </div>
                                        <div class="col-6">
                                            <span>Floors: <field name="floor_count"/></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="is_active" widget="boolean_toggle"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Building Search View -->
    <record id="nebular_building_view_search" model="ir.ui.view">
        <field name="name">nebular.building.search</field>
        <field name="model">nebular.building</field>
        <field name="arch" type="xml">
            <search string="Buildings">
                <field name="name"/>
                <field name="code"/>
                <separator/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_active" context="{'group_by': 'is_active'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Building Action -->
    <record id="nebular_building_action" model="ir.actions.act_window">
        <field name="name">Buildings</field>
        <field name="res_model">nebular.building</field>
        <field name="view_mode">list,form,kanban</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first building!
            </p>
            <p>
                Buildings are the top-level organizational units in the Nebular system.
                Each building can contain multiple zones, floors, and rooms.
            </p>
        </field>
    </record>

</odoo>