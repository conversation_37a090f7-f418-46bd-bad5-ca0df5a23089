<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Gate Event List View -->
    <record id="nebular_gate_event_view_list" model="ir.ui.view">
        <field name="name">nebular.gate.event.list</field>
        <field name="model">nebular.gate.event</field>
        <field name="arch" type="xml">
            <list string="Gate Events" default_order="event_time desc">
                <field name="event_time"/>
                <field name="event_code"/>
                <field name="gate_code"/>
                <field name="name"/>
                <field name="status"/>
                <field name="vehicle_plate"/>
                <field name="trigger"/>
                <field name="anpr_conf"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="floor_id"/>
                <field name="severity" />
                 <field name="source_event_code"/>
                            <field name="source_state"/>
            </list>
        </field>
    </record>

    <!-- Gate Event Form View -->
    <record id="nebular_gate_event_view_form" model="ir.ui.view">
        <field name="name">nebular.gate.event.form</field>
        <field name="model">nebular.gate.event</field>
        <field name="arch" type="xml">
            <form string="Gate Event">
                <header>
                    <field name="status" widget="statusbar" />
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <!-- Add action buttons here if needed -->
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="gate_code" placeholder="Gate Code"/>
                        </h1>
                        <h2>
                            <field name="name" placeholder="Gate Name"/>
                        </h2>
                    </div>
                    <group>
                        <group name="gate_info" string="Gate Information">
                            <field name="gate_id"/>
                            <field name="gate_type"/>
                            <field name="location"/>
                            <field name="direction"/>
                        </group>
                        <group name="event_info" string="Event Information">
                            <field name="event_code"/>
                            <field name="event_time"/>
                            <field name="event_type"/>
                            <field name="severity"/>
                             <field name="source_event_code"/>
                            <field name="source_state"/>
                            <field name="trigger"/>
                        </group>
                    </group>
                    <group>
                        <group name="location_info" string="Location">
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                        </group>
                        <group name="vehicle_info" string="Vehicle Information">
                            <field name="vehicle_plate"/>
                            <field name="anpr_conf"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Details" name="details">
                            <group>
                                <field name="description" widget="text"/>
                                <field name="message" widget="text"/>
                            </group>
                        </page>
                        <page string="System Information" name="system_info">
                            <group>
                                <field name="system_id"/>
                                <field name="create_date"/>
                                <field name="write_date"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Gate Event Search View -->
    <record id="nebular_gate_event_view_search" model="ir.ui.view">
        <field name="name">nebular.gate.event.search</field>
        <field name="model">nebular.gate.event</field>
        <field name="arch" type="xml">
            <search string="Search Gate Events">
                <field name="event_code"/>
                <field name="gate_code"/>
                <field name="name"/>
                <field name="vehicle_plate"/>
                <field name="building_id"/>
                <field name="zone_id"/>
                <field name="floor_id"/>
                <separator/>
                <separator/>
                <filter string="High Severity" name="high_severity" domain="[('severity', '>=', 3)]"/>
                <filter string="Medium Severity" name="medium_severity" domain="[('severity', '=', 2)]"/>
                <filter string="Low Severity" name="low_severity" domain="[('severity', '=', 1)]"/>
                <separator/>
                <filter string="Today" name="today" domain="[('event_time', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('event_time', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="this_week" domain="[('event_time', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('event_time', '&lt;', (context_today() + datetime.timedelta(days=7-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" domain="[('event_time', '>=', datetime.datetime.now().strftime('%Y-%m-01'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                    <filter string="Gate Type" name="group_gate_type" context="{'group_by': 'gate_type'}"/>
                    <filter string="Trigger" name="group_trigger" context="{'group_by': 'trigger'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Zone" name="group_zone" context="{'group_by': 'zone_id'}"/>
                    <filter string="Floor" name="group_floor" context="{'group_by': 'floor_id'}"/>
                    <filter string="Event Date" name="group_event_date" context="{'group_by': 'event_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Gate Event Action -->
    <record id="nebular_gate_event_action" model="ir.actions.act_window">
        <field name="name">Gate Events</field>
        <field name="res_model">nebular.gate.event</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_today': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No gate events found!
            </p>
            <p>
                Gate events are generated when gates are operated, vehicles pass through, or system faults occur.
            </p>
        </field>
    </record>

</odoo>