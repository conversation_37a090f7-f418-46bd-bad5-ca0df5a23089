<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- System Metric Form View -->
    <record id="nebular_system_metric_view_form" model="ir.ui.view">
        <field name="name">nebular.system.metric.form</field>
        <field name="model">nebular.system.metric</field>
        <field name="arch" type="xml">
            <form string="System Metric">
                <header>
                    <button name="action_reset_thresholds" type="object" string="Reset Thresholds" 
                            class="btn-secondary"/>
                    <button name="action_calibrate" type="object" string="Calibrate" 
                            class="btn-primary"/>
                    <field name="status" widget="statusbar" statusbar_visible="normal,warning,critical"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_alerts" type="object" class="oe_stat_button" icon="fa-bell" invisible="not has_active_alerts">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Alerts</span>
                            </div>
                        </button>
                        <button name="action_view_history" type="object" class="oe_stat_button" icon="fa-line-chart">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">History</span>
                            </div>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Critical" bg_color="bg-danger" 
                            invisible="status != 'critical'"/>
                    <widget name="web_ribbon" title="Warning" bg_color="bg-warning" 
                            invisible="status != 'warning'"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                        <h3>
                            <field name="metric_type"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="source"/>
                            <field name="unit"/>
                            <field name="current_value" widget="float"/>
                            <field name="last_updated"/>
                            <field name="active"/>
                        </group>
                        <group name="location">
                            <field name="building_id"/>
                            <field name="floor_id"/>
                            <field name="zone_id"/>
                            <field name="room_id"/>
                            <field name="device_id"/>
                        </group>
                    </group>
                    
                    <group string="Thresholds">
                        <group name="warning_thresholds">
                            <field name="warning_min"/>
                            <field name="warning_max"/>
                        </group>
                        <group name="critical_thresholds">
                            <field name="critical_min"/>
                            <field name="critical_max"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="statistics">
                            <field name="min_value"/>
                            <field name="max_value"/>
                            <field name="avg_value"/>
                            <field name="sample_count"/>
                        </group>
                        <group name="configuration">
                            <field name="collection_interval"/>
                            <field name="retention_days"/>
                            <field name="aggregation_method"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Metric description..."/>
                    </group>
                    
                    <notebook>
                        <page string="Configuration" name="configuration">
                            <group>
                                <field name="config_data" widget="ace" options="{'mode': 'json'}"/>
                            </group>
                        </page>
                        <page string="Metadata" name="metadata">
                            <group>
                                <field name="metadata" widget="ace" options="{'mode': 'json'}"/>
                            </group>
                        </page>
                        <page string="Alerts" name="alerts">
                            <field name="alert_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="alert_type"/>
                                    <field name="severity"/>
                                    <field name="timestamp"/>
                                    <field name="acknowledged"/>
                                    <field name="resolved"/>
                                    <field name="status"/>
                                </list>
                            </field>
                        </page>
                        <page string="Calibration" name="calibration">
                            <group>
                                <group name="calibration_info">
                                    <field name="calibration_offset"/>
                                    <field name="calibration_factor"/>
                                    <field name="last_calibrated"/>
                                    <field name="calibration_due"/>
                                </group>
                                <group name="calibration_notes">
                                    <field name="calibration_notes" placeholder="Calibration notes and procedures..."/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- System Metric List View -->
    <record id="nebular_system_metric_view_list" model="ir.ui.view">
        <field name="name">nebular.system.metric.list</field>
        <field name="model">nebular.system.metric</field>
        <field name="arch" type="xml">
            <list string="System Metrics" default_order="metric_type, name" 
                  decoration-danger="status == 'critical'" decoration-warning="status == 'warning'">
                <field name="name"/>
                <field name="metric_type"/>
                <field name="current_value" widget="float"/>
                <field name="unit"/>
                <field name="status" widget="badge" 
                       decoration-danger="status == 'critical'"
                       decoration-warning="status == 'warning'"
                       decoration-success="status == 'normal'"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                <field name="last_updated"/>
                <field name="has_active_alerts"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- System Metric Kanban View -->
    <record id="nebular_system_metric_view_kanban" model="ir.ui.view">
        <field name="name">nebular.system.metric.kanban</field>
        <field name="model">nebular.system.metric</field>
        <field name="arch" type="xml">
            <kanban default_group_by="metric_type" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="metric_type"/>
                <field name="current_value"/>
                <field name="unit"/>
                <field name="status"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                <field name="last_updated"/>
                <field name="warning_min"/>
                <field name="warning_max"/>
                <field name="critical_min"/>
                <field name="critical_max"/>
                <field name="has_active_alerts"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="source"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_record_title">
                                        <span t-att-class="'badge ' + (record.status.raw_value == 'critical' ? 'badge-danger' : 
                                                          record.status.raw_value == 'warning' ? 'badge-warning' : 'badge-success')">
                                            <field name="status"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="o_kanban_gauge">
                                        <h2 class="text-center">
                                            <field name="current_value"/> <small><field name="unit"/></small>
                                        </h2>
                                    </div>
                                    <div t-if="record.building_id.raw_value">
                                        <i class="fa fa-building"/> <field name="building_id"/>
                                    </div>
                                    <div t-if="record.room_id.raw_value">
                                        <i class="fa fa-home"/> <field name="room_id"/>
                                    </div>
                                    <div t-if="record.device_id.raw_value">
                                        <i class="fa fa-microchip"/> <field name="device_id"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block" t-if="record.has_active_alerts.raw_value">
                                            <i class="fa fa-bell text-warning" title="Has Active Alerts"/> 
                                            <span>Active Alerts</span>
                                        </span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <small class="text-muted">
                                            <field name="last_updated"/>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- System Metric Search View -->
    <record id="nebular_system_metric_view_search" model="ir.ui.view">
        <field name="name">nebular.system.metric.search</field>
        <field name="model">nebular.system.metric</field>
        <field name="arch" type="xml">
            <search string="System Metrics">
                <field name="name" string="Metric" 
                       filter_domain="['|', ('name', 'ilike', self), ('description', 'ilike', self)]"/>
                <field name="metric_type"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Normal" name="normal" domain="[('status', '=', 'normal')]"/>
                <filter string="Warning" name="warning" domain="[('status', '=', 'warning')]"/>
                <filter string="Critical" name="critical" domain="[('status', '=', 'critical')]"/>
                
                <separator/>
                <filter string="Temperature" name="temperature" domain="[('metric_type', '=', 'temperature')]"/>
                <filter string="Humidity" name="humidity" domain="[('metric_type', '=', 'humidity')]"/>
                <filter string="Pressure" name="pressure" domain="[('metric_type', '=', 'pressure')]"/>
                <filter string="Air Quality" name="air_quality" domain="[('metric_type', '=', 'air_quality')]"/>
                <filter string="Light Level" name="light_level" domain="[('metric_type', '=', 'light_level')]"/>
                <filter string="Noise Level" name="noise_level" domain="[('metric_type', '=', 'noise_level')]"/>
                <filter string="Motion" name="motion" domain="[('metric_type', '=', 'motion')]"/>
                <filter string="Occupancy" name="occupancy" domain="[('metric_type', '=', 'occupancy')]"/>
                <filter string="Energy" name="energy" domain="[('metric_type', '=', 'energy')]"/>
                <filter string="Power" name="power" domain="[('metric_type', '=', 'power')]"/>
                <filter string="Voltage" name="voltage" domain="[('metric_type', '=', 'voltage')]"/>
                <filter string="Current" name="current" domain="[('metric_type', '=', 'current')]"/>
                <filter string="Flow Rate" name="flow_rate" domain="[('metric_type', '=', 'flow_rate')]"/>
                <filter string="Level" name="level" domain="[('metric_type', '=', 'level')]"/>
                <filter string="Vibration" name="vibration" domain="[('metric_type', '=', 'vibration')]"/>
                <filter string="Gas Concentration" name="gas_concentration" domain="[('metric_type', '=', 'gas_concentration')]"/>
                <filter string="Radiation" name="radiation" domain="[('metric_type', '=', 'radiation')]"/>
                <filter string="pH Level" name="ph_level" domain="[('metric_type', '=', 'ph_level')]"/>
                <filter string="Conductivity" name="conductivity" domain="[('metric_type', '=', 'conductivity')]"/>
                <filter string="Distance" name="distance" domain="[('metric_type', '=', 'distance')]"/>
                <filter string="Speed" name="speed" domain="[('metric_type', '=', 'speed')]"/>
                <filter string="Acceleration" name="acceleration" domain="[('metric_type', '=', 'acceleration')]"/>
                <filter string="Force" name="force" domain="[('metric_type', '=', 'force')]"/>
                <filter string="Torque" name="torque" domain="[('metric_type', '=', 'torque')]"/>
                <filter string="Frequency" name="frequency" domain="[('metric_type', '=', 'frequency')]"/>
                <filter string="Bandwidth" name="bandwidth" domain="[('metric_type', '=', 'bandwidth')]"/>
                <filter string="Latency" name="latency" domain="[('metric_type', '=', 'latency')]"/>
                <filter string="Throughput" name="throughput" domain="[('metric_type', '=', 'throughput')]"/>
                <filter string="CPU Usage" name="cpu_usage" domain="[('metric_type', '=', 'cpu_usage')]"/>
                <filter string="Memory Usage" name="memory_usage" domain="[('metric_type', '=', 'memory_usage')]"/>
                <filter string="Disk Usage" name="disk_usage" domain="[('metric_type', '=', 'disk_usage')]"/>
                <filter string="Network Usage" name="network_usage" domain="[('metric_type', '=', 'network_usage')]"/>
                <filter string="Battery Level" name="battery_level" domain="[('metric_type', '=', 'battery_level')]"/>
                <filter string="Signal Strength" name="signal_strength" domain="[('metric_type', '=', 'signal_strength')]"/>
                <filter string="Custom" name="custom" domain="[('metric_type', '=', 'custom')]"/>
                
                <separator/>
                <filter string="Has Alerts" name="has_alerts" domain="[('has_active_alerts', '=', True)]"/>
                <filter string="Needs Calibration" name="needs_calibration" 
                        domain="[('calibration_due', '&lt;=', context_today())]"/>
                
                <separator/>
                <filter string="Updated Today" name="updated_today" 
                        domain="[('last_updated', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter string="Updated This Week" name="updated_week" 
                        domain="[('last_updated', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Metric Type" name="group_metric_type" 
                            context="{'group_by': 'metric_type'}"/>
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'status'}"/>
                    <filter string="Source" name="group_source" 
                            context="{'group_by': 'source'}"/>
                    <filter string="Building" name="group_building" 
                            context="{'group_by': 'building_id'}"/>
                    <filter string="Unit" name="group_unit" 
                            context="{'group_by': 'unit'}"/>
                    <filter string="Last Updated" name="group_last_updated" 
                            context="{'group_by': 'last_updated:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- System Metric Action -->
    <record id="nebular_system_metric_action" model="ir.actions.act_window">
        <field name="name">System Metrics</field>
        <field name="res_model">nebular.system.metric</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1,
            'search_default_group_metric_type': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No system metrics found!
            </p>
            <p>
                System metrics are automatically collected from your
                IoT devices and sensors to monitor system performance.
            </p>
        </field>
    </record>
</odoo>