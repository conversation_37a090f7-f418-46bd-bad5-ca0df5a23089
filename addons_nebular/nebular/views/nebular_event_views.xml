<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Event Form View -->
    <record id="nebular_event_view_form" model="ir.ui.view">
        <field name="name">nebular.event.form</field>
        <field name="model">nebular.event</field>
        <field name="arch" type="xml">
            <form string="Event">
                <header>
                    <button name="action_acknowledge" type="object" string="Acknowledge" 
                            class="btn-primary" invisible="acknowledged"/>
                    <button name="action_resolve" type="object" string="Resolve" 
                            class="btn-success" invisible="resolved"/>
                    <button name="action_escalate" type="object" string="Escalate" 
                            class="btn-warning" invisible="resolved"/>
                    <field name="status" widget="statusbar" statusbar_visible="new,acknowledged,resolved"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_alerts" type="object" class="oe_stat_button" icon="fa-bell" invisible="not is_alert">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Alert</span>
                            </div>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Critical" bg_color="bg-danger" 
                            invisible="severity != 'critical'"/>
                    <widget name="web_ribbon" title="High" bg_color="bg-warning" 
                            invisible="severity != 'high'"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <h3>
                            <field name="event_type" readonly="1"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="timestamp" readonly="1"/>
                            <field name="severity" readonly="1"/>
                            <field name="source" readonly="1"/>
                            <field name="acknowledged"/>
                            <field name="resolved"/>
                        </group>
                        <group name="location">
                            <field name="building_id" readonly="1"/>
                            <field name="floor_id" readonly="1"/>
                            <field name="zone_id" readonly="1"/>
                            <field name="room_id" readonly="1"/>
                            <field name="device_id" readonly="1"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="handling">
                            <field name="acknowledged_by"/>
                            <field name="acknowledged_at"/>
                            <field name="resolved_by"/>
                            <field name="resolved_at"/>
                        </group>
                        <group name="escalation">
                            <field name="escalated"/>
                            <field name="escalated_at"/>
                            <field name="escalation_level"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" readonly="1"/>
                    </group>
                    
                    <notebook>
                        <page string="Event Data" name="event_data">
                            <group>
                                <field name="event_data" widget="ace" options="{'mode': 'json'}" readonly="1"/>
                            </group>
                        </page>
                        <page string="Metadata" name="metadata">
                            <group>
                                <field name="metadata" widget="ace" options="{'mode': 'json'}" readonly="1"/>
                            </group>
                        </page>
                        <page string="Alerts" name="alerts">
                            <field name="alert_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="alert_type"/>
                                    <field name="severity"/>
                                    <field name="timestamp"/>
                                    <field name="acknowledged"/>
                                    <field name="resolved"/>
                                    <field name="status"/>
                                </list>
                            </field>
                        </page>
                        <page string="Response Actions" name="actions">
                            <group>
                                <field name="response_actions" placeholder="Document response actions taken..."/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Event List View -->
    <record id="nebular_event_view_list" model="ir.ui.view">
        <field name="name">nebular.event.list</field>
        <field name="model">nebular.event</field>
        <field name="arch" type="xml">
            <list string="Events" default_order="timestamp desc" decoration-danger="severity == 'critical'" 
                  decoration-warning="severity == 'high'" decoration-muted="resolved">
                <field name="timestamp"/>
                <field name="name"/>
                <field name="event_type"/>
                <field name="severity" widget="badge" 
                       decoration-danger="severity == 'critical'"
                       decoration-warning="severity == 'high'"
                       decoration-info="severity == 'medium'"
                       decoration-success="severity == 'low'"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                <field name="acknowledged" widget="boolean_toggle"/>
                <field name="resolved" widget="boolean_toggle"/>
                <field name="status" widget="badge"/>
                <field name="is_alert" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <!-- Event Kanban View -->
    <record id="nebular_event_view_kanban" model="ir.ui.view">
        <field name="name">nebular.event.kanban</field>
        <field name="model">nebular.event</field>
        <field name="arch" type="xml">
            <kanban default_group_by="status" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="event_type"/>
                <field name="severity"/>
                <field name="timestamp"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                <field name="acknowledged"/>
                <field name="resolved"/>
                <field name="status"/>
                <field name="is_alert"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="event_type"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_record_title">
                                        <span t-att-class="'badge ' + (record.severity.raw_value == 'critical' ? 'badge-danger' : 
                                                          record.severity.raw_value == 'high' ? 'badge-warning' : 
                                                          record.severity.raw_value == 'medium' ? 'badge-info' : 'badge-success')">
                                            <field name="severity"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-clock-o"/> <field name="timestamp"/>
                                    </div>
                                    <div t-if="record.building_id.raw_value">
                                        <i class="fa fa-building"/> <field name="building_id"/>
                                    </div>
                                    <div t-if="record.room_id.raw_value">
                                        <i class="fa fa-home"/> <field name="room_id"/>
                                    </div>
                                    <div t-if="record.device_id.raw_value">
                                        <i class="fa fa-microchip"/> <field name="device_id"/>
                                    </div>
                                    <div class="o_kanban_tags_section">
                                        <span t-if="record.acknowledged.raw_value" 
                                              class="badge badge-info">Acknowledged</span>
                                        <span t-if="record.resolved.raw_value" 
                                              class="badge badge-success">Resolved</span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block" t-if="record.is_alert.raw_value">
                                            <i class="fa fa-bell text-warning" title="Alert Event"/> 
                                            <span>Alert</span>
                                        </span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <field name="source"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Event Search View -->
    <record id="nebular_event_view_search" model="ir.ui.view">
        <field name="name">nebular.event.search</field>
        <field name="model">nebular.event</field>
        <field name="arch" type="xml">
            <search string="Events">
                <field name="name" string="Event" 
                       filter_domain="['|', ('name', 'ilike', self), ('description', 'ilike', self)]"/>
                <field name="event_type"/>
                <field name="source"/>
                <field name="building_id"/>
                <field name="room_id"/>
                <field name="device_id"/>
                
                <separator/>
                <filter string="Unacknowledged" name="unacknowledged" domain="[('acknowledged', '=', False)]"/>
                <filter string="Acknowledged" name="acknowledged" domain="[('acknowledged', '=', True)]"/>
                <filter string="Unresolved" name="unresolved" domain="[('resolved', '=', False)]"/>
                <filter string="Resolved" name="resolved" domain="[('resolved', '=', True)]"/>
                
                <separator/>
                <filter string="Critical" name="critical" domain="[('severity', '=', 'critical')]"/>
                <filter string="High" name="high" domain="[('severity', '=', 'high')]"/>
                <filter string="Medium" name="medium" domain="[('severity', '=', 'medium')]"/>
                <filter string="Low" name="low" domain="[('severity', '=', 'low')]"/>
                
                <separator/>
                <filter string="Security Events" name="security" domain="[('event_type', '=', 'security')]"/>
                <filter string="Fire Safety" name="fire_safety" domain="[('event_type', '=', 'fire_safety')]"/>
                <filter string="Access Control" name="access_control" domain="[('event_type', '=', 'access_control')]"/>
                <filter string="Environmental" name="environmental" domain="[('event_type', '=', 'environmental')]"/>
                <filter string="System Events" name="system" domain="[('event_type', '=', 'system')]"/>
                <filter string="Device Events" name="device" domain="[('event_type', '=', 'device')]"/>
                <filter string="Maintenance" name="maintenance" domain="[('event_type', '=', 'maintenance')]"/>
                
                <separator/>
                <filter string="Escalated" name="escalated" domain="[('escalated', '=', True)]"/>
                <filter string="Has Alerts" name="has_alerts" domain="[('is_alert', '=', True)]"/>
                
                <separator/>
                <filter string="Today" name="today" domain="[('timestamp', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('timestamp', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter string="Last 7 Days" name="last_week" domain="[('timestamp', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="Last 30 Days" name="last_month" domain="[('timestamp', '>=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Event Type" name="group_event_type" 
                            context="{'group_by': 'event_type'}"/>
                    <filter string="Severity" name="group_severity" 
                            context="{'group_by': 'severity'}"/>
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'status'}"/>
                    <filter string="Source" name="group_source" 
                            context="{'group_by': 'source'}"/>
                    <filter string="Building" name="group_building" 
                            context="{'group_by': 'building_id'}"/>
                    <filter string="Date" name="group_date" 
                            context="{'group_by': 'timestamp:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Event Action -->
    <record id="nebular_event_action" model="ir.actions.act_window">
        <field name="name">Events</field>
        <field name="res_model">nebular.event</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_unresolved': 1,
            'search_default_group_status': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No events found!
            </p>
            <p>
                Events are automatically generated by the system
                when incidents or activities occur in your facilities.
            </p>
        </field>
    </record>
</odoo>