<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Room Form View -->
    <record id="nebular_room_view_form" model="ir.ui.view">
        <field name="name">nebular.room.form</field>
        <field name="model">nebular.room</field>
        <field name="arch" type="xml">
            <form string="Room">
                <header>
                    <button name="action_activate" type="object" string="Activate" 
                            invisible="is_active" class="btn-primary"/>
                    <button name="action_deactivate" type="object" string="Deactivate" 
                            invisible="not is_active" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Room Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="room_number"/>
                            <field name="floor_id"/>
                            <field name="zone_id"/>
                            <field name="room_type"/>
                            <field name="is_active"/>
                        </group>
                        <group>
                            <field name="area"/>
                            <field name="capacity"/>
                            <field name="door_count"/>
                            <field name="device_count"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Status &amp; Occupancy">
                            <group>
                                <group>
                                    <field name="is_occupied"/>
                                    <field name="occupancy_status"/>
                                    <field name="event_count"/>
                                    <field name="has_alerts"/>
                                </group>
                                <group>
                                    <field name="display_name"/>
                                    <field name="room_type"/>
                                    <field name="description"/>
                                </group>
                            </group>
                        </page>
                        <page string="Related Records">
                            <group>
                                <group>
                                    <field name="door_count"/>
                                    <field name="device_count"/>
                                    <field name="event_count"/>
                                </group>
                                <group>
                                    <field name="has_alerts"/>
                                    <field name="is_occupied"/>
                                    <field name="occupancy_status"/>
                                </group>
                            </group>
                        </page>
                        <page string="Description">
                            <field name="description" placeholder="Detailed description of the room..."/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Room List View -->
    <record id="nebular_room_view_list" model="ir.ui.view">
        <field name="name">nebular.room.list</field>
        <field name="model">nebular.room</field>
        <field name="arch" type="xml">
            <list string="Rooms">
                <field name="name"/>
                <field name="room_number"/>
                <field name="floor_id"/>
                <field name="zone_id"/>
                <field name="room_type"/>
                <field name="area"/>
                <field name="capacity"/>
                <field name="is_active"/>
            </list>
        </field>
    </record>

    <!-- Room Kanban View -->
    <record id="nebular_room_view_kanban" model="ir.ui.view">
        <field name="name">nebular.room.kanban</field>
        <field name="model">nebular.room</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="room_number"/>
                <field name="floor_id"/>
                <field name="zone_id"/>
                <field name="room_type"/>
                <field name="area"/>
                <field name="capacity"/>
                <field name="is_occupied"/>
                <field name="is_active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            Room <field name="room_number"/> - <field name="room_type"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_manage_button_section">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>Floor: <field name="floor_id"/></span>
                                        </div>
                                        <div class="col-6">
                                            <span>Zone: <field name="zone_id"/></span>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <span>Area: <field name="area"/> m²</span>
                                        </div>
                                        <div class="col-6">
                                            <span>Capacity: <field name="capacity"/></span>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <span t-if="record.is_occupied.raw_value" class="badge badge-warning">Occupied</span>
                                <span t-if="!record.is_occupied.raw_value" class="badge badge-success">Available</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="is_active" widget="boolean_toggle"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Room Search View -->
    <record id="nebular_room_view_search" model="ir.ui.view">
        <field name="name">nebular.room.search</field>
        <field name="model">nebular.room</field>
        <field name="arch" type="xml">
            <search string="Rooms">
                <field name="name"/>
                <field name="room_number"/>
                <field name="floor_id"/>
                <field name="zone_id"/>
                <field name="room_type"/>
                <separator/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <separator/>
                <filter string="Occupied" name="occupied" domain="[('is_occupied', '=', True)]"/>
                    <filter string="Available" name="available" domain="[('is_occupied', '=', False)]"/>
                <separator/>
                <filter string="Office" name="office" domain="[('room_type', '=', 'office')]"/>
                <filter string="Meeting Room" name="meeting" domain="[('room_type', '=', 'meeting')]"/>
                <filter string="Conference Room" name="conference" domain="[('room_type', '=', 'conference')]"/>
                <filter string="Office" name="office" domain="[('room_type', '=', 'office')]"/>
                <filter string="Meeting Room" name="meeting" domain="[('room_type', '=', 'meeting')]"/>
                <filter string="Conference Room" name="conference" domain="[('room_type', '=', 'conference')]"/>
                <filter string="Storage" name="storage" domain="[('room_type', '=', 'storage')]"/>
                <separator/>
                <filter string="Large Rooms" name="large" domain="[('area', '>', 50)]"/>
                <filter string="High Capacity" name="high_capacity" domain="[('capacity', '>', 20)]"/>
                <filter string="With Alerts" name="has_alerts" domain="[('has_alerts', '=', True)]"/>
                    <filter string="Currently Occupied" name="currently_occupied" domain="[('is_occupied', '=', True)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Floor" name="group_by_floor" context="{'group_by': 'floor_id'}"/>
                    <filter string="Zone" name="group_by_zone" context="{'group_by': 'zone_id'}"/>
                    <filter string="Room Type" name="group_by_type" context="{'group_by': 'room_type'}"/>
                    <filter string="Occupancy Status" name="group_by_occupancy" context="{'group_by': 'is_occupied'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Room Action -->
    <record id="nebular_room_action" model="ir.actions.act_window">
        <field name="name">Rooms</field>
        <field name="res_model">nebular.room</field>
        <field name="view_mode">list,form,kanban</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first room!
            </p>
            <p>
                Rooms are individual spaces within floors that can be used for various purposes.
                They can be equipped with devices, sensors, and tracked for occupancy status.
            </p>
        </field>
    </record>
</odoo>