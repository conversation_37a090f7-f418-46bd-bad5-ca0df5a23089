{
    'name': 'Nebular Dashboard System',
    'version': '********.0',
    'category': 'Building Management',
    'summary': 'Comprehensive building management and monitoring dashboard system',
    'description': """
Nebular Dashboard System
========================

A comprehensive building management and monitoring system that provides:

* Geographic hierarchy management (Buildings, Zones, Floors, Rooms, Doors, Devices)
* Real-time event monitoring (Fire, Access, CCTV, Gate, PA, Presence)
* Interis_active floor plan markers
* System health monitoring and metrics
* Alert management and notifications
* Dashboard visualization

Features:
---------
* Multi-level building structure management
* Real-time event tracking and alerts
* Interis_active floor plan visualization
* System monitoring and health checks
* Comprehensive security and access control
* API integration for external systems
* Mobile-responsive dashboard interface

This module is designed for facility managers, security personnel, and building operators
who need comprehensive oversight of building systems and operations.
    """,
    'author': 'Laplacesoftware',
    'website': 'https://www.laplacesoftware.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'mail',
        'web',
        'portal',
    ],
    'data': [
        # Security
        'security/nebular_groups.xml',
        'security/ir.model.access.csv',


        # Data
        'data/data.xml',

        # Views - Geographic Hierarchy
        'views/building_views.xml',
        'views/floor_views.xml',
        'views/zone_views.xml',
        'views/room_views.xml',
        'views/door_views.xml',

        # Views - Device Management
        'views/device_type_views.xml',
        'views/device_views.xml',

        # Views - Event System
        'views/event_views.xml',

        # Views - System Monitoring
        'views/system_views.xml',

        # Views - Configuration
        'views/config_views.xml',

        # Menus (loaded after views so actions are available)
        'views/menus.xml',
    ],
    'demo': [
        'data/demo.xml',
        'data/event_demo.xml',
    ],
    'assets': {
        # 'web.assets_backend': [
        #     'nebular/static/src/scss/nebular_dashboard.scss',
        #     'nebular/static/src/js/nebular_dashboard.js',
        # ],
        # 'web.assets_frontend': [
        #     'nebular/static/src/scss/nebular_portal.scss',
        # ],
    },
    'images': [
        # 'static/description/icon.png',
        # 'static/description/banner.png',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
    # 'post_init_hook': 'post_init_hook',
    # 'uninstall_hook': 'uninstall_hook',
}