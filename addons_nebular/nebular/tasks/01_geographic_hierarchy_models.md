# Task 01: Geographic Hierarchy Models

## Overview
Create the core geographic hierarchy models for the Nebular Dashboard System. These models form the foundation of the building management system with proper relationships and computed fields.

## Models to Create

### 1. Building Model (`nebular.building`)
**File**: `models/nebular_building.py`

**Key Fields**:
- `name` (Char, required) - Building Name
- `short_code` (Char, required, size=10) - Short Code
- `address` (Text) - Address
- `description` (Text) - Description
- `is_active` (<PERSON><PERSON><PERSON>, default=True) - is_active status
- `total_floors` (Integer, computed) - Total Floors
- `total_rooms` (Integer, computed) - Total Rooms  
- `total_doors` (Integer, computed) - Total Doors

**Relationships**:
- `zone_ids` (One2many) → `nebular.zone`
- `floor_ids` (One2many) → `nebular.floor`
- `event_ids` (One2many) → `nebular.event`
- `device_ids` (One2many) → `nebular.device`

**Constraints**:
- Unique short_code
- Unique name

### 2. Zone Model (`nebular.zone`)
**File**: `models/nebular_zone.py`

**Key Fields**:
- `name` (Char, required) - Zone Name
- `zone_code` (Char, required, size=10) - Zone Code
- `description` (Text) - Description
- `building_id` (Many2one, required) → `nebular.building`
- `is_active` (Boolean, default=True) - is_active status
- `display_name` (Char, computed) - Display Name

**Relationships**:
- `floor_ids` (One2many) → `nebular.floor`
- `event_ids` (One2many) → `nebular.event`
- `device_ids` (One2many) → `nebular.device`

**Constraints**:
- Unique zone_code within building

### 3. Floor Model (`nebular.floor`)
**File**: `models/nebular_floor.py`

**Key Fields**:
- `name` (Char, required) - Floor Name
- `level` (Integer, required) - Floor Level
- `floor_code` (Char, required, size=10) - Floor Code
- `floor_plan_url` (Char) - Floor Plan URL
- `building_id` (Many2one, required) → `nebular.building`
- `zone_id` (Many2one, required) → `nebular.zone`
- `is_active` (Boolean, default=True) - is_active status

**Relationships**:
- `room_ids` (One2many) → `nebular.room`
- `event_ids` (One2many) → `nebular.event`
- `device_ids` (One2many) → `nebular.device`

**Constraints**:
- Unique floor_code within building
- Unique level within zone

### 4. Room Model (`nebular.room`)
**File**: `models/nebular_room.py`

**Key Fields**:
- `name` (Char, required) - Room Name
- `room_code` (Char, required, size=20) - Room Code
- `room_type` (Selection, required) - Room Type (office, conference, storage, etc.)
- `capacity` (Integer) - Capacity (People)
- `area` (Float, digits=(10,2)) - Area (m²)
- `floor_id` (Many2one, required) → `nebular.floor`
- `building_id` (Many2one, related, readonly) - Building
- `zone_id` (Many2one, related, readonly) - Zone
- `is_active` (Boolean, default=True) - is_active status

**Relationships**:
- `door_ids` (One2many) → `nebular.door`

**Constraints**:
- Unique room_code within floor
- Positive capacity and area

### 5. Door Model (`nebular.door`)
**File**: `models/nebular_door.py`

**Key Fields**:
- `name` (Char, required) - Door Name
- `door_code` (Char, required, size=20) - Door Code
- `door_type` (Selection, required) - Door Type (main_entry, emergency_exit, etc.)
- `access_level` (Selection, required) - Access Level (public, restricted, etc.)
- `status` (Selection, required) - Status (open, closed, locked, malfunction)
- `room_id` (Many2one, required) → `nebular.room`
- `is_active` (Boolean, default=True) - is_active status
- `last_status_change` (Datetime) - Last Status Change
- `is_alert` (Boolean, computed) - Is Alert

**Constraints**:
- Unique door_code

### 6. Device Model (`nebular.device`)
**File**: `models/nebular_device.py`

**Key Fields**:
- `name` (Char, required) - Device Name
- `title` (Char, required) - Title
- `subtitle` (Char) - Subtitle
- `description` (Text) - Description
- `device_type` (Selection, required) - Device Type (fire, door, gate, camera, etc.)
- `position_x` (Float, required, digits=(10,2)) - Position X
- `position_y` (Float, required, digits=(10,2)) - Position Y
- `status` (Char, required) - Status
- `is_alert` (Boolean, default=False) - Is Alert
- `building_id` (Many2one, required) → `nebular.building`
- `zone_id` (Many2one, required) → `nebular.zone`
- `floor_id` (Many2one, required) → `nebular.floor`

## Views to Create

### List Views
- Building list with short_code, name, total_floors, total_rooms, is_active
- Zone list with zone_code, name, building_id, total_floors, is_active
- Floor list with floor_code, name, level, zone_id, building_id, is_active
- Room list with room_code, name, room_type, floor_id, capacity, is_active
- Door list with door_code, name, door_type, status, room_id, is_alert
- Device list with name, device_type, status, floor_id, is_alert

### Form Views
- Building form with basic info, statistics, and related records tabs
- Zone form with basic info and related records
- Floor form with basic info, floor plan, and related records
- Room form with basic info, specifications, and doors
- Door form with basic info, configuration, and status
- Device form with basic info, position, and status

### Search Views
- Building search: name, short_code, is_active
- Zone search: name, zone_code, building_id, is_active
- Floor search: name, floor_code, level, building_id, zone_id
- Room search: name, room_code, room_type, floor_id
- Door search: name, door_code, door_type, status, access_level
- Device search: name, device_type, status, is_alert, floor_id

## Reference
Model definitions from: `/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_nebular/_docs/02-odoo-model-mapping.md` (Lines 1-400)

## Implementation Notes
- Follow Odoo 18 standards (use `<list>` instead of `<tree>`)
- Implement proper computed fields with dependencies
- Add SQL constraints for data integrity
- Use proper field ordering in models
- Include `_order` and `_rec_name` attributes