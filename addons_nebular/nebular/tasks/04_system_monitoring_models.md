# Task 04: System Monitoring Models

## Overview
Create system monitoring models for tracking system metrics, alerts, and overall system health. These models provide dashboard-level monitoring capabilities.

## Models to Create

### 1. System Model (`nebular.system`)
**File**: `models/nebular_system.py`

**Key Fields**:
- `title` (Char, required) - System Title
- `icon_name` (Char, required) - Icon Name
- `icon_color` (Char, required) - Icon Color

**Relationships**:
- `metric_ids` (One2many) → `nebular.system.metric`

**Computed Fields**:
- `has_alerts` (<PERSON><PERSON><PERSON>, computed) - Has Alerts
- `alert_count` (Integer, computed) - Alert Count

**Methods**:
- `_compute_has_alerts()` - Check if any metrics have alerts
- `_compute_alert_count()` - Count metrics with alerts

### 2. System Metric Model (`nebular.system.metric`)
**File**: `models/nebular_system_metric.py`

**Key Fields**:
- `key` (<PERSON><PERSON>, required) - Metric Key
- `value` (<PERSON>r, required) - Metric Value
- `is_alert` (<PERSON><PERSON><PERSON>, default=False) - Is Alert
- `system_id` (Many2one, required) → `nebular.system`

**Computed Fields**:
- `display_name` (Char, computed) - Display Name

**Methods**:
- `_compute_display_name()` - Format as "System - Key: Value"

## Views to Create

### System Views

#### List View
**Fields to Display**:
- `title` - System Title
- `icon_name` - Icon Name
- `icon_color` - Icon Color (with color indicator)
- `alert_count` - Alert Count
- `has_alerts` - Has Alerts (boolean indicator)

**Features**:
- Color-coded rows based on alert status
- Quick filters for systems with alerts
- Sort by alert count

#### Form View
**Layout**:

**Main Tab - System Information**:
- `title` - System Title
- `icon_name` - Icon Name
- `icon_color` - Icon Color (with color picker)

**Metrics Tab**:
- `metric_ids` - System Metrics (One2many list)
  - Inline list view showing key, value, is_alert
  - Quick add/edit capabilities
  - Alert indicators

**Statistics Tab** (readonly):
- `has_alerts` - Has Alerts
- `alert_count` - Alert Count
- Metric summary information

#### Search View
**Search Fields**:
- `title` - System Title
- `icon_name` - Icon Name

**Filters**:
- Systems with Alerts
- Systems without Alerts
- By Icon Name (group by)

### System Metric Views

#### List View
**Fields to Display**:
- `system_id` - System
- `key` - Metric Key
- `value` - Metric Value
- `is_alert` - Is Alert (with visual indicator)

**Features**:
- Color-coded rows for alerts
- Quick edit capabilities
- Filter by alert status

#### Form View
**Layout**:
- `system_id` - System
- `key` - Metric Key
- `value` - Metric Value
- `is_alert` - Is Alert
- `display_name` - Display Name (readonly)

#### Search View
**Search Fields**:
- `key` - Metric Key
- `value` - Metric Value
- `system_id` - System

**Filters**:
- Alert Metrics
- Non-Alert Metrics
- By System (group by)
- By Key (group by)

## Dashboard Integration

### System Health Dashboard
Create a dashboard view showing:
- System overview cards
- Alert summary
- Metric trends
- System status indicators

### Alert Management
- Real-time alert notifications
- Alert history tracking
- Alert acknowledgment system
- Escalation procedures

## Common System Types

### Predefined Systems
Consider creating data records for common systems:

```xml
<!-- Fire System -->
<record id="system_fire" model="nebular.system">
    <field name="title">Fire System</field>
    <field name="icon_name">fire</field>
    <field name="icon_color">#FF4444</field>
</record>

<!-- Access Control -->
<record id="system_access" model="nebular.system">
    <field name="title">Access Control</field>
    <field name="icon_name">door</field>
    <field name="icon_color">#4CAF50</field>
</record>

<!-- CCTV -->
<record id="system_cctv" model="nebular.system">
    <field name="title">CCTV</field>
    <field name="icon_name">camera</field>
    <field name="icon_color">#2196F3</field>
</record>

<!-- Gate Control -->
<record id="system_gate" model="nebular.system">
    <field name="title">Gate Control</field>
    <field name="icon_name">gate</field>
    <field name="icon_color">#FF9800</field>
</record>

<!-- Public Address -->
<record id="system_pa" model="nebular.system">
    <field name="title">Public Address</field>
    <field name="icon_name">speaker</field>
    <field name="icon_color">#9C27B0</field>
</record>

<!-- Presence Detection -->
<record id="system_presence" model="nebular.system">
    <field name="title">Presence Detection</field>
    <field name="icon_name">sensor</field>
    <field name="icon_color">#607D8B</field>
</record>
```

### Common Metrics
Examples of typical metrics for each system:
- **Fire System**: is_active Alarms, Fault Count, Battery Status
- **Access Control**: Failed Attempts, Door Status, Controller Status
- **CCTV**: Camera Count, Recording Status, Storage Usage
- **Gate Control**: Gate Status, Vehicle Count, ANPR Accuracy
- **PA**: Zone Status, Volume Levels, Announcement Queue
- **Presence**: Occupancy Count, Sensor Status, Detection Rate

## API Integration

### Metric Updates
- REST API endpoints for updating metric values
- Bulk metric update capabilities
- Real-time metric streaming

### Alert Triggers
- Automatic alert generation based on metric thresholds
- Custom alert rules
- Integration with notification systems

## Performance Considerations

### Indexing
- Add indexes on frequently queried fields
- Consider composite indexes for complex queries
- Monitor query performance

### Caching
- Cache computed fields for better performance
- Consider Redis for real-time metrics
- Implement metric aggregation

## Reference
Model definitions from: `/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_nebular/_docs/02-odoo-model-mapping.md` (Lines 950-974)

## Implementation Notes
- Use proper field ordering in model definitions
- Add constraints for data validation
- Implement proper cascade deletion
- Consider metric history tracking
- Add proper security rules
- Use `_order = 'title'` for consistent ordering
- Implement real-time update mechanisms