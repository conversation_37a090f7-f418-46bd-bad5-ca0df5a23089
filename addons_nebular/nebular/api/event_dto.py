from dataclasses import dataclass
from typing import Any, List, Optional, Dict
from .utils import *


@dataclass
class EventDTO:
    """
    Data Transfer Object for Event entities.
    Supports all system types: fire, access, cctv, gate, pa, presence
    """
    # Standard response fields
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''
    error_message: str = ''
    
    # Common event envelope fields
    name: str = ''
    building_id: int = 0
    # building_code: str = ''
    floor_id: int = 0
    floor_code: str = ''
    system_code: str = ''  # fire|access|cctv|gate|pa|presence
    system_name: str = ''
    event_type: str = ''
    datetime: str = ''  # ISO-8601 UTC
    message: str = ''
    description: str = ''
    source_event_code: str = ''  # Device code, door code, or gate code
    source_state: str = ''  # State of device, door, or gate (closed, open, unauthorized access)
    state: str = ''  # optional current state
    severity: str = ''  # optional severity level
    command: str = ''  # optional command ack name
    
    # System-specific data fields (all optional, populated based on system_code)
    # Fire Alarm System
    panel_id: int = 0
    panel_code: str = ''
    panel_name: str = ''
    zone: str = ''
    loop: str = ''
    node_id: int = 0
    node_code: str = ''
    address: str = ''
    alarm_type: str = ''  # smoke|heat|flame|gas|manual|other
    sensor_status: str = ''  # normal|alarm|fault|maintenance|disabled
    
    # Access Control System
    controller_id: int = 0
    controller_code: str = ''
    controller_name: str = ''
    reader_id: int = 0
    reader_code: str = ''
    card_id: int = 0
    card_code: str = ''
    user_id: int = 0
    user_code: str = ''
    door_id: str = ''
    door_name: str = ''
    vendor: str = ''
    model: str = ''
    holder: str = ''
    result: str = ''  # granted|denied|timeout
    reason: str = ''  # schedule|invalidCard|expired|antipassback|pinRequired|other
    held_open_seconds: int = 0
    
    # CCTV System
    camera_id: int = 0
    camera_code: str = ''
    camera_name: str = ''
    location: str = ''
    ip: str = ''
    channel: int = 0
    analytic_type: str = ''  # motion|smoke|fire|audio|lineCrossing|loitering|peopleCount|face
    count: int = 0
    face_id: str = ''
    uri: str = ''
    snapshot_url: str = ''
    recording: bool = False
    last_heartbeat: str = ''
    
    # Gate Barrier System
    gate_id: int = 0
    gate_code: str = ''
    gate_name: str = ''
    status: str = ''  # open|closed|opening|closing|fault
    vehicle_plate: str = ''
    trigger: str = ''
    anpr_confidence: float = 0.0
    
    # Public Address System
    zone_id: int = 0
    zone_code: str = ''
    zone_name: str = ''
    volume: int = 0
    announcement_id: str = ''
    script: str = ''
    duration_sec: int = 0
    
    # Presence Sensors System
    sensor_id: int = 0
    sensor_code: str = ''
    sensor_name: str = ''
    sensor_type: str = ''  # pir|microwave|ultrasonic|camera
    detection_count : int = 0
    occupancy: bool = False
    
    @staticmethod
    def from_dict(obj: Any) -> 'EventDTO':
        """Create EventDTO from dictionary"""
        assert isinstance(obj, dict)
        return EventDTO(
            # Standard response fields
            response_code=from_str(obj.get("ResponseCode", obj.get("response_code", ""))),
            response_message=from_str(obj.get("ResponseMessage", obj.get("response_message", ""))),
            response_message_ar=from_str(obj.get("ResponseMessageAR", obj.get("response_message_ar", ""))),
            error_message=from_str(obj.get("ErrorMessage", obj.get("error_message", ""))),
            
            # Common event envelope
            name=from_str(obj.get("name", obj.get("name", ""))),
            building_id=from_int(obj.get("buildingId", obj.get("building_id", 0))),
            floor_id=from_int(obj.get("floorId", obj.get("floor_id", 0))),
            floor_code=from_str(obj.get("floorCode", obj.get("floor_code", ""))),
            system_code=from_str(obj.get("systemCode", obj.get("system_code", ""))),
            system_name=from_str(obj.get("systemName", obj.get("system_name", ""))),
            event_type=from_str(obj.get("eventType", obj.get("event_type", ""))),
            datetime=from_str(obj.get("datetime", "")),
            message=from_str(obj.get("message", "")),
            description=from_str(obj.get("description", "")),
            # Don't pass zone at root level - it should come from _extract_data_fields only
            # zone=from_str(obj.get("zone", "")),
            source_event_code=from_str(obj.get("sourceEventCode", obj.get("source_event_code", ""))),
            source_state=from_str(obj.get("sourceState", obj.get("source_state", ""))),
            state=from_str(obj.get("state", "")),
            severity=from_str(obj.get("severity", obj.get("severity", ""))),
            # Don't pass detection_count at root level - it should come from _extract_data_fields only
            # detection_count=from_int(obj.get("detectionCount", obj.get("detection_count", 0))),
            command=from_str(obj.get("command", "")),
            
            # Extract data fields from nested 'data' object or root level
            **EventDTO._extract_data_fields(obj)
        )
    
    @staticmethod
    def _extract_data_fields(obj: dict) -> dict:
        """Extract system-specific data fields from nested data object or root level"""
        data_obj = obj.get("data", obj)
        
        return {
            # Fire Alarm System
            "panel_id": from_int(data_obj.get("id", data_obj.get("panel_id", 0))),
            "panel_code": from_str(data_obj.get("panelCode", data_obj.get("panel_code", ""))),
            "panel_name": from_str(data_obj.get("name", data_obj.get("panel_name", ""))),
            "zone": from_str(data_obj.get("zone", "")),
            "loop": from_str(data_obj.get("loop", "")),
            "node_id": from_int(data_obj.get("nodeId", data_obj.get("node_id", 0))),
            "node_code": from_str(data_obj.get("nodeCode", data_obj.get("node_code", ""))),
            "address": from_str(data_obj.get("address", "")),
            "alarm_type": from_str(data_obj.get("alarmType", data_obj.get("alarm_type", ""))),
            "sensor_status": from_str(data_obj.get("sensorStatus", data_obj.get("sensor_status", ""))),
            
            # Access Control System
            "controller_id": from_int(data_obj.get("id", data_obj.get("controller_id", 0))),
            "controller_code": from_str(data_obj.get("controllerCode", data_obj.get("controller_code", ""))),
            "controller_name": from_str(data_obj.get("controllerName", data_obj.get("controller_name", ""))),
            "reader_id": from_int(data_obj.get("readerId", data_obj.get("reader_id", 0))),
            "reader_code": from_str(data_obj.get("readerCode", data_obj.get("reader_code", ""))),
            "card_id": from_int(data_obj.get("cardId", data_obj.get("card_id", 0))),
            "card_code": from_str(data_obj.get("cardCode", data_obj.get("card_code", ""))),
            "user_id": from_int(data_obj.get("userId", data_obj.get("user_id", 0))),
            "user_code": from_str(data_obj.get("userCode", data_obj.get("user_code", ""))),
            "door_id": from_str(data_obj.get("doorId", data_obj.get("door_id", ""))),
            "door_name": from_str(data_obj.get("doorName", data_obj.get("door_name", ""))),
            "vendor": from_str(data_obj.get("vendor", "")),
            "model": from_str(data_obj.get("model", "")),
            "holder": from_str(data_obj.get("holder", "")),
            "result": from_str(data_obj.get("result", "")),
            "reason": from_str(data_obj.get("reason", "")),
            "held_open_seconds": from_int(data_obj.get("heldOpenSeconds", data_obj.get("held_open_seconds", 0))),
            
            # CCTV System
            "camera_id": from_int(data_obj.get("id", data_obj.get("camera_id", 0))),
            "camera_code": from_str(data_obj.get("cameraCode", data_obj.get("camera_code", ""))),
            "camera_name": from_str(data_obj.get("cameraName", data_obj.get("camera_name", ""))),
            "location": from_str(data_obj.get("location", "")),
            "ip": from_str(data_obj.get("ip", "")),
            "channel": from_int(data_obj.get("channel", 0)),
            "analytic_type": from_str(data_obj.get("analyticType", data_obj.get("analytic_type", ""))),
            "count": from_int(data_obj.get("count", 0)),
            "face_id": from_str(data_obj.get("faceId", data_obj.get("face_id", ""))),
            "uri": from_str(data_obj.get("uri", "")),
            "snapshot_url": from_str(data_obj.get("snapshotUrl", data_obj.get("snapshot_url", ""))),
            "recording": from_bool(data_obj.get("recording", False)),
            "last_heartbeat": from_str(data_obj.get("lastHeartbeat", data_obj.get("last_heartbeat", ""))),
            
            # Gate Barrier System
            "gate_id": from_int(data_obj.get("id", data_obj.get("gate_id", 0))),
            "gate_code": from_str(data_obj.get("gateCode", data_obj.get("gate_code", ""))),
            "gate_name": from_str(data_obj.get("gateName", data_obj.get("gate_name", ""))),
            "status": from_str(data_obj.get("status", "")),
            "vehicle_plate": from_str(data_obj.get("vehiclePlate", data_obj.get("vehicle_plate", ""))),
            "trigger": from_str(data_obj.get("trigger", "")),
            "anpr_confidence": from_float(data_obj.get("anprConfidence", data_obj.get("anpr_confidence", 0.0))),
            
            # Public Address System
            "zone_id": from_int(data_obj.get("id", data_obj.get("zone_id", 0))),
            "zone_code": from_str(data_obj.get("zoneCode", data_obj.get("zone_code", ""))),
            "zone_name": from_str(data_obj.get("zoneName", data_obj.get("zone_name", ""))),
            "volume": from_int(data_obj.get("volume", 0)),
            "announcement_id": from_str(data_obj.get("announcementId", data_obj.get("announcement_id", ""))),
            "script": from_str(data_obj.get("script", "")),
            "duration_sec": from_int(data_obj.get("durationSec", data_obj.get("duration_sec", 0))),
            
            # Presence Sensors System
            "sensor_id": from_int(data_obj.get("id", data_obj.get("sensor_id", 0))),
            "sensor_code": from_str(data_obj.get("sensorCode", data_obj.get("sensor_code", ""))),
            "sensor_name": from_str(data_obj.get("sensorName", data_obj.get("sensor_name", ""))),
            "sensor_type": from_str(data_obj.get("type", data_obj.get("sensor_type", ""))),
            "detection_count": from_int(data_obj.get("count", 0)),
            "occupancy": from_bool(data_obj.get("occupancy", False)),
        }
    
    def to_dict(self) -> dict:
        """Convert EventDTO to dictionary with nested data structure"""
        # Build the data object based on system_code
        data = self._build_data_object()
        
        result = {
            "ResponseCode": from_str(self.response_code),
            "ResponseMessage": from_str(self.response_message),
            "ResponseMessageAR": from_str(self.response_message_ar),
            "ErrorMessage": from_str(self.error_message),
            "name": from_str(self.name),
            "buildingId": self.building_id,
            # "buildingCode": from_str(self.building_code),
            "floorId": self.floor_id,
            "floorCode": from_str(self.floor_code),
            "systemCode": from_str(self.system_code),
            "systemName": from_str(self.system_name),
            "eventType": from_str(self.event_type),
            "datetime": from_str(self.datetime),
            "message": from_str(self.message),
            "description": from_str(self.description),
            "zone": from_str(self.zone),
            "sourceEventCode": from_str(self.source_event_code),
            "sourceState": from_str(self.source_state),
            "state": from_str(self.state),
            "severity": from_str(self.severity),
            "command": from_str(self.command),
            "data": data
        }
        
        # Remove empty fields to keep response clean
        return {k: v for k, v in result.items() if v not in [None, '', 0, False, {}]}
    
    def _build_data_object(self) -> dict:
        """Build system-specific data object based on system_code"""
        if self.system_code == "fire":
            return self._build_fire_data()
        elif self.system_code == "access":
            return self._build_access_data()
        elif self.system_code == "cctv":
            return self._build_cctv_data()
        elif self.system_code == "gate":
            return self._build_gate_data()
        elif self.system_code == "pa":
            return self._build_pa_data()
        elif self.system_code == "presence":
            return self._build_presence_data()
        else:
            return {}
    
    def _build_fire_data(self) -> dict:
        """Build fire alarm system data object"""
        data = {}
        if self.panel_id: data["id"] = self.panel_id
        if self.panel_code: data["panelCode"] = self.panel_code
        if self.panel_name: data["name"] = self.panel_name
        if self.zone: data["zone"] = self.zone
        if self.loop: data["loop"] = self.loop
        if self.node_id: data["nodeId"] = self.node_id
        if self.node_code: data["nodeCode"] = self.node_code
        if self.address: data["address"] = self.address
        if self.alarm_type: data["alarmType"] = self.alarm_type
        if self.sensor_status: data["sensorStatus"] = self.sensor_status
        return data
    
    def _build_access_data(self) -> dict:
        """Build access control system data object"""
        data = {}
        if self.controller_id: data["id"] = self.controller_id
        if self.controller_code: data["controllerCode"] = self.controller_code
        if self.controller_name: data["name"] = self.controller_name
        if self.reader_id: data["readerId"] = self.reader_id
        if self.reader_code: data["readerCode"] = self.reader_code
        if self.card_id: data["cardId"] = self.card_id
        if self.card_code: data["cardCode"] = self.card_code
        if self.user_id: data["userId"] = self.user_id
        if self.user_code: data["userCode"] = self.user_code
        if self.door_id: data["doorId"] = self.door_id
        if self.door_name: data["doorName"] = self.door_name
        if self.vendor: data["vendor"] = self.vendor
        if self.model: data["model"] = self.model
        if self.holder: data["holder"] = self.holder
        if self.result: data["result"] = self.result
        if self.reason: data["reason"] = self.reason
        if self.held_open_seconds: data["heldOpenSeconds"] = self.held_open_seconds
        return data
    
    def _build_cctv_data(self) -> dict:
        """Build CCTV system data object"""
        data = {}
        if self.camera_id: data["id"] = self.camera_id
        if self.camera_code: data["cameraCode"] = self.camera_code
        if self.camera_name: data["name"] = self.camera_name
        if self.location: data["location"] = self.location
        if self.vendor: data["vendor"] = self.vendor
        if self.model: data["model"] = self.model
        if self.ip: data["ip"] = self.ip
        if self.channel: data["channel"] = self.channel
        if self.analytic_type: data["analyticType"] = self.analytic_type
        if self.count: data["count"] = self.count
        if self.face_id: data["faceId"] = self.face_id
        if self.uri: data["uri"] = self.uri
        if self.snapshot_url: data["snapshotUrl"] = self.snapshot_url
        if self.recording: data["recording"] = self.recording
        if self.last_heartbeat: data["lastHeartbeat"] = self.last_heartbeat
        return data
    
    def _build_gate_data(self) -> dict:
        """Build gate barrier system data object"""
        data = {}
        if self.gate_id: data["id"] = self.gate_id
        if self.gate_code: data["gateCode"] = self.gate_code
        if self.gate_name: data["name"] = self.gate_name
        if self.location: data["location"] = self.location
        if self.vendor: data["vendor"] = self.vendor
        if self.model: data["model"] = self.model
        if self.ip: data["ip"] = self.ip
        if self.status: data["status"] = self.status
        if self.vehicle_plate: data["vehiclePlate"] = self.vehicle_plate
        if self.trigger: data["trigger"] = self.trigger
        if self.anpr_confidence: data["anprConfidence"] = self.anpr_confidence
        return data
    
    def _build_pa_data(self) -> dict:
        """Build public address system data object"""
        data = {}
        if self.zone_id: data["id"] = self.zone_id
        if self.zone_code: data["zoneCode"] = self.zone_code
        if self.zone_name: data["name"] = self.zone_name
        if self.location: data["location"] = self.location
        if self.vendor: data["vendor"] = self.vendor
        if self.model: data["model"] = self.model
        if self.volume: data["volume"] = self.volume
        if self.message: data["message"] = self.message
        if self.announcement_id: data["announcementId"] = self.announcement_id
        if self.script: data["script"] = self.script
        if self.duration_sec: data["durationSec"] = self.duration_sec
        return data
    
    def _build_presence_data(self) -> dict:
        """Build presence sensors system data object"""
        data = {}
        if self.sensor_id: data["id"] = self.sensor_id
        if self.sensor_code: data["sensorCode"] = self.sensor_code
        if self.sensor_name: data["name"] = self.sensor_name
        if self.location: data["location"] = self.location
        if self.vendor: data["vendor"] = self.vendor
        if self.model: data["model"] = self.model
        if self.sensor_type: data["type"] = self.sensor_type
        if self.detection_count: data["detection_count"] = self.detection_count
        if self.occupancy: data["occupancy"] = self.occupancy
        return data


@dataclass
class EventListDTO:
    """
    Data Transfer Object for Event collections.
    Contains a list of EventDTO objects with standardized response fields.
    """
    response_code: str = ''
    response_message: str = ''
    response_message_ar: str = ''
    events: List[EventDTO] = None
    error_message: str = ''
    total_count: int = 0
    
    def __post_init__(self):
        if self.events is None:
            self.events = []
    
    @staticmethod
    def from_dict(obj: Any) -> 'EventListDTO':
        """Create EventListDTO from dictionary"""
        assert isinstance(obj, dict)
        return EventListDTO(
            response_code=from_str(obj.get("ResponseCode", obj.get("response_code", ""))),
            response_message=from_str(obj.get("ResponseMessage", obj.get("response_message", ""))),
            response_message_ar=from_str(obj.get("ResponseMessageAR", obj.get("response_message_ar", ""))),
            events=from_list(EventDTO.from_dict, obj.get("Events", obj.get("events", []))),
            error_message=from_str(obj.get("ErrorMessage", obj.get("error_message", ""))),
            total_count=from_int(obj.get("TotalCount", obj.get("total_count", 0)))
        )
    
    def to_dict(self) -> dict:
        """Convert EventListDTO to dictionary"""
        return {
            "ResponseCode": from_str(self.response_code),
            "ResponseMessage": from_str(self.response_message),
            "ResponseMessageAR": from_str(self.response_message_ar),
            "Events": [event.to_dict() for event in self.events],
            "ErrorMessage": from_str(self.error_message),
            "TotalCount": self.total_count
        }