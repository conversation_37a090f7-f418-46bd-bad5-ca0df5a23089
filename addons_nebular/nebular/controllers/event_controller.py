from odoo import http
from odoo.http import request
from datetime import datetime
from .base_controller import BaseController
from ..api.event_dto import EventDTO, EventListDTO
from ..api.utils import (
    validate_system_code, validate_event_type_for_system, 
    format_iso_datetime, parse_iso_datetime, safe_int_conversion
)


class EventController(BaseController):
    """
    RESTful API Controller for Event management.
    Supports all system types: fire, access, cctv, gate, pa, presence
    """
    
    @property
    def event_model(self):
        """Get event model with sudo access"""
        return self.request_context.env['nebular.event'].sudo()
    
    @http.route('/api/v1/events', type='http', auth='public', 
                methods=['GET'], csrf=False, cors="*")
    def get_events(self, system_code=None, event_type=None, building_id=None, 
                   floor_id=None, date_from=None, date_to=None, 
                   limit=100, offset=0, search=None, **kwargs):
        """
        Get list of events with optional filtering and pagination.
        
        Query Parameters:
        - system_code: Filter by system (fire|access|cctv|gate|pa|presence)
        - event_type: Filter by event type
        - building_id: Filter by building ID
        - floor_id: Filter by floor ID
        - date_from: Filter from date (ISO-8601 format)
        - date_to: Filter to date (ISO-8601 format)
        - limit: Maximum number of results (default: 100)
        - offset: Pagination offset (default: 0)
        - search: Search in message field
        """
        try:
            self.log_api_access('/api/v1/events', 'GET', {
                'system_code': system_code, 'event_type': event_type,
                'building_id': building_id, 'floor_id': floor_id,
                'limit': limit, 'offset': offset
            })
            
            # Build domain for filtering
            domain = []
            
            if system_code:
                if not validate_system_code(system_code):
                    return self.handle_api_error(
                        f"Invalid system_code: {system_code}", "101", 400, "ValidationError"
                    )
                domain.append(('system_id.code', '=', system_code))
            
            if event_type:
                domain.append(('event_type', '=', event_type))
            
            if building_id:
                domain.append(('building_id', '=', safe_int_conversion(building_id)))
            
            if floor_id:
                domain.append(('floor_id', '=', safe_int_conversion(floor_id)))
            
            if date_from:
                try:
                    dt_from = parse_iso_datetime(date_from)
                    domain.append(('event_time', '>=', dt_from))
                except ValueError:
                    return self.handle_api_error(
                        "Invalid date_from format. Use ISO-8601", "101", 400, "ValidationError"
                    )
            
            if date_to:
                try:
                    dt_to = parse_iso_datetime(date_to)
                    domain.append(('event_time', '<=', dt_to))
                except ValueError:
                    return self.handle_api_error(
                        "Invalid date_to format. Use ISO-8601", "101", 400, "ValidationError"
                    )
            
            if search:
                domain.append(('message', 'ilike', search))
            
            # Convert limit and offset to integers
            limit = safe_int_conversion(limit, 100)
            offset = safe_int_conversion(offset, 0)
            
            # Validate limit
            if limit > 1000:
                limit = 1000  # Cap at 1000 for performance
            
            # Get events with pagination
            events = self.event_model.search(
                domain,
                limit=limit,
                offset=offset,
                order='event_time desc'
            )
            
            # Get total count for pagination info
            total_count = self.event_model.search_count(domain)
            
            # Convert to DTOs
            event_dtos = [self._event_to_dto(event) for event in events]
            
            result = EventListDTO(
                response_code="000",
                response_message="Success",
                response_message_ar="نجح",
                events=event_dtos,
                total_count=total_count
            )
            
            return request.make_json_response(result.to_dict(), status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/v1/event/<int:event_id>', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_event(self, event_id, **kwargs):
        """
        Get single event by ID.
        
        Path Parameters:
        - event_id: Event ID
        """
        try:
            self.log_api_access(f'/api/v1/event/{event_id}', 'GET')
            
            event = self.event_model.browse(event_id)
            if not event.exists():
                return self.handle_api_error(
                    f"Event with ID {event_id} not found", "404", 404, "NotFound"
                )
            
            result = self._event_to_dto(event)
            result.response_code = "000"
            result.response_message = "Success"
            result.response_message_ar = "نجح"
            
            return request.make_json_response(result.to_dict(), status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/v1/test-debug', type='http', auth='public',
                methods=['POST'], csrf=False, cors="*")
    def test_debug(self):
        """Simple test endpoint to verify controller is working"""
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info("=== TEST DEBUG ENDPOINT REACHED ===")
        
        try:
            data = request.get_json_data()
            _logger.info(f"Test endpoint received data: {data}")
            
            return request.make_json_response({
                'response_code': '000',
                'response_message': 'Success',
                'message': 'Test endpoint working',
                'received_data': data
            })
        except Exception as e:
            _logger.error(f"Test endpoint error: {str(e)}")
            return request.make_json_response({
                'response_code': '101',
                'response_message': 'Error',
                'error': str(e)
            })

    @http.route('/api/v1/event', type='http', auth='public',
                methods=['POST'], csrf=False, cors="*")
    def create_event(self):
        """Create a new event"""
        try:
            # Debug logging at method start
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"=== CREATE EVENT START ===")
            
            # Parse JSON data
            try:
                data = request.get_json_data()
                if not data:
                    return self._error_response(
                        '101', 
                        'Request body is required',
                        'مطلوب نص الطلب'
                    )
            except Exception as e:
                return self._error_response(
                    '101',
                    'Invalid JSON format',
                    'تنسيق JSON غير صالح'
                )
            
            # Log API access
            self.log_api_access('/api/v1/event', 'POST', data)
            
            _logger.info(f"Received data: {data}")
            
            # Validate required fields
            required_fields = ['buildingId', 'systemCode', 'eventType']
            for field in required_fields:
                if field not in data:
                    return self._error_response(
                        '102', 
                        f'Missing required field: {field}',
                        f'حقل مطلوب مفقود: {field}'
                    )
            
            # Validate system code and event type BEFORE preparing data
            system_code = data.get('systemCode')
            api_event_type = data.get('eventType')
            
            _logger.info(f"Validating system_code: {system_code}, api_event_type: {api_event_type}")
            
            validation_result = validate_event_type_for_system(system_code, api_event_type)
            _logger.info(f"Validation result: {validation_result}")
            
            if not validation_result:
                _logger.info(f"Validation failed for system_code: {system_code}, api_event_type: {api_event_type}")
                return self._error_response(
                    '101',
                    f'Invalid event type "{api_event_type}" for system "{system_code}"',
                    f'نوع حدث غير صالح "{api_event_type}" للنظام "{system_code}"'
                )
            
            _logger.info("Validation passed, proceeding with event creation")
            
            # Map API event type to database event type BEFORE creating event data
            db_event_type = self._map_api_to_db_event_type(system_code, api_event_type)
            _logger.info(f"Mapped {api_event_type} -> {db_event_type}")
            
            # Update the data with the mapped event type
            data_copy = data.copy()
            data_copy['eventType'] = db_event_type
            _logger.info(f"Updated data with mapped event type: {data_copy}")
            
            # Parse datetime
            event_datetime = self._parse_datetime(data.get('datetime'))
            
            # Prepare event data with the mapped event type
            event_data = self._prepare_event_data(data_copy, event_datetime)
            _logger.info(f"Prepared event data: {event_data}")
            
            # Create the event (pass dictionary directly, not as list)
            _logger.info("Creating event record...")
            try:
                _logger.info("Event data type: %s", type(event_data))
                _logger.info("Event data content: %s", event_data)
                event = request.env['nebular.event'].sudo().create(event_data)
                _logger.info("Event created successfully with ID: %s", event.id)
            except Exception as e:
                _logger.error("Error during create: %s", e)
                _logger.error("Event data at error: %s", event_data)
                _logger.error("Event data type at error: %s", type(event_data))
                raise
            
            return self._success_response({
                'event_id': event.id,
                'event_code': event.event_code,
                'message': 'Event created successfully'
            })
            
        except ValueError as ve:
            _logger.error(f"ValueError in create_event: {str(ve)}")
            return self.handle_api_error(str(ve), "101", 400, "ValidationError")
        except Exception as ex:
            # Add detailed error logging
            import traceback
            _logger.error(f"Exception in create_event: {ex}")
            _logger.error(f"Exception type: {type(ex)}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            return self.handle_api_error(ex)
    
    @http.route('/api/v1/systems/<string:system_code>/events', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_system_events(self, system_code, event_type=None, building_id=None, 
                         floor_id=None, limit=100, offset=0, **kwargs):
        """
        Get events for a specific system.
        
        Path Parameters:
        - system_code: System identifier (fire|access|cctv|gate|pa|presence)
        
        Query Parameters:
        - event_type: Filter by event type
        - building_id: Filter by building ID
        - floor_id: Filter by floor ID
        - limit: Maximum number of results (default: 100)
        - offset: Pagination offset (default: 0)
        """
        try:
            if not validate_system_code(system_code):
                return self.handle_api_error(
                    f"Invalid system_code: {system_code}", "101", 400, "ValidationError"
                )
            
            # Forward to main get_events method with system_code filter
            return self.get_events(
                system_code=system_code,
                event_type=event_type,
                building_id=building_id,
                floor_id=floor_id,
                limit=limit,
                offset=offset,
                **kwargs
            )
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/v1/systems/<string:system_code>/status', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_system_status(self, system_code, **kwargs):
        """
        Get current status summary for a specific system.
        
        Path Parameters:
        - system_code: System identifier (fire|access|cctv|gate|pa|presence)
        """
        try:
            if not validate_system_code(system_code):
                return self.handle_api_error(
                    f"Invalid system_code: {system_code}", "101", 400, "ValidationError"
                )
            
            self.log_api_access(f'/api/v1/systems/{system_code}/status', 'GET')
            
            # Get latest events for the system
            domain = [('system_id.code', '=', system_code)]
            latest_events = self.event_model.search(
                domain, limit=10, order='event_time desc'
            )
            
            # Build status summary
            status_data = {
                "ResponseCode": "000",
                "ResponseMessage": "Success",
                "ResponseMessageAR": "نجح",
                # "SystemCode": system_code,
                # "SystemName": self._get_system_name(system_code),
                "LastUpdate": format_iso_datetime(latest_events[0].event_time) if latest_events else "",
                "EventCount": self.event_model.search_count(domain),
                "RecentEvents": [self._event_to_dto(event).to_dict() for event in latest_events[:5]]
            }
            
            return request.make_json_response(status_data, status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    def _event_to_dto(self, event):
        """Convert event record to DTO with comprehensive field mapping"""
        # Get specialized event data based on event type
        specialized_data = {}
        
        try:
            if event.event_type == 'presence':
                presence_event = request.env['nebular.presence.event'].sudo().search([
                    ('event_id', '=', event.id)
                ], limit=1)
                if presence_event:
                    specialized_data = {
                        'sensorId': presence_event.sensor_id,
                        'sensorCode': presence_event.sensor_code,
                        'sensorName': presence_event.sensor_name,
                        'sensorType': presence_event.type,
                        'count': presence_event.count,
                        'occupancy': presence_event.occupancy,
                        'location': presence_event.sensor_location,
                        'vendor': presence_event.vendor,
                        'model': presence_event.model
                    }
            
            elif event.event_type == 'cctv':
                cctv_event = request.env['nebular.cctv.event'].sudo().search([
                    ('event_id', '=', event.id)
                ], limit=1)
                if cctv_event:
                    specialized_data = {
                        'cameraId': cctv_event.camera_id,
                        'cameraCode': cctv_event.camera_code,
                        'cameraName': cctv_event.camera_name,
                        'location': cctv_event.camera_location,
                        'ip': cctv_event.ip_address,
                        'channel': cctv_event.channel,
                        'analyticType': cctv_event.detection_type,
                        'count': cctv_event.person_count,
                        'vendor': cctv_event.vendor,
                        'model': cctv_event.model
                    }
            
            elif event.event_type == 'pa':
                pa_event = request.env['nebular.pa.event'].sudo().search([
                    ('event_id', '=', event.id)
                ], limit=1)
                if pa_event:
                    specialized_data = {
                        'zoneId': pa_event.zone_id,
                        'zoneCode': pa_event.zone_code,
                        'zoneName': pa_event.zone_name,
                        'location': pa_event.location,
                        'volume': pa_event.volume,
                        'announcementId': pa_event.announcement_id,
                        'script': pa_event.script,
                        'durationSec': pa_event.duration_sec
                    }
            
            elif event.event_type == 'fire':
                fire_event = request.env['nebular.fire.event'].sudo().search([
                    ('event_id', '=', event.id)
                ], limit=1)
                if fire_event:
                    specialized_data = {
                        'panelCode': fire_event.panel_code,
                        'panelName': fire_event.panel_name,
                        'zone': fire_event.fire_zone,
                        'loop': fire_event.loop,
                        'nodeId': fire_event.node_id,
                        'nodeCode': fire_event.node_code,
                        'address': fire_event.address,
                        'alarmType': fire_event.alarm_type,
                        'sensorStatus': fire_event.sensor_status
                    }
            
            elif event.event_type == 'access':
                access_event = request.env['nebular.access.event'].sudo().search([
                    ('event_id', '=', event.id)
                ], limit=1)
                if access_event:
                    specialized_data = {
                        'cardNumber': access_event.card_number,
                        'cardHolderName': access_event.card_holder_name,
                        'employeeCode': access_event.employee_code,
                        'accessType': access_event.access_type,
                        'accessResult': access_event.access_result,
                        'accessPoint': access_event.access_point,
                        'userCode': access_event.user_code
                    }
            
        except Exception as e:
            # Log error but continue with base event data
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"Error getting specialized event data: {str(e)}")
        
        # Build comprehensive DTO
        dto_data = {
            'id': event.id,
            'name': event.name,
            'eventCode': event.event_code,
            'eventType': event.event_type,
            'buildingId': event.building_id.id if event.building_id else None,
            'buildingName': event.building_id.name if event.building_id else '',
            'floorId': event.floor_id.id if event.floor_id else None,
            'floorName': event.floor_id.name if event.floor_id else '',
            'zoneId': event.zone_id.id if event.zone_id else None,
            'zoneName': event.zone_id.name if event.zone_id else '',
            'systemCode': event.system_id.code if event.system_id else event.event_type,
            'systemName': event.system_id.name if event.system_id else self._get_system_name(event.event_type),
            'datetime': format_iso_datetime(event.event_time),
            'description': event.description or '',
            'message': event.message or '',
            'sourceEventCode': event.source_event_code or '',
            'sourceState': event.source_state or '',
            'state': event.state,
            'severity': event.severity,
            'priority': int(event.priority) if event.priority else 2,
            'isAlert': event.is_alert,
            'isActive': event.is_active,
            'location': event.location_display or event.location or '',
            'data': specialized_data
        }
        
        # Remove None values
        dto_data = {k: v for k, v in dto_data.items() if v is not None}
        
        return EventDTO(dto_data)
    
    def _populate_system_specific_fields(self, dto, event, event_id):
        """Populate system-specific fields in DTO based on system_code - DEPRECATED"""
        # This method is deprecated and replaced by the new _event_to_dto implementation
        # Keeping for backward compatibility
        pass
    
    def _prepare_event_data(self, data, event_datetime):
        """Prepare comprehensive event data for database insertion"""
        # Get system record
        system_code = data.get('systemCode')
        system = request.env['nebular.system'].sudo().search([
            ('code', '=', system_code)
        ], limit=1)
        
        # Generate event code if not provided
        event_code = data.get('eventCode')
        if not event_code:
            event_code = f"{system_code.upper()}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Map API event types to database event types
        api_event_type = data.get('eventType')
        db_event_type = self._map_api_to_db_event_type(system_code, api_event_type)
        
        # Debug logging for mapping
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info(f"=== EVENT TYPE MAPPING DEBUG ===")
        _logger.info(f"System code: {system_code}")
        _logger.info(f"API event type: {api_event_type}")
        _logger.info(f"Mapped DB event type: {db_event_type}")
        _logger.info(f"=== END MAPPING DEBUG ===")
        
        event_data = {
            # Required fields
            'name': data.get('name', f"{system_code} {api_event_type} event"),
            'event_code': event_code,
            'event_type': db_event_type,  # This should be 'access', not 'accessGranted'
            'building_id': safe_int_conversion(data.get('buildingId')),
            'event_time': event_datetime,
            
            # Optional core fields
            'description': data.get('description', ''),
            'message': data.get('message', ''),
            'severity': data.get('severity', 'medium'),
            'priority': str(data.get('priority', 2)),
            'state': data.get('state', 'new'),
            
            # Location fields
            'floor_id': safe_int_conversion(data.get('floorId')) or False,
            'zone_id': safe_int_conversion(data.get('zoneId')) or False,
            'location': data.get('location', ''),
            
            # Source information
            'source_event_code': data.get('sourceEventCode', ''),
            'source_state': data.get('sourceState', ''),
            
            # System information
            'system_id': system.id if system else False,
            
            # Additional fields
            'is_alert': data.get('isAlert', False),
            'is_active': data.get('isActive', True),
        }
        
        # Remove False values to avoid database issues
        event_data = {k: v for k, v in event_data.items() if v is not False}
        
        _logger.info(f"Final event_data before create: {event_data}")
        _logger.info(f"Final event_type value: {event_data.get('event_type')}")
        
        return event_data
    
    def _map_api_to_db_event_type(self, system_code, api_event_type):
        """Map API event types to database event types"""
        # The database event_type field expects system-level types
        # while the API validates specific event types per system
        system_to_db_type = {
            'fire': 'fire',
            'access': 'access', 
            'cctv': 'cctv',
            'gate': 'gate',
            'pa': 'pa',
            'presence': 'presence'
        }
        
        # Return the system-level event type for the database
        return system_to_db_type.get(system_code, 'other')
    
    def _extract_system_specific_data(self, system_code, data_obj):
        """Extract system-specific data fields for database storage"""
        extracted = {}
        
        if system_code == "fire":
            extracted.update({
                'panel_id': safe_int_conversion(data_obj.get('id')),
                'panel_code': data_obj.get('panelCode', ''),
                'panel_name': data_obj.get('name', ''),
                'zone': data_obj.get('zone', ''),
                'loop': data_obj.get('loop', ''),
                'node_id': safe_int_conversion(data_obj.get('nodeId')),
                'node_code': data_obj.get('nodeCode', ''),
                'address': data_obj.get('address', '')
            })
            
        elif system_code == "access":
            extracted.update({
                'controller_id': safe_int_conversion(data_obj.get('id')),
                'controller_code': data_obj.get('controllerCode', ''),
                'controller_name': data_obj.get('name', ''),
                'reader_id': safe_int_conversion(data_obj.get('readerId')),
                'reader_code': data_obj.get('readerCode', ''),
                'card_id': safe_int_conversion(data_obj.get('cardId')),
                'card_code': data_obj.get('cardCode', ''),
                'user_id': safe_int_conversion(data_obj.get('userId')),
                'user_code': data_obj.get('userCode', ''),
                'door_id': data_obj.get('doorId', ''),
                'door_name': data_obj.get('doorName', ''),
                'vendor': data_obj.get('vendor', ''),
                'model': data_obj.get('model', ''),
                'holder': data_obj.get('holder', ''),
                'result': data_obj.get('result', ''),
                'reason': data_obj.get('reason', ''),
                'held_open_seconds': safe_int_conversion(data_obj.get('heldOpenSeconds'))
            })

        elif system_code == "presence":
            extracted.update({
                'detection_count': safe_int_conversion(data_obj.get('detectionCount')),

            })
            
        # Add similar blocks for other systems...
        # (cctv, gate, pa, presence)
        
        return extracted
    
    def _create_specialized_event(self, event, data):
        """Create specialized event records based on system type"""
        import logging
        _logger = logging.getLogger(__name__)
        
        _logger.info(f"_create_specialized_event called with data type: {type(data)}")
        _logger.info(f"_create_specialized_event data content: {data}")
        
        try:
            system_code = data.get('systemCode')
            _logger.info(f"System code extracted: {system_code}")
        except AttributeError as e:
            _logger.error(f"AttributeError when getting systemCode: {e}")
            _logger.error(f"Data type: {type(data)}, Data value: {data}")
            raise
        
        try:
            if system_code == 'presence':
                # Create presence-specific event
                presence_data = {
                    'event_id': event.id,
                    'count': safe_int_conversion(data.get('detectionCount', 0)),
                    'sensor_id': safe_int_conversion(data.get('sensorId')),
                    'sensor_code': data.get('sensorCode', ''),
                    'sensor_name': data.get('sensorName', ''),
                    'sensor_type': data.get('sensorType', ''),
                    'occupancy': data.get('occupancy', False),
                    'location': data.get('location', ''),
                    'vendor': data.get('vendor', ''),
                    'model': data.get('model', '')
                }
                # Remove None/False values
                presence_data = {k: v for k, v in presence_data.items() if v not in [None, False, '']}
                request.env['nebular.presence.event'].sudo().create(presence_data)
            
            elif system_code == 'cctv':
                # Create CCTV-specific event
                cctv_data = {
                    'event_id': event.id,
                    'person_count': safe_int_conversion(data.get('count', 0)),
                    'camera_id': safe_int_conversion(data.get('cameraId')),
                    'camera_code': data.get('cameraCode', ''),
                    'camera_name': data.get('cameraName', ''),
                    'location': data.get('location', ''),
                    'ip': data.get('ip', ''),
                    'channel': safe_int_conversion(data.get('channel')),
                    'analytic_type': data.get('analyticType', ''),
                    'vendor': data.get('vendor', ''),
                    'model': data.get('model', '')
                }
                # Remove None/False values
                cctv_data = {k: v for k, v in cctv_data.items() if v not in [None, False, '']}
                request.env['nebular.cctv.event'].sudo().create(cctv_data)
            
            elif system_code == 'pa':
                # Create PA-specific event
                pa_data = {
                    'event_id': event.id,
                    'zone_id': safe_int_conversion(data.get('zoneId')),
                    'zone_code': data.get('zoneCode', ''),
                    'zone_name': data.get('zoneName', ''),
                    'location': data.get('location', ''),
                    'volume': safe_int_conversion(data.get('volume')),
                    'announcement_id': data.get('announcementId', ''),
                    'script': data.get('script', ''),
                    'duration_sec': safe_int_conversion(data.get('durationSec')),
                    'vendor': data.get('vendor', ''),
                    'model': data.get('model', '')
                }
                # Remove None/False values
                pa_data = {k: v for k, v in pa_data.items() if v not in [None, False, '']}
                request.env['nebular.pa.event'].sudo().create(pa_data)
            
            # Add other system types as needed...
            
        except Exception as e:
            # Log error but don't fail the main event creation
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"Failed to create specialized event for {system_code}: {str(e)}")
    
    def _get_system_name(self, system_code):
        """Get human-readable system name"""
        system_names = {
            'fire': 'Fire Alarm',
            'access': 'Access Control',
            'cctv': 'CCTV',
            'gate': 'Gate Barrier',
            'pa': 'Public Address',
            'presence': 'Presence Sensors'
        }
        return system_names.get(system_code, system_code)
    
    @http.route('/api/v1/fast_test', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def fast_test_event(self, system_code, **kwargs):
        """
        Fast test endpoint to generate events based on system_code parameter only.
        Generates all required data intelligently for testing purposes.
        
        Usage: GET /api/v1/fast_test?system_code=fire
        """
        try:
            # Validate system_code
            if not system_code:
                return self.handle_api_error(
                    ValueError("system_code parameter is required"),
                    response_code="101",
                    status=400,
                    message="Missing required parameter: system_code"
                )
            
            if not validate_system_code(system_code):
                return self.handle_api_error(
                    ValueError(f"Invalid system_code: {system_code}"),
                    response_code="101", 
                    status=400,
                    message=f"Invalid system_code. Must be one of: fire, access, cctv, gate, pa, presence"
                )
            
            # Generate smart test data based on system_code
            test_data = self._generate_smart_test_data(system_code)
            
            # Create the event
            event = self.event_model.create(test_data)
            
            # Convert to DTO for response
            dto = self._event_to_dto(event)
            dto.response_code = '000'
            dto.response_message = 'Test event created successfully'
            dto.response_message_ar = 'تم إنشاء حدث الاختبار بنجاح'
            
            return request.make_json_response(dto.to_dict(), status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex, response_code="100", status=500)
    
    def _generate_smart_test_data(self, system_code):
        """Generate intelligent test data based on system_code"""
        from datetime import datetime
        import random
        
        # Get system record
        system = request.env['nebular.system'].sudo().search([
            ('code', '=', system_code)
        ], limit=1)
        
        # Generate event code
        event_code = f"{system_code.upper()}-TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Base event data
        base_data = {
            # Required fields
            'name': f'Test {system_code} event',
            'event_code': event_code,
            'event_type': random.choice(['test', 'alarm', 'fault', 'normal']),
            'building_id': 1,
            'event_time': datetime.now(),
            
            # Optional fields
            'description': f'Automated test event for {system_code} system',
            'message': f'Test {system_code} event generated automatically',
            'severity': random.choice(['low', 'medium', 'high']),
            'priority': str(random.randint(1, 5)),
            'state': random.choice(['new', 'acknowledged', 'resolved']),
            'floor_id': random.randint(1, 5),
            'source_state': random.choice(['closed', 'open', 'active', 'inactive']),
            'system_id': system.id if system else False,
            'is_alert': random.choice([True, False]),
            'is_active': True,
        }
        
        # System-specific data generation
        if system_code == 'fire':
            base_data.update({
                'event_type': random.choice(['alarm', 'fault', 'test', 'restore']),
                'source_event_code': f'FP-{random.randint(100, 999)}',
                'panel_id': random.randint(1, 10),
                'panel_code': f'FP-{random.randint(1, 10):02d}',
                'panel_name': f'Fire Panel {random.randint(1, 10)}',
                'zone': f'Zone-{random.randint(1, 16)}',
                'loop': f'Loop-{random.randint(1, 4)}',
                'node_id': random.randint(1, 100),
                'node_code': f'FD-{random.randint(100, 999)}',
                'address': f'{random.randint(1, 255)}'
            })
            
        elif system_code == 'access':
            base_data.update({
                'event_type': random.choice(['entry', 'exit', 'denied', 'forced']),
                'source_event_code': f'AC-{random.randint(100, 999)}',
                'controller_id': random.randint(1, 20),
                'controller_code': f'AC-{random.randint(1, 20):02d}',
                'controller_name': f'Access Controller {random.randint(1, 20)}',
                'reader_id': random.randint(1, 50),
                'reader_code': f'RD-{random.randint(100, 999)}',
                'card_id': random.randint(1000, 9999),
                'card_code': f'CD-{random.randint(10000, 99999)}',
                'user_id': random.randint(1, 500),
                'user_code': f'USR-{random.randint(1000, 9999)}',
                'door_id': f'DR-{random.randint(100, 999)}',
                'door_name': f'Door {random.randint(1, 100)}',
                'result': random.choice(['granted', 'denied', 'timeout']),
                'reason': random.choice(['schedule', 'invalidCard', 'expired', 'antipassback', 'pinRequired']),
                'held_open_seconds': random.randint(5, 30)
            })
            
        elif system_code == 'cctv':
            base_data.update({
                'event_type': random.choice(['motion', 'recording', 'offline', 'online']),
                'source_event_code': f'CAM-{random.randint(100, 999)}',
                'camera_id': random.randint(1, 100),
                'camera_code': f'CAM-{random.randint(1, 100):03d}',
                'camera_name': f'Camera {random.randint(1, 100)}',
                'location': f'Location-{random.randint(1, 50)}',
                'ip': f'192.168.1.{random.randint(100, 200)}',
                'channel': random.randint(1, 16),
                'analytic_type': random.choice(['motion', 'lineCrossing', 'loitering', 'peopleCount']),
                'count': random.randint(1, 10),
                'recording': random.choice([True, False])
            })
            
        elif system_code == 'gate':
            base_data.update({
                'event_type': random.choice(['open', 'close', 'vehicle_detected', 'fault']),
                'source_event_code': f'GT-{random.randint(100, 999)}',
                'gate_code': f'GT-{random.randint(1, 20):02d}',
                'gate_name': f'Gate {random.randint(1, 20)}',
                'status': random.choice(['open', 'closed', 'opening', 'closing']),
                'vehicle_plate': f'ABC-{random.randint(1000, 9999)}',
                'trigger': random.choice(['manual', 'card', 'remote', 'anpr']),
                'anpr_confidence': round(random.uniform(0.7, 1.0), 2)
            })
            
        elif system_code == 'pa':
            base_data.update({
                'event_type': random.choice(['announcement', 'emergency', 'test', 'fault']),
                'source_event_code': f'PA-{random.randint(100, 999)}',
                'zone_id': random.randint(1, 50),
                'zone_code': f'PA-Z{random.randint(1, 50):02d}',
                'zone_name': f'PA Zone {random.randint(1, 50)}',
                'volume': random.randint(1, 10),
                'announcement_id': f'ANN-{random.randint(1000, 9999)}',
                'script': f'Test announcement script {random.randint(1, 100)}',
                'duration_sec': random.randint(10, 300)
            })
            
        elif system_code == 'presence':
            base_data.update({
                'event_type': random.choice(['occupied', 'vacant', 'motion', 'fault']),
                'source_event_code': f'PR-{random.randint(100, 999)}',
                'sensor_id': random.randint(1, 200),
                'sensor_code': f'PR-{random.randint(1, 200):03d}',
                'sensor_name': f'Presence Sensor {random.randint(1, 200)}',
                'sensor_type': random.choice(['pir', 'microwave', 'ultrasonic', 'camera']),
                'occupancy': random.choice([True, False]),
            })
        
        return base_data
    
    def _error_response(self, code, message, message_ar):
        """Helper method to create error responses"""
        return request.make_json_response({
            'response_code': code,
            'response_message': 'ValidationError',
            'response_message_ar': 'خطأ في التحقق',
            'ErrorMessage': message
        }, status=400)
    
    def _success_response(self, data):
        """Helper method to create success responses"""
        response_data = {
            'response_code': '000',
            'response_message': 'Success',
            'response_message_ar': 'نجح'
        }
        response_data.update(data)
        return request.make_json_response(response_data, status=201)
    
    def _parse_datetime(self, datetime_str):
        """Parse datetime string"""
        if not datetime_str:
            return datetime.now()
        try:
            return parse_iso_datetime(datetime_str)
        except:
            return datetime.now()