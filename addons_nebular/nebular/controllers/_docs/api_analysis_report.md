# Nebular Event API Analysis Report

## Overview
This document provides a comprehensive analysis of the Nebular Event API based on actual data retrieved from the `get_events` endpoint. The analysis covers data structures, field relationships, and system-specific patterns observed in the current implementation.

## API Endpoints Analysis

### GET /api/v1/events
- **Status**: ✅ Fully Functional
- **Total Events in Database**: 32 events (after testing)
- **Response Format**: JSON with pagination support
- **Filtering Support**: system_code, event_type, building_id, floor_id, date_from, date_to, search
- **Pagination**: limit, offset parameters

### POST /api/v1/event
- **Status**: ✅ Fully Functional
- **URL**: `/api/v1/event` (singular, not plural)
- **Auto-generates**: event_code with format: `{SYSTEM}-{TIMESTAMP}`
- **Data Validation**: Comprehensive validation for all system types
- **Specialized Data**: Supports system-specific data in `data` attribute
- **Event Type Validation**: Strict validation per system type (see valid event types below)

## Valid Event Types by System

Based on the validation rules in `utils.py`, each system accepts specific event types:

### Fire System (`fire`)
- `online`, `offline`, `reset`, `fault`, `other`, `alarm`

### Access Control System (`access`)
- `accessGranted`, `accessDenied`, `doorOpen`, `doorClose`, `heldOpen`, `forcedOpen`, `tamper`, `fault`, `status`, `other`

### CCTV System (`cctv`)
- `alarm`, `motion`, `smoke`, `fire`, `audioDetection`, `lineCrossing`, `loitering`, `peopleCount`, `faceDetection`

### Gate System (`gate`)
- `status`, `open`, `close`, `blocked`, `fault`, `tamper`, `other`

### PA System (`pa`)
- `announcementStarted`, `announcementEnded`, `fault`, `status`, `other`

### Presence System (`presence`)
- `presenceDetected`, `presenceCleared`, `peopleCount`, `fault`, `status`, `other`

## Current Data Structure Analysis

### Common Event Fields (All Events)
Based on analysis of 31 events in the database:

```json
{
  "name": "string",              // Event display name
  "buildingId": "integer",       // Building reference (1-2 observed)
  "floorId": "integer",          // Floor reference (1-4 observed)
  "systemCode": "string",        // System identifier
  "systemName": "string",        // System display name
  "eventType": "string",         // Event category
  "datetime": "ISO-8601",        // Event timestamp in UTC
  "description": "string",       // Detailed description
  "message": "string",           // Optional message field
  "sourceEventCode": "string",   // Source device/system code
  "sourceState": "string",       // Source device state
  "state": "string",             // Current event state
  "severity": "string",          // Event severity level
  "data": {}                     // System-specific data object
}
```

### System Code Distribution
From current database analysis:

| System Code | Count | Percentage | System Name |
|-------------|-------|------------|-------------|
| fire        | 9     | 29%        | Fire Alarm System |
| gate        | 7     | 23%        | Gate Barrier |
| access      | 6     | 19%        | Access Control |
| cctv        | 4     | 13%        | CCTV Surveillance |
| pa          | 3     | 10%        | Public Address System |
| presence    | 2     | 6%         | Presence Sensors |

### Event Type Distribution
From current database analysis:

| Event Type   | Count | Systems Using |
|--------------|-------|---------------|
| maintenance  | 6     | fire, gate, pa |
| fire         | 3     | fire |
| pa           | 3     | access, pa |
| gate         | 3     | gate, access |
| access       | 3     | gate, access |
| cctv         | 3     | gate, cctv |
| alarm        | 2     | gate, fire |
| presence     | 2     | cctv, presence |
| system       | 2     | fire |
| other        | 1     | fire |

### Severity Level Distribution

| Severity | Count | Percentage |
|----------|-------|------------|
| critical | 9     | 29%        |
| high     | 7     | 23%        |
| medium   | 6     | 19%        |
| low      | 9     | 29%        |

### State Distribution

| State        | Count | Percentage |
|--------------|-------|------------|
| new          | 18    | 58%        |
| resolved     | 4     | 13%        |
| in_progress  | 4     | 13%        |
| acknowledged | 3     | 10%        |
| closed       | 2     | 6%         |

## System-Specific Data Analysis

### 1. Presence Sensors System
**Current Data Structure Observed**:
```json
{
  "data": {
    "sensorCode": "Presence  5",
    "detection_count": 5
  }
}
```

**Available Fields** (from controller mapping):
- sensorId, sensorCode, sensorName, sensorType
- count (mapped to detection_count in response)
- occupancy, location, vendor, model

### 2. Public Address System
**Current Data Structure Observed**:
```json
{
  "data": {
    "name": "North Wing"  // Zone name
  }
}
```

**Available Fields** (from controller mapping):
- zoneId, zoneCode, zoneName, location
- volume, announcementId, script, durationSec

### 3. Fire Alarm System
**Current Data Structure**: No specialized data observed in current events
**Available Fields** (from controller mapping):
- panelCode, panelName, zone, loop
- nodeId, nodeCode, address
- alarmType, sensorStatus

### 4. Access Control System
**Current Data Structure**: No specialized data observed in current events
**Available Fields** (from controller mapping):
- cardNumber, cardHolderName, employeeCode
- accessType, accessResult, accessPoint, userCode

### 5. CCTV Surveillance System
**Current Data Structure**: No specialized data observed in current events
**Available Fields** (from controller mapping):
- cameraId, cameraCode, cameraName, location
- ip, channel, analyticType, count
- vendor, model

### 6. Gate Barrier System
**Current Data Structure**: No specialized data observed in current events
**Available Fields**: Uses common event fields only

## Field Relationship Analysis

### Building and Floor Relationships
- **Building IDs**: 1, 2 (2 buildings in system)
- **Floor IDs**: 1, 2, 3, 4 (up to 4 floors per building)
- **Relationship**: Many-to-one (floors belong to buildings)

### System and Event Type Relationships
- **Cross-system Event Types**: Some event types appear across multiple systems
  - `access`: Used by gate and access systems
  - `cctv`: Used by gate and cctv systems
  - `maintenance`: Used by fire, gate, and pa systems

### Source Event Code Patterns
- **Fire System**: `FD-{floor}{room}-{address}` (e.g., "FD-201-15")
- **Access System**: `AC-{sequence}` (e.g., "AC-001")
- **Gate System**: `GATE-{sequence}` (e.g., "GATE-001")
- **CCTV System**: `CAM-{id}` (e.g., "CAM-105")
- **PA System**: `PA-{zone}-{sequence}` (e.g., "PA-ZONE-A2")
- **Presence System**: `PRES-{floor}{sequence}` (e.g., "PRES-301")

## Data Quality Observations

### Complete Records
- All events have required common fields
- Datetime fields are properly formatted in ISO-8601 UTC
- Building and floor references are consistent

### Missing Specialized Data
- Most events (29/31) lack specialized data in the `data` field
- Only presence sensors events consistently include specialized data
- PA events occasionally include zone information

### Inconsistencies
- Some PA events are categorized under "access" system instead of "pa" system
- Event type usage varies across systems (not strictly enforced)

## Recommendations for Testing

### 1. Data Completeness Testing
Test events with full specialized data for each system type to ensure proper storage and retrieval.

### 2. Cross-System Event Type Testing
Verify that event types work correctly across different systems (e.g., "access" events in both gate and access systems).

### 3. Field Validation Testing
Test boundary conditions for:
- Required vs optional fields
- Field length limits
- Data type validation
- Foreign key relationships

### 4. Performance Testing
With 31 events showing good performance, test with larger datasets to ensure scalability.

## Current System Health
- ✅ **Database Connectivity**: Excellent
- ✅ **API Response Time**: Fast (<1 second for 20 events)
- ✅ **Data Consistency**: Good (no null/corrupt records)
- ✅ **Error Handling**: Robust (handles missing specialized data gracefully)
- ⚠️ **Data Completeness**: Moderate (specialized data missing in most events)
- ✅ **Field Mapping**: Accurate (all common fields properly mapped)

## Next Steps for Development
1. **Enhance Demo Data**: Add specialized data for all system types
2. **Validation Rules**: Implement stricter event type validation per system
3. **Data Migration**: Populate missing specialized data for existing events
4. **Documentation**: Update API documentation with actual field examples
5. **Testing Suite**: Implement comprehensive automated testing for all system types
