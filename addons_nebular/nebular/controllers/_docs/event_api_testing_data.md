# Event API Testing Data Documentation

## Overview
This document provides comprehensive testing data for the Nebular Event API, specifically for the `create_event` endpoint. The data structure varies based on the `systemCode` parameter, with each system type having specific fields in the `data` attribute.

## API Endpoint
- **URL**: `POST /api/v1/events`
- **Content-Type**: `application/json`

## Common Event Structure
All events share these common fields:
```json
{
  "name": "Event Name",
  "buildingId": 1,
  "floorId": 1,
  "systemCode": "system_type",
  "eventType": "event_category",
  "datetime": "2024-01-25T10:30:00Z",
  "description": "Event description",
  "message": "Optional message",
  "sourceEventCode": "device_or_source_code",
  "sourceState": "device_state",
  "state": "new|in_progress|resolved|closed",
  "severity": "low|medium|high|critical",
  "data": {
    // System-specific fields go here
  }
}
```

## System-Specific Data Structures

### 1. Fire Alarm System (`systemCode: "fire"`)

**Available Event Types**: `online`, `offline`, `reset`, `fault`, `other`, `alarm`

**Data Structure**:
```json
{
  "panelCode": "FP-001",
  "panelName": "Main Fire Panel",
  "zone": "Zone A-1",
  "loop": "Loop 1",
  "nodeId": 101,
  "nodeCode": "FD-101",
  "address": "Address 15",
  "alarmType": "smoke|heat|manual|sprinkler",
  "sensorStatus": "normal|alarm|fault|test"
}
```

**Test Data Examples**:
```json
{
  "name": "Smoke Detector Activation",
  "buildingId": 1,
  "floorId": 2,
  "systemCode": "fire",
  "eventType": "fire",
  "datetime": "2024-01-25T14:30:00Z",
  "description": "Smoke detector activated in server room",
  "sourceEventCode": "FD-201-15",
  "sourceState": "alarm",
  "state": "new",
  "severity": "critical",
  "data": {
    "panelCode": "FP-002",
    "panelName": "Floor 2 Fire Panel",
    "zone": "Server Room Zone",
    "loop": "Loop 2",
    "nodeId": 215,
    "nodeCode": "FD-215",
    "address": "Address 15",
    "alarmType": "smoke",
    "sensorStatus": "alarm"
  }
}
```

### 2. Access Control System (`systemCode: "access"`)

**Available Event Types**: `accessGranted`, `accessDenied`, `doorOpen`, `doorClose`, `heldOpen`, `forcedOpen`, `tamper`, `fault`, `status`, `other`

**Data Structure**:
```json
{
  "cardNumber": "123456789",
  "cardHolderName": "John Doe",
  "employeeCode": "EMP001",
  "accessType": "card|pin|biometric|mobile",
  "accessResult": "granted|denied|timeout",
  "accessPoint": "Main Entrance",
  "userCode": "USER001"
}
```

**Test Data Examples**:
```json
{
  "name": "Access Granted",
  "buildingId": 1,
  "floorId": 1,
  "systemCode": "access",
  "eventType": "access",
  "datetime": "2024-01-25T08:15:00Z",
  "description": "Employee access granted at main entrance",
  "sourceEventCode": "AC-001",
  "sourceState": "granted",
  "state": "new",
  "severity": "low",
  "data": {
    "cardNumber": "987654321",
    "cardHolderName": "Jane Smith",
    "employeeCode": "EMP002",
    "accessType": "card",
    "accessResult": "granted",
    "accessPoint": "Main Entrance Door",
    "userCode": "JSMITH"
  }
}
```

### 3. CCTV Surveillance System (`systemCode: "cctv"`)

**Available Event Types**: `alarm`, `motion`, `smoke`, `fire`, `audioDetection`, `lineCrossing`, `loitering`, `peopleCount`, `faceDetection`

**Data Structure**:
```json
{
  "cameraId": 101,
  "cameraCode": "CAM-001",
  "cameraName": "Main Entrance Camera",
  "location": "Building A - Main Entrance",
  "ip": "*************",
  "channel": 1,
  "analyticType": "motion|smoke|fire|audio|lineCrossing|loitering|peopleCount|face",
  "count": 5,
  "vendor": "Hikvision",
  "model": "DS-2CD2385FWD-I"
}
```

**Test Data Examples**:
```json
{
  "name": "Motion Detection",
  "buildingId": 1,
  "floorId": 1,
  "systemCode": "cctv",
  "eventType": "cctv",
  "datetime": "2024-01-25T22:45:00Z",
  "description": "Motion detected in restricted area after hours",
  "sourceEventCode": "CAM-105",
  "sourceState": "motion_detected",
  "state": "new",
  "severity": "high",
  "data": {
    "cameraId": 105,
    "cameraCode": "CAM-105",
    "cameraName": "Warehouse Camera 5",
    "location": "Warehouse - Section C",
    "ip": "*************",
    "channel": 5,
    "analyticType": "motion",
    "count": 1,
    "vendor": "Dahua",
    "model": "IPC-HFW4431R-Z"
  }
}
```

### 4. Gate Barrier System (`systemCode: "gate"`)

**Available Event Types**: `status`, `open`, `close`, `blocked`, `fault`, `tamper`, `other`

**Data Structure**: Gate events typically don't have specialized data fields, but use common event fields.

**Test Data Examples**:
```json
{
  "name": "Gate Barrier Malfunction",
  "buildingId": 1,
  "floorId": 1,
  "systemCode": "gate",
  "eventType": "gate",
  "datetime": "2024-01-25T16:20:00Z",
  "description": "Main gate barrier failed to close properly",
  "sourceEventCode": "GATE-001",
  "sourceState": "fault",
  "state": "new",
  "severity": "high",
  "data": {}
}
```

### 5. Public Address System (`systemCode: "pa"`)

**Available Event Types**: `announcementStarted`, `announcementEnded`, `fault`, `status`, `other`

**Data Structure**:
```json
{
  "zoneId": 1,
  "zoneCode": "ZONE-A1",
  "zoneName": "North Wing",
  "location": "Building A - North Wing",
  "volume": 75,
  "announcementId": "ANN-001",
  "script": "Emergency evacuation announcement",
  "durationSec": 30
}
```

**Test Data Examples**:
```json
{
  "name": "Emergency Announcement",
  "buildingId": 1,
  "floorId": 2,
  "systemCode": "pa",
  "eventType": "pa",
  "datetime": "2024-01-25T11:00:00Z",
  "description": "Emergency evacuation announcement broadcast",
  "sourceEventCode": "PA-ZONE-A2",
  "sourceState": "broadcasting",
  "state": "new",
  "severity": "critical",
  "data": {
    "zoneId": 2,
    "zoneCode": "ZONE-A2",
    "zoneName": "South Wing Floor 2",
    "location": "Building A - South Wing - Floor 2",
    "volume": 85,
    "announcementId": "EMRG-001",
    "script": "Attention all personnel. Please evacuate the building immediately via the nearest exit.",
    "durationSec": 45
  }
}
```

### 6. Presence Sensors System (`systemCode: "presence"`)

**Available Event Types**: `presenceDetected`, `presenceCleared`, `peopleCount`, `fault`, `status`, `other`

**Data Structure**:
```json
{
  "sensorId": 201,
  "sensorCode": "PRES-001",
  "sensorName": "Conference Room Sensor",
  "sensorType": "pir|microwave|ultrasonic|camera",
  "count": 5,
  "occupancy": true,
  "location": "Conference Room A",
  "vendor": "Bosch",
  "model": "ISC-FPX1-W12"
}
```

**Test Data Examples**:
```json
{
  "name": "Occupancy Detected",
  "buildingId": 1,
  "floorId": 3,
  "systemCode": "presence",
  "eventType": "presence",
  "datetime": "2024-01-25T09:30:00Z",
  "description": "Multiple people detected in conference room",
  "sourceEventCode": "PRES-301",
  "sourceState": "occupied",
  "state": "new",
  "severity": "low",
  "data": {
    "sensorId": 301,
    "sensorCode": "PRES-301",
    "sensorName": "Conference Room C Sensor",
    "sensorType": "pir",
    "count": 8,
    "occupancy": true,
    "location": "Floor 3 - Conference Room C",
    "vendor": "Honeywell",
    "model": "PIR-HC-SR501"
  }
}
```

## Field Mapping Reference

### From API Request to Database
The controller maps API fields to database fields as follows:

**Common Fields**:
- `name` → `name`
- `buildingId` → `building_id`
- `floorId` → `floor_id`
- `systemCode` → `system_id` (lookup)
- `eventType` → `event_type`
- `datetime` → `event_time`
- `description` → `description`
- `message` → `message`
- `sourceEventCode` → `source_event_code`
- `sourceState` → `source_state`
- `state` → `state`
- `severity` → `severity`

**System-Specific Data Fields**:
The `data` object fields are mapped to specialized event model fields based on the system type.

## Testing Commands

### Basic Fire Event Test
```bash
curl -X POST "http://localhost:8088/api/v1/events" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Fire Alarm",
    "buildingId": 1,
    "floorId": 1,
    "systemCode": "fire",
    "eventType": "fire",
    "datetime": "2024-01-25T15:00:00Z",
    "description": "Test fire alarm activation",
    "state": "new",
    "severity": "critical",
    "data": {
      "panelCode": "FP-TEST",
      "zone": "Test Zone",
      "alarmType": "smoke"
    }
  }'
```

### Basic Access Event Test
```bash
curl -X POST "http://localhost:8088/api/v1/events" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Access Event",
    "buildingId": 1,
    "floorId": 1,
    "systemCode": "access",
    "eventType": "access",
    "datetime": "2024-01-25T15:00:00Z",
    "description": "Test access control event",
    "state": "new",
    "severity": "low",
    "data": {
      "cardNumber": "TEST123",
      "cardHolderName": "Test User",
      "accessResult": "granted"
    }
  }'
```

## Notes
- All datetime fields should be in ISO-8601 UTC format
- The `data` object is optional but recommended for system-specific information
- Event codes are auto-generated if not provided
- Building and floor IDs must exist in the system
- System codes must match existing system records in the database
