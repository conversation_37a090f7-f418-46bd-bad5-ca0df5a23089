#!/bin/bash

# Event API Testing Script
# This script tests the create_event endpoint with various system types and data structures

BASE_URL="http://localhost:8088/api/v1/event"
CONTENT_TYPE="Content-Type: application/json"

echo "=== Nebular Event API Testing Script ==="
echo "Testing endpoint: $BASE_URL"
echo ""

# Function to test event creation
test_event() {
    local test_name="$1"
    local json_data="$2"
    
    echo "Testing: $test_name"
    echo "----------------------------------------"
    
    response=$(curl -s -X POST "$BASE_URL" \
        -H "$CONTENT_TYPE" \
        -d "$json_data")
    
    echo "Response: $response"
    echo ""
    
    # Extract event ID if successful
    event_id=$(echo "$response" | jq -r '.event_id // empty')
    if [ ! -z "$event_id" ]; then
        echo "✅ Event created successfully with ID: $event_id"
    else
        echo "❌ Event creation failed"
    fi
    echo ""
    echo "=========================================="
    echo ""
}

# Test 1: Fire Alarm System Event
echo "1. Testing Fire Alarm System Event"
fire_event='{
  "name": "Smoke Detector Activation - Test",
  "buildingId": 1,
  "floorId": 2,
  "systemCode": "fire",
  "eventType": "alarm",
  "datetime": "2024-01-25T14:30:00Z",
  "description": "Test smoke detector activation in server room",
  "sourceEventCode": "FD-TEST-001",
  "sourceState": "alarm",
  "state": "new",
  "severity": "critical",
  "data": {
    "panelCode": "FP-TEST-002",
    "panelName": "Test Fire Panel Floor 2",
    "zone": "Server Room Test Zone",
    "loop": "Loop 2",
    "nodeId": 215,
    "nodeCode": "FD-TEST-215",
    "address": "Address 15",
    "alarmType": "smoke",
    "sensorStatus": "alarm"
  }
}'

test_event "Fire Alarm System Event" "$fire_event"

# Test 2: Access Control System Event
echo "2. Testing Access Control System Event"
access_event='{
  "name": "Access Granted - Test User",
  "buildingId": 1,
  "floorId": 1,
  "systemCode": "access",
  "eventType": "accessGranted",
  "datetime": "2024-01-25T08:15:00Z",
  "description": "Test employee access granted at main entrance",
  "sourceEventCode": "AC-TEST-001",
  "sourceState": "granted",
  "state": "new",
  "severity": "low",
  "data": {
    "cardNumber": "TEST987654321",
    "cardHolderName": "Test User Jane Smith",
    "employeeCode": "TESTMP002",
    "accessType": "card",
    "accessResult": "granted",
    "accessPoint": "Test Main Entrance Door",
    "userCode": "TESTUSER"
  }
}'

test_event "Access Control System Event" "$access_event"

# Test 3: CCTV Surveillance System Event
echo "3. Testing CCTV Surveillance System Event"
cctv_event='{
  "name": "Motion Detection - Test Camera",
  "buildingId": 1,
  "floorId": 1,
  "systemCode": "cctv",
  "eventType": "motion",
  "datetime": "2024-01-25T22:45:00Z",
  "description": "Test motion detected in restricted area after hours",
  "sourceEventCode": "CAM-TEST-105",
  "sourceState": "motion_detected",
  "state": "new",
  "severity": "high",
  "data": {
    "cameraId": 105,
    "cameraCode": "CAM-TEST-105",
    "cameraName": "Test Warehouse Camera 5",
    "location": "Test Warehouse - Section C",
    "ip": "*************",
    "channel": 5,
    "analyticType": "motion",
    "count": 1,
    "vendor": "Test Dahua",
    "model": "IPC-HFW4431R-Z"
  }
}'

test_event "CCTV Surveillance System Event" "$cctv_event"

# Test 4: Gate Barrier System Event
echo "4. Testing Gate Barrier System Event"
gate_event='{
  "name": "Gate Barrier Test Malfunction",
  "buildingId": 1,
  "floorId": 1,
  "systemCode": "gate",
  "eventType": "fault",
  "datetime": "2024-01-25T16:20:00Z",
  "description": "Test main gate barrier failed to close properly",
  "sourceEventCode": "GATE-TEST-001",
  "sourceState": "fault",
  "state": "new",
  "severity": "high",
  "data": {}
}'

test_event "Gate Barrier System Event" "$gate_event"

# Test 5: Public Address System Event
echo "5. Testing Public Address System Event"
pa_event='{
  "name": "Test Emergency Announcement",
  "buildingId": 1,
  "floorId": 2,
  "systemCode": "pa",
  "eventType": "announcementStarted",
  "datetime": "2024-01-25T11:00:00Z",
  "description": "Test emergency evacuation announcement broadcast",
  "sourceEventCode": "PA-TEST-ZONE-A2",
  "sourceState": "broadcasting",
  "state": "new",
  "severity": "critical",
  "data": {
    "zoneId": 2,
    "zoneCode": "TEST-ZONE-A2",
    "zoneName": "Test South Wing Floor 2",
    "location": "Test Building A - South Wing - Floor 2",
    "volume": 85,
    "announcementId": "TEST-EMRG-001",
    "script": "TEST: Attention all personnel. Please evacuate the building immediately via the nearest exit.",
    "durationSec": 45
  }
}'

test_event "Public Address System Event" "$pa_event"

# Test 6: Presence Sensors System Event
echo "6. Testing Presence Sensors System Event"
presence_event='{
  "name": "Test Occupancy Detected",
  "buildingId": 1,
  "floorId": 3,
  "systemCode": "presence",
  "eventType": "presenceDetected",
  "datetime": "2024-01-25T09:30:00Z",
  "description": "Test multiple people detected in conference room",
  "sourceEventCode": "PRES-TEST-301",
  "sourceState": "occupied",
  "state": "new",
  "severity": "low",
  "data": {
    "sensorId": 301,
    "sensorCode": "PRES-TEST-301",
    "sensorName": "Test Conference Room C Sensor",
    "sensorType": "pir",
    "count": 8,
    "occupancy": true,
    "location": "Test Floor 3 - Conference Room C",
    "vendor": "Test Honeywell",
    "model": "PIR-HC-SR501"
  }
}'

test_event "Presence Sensors System Event" "$presence_event"

# Test 7: Event without data field (minimal test)
echo "7. Testing Minimal Event (no data field)"
minimal_event='{
  "name": "Minimal Test Event",
  "buildingId": 1,
  "floorId": 1,
  "systemCode": "fire",
  "eventType": "other",
  "datetime": "2024-01-25T12:00:00Z",
  "description": "Minimal test event without data field",
  "state": "new",
  "severity": "low"
}'

test_event "Minimal Event (no data field)" "$minimal_event"

echo "=== Testing Complete ==="
echo ""
echo "To verify the created events, run:"
echo "curl -X GET \"http://localhost:8088/api/v1/events?limit=10\" -H \"$CONTENT_TYPE\" | jq '.'"
