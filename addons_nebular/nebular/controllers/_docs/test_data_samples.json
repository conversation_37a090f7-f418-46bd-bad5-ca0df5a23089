{"fire_events": [{"name": "Smoke Detector Activation", "buildingId": 1, "floorId": 2, "zoneId": 1, "systemCode": "fire", "datetime": "2024-01-25T14:30:00Z", "description": "Smoke detector activated in server room", "sourceEventCode": "1758114521835", "sourceState": "fire on ", "state": "new", "severity": "low", "message": "Go to the ground floor ", "data": {"panelCode": "FP-002", "panelName": "Floor 2 Fire Panel", "zone": "Server Room Zone", "loop": "Loop 2", "nodeId": 215, "nodeCode": "FD-215", "address": "Address 15", "alarmType": "smoke", "sensorStatus": "alarm"}}, {"name": "Heat Detector Fault", "buildingId": 1, "floorId": 1, "systemCode": "fire", "eventType": "maintenance", "datetime": "2024-01-25T10:15:00Z", "description": "Heat detector showing fault condition", "sourceEventCode": "FD-101-08", "sourceState": "fault", "state": "new", "severity": "medium", "data": {"panelCode": "FP-001", "panelName": "Main Fire Panel", "zone": "Kitchen Zone", "loop": "Loop 1", "nodeId": 108, "nodeCode": "FD-108", "address": "Address 08", "alarmType": "heat", "sensorStatus": "fault"}}], "access_events": [{"name": "Access Granted", "buildingId": 1, "floorId": 1, "systemCode": "access", "eventType": "access", "datetime": "2024-01-25T08:15:00Z", "description": "Employee access granted at main entrance", "sourceEventCode": "AC-001", "sourceState": "granted", "state": "new", "severity": "low", "data": {"cardNumber": "987654321", "cardHolderName": "<PERSON>", "employeeCode": "EMP002", "accessType": "card", "accessResult": "granted", "accessPoint": "Main Entrance Door", "userCode": "JSMITH"}}, {"name": "Access Denied - Invalid Card", "buildingId": 1, "floorId": 1, "systemCode": "access", "eventType": "access", "datetime": "2024-01-25T18:45:00Z", "description": "Access denied due to invalid card", "sourceEventCode": "AC-002", "sourceState": "denied", "state": "new", "severity": "medium", "data": {"cardNumber": "111111111", "cardHolderName": "Unknown", "employeeCode": "", "accessType": "card", "accessResult": "denied", "accessPoint": "Side Entrance Door", "userCode": ""}}], "cctv_events": [{"name": "Motion Detection", "buildingId": 1, "floorId": 1, "systemCode": "cctv", "eventType": "cctv", "datetime": "2024-01-25T22:45:00Z", "description": "Motion detected in restricted area after hours", "sourceEventCode": "CAM-105", "sourceState": "motion_detected", "state": "new", "severity": "high", "data": {"cameraId": 105, "cameraCode": "CAM-105", "cameraName": "Warehouse Camera 5", "location": "Warehouse - Section C", "ip": "*************", "channel": 5, "analyticType": "motion", "count": 1, "vendor": "Dahua", "model": "IPC-HFW4431R-Z"}}, {"name": "People Counting <PERSON><PERSON>", "buildingId": 1, "floorId": 2, "systemCode": "cctv", "eventType": "presence", "datetime": "2024-01-25T14:20:00Z", "description": "High occupancy detected in conference room", "sourceEventCode": "CAM-201", "sourceState": "high_occupancy", "state": "new", "severity": "medium", "data": {"cameraId": 201, "cameraCode": "CAM-201", "cameraName": "Conference Room Camera", "location": "Floor 2 - Conference Room A", "ip": "*************", "channel": 1, "analyticType": "peopleCount", "count": 15, "vendor": "Hikvision", "model": "DS-2CD2385FWD-I"}}], "gate_events": [{"name": "Gate Barrier Malfunction", "buildingId": 1, "floorId": 1, "systemCode": "gate", "eventType": "gate", "datetime": "2024-01-25T16:20:00Z", "description": "Main gate barrier failed to close properly", "sourceEventCode": "GATE-001", "sourceState": "fault", "state": "new", "severity": "high", "data": {}}, {"name": "Unauthorized Vehicle Access", "buildingId": 1, "floorId": 1, "systemCode": "gate", "eventType": "access", "datetime": "2024-01-25T19:30:00Z", "description": "Vehicle attempted access without valid credentials", "sourceEventCode": "GATE-002", "sourceState": "access_denied", "state": "new", "severity": "high", "data": {}}], "pa_events": [{"name": "Emergency Announcement", "buildingId": 1, "floorId": 2, "systemCode": "pa", "eventType": "pa", "datetime": "2024-01-25T11:00:00Z", "description": "Emergency evacuation announcement broadcast", "sourceEventCode": "PA-ZONE-A2", "sourceState": "broadcasting", "state": "new", "severity": "critical", "data": {"zoneId": 2, "zoneCode": "ZONE-A2", "zoneName": "South Wing Floor 2", "location": "Building A - South Wing - Floor 2", "volume": 85, "announcementId": "EMRG-001", "script": "Attention all personnel. Please evacuate the building immediately via the nearest exit.", "durationSec": 45}}, {"name": "PA System Test", "buildingId": 1, "floorId": 1, "systemCode": "pa", "eventType": "maintenance", "datetime": "2024-01-25T09:00:00Z", "description": "Weekly PA system test completed", "sourceEventCode": "PA-TEST-001", "sourceState": "test_complete", "state": "closed", "severity": "low", "data": {"zoneId": 1, "zoneCode": "ZONE-A1", "zoneName": "North Wing Floor 1", "location": "Building A - North Wing - Floor 1", "volume": 50, "announcementId": "TEST-001", "script": "This is a test of the public address system.", "durationSec": 10}}], "presence_events": [{"name": "Occupancy Detected", "buildingId": 1, "floorId": 3, "systemCode": "presence", "eventType": "presence", "datetime": "2024-01-25T09:30:00Z", "description": "Multiple people detected in conference room", "sourceEventCode": "PRES-301", "sourceState": "occupied", "state": "new", "severity": "low", "data": {"sensorId": 301, "sensorCode": "PRES-301", "sensorName": "Conference Room C Sensor", "sensorType": "pir", "count": 8, "occupancy": true, "location": "Floor 3 - Conference Room C", "vendor": "<PERSON><PERSON>", "model": "PIR-HC-SR501"}}, {"name": "Sensor Malfunction", "buildingId": 1, "floorId": 2, "systemCode": "presence", "eventType": "maintenance", "datetime": "2024-01-25T13:15:00Z", "description": "Presence sensor not responding", "sourceEventCode": "PRES-205", "sourceState": "fault", "state": "new", "severity": "medium", "data": {"sensorId": 205, "sensorCode": "PRES-205", "sensorName": "Hallway Sensor 5", "sensorType": "microwave", "count": 0, "occupancy": false, "location": "Floor 2 - Main Hallway", "vendor": "<PERSON><PERSON>", "model": "ISC-FPX1-W12"}}]}