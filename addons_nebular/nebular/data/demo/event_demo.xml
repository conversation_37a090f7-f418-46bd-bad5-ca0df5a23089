<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Event Demo Data - 20 Events for Different Systems -->
        
        <!-- Event 1: Fire System - Critical -->
        <record id="event_fire_alarm_001" model="nebular.event">
            <field name="name">Fire Alarm Triggered - Server Room</field>
            <field name="event_code">FIRE-001-2024</field>
            <field name="description">Smoke detector activated in server room. Immediate evacuation required.</field>
            <field name="event_type">fire</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">new</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-15 09:30:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_2"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 2: Access Control - High Priority -->
        <record id="event_access_denied_002" model="nebular.event">
            <field name="name">Unauthorized Access Attempt</field>
            <field name="event_code">ACC-002-2024</field>
            <field name="description">Multiple failed access attempts detected at main entrance.</field>
            <field name="event_type">access</field>
            <field name="severity">high</field>
            <field name="priority">3</field>
            <field name="state">acknowledged</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-15 10:15:00</field>
            <field name="acknowledged_time">2024-01-15 10:20:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <!-- Event 3: CCTV System - Medium -->
        <record id="event_cctv_motion_003" model="nebular.event">
            <field name="name">Motion Detected in Restricted Area</field>
            <field name="event_code">CCTV-003-2024</field>
            <field name="description">Unexpected motion detected in warehouse after hours.</field>
            <field name="event_type">cctv</field>
            <field name="severity">medium</field>
            <field name="priority">2</field>
            <field name="state">in_progress</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-15 22:45:00</field>
            <field name="acknowledged_time">2024-01-15 22:50:00</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="system_id" ref="system_cctv_surveillance"/>
        </record>

        <!-- Event 4: Gate Control - Critical -->
        <record id="event_gate_malfunction_004" model="nebular.event">
            <field name="name">Emergency Gate Malfunction</field>
            <field name="event_code">GATE-004-2024</field>
            <field name="description">Emergency exit gate failed to open during fire drill.</field>
            <field name="event_type">gate</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">new</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-16 14:20:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <!-- Event 5: Public Address - High -->
        <record id="event_pa_system_005" model="nebular.event">
            <field name="name">PA System Audio Failure</field>
            <field name="event_code">PA-005-2024</field>
            <field name="description">Public address system speakers not responding in Zone A.</field>
            <field name="event_type">pa</field>
            <field name="severity">high</field>
            <field name="priority">3</field>
            <field name="state">resolved</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="False"/>
            <field name="event_time">2024-01-16 08:30:00</field>
            <field name="acknowledged_time">2024-01-16 08:35:00</field>
            <field name="resolved_time">2024-01-16 09:15:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_2"/>
            <field name="system_id" ref="system_public_address"/>
        </record>

        <!-- Event 6: Presence Sensors - Low -->
        <record id="event_presence_sensor_006" model="nebular.event">
            <field name="name">Presence Sensor Calibration Required</field>
            <field name="event_code">PRES-006-2024</field>
            <field name="description">Presence sensor showing inconsistent readings in meeting room.</field>
            <field name="event_type">presence</field>
            <field name="severity">low</field>
            <field name="priority">1</field>
            <field name="state">closed</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="False"/>
            <field name="event_time">2024-01-16 11:00:00</field>
            <field name="acknowledged_time">2024-01-16 11:30:00</field>
            <field name="resolved_time">2024-01-16 12:00:00</field>
            <field name="closed_time">2024-01-16 12:05:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_2"/>
            <field name="system_id" ref="system_presence_sensors"/>
        </record>

        <!-- Event 7: System Event - Critical -->
        <record id="event_system_power_007" model="nebular.event">
            <field name="name">UPS Battery Critical Level</field>
            <field name="event_code">SYS-007-2024</field>
            <field name="description">Uninterruptible Power Supply battery level critically low.</field>
            <field name="event_type">system</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">acknowledged</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-17 03:15:00</field>
            <field name="acknowledged_time">2024-01-17 03:20:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_west_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_3"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 8: Alarm System - High -->
        <record id="event_alarm_intrusion_008" model="nebular.event">
            <field name="name">Perimeter Intrusion Detected</field>
            <field name="event_code">ALM-008-2024</field>
            <field name="description">Motion detected along building perimeter fence.</field>
            <field name="event_type">alarm</field>
            <field name="severity">high</field>
            <field name="priority">3</field>
            <field name="state">in_progress</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-17 23:30:00</field>
            <field name="acknowledged_time">2024-01-17 23:32:00</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <!-- Event 9: Maintenance - Medium -->
        <record id="event_maintenance_hvac_009" model="nebular.event">
            <field name="name">HVAC Filter Replacement Due</field>
            <field name="event_code">MAINT-009-2024</field>
            <field name="description">Air conditioning filters require replacement in office area.</field>
            <field name="event_type">maintenance</field>
            <field name="severity">medium</field>
            <field name="priority">2</field>
            <field name="state">new</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-18 07:00:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 10: Fire System - Critical -->
        <record id="event_fire_sprinkler_010" model="nebular.event">
            <field name="name">Sprinkler System Pressure Drop</field>
            <field name="event_code">FIRE-010-2024</field>
            <field name="description">Water pressure in fire sprinkler system below minimum threshold.</field>
            <field name="event_type">fire</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">new</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-18 12:45:00</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 11: Access Control - Medium -->
        <record id="event_access_card_011" model="nebular.event">
            <field name="name">Access Card Reader Offline</field>
            <field name="event_code">ACC-011-2024</field>
            <field name="description">Card reader at secondary entrance not responding.</field>
            <field name="event_type">access</field>
            <field name="severity">medium</field>
            <field name="priority">2</field>
            <field name="state">resolved</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="False"/>
            <field name="event_time">2024-01-18 16:20:00</field>
            <field name="acknowledged_time">2024-01-18 16:25:00</field>
            <field name="resolved_time">2024-01-18 17:10:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <!-- Event 12: CCTV - High -->
        <record id="event_cctv_tampering_012" model="nebular.event">
            <field name="name">Camera Tampering Detected</field>
            <field name="event_code">CCTV-012-2024</field>
            <field name="description">Security camera lens appears to be obstructed or tampered with.</field>
            <field name="event_type">cctv</field>
            <field name="severity">high</field>
            <field name="priority">3</field>
            <field name="state">acknowledged</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-19 02:15:00</field>
            <field name="acknowledged_time">2024-01-19 02:18:00</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="system_id" ref="system_cctv_surveillance"/>
        </record>

        <!-- Event 13: Gate Control - Low -->
        <record id="event_gate_sensor_013" model="nebular.event">
            <field name="name">Gate Position Sensor Drift</field>
            <field name="event_code">GATE-013-2024</field>
            <field name="description">Automatic gate position sensor showing minor calibration drift.</field>
            <field name="event_type">gate</field>
            <field name="severity">low</field>
            <field name="priority">1</field>
            <field name="state">closed</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="False"/>
            <field name="event_time">2024-01-19 10:30:00</field>
            <field name="acknowledged_time">2024-01-19 11:00:00</field>
            <field name="resolved_time">2024-01-19 11:45:00</field>
            <field name="closed_time">2024-01-19 11:50:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <!-- Event 14: Public Address - Critical -->
        <record id="event_pa_emergency_014" model="nebular.event">
            <field name="name">Emergency Broadcast System Failure</field>
            <field name="event_code">PA-014-2024</field>
            <field name="description">Emergency broadcast capability not functioning during system test.</field>
            <field name="event_type">pa</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">in_progress</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-19 14:00:00</field>
            <field name="acknowledged_time">2024-01-19 14:05:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_2"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <!-- Event 15: Presence Sensors - High -->
        <record id="event_presence_occupancy_015" model="nebular.event">
            <field name="name">Occupancy Limit Exceeded</field>
            <field name="event_code">PRES-015-2024</field>
            <field name="description">Conference room occupancy exceeds fire safety limits.</field>
            <field name="event_type">presence</field>
            <field name="severity">high</field>
            <field name="priority">3</field>
            <field name="state">resolved</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="False"/>
            <field name="event_time">2024-01-20 09:15:00</field>
            <field name="acknowledged_time">2024-01-20 09:18:00</field>
            <field name="resolved_time">2024-01-20 09:30:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_2"/>
            <field name="system_id" ref="nebular.system_cctv_surveillance"/>
        </record>

        <!-- Event 16: System Event - Medium -->
        <record id="event_system_network_016" model="nebular.event">
            <field name="name">Network Latency Spike</field>
            <field name="event_code">SYS-016-2024</field>
            <field name="description">Unusual network latency detected in building management system.</field>
            <field name="event_type">system</field>
            <field name="severity">medium</field>
            <field name="priority">2</field>
            <field name="state">acknowledged</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-20 15:45:00</field>
            <field name="acknowledged_time">2024-01-20 15:50:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_west_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_3"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 17: Alarm System - Critical -->
        <record id="event_alarm_fire_017" model="nebular.event">
            <field name="name">Fire Alarm Panel Communication Lost</field>
            <field name="event_code">ALM-017-2024</field>
            <field name="description">Lost communication with fire alarm control panel in warehouse.</field>
            <field name="event_type">alarm</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">new</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-21 06:30:00</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 18: Maintenance - High -->
        <record id="event_maintenance_elevator_018" model="nebular.event">
            <field name="name">Elevator Safety Inspection Overdue</field>
            <field name="event_code">MAINT-018-2024</field>
            <field name="description">Annual elevator safety inspection is 30 days overdue.</field>
            <field name="event_type">maintenance</field>
            <field name="severity">high</field>
            <field name="priority">3</field>
            <field name="state">new</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-21 08:00:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 19: Other System - Medium -->
        <record id="event_other_lighting_019" model="nebular.event">
            <field name="name">Emergency Lighting Circuit Fault</field>
            <field name="event_code">OTHER-019-2024</field>
            <field name="description">Emergency lighting circuit showing intermittent faults in stairwell.</field>
            <field name="event_type">other</field>
            <field name="severity">medium</field>
            <field name="priority">2</field>
            <field name="state">in_progress</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-21 13:20:00</field>
            <field name="acknowledged_time">2024-01-21 13:25:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 20: Fire System - Critical (Last Event with Sequence) -->
        <record id="event_fire_evacuation_020" model="nebular.event">
            <field name="name">Building Evacuation Required</field>
            <field name="event_code">FIRE-020-2024</field>
            <field name="description">Multiple fire detection systems activated. Full building evacuation initiated.</field>
            <field name="event_type">fire</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">new</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-21 16:45:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Event 21: Maintenance - Low -->
        <record id="event_maintenance_cleaning_021" model="nebular.event">
            <field name="name">Routine Cleaning Schedule</field>
            <field name="event_code">MAINT-021-2024</field>
            <field name="description">Weekly cleaning maintenance for office areas completed.</field>
            <field name="event_type">maintenance</field>
            <field name="severity">low</field>
            <field name="priority">1</field>
            <field name="state">closed</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="False"/>
            <field name="event_time">2024-01-22 18:00:00</field>
            <field name="acknowledged_time">2024-01-22 18:05:00</field>
            <field name="resolved_time">2024-01-22 20:30:00</field>
            <field name="closed_time">2024-01-22 20:35:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <!-- Gate Barrier System Events -->
        
        <!-- Event 22: Gate Barrier - Critical -->
        <record id="event_gate_barrier_001" model="nebular.event">
            <field name="name">Main Gate Barrier Stuck Open</field>
            <field name="event_code">GBS-001-2024</field>
            <field name="description">Main entrance gate barrier failed to close after vehicle passage. Security breach risk.</field>
            <field name="event_type">gate</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">new</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-23 08:15:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <!-- Event 23: Gate Barrier - High -->
        <record id="event_gate_barrier_002" model="nebular.event">
            <field name="name">Unauthorized Vehicle Detection</field>
            <field name="event_code">GBS-002-2024</field>
            <field name="description">Vehicle detected at visitor gate without valid access credentials.</field>
            <field name="event_type">access</field>
            <field name="severity">high</field>
            <field name="priority">3</field>
            <field name="state">acknowledged</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-23 14:30:00</field>
            <field name="acknowledged_time">2024-01-23 14:35:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <!-- Event 24: Gate Barrier - Medium -->
        <record id="event_gate_barrier_003" model="nebular.event">
            <field name="name">License Plate Reader Offline</field>
            <field name="event_code">GBS-003-2024</field>
            <field name="description">Automatic license plate recognition camera at service gate not responding.</field>
            <field name="event_type">cctv</field>
            <field name="severity">medium</field>
            <field name="priority">2</field>
            <field name="state">in_progress</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-23 16:45:00</field>
            <field name="acknowledged_time">2024-01-23 16:50:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <!-- Event 25: Gate Barrier - High -->
        <record id="event_gate_barrier_004" model="nebular.event">
            <field name="name">Vehicle Sensor Malfunction</field>
            <field name="event_code">GBS-004-2024</field>
            <field name="description">Inductive loop vehicle sensor at main gate showing intermittent failures.</field>
            <field name="event_type">gate</field>
            <field name="severity">high</field>
            <field name="priority">3</field>
            <field name="state">resolved</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="False"/>
            <field name="event_time">2024-01-24 09:20:00</field>
            <field name="acknowledged_time">2024-01-24 09:25:00</field>
            <field name="resolved_time">2024-01-24 11:15:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <!-- Event 26: Gate Barrier - Low -->
        <record id="event_gate_barrier_005" model="nebular.event">
            <field name="name">Visitor Intercom System Test</field>
            <field name="event_code">GBS-005-2024</field>
            <field name="description">Monthly test of visitor intercom system completed successfully.</field>
            <field name="event_type">maintenance</field>
            <field name="severity">low</field>
            <field name="priority">1</field>
            <field name="state">closed</field>
            <field name="is_alert" eval="False"/>
            <field name="is_active" eval="False"/>
            <field name="event_time">2024-01-24 12:00:00</field>
            <field name="acknowledged_time">2024-01-24 12:05:00</field>
            <field name="resolved_time">2024-01-24 12:30:00</field>
            <field name="closed_time">2024-01-24 12:35:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <!-- Event 27: Gate Barrier - Critical -->
        <record id="event_gate_barrier_006" model="nebular.event">
            <field name="name">Emergency Gate Override Activated</field>
            <field name="event_code">GBS-006-2024</field>
            <field name="description">Emergency override activated - all gates opened for evacuation procedure.</field>
            <field name="event_type">alarm</field>
            <field name="severity">critical</field>
            <field name="priority">4</field>
            <field name="state">acknowledged</field>
            <field name="is_alert" eval="True"/>
            <field name="is_active" eval="True"/>
            <field name="event_time">2024-01-25 15:45:00</field>
            <field name="acknowledged_time">2024-01-25 15:46:00</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

    </data>
</odoo>