<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Access Event Demo Data -->
        <!-- 
        Example structure for access events:
        <record id="access_event_demo_1" model="nebular.access.event">
            <field name="controller_id">1</field>
            <field name="controller_code">AC001</field>
            <field name="controller_name">Main Access Controller</field>
            <field name="reader_id">101</field>
            <field name="reader_code">RD001</field>
            <field name="card_id">1001</field>
            <field name="card_code">CARD001</field>
            <field name="user_id">1</field>
            <field name="user_code">USER001</field>
            <field name="door_id">DOOR001</field>
            <field name="door_name">Main Entrance</field>
            <field name="result">granted</field>
            <field name="reason">valid_card</field>
            <field name="held_open_seconds">5</field>
        </record>
        -->
    </data>
</odoo>