<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- PA (Public Address) Event Demo Data -->
        <!-- 
        Example structure for PA events:
        <record id="pa_event_demo_1" model="nebular.pa.event">
            <field name="zone_id">1</field>
            <field name="zone_code">PA001</field>
            <field name="zone_name">Main Building Zone</field>
            <field name="location">Building A - All Floors</field>
            <field name="volume">75</field>
            <field name="message">Emergency evacuation announcement</field>
            <field name="announcement_id">ANN001</field>
            <field name="script">Please evacuate the building immediately</field>
            <field name="duration_sec">30</field>
        </record>
        -->
    </data>
</odoo>