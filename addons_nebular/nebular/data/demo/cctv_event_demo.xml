<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- CCTV Event Demo Data -->
        <!-- 
        Example structure for CCTV events:
        <record id="cctv_event_demo_1" model="nebular.cctv.event">
            <field name="camera_id">1</field>
            <field name="camera_code">CAM001</field>
            <field name="camera_name">Main Entrance Camera</field>
            <field name="location">Building A - Main Entrance</field>
            <field name="vendor">Hikvision</field>
            <field name="model">DS-2CD2143G0-I</field>
            <field name="ip">*************</field>
            <field name="channel">1</field>
            <field name="analytic_type">motion_detection</field>
            <field name="count">1</field>
            <field name="face_id">FACE001</field>
            <field name="uri">rtsp://*************/stream1</field>
            <field name="snapshot_url">http://*************/snapshot.jpg</field>
            <field name="recording">True</field>
        </record>
        -->
    </data>
</odoo>