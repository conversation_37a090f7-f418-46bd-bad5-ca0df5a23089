<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Buildings -->
        <record id="building_a" model="nebular.building">
            <field name="name">Building A</field>
            <field name="code">A</field>
            <field name="description">Main headquarters building with offices, meeting rooms, and data center</field>
            <field name="address">123 Technology Drive, Innovation City, IC 12345</field>
            <field name="is_active" eval="True"/>
        </record>

        <record id="building_b" model="nebular.building">
            <field name="name">Building B</field>
            <field name="code">B</field>
            <field name="description">Primary distribution and logistics center</field>
            <field name="address">456 Logistics Boulevard, Industrial Park, IP 67890</field>
            <field name="is_active" eval="True"/>
        </record>

        <!-- Zones -->
        <record id="b_a_north_wing" model="nebular.zone">
            <field name="name">North Wing</field>
            <field name="code">NW</field>
            <field name="description">North wing office workspace area</field>
            <field name="building_id" ref="building_a"/>
            <field name="is_active" eval="True"/>
        </record>

        <record id="b_a_west_wing" model="nebular.zone">
            <field name="name">West Wing</field>
            <field name="code">WW</field>
            <field name="description">West wing data center and server infrastructure</field>
            <field name="building_id" ref="building_a"/>
            <field name="is_active" eval="True"/>
        </record>

        <record id="b_b_south_wing" model="nebular.zone">
            <field name="name">South Wing</field>
            <field name="code">SW</field>
            <field name="description">South wing storage and inventory area</field>
            <field name="building_id" ref="building_b"/>
            <field name="is_active" eval="True"/>
        </record>

        <!-- Floors -->
        <record id="b_a_z_north_wing_floor_1" model="nebular.floor">
            <field name="name">Floor1</field>
            <field name="code">F1</field>
            <field name="floor_number">1</field>
            <field name="description">Ground floor with lobby, reception, and meeting rooms</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="is_active" eval="True"/>
        </record>

        <record id="b_a_z_north_wing_floor_2" model="nebular.floor">
            <field name="name">Floor2</field>
            <field name="code">F2</field>
            <field name="floor_number">2</field>
            <field name="description">First floor with office spaces and conference rooms</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="is_active" eval="True"/>
        </record>

        <record id="b_a_z_north_wing_floor_3" model="nebular.floor">
            <field name="name">Floor3</field>
            <field name="code">F3</field>
            <field name="floor_number">3</field>
            <field name="description">Basement level with server room and utilities</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="is_active" eval="True"/>
        </record>

        <record id="b_b_z_south_wing_floor_warehouse_1" model="nebular.floor">
            <field name="name">Floor1</field>
            <field name="code">WF1</field>
            <field name="floor_number">1</field>
            <field name="description">Main warehouse floor with storage areas</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="is_active" eval="True"/>
        </record>

        <!-- Rooms -->
        <record id="room_main_lobby" model="nebular.room">
            <field name="name">Main Lobby</field>
            <field name="room_number">G-001</field>
            <field name="description">Main entrance lobby with reception desk</field>
            <field name="room_type">lobby</field>
            <field name="capacity">50</field>
            <field name="area">120.5</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="is_active" eval="True"/>
            <field name="is_occupied" eval="False"/>
        </record>

        <record id="room_conference_room_a" model="nebular.room">
            <field name="name">Conference Room A</field>
            <field name="room_number">G-101</field>
            <field name="description">Large conference room with video conferencing equipment</field>
            <field name="room_type">conference</field>
            <field name="capacity">20</field>
            <field name="area">45.2</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="is_active" eval="True"/>
            <field name="is_occupied" eval="False"/>
        </record>

        <record id="room_office_101" model="nebular.room">
            <field name="name">Office 101</field>
            <field name="room_number">1-101</field>
            <field name="description">Individual office space</field>
            <field name="room_type">office</field>
            <field name="capacity">2</field>
            <field name="area">15.8</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_2"/>
            <field name="is_active" eval="True"/>
            <field name="is_occupied" eval="True"/>
        </record>

        <record id="room_main_server_room" model="nebular.room">
            <field name="name">Main Server Room</field>
            <field name="room_number">B-001</field>
            <field name="description">Primary server and network equipment room</field>
            <field name="room_type">server</field>
            <field name="capacity">5</field>
            <field name="area">85.0</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_3"/>
            <field name="is_active" eval="True"/>
            <field name="is_occupied" eval="False"/>
        </record>

        <record id="room_storage_area_a1" model="nebular.room">
            <field name="name">Storage Area A1</field>
            <field name="room_number">W-A1</field>
            <field name="description">Primary storage area for inventory</field>
            <field name="room_type">storage</field>
            <field name="capacity">10</field>
            <field name="area">500.0</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="is_active" eval="True"/>
            <field name="is_occupied" eval="False"/>
        </record>

        <!-- Systems -->
        <record id="system_fire_alarm_system" model="nebular.system">
            <field name="name">Fire Alarm System</field>
            <field name="code">fire</field>
            <field name="system_type">fire_alarm</field>
            <field name="description">Building-wide fire detection and alarm system</field>
            <field name="manufacturer">SafeTech Solutions</field>
            <field name="model">FAS-3000</field>
            <field name="building_id" ref="building_a"/>
            <field name="icon_name">fa-fire</field>
            <field name="icon_color">#E87027</field>
            <field name="is_active" eval="True"/>
        </record>

        <record id="system_access_control" model="nebular.system">
            <field name="name">Access Control</field>
            <field name="code">access</field>
            <field name="system_type">access_control</field>
            <field name="description">Card-based access control for secure areas</field>
            <field name="manufacturer">SecureTech</field>
            <field name="model">ACS-2000</field>
            <field name="building_id" ref="building_a"/>
            <field name="icon_name">fa-door</field>
            <field name="icon_color">#10BCAD</field>
            <field name="is_active" eval="True"/>
        </record>

        <record id="system_cctv_surveillance" model="nebular.system">
            <field name="name">CCTV Surveillance</field>
            <field name="code">cctv</field>
            <field name="system_type">cctv</field>
            <field name="description">Digital video surveillance system</field>
            <field name="manufacturer">VisionTech</field>
            <field name="model">CCTV-Pro-500</field>
            <field name="building_id" ref="building_a"/>
            <field name="icon_name">fa-camera</field>
            <field name="icon_color">#877BD7</field>
            <field name="is_active" eval="True"/>
        </record>

        <record id="system_gate_barrier" model="nebular.system">
            <field name="name">Gate Barrier</field>
            <field name="code">gate</field>
            <field name="system_type">access_control</field>
            <field name="description">Automated gate barrier system for vehicle access control with license plate recognition</field>
            <field name="manufacturer">BarrierTech Systems</field>
            <field name="model">BTS-Gate-Pro-2024</field>
            <field name="building_id" ref="building_a"/>
            <field name="icon_name">fa-gateBarrier</field>
            <field name="icon_color">#5C9DD5</field>
            <field name="is_active" eval="True"/>
        </record>

        <record id="system_public_address" model="nebular.system">
            <field name="name">Public Address System</field>
            <field name="code">pa</field>
            <field name="system_type">pa</field>
            <field name="description">Building-wide public address and announcement system</field>
            <field name="manufacturer">AudioTech Systems</field>
            <field name="model">PA-Pro-1000</field>
            <field name="building_id" ref="building_a"/>
            <field name="icon_name">fa-volume-up</field>
            <field name="icon_color">#6f42c1</field>
            <field name="is_active" eval="True"/>
        </record>

        <record id="system_presence_sensors" model="nebular.system">
            <field name="name">Presence Detection System</field>
            <field name="code">presence</field>
            <field name="system_type">presence</field>
            <field name="description">Occupancy and presence detection system for space management</field>
            <field name="manufacturer">SensorTech Solutions</field>
            <field name="model">PDS-Smart-200</field>
            <field name="building_id" ref="building_a"/>
            <field name="icon_name">fa-user-check</field>
            <field name="icon_color">#17a2b8</field>
            <field name="is_active" eval="True"/>
        </record>

        <!-- Devices -->
        <record id="device_fire_detector_main_lobby" model="nebular.device">
            <field name="name">Fire Detector - Main Lobby</field>
            <field name="device_code">FD-ML-001</field>
            <field name="device_type">fire_detector</field>
            <field name="device_type_id" ref="device_type_fire_detector"/>
            <field name="device_model">FD-2000X</field>
            <field name="system_type">fire_alarm</field>
            <field name="manufacturer">SafeTech</field>
            <field name="serial_number">FD20001</field>
            <field name="installation_date">2024-01-15</field>
            <field name="last_maintenance">2024-08-15</field>
            <field name="next_maintenance">2025-02-15</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="room_id" ref="room_main_lobby"/>
            <field name="system_id" ref="system_fire_alarm_system"/>
        </record>

        <record id="device_motion_detector_office_101" model="nebular.device">
            <field name="name">Motion Detector - Office 101</field>
            <field name="device_code">MD-O101-001</field>
            <field name="device_type">motion_detector</field>
            <field name="device_type_id" ref="device_type_motion_detector"/>
            <field name="device_model">MD-500</field>
            <field name="system_type">access_control</field>
            <field name="manufacturer">SecureTech</field>
            <field name="serial_number">MD50001</field>
            <field name="installation_date">2024-01-20</field>
            <field name="last_maintenance">2024-07-20</field>
            <field name="next_maintenance">2025-01-20</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_2"/>
            <field name="room_id" ref="room_office_101"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <record id="device_camera_conference_room_a" model="nebular.device">
            <field name="name">Security Camera - Conference Room A</field>
            <field name="device_code">CAM-CRA-001</field>
            <field name="device_type">camera</field>
            <field name="device_type_id" ref="device_type_cctv_camera"/>
            <field name="device_model">CAM-HD1080</field>
            <field name="system_type">security</field>
            <field name="manufacturer">VisionTech</field>
            <field name="serial_number">CAM10001</field>
            <field name="installation_date">2024-02-01</field>
            <field name="last_maintenance">2024-08-01</field>
            <field name="next_maintenance">2025-02-01</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="room_id" ref="room_conference_room_a"/>
            <field name="system_id" ref="system_cctv_surveillance"/>
        </record>

        <record id="device_temperature_sensor_server_room" model="nebular.device">
            <field name="name">Temperature Sensor - Main Server Room</field>
            <field name="device_code">TS-MSR-001</field>
            <field name="device_type">temperature_sensor</field>
            <field name="device_type_id" ref="device_type_temperature_sensor"/>
            <field name="device_model">TS-300</field>
            <field name="system_type">hvac</field>
            <field name="manufacturer">EnviroTech</field>
            <field name="serial_number">TS30001</field>
            <field name="installation_date">2024-01-10</field>
            <field name="last_maintenance">2024-07-10</field>
            <field name="next_maintenance">2025-01-10</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_3"/>
            <field name="room_id" ref="room_main_server_room"/>
        </record>

        <record id="device_camera_storage_area_a1" model="nebular.device">
            <field name="name">Security Camera - Storage Area A1</field>
            <field name="device_code">CAM-SA1-001</field>
            <field name="device_type">camera</field>
            <field name="device_type_id" ref="device_type_cctv_camera"/>
            <field name="device_model">CAM-HD4K</field>
            <field name="system_type">security</field>
            <field name="manufacturer">VisionTech</field>
            <field name="serial_number">CAM40001</field>
            <field name="installation_date">2024-02-15</field>
            <field name="last_maintenance">2024-08-15</field>
            <field name="next_maintenance">2025-02-15</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="room_id" ref="room_storage_area_a1"/>
            <field name="system_id" ref="system_cctv_surveillance"/>
        </record>

        <!-- Gate Barrier Doors/Entry Points -->
        <record id="door_main_entrance_gate" model="nebular.door">
            <field name="name">Main Entrance Gate</field>
            <field name="door_number">GATE-001</field>
            <field name="description">Main vehicle entrance gate with automated barrier system</field>
            <field name="door_type">automatic</field>
            <field name="access_level">restricted</field>
            <field name="is_active" eval="True"/>
            <field name="is_locked" eval="False"/>
            <field name="is_open" eval="False"/>
            <field name="is_alert" eval="False"/>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="room_id" ref="room_main_lobby"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <record id="door_visitor_entrance_gate" model="nebular.door">
            <field name="name">Visitor Entrance Gate</field>
            <field name="door_number">GATE-002</field>
            <field name="description">Visitor vehicle entrance gate with intercom system</field>
            <field name="door_type">automatic</field>
            <field name="access_level">public</field>
            <field name="is_active" eval="True"/>
            <field name="is_locked" eval="False"/>
            <field name="is_open" eval="False"/>
            <field name="is_alert" eval="False"/>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="room_id" ref="room_main_lobby"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <record id="door_service_entrance_gate" model="nebular.door">
            <field name="name">Service Entrance Gate</field>
            <field name="door_number">GATE-003</field>
            <field name="description">Service and delivery vehicle entrance gate</field>
            <field name="door_type">automatic</field>
            <field name="access_level">authorized</field>
            <field name="is_active" eval="True"/>
            <field name="is_locked" eval="True"/>
            <field name="is_open" eval="False"/>
            <field name="is_alert" eval="False"/>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="room_id" ref="room_storage_area_a1"/>
            <field name="system_id" ref="system_access_control"/>
        </record>

        <!-- Gate Barrier Devices -->
        <record id="device_gate_controller_main" model="nebular.device">
            <field name="name">Gate Controller - Main Entrance</field>
            <field name="device_code">GC-ME-001</field>
            <field name="device_type">access_control</field>
            <field name="device_type_id" ref="device_type_gate_controller"/>
            <field name="device_model">BTS-GC-Pro-2024</field>
            <field name="system_type">access_control</field>
            <field name="manufacturer">BarrierTech Systems</field>
            <field name="serial_number">GC24001</field>
            <field name="installation_date">2024-03-01</field>
            <field name="last_maintenance">2024-09-01</field>
            <field name="next_maintenance">2025-03-01</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="door_id" ref="door_main_entrance_gate"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <record id="device_barrier_arm_main" model="nebular.device">
            <field name="name">Barrier Arm - Main Entrance</field>
            <field name="device_code">BA-ME-001</field>
            <field name="device_type">access_control</field>
            <field name="device_type_id" ref="device_type_barrier_arm"/>
            <field name="device_model">BTS-BA-HD-LED</field>
            <field name="system_type">access_control</field>
            <field name="manufacturer">BarrierTech Systems</field>
            <field name="serial_number">BA24001</field>
            <field name="installation_date">2024-03-01</field>
            <field name="last_maintenance">2024-09-01</field>
            <field name="next_maintenance">2025-03-01</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="door_id" ref="door_main_entrance_gate"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <record id="device_vehicle_sensor_main_entry" model="nebular.device">
            <field name="name">Vehicle Sensor - Main Entry</field>
            <field name="device_code">VS-ME-001</field>
            <field name="device_type">sensor</field>
            <field name="device_type_id" ref="device_type_vehicle_sensor"/>
            <field name="device_model">AST-VDS-Loop-Pro</field>
            <field name="system_type">access_control</field>
            <field name="manufacturer">AutoSense Technologies</field>
            <field name="serial_number">VS24001</field>
            <field name="installation_date">2024-03-01</field>
            <field name="last_maintenance">2024-09-01</field>
            <field name="next_maintenance">2025-03-01</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="door_id" ref="door_main_entrance_gate"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <record id="device_vehicle_sensor_main_exit" model="nebular.device">
            <field name="name">Vehicle Sensor - Main Exit</field>
            <field name="device_code">VS-ME-002</field>
            <field name="device_type">sensor</field>
            <field name="device_type_id" ref="device_type_vehicle_sensor"/>
            <field name="device_model">AST-VDS-Loop-Pro</field>
            <field name="system_type">access_control</field>
            <field name="manufacturer">AutoSense Technologies</field>
            <field name="serial_number">VS24002</field>
            <field name="installation_date">2024-03-01</field>
            <field name="last_maintenance">2024-09-01</field>
            <field name="next_maintenance">2025-03-01</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="door_id" ref="door_main_entrance_gate"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <record id="device_lpr_camera_main" model="nebular.device">
            <field name="name">License Plate Camera - Main Gate</field>
            <field name="device_code">LPR-MG-001</field>
            <field name="device_type">camera</field>
            <field name="device_type_id" ref="device_type_license_plate_reader"/>
            <field name="device_model">VAS-LPR-AI-4K</field>
            <field name="system_type">security</field>
            <field name="manufacturer">VisionAI Systems</field>
            <field name="serial_number">LPR24001</field>
            <field name="installation_date">2024-03-01</field>
            <field name="last_maintenance">2024-09-01</field>
            <field name="next_maintenance">2025-03-01</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="door_id" ref="door_main_entrance_gate"/>
            <field name="system_id" ref="system_cctv_surveillance"/>
        </record>

        <record id="device_gate_intercom_visitor" model="nebular.device">
            <field name="name">Gate Intercom - Visitor Entrance</field>
            <field name="device_code">GI-VE-001</field>
            <field name="device_type">gate</field>
            <field name="device_type_id" ref="device_type_gate_intercom"/>
            <field name="device_model">CSS-GI-AV-HD</field>
            <field name="system_type">gate</field>
            <field name="manufacturer">CommSecure Solutions</field>
            <field name="serial_number">GI24001</field>
            <field name="installation_date">2024-03-01</field>
            <field name="last_maintenance">2024-09-01</field>
            <field name="next_maintenance">2025-03-01</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_a"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="door_id" ref="door_visitor_entrance_gate"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>

        <record id="device_gate_safety_sensor_service" model="nebular.device">
            <field name="name">Gate Safety Sensor - Service Entrance</field>
            <field name="device_code">GSS-SE-001</field>
            <field name="device_type">gate</field>
            <field name="device_type_id" ref="device_type_gate_safety_sensor"/>
            <field name="device_model">SGT-SS-IR-Dual</field>
            <field name="system_type">gate</field>
            <field name="manufacturer">SafeGate Technologies</field>
            <field name="serial_number">GSS24001</field>
            <field name="installation_date">2024-03-01</field>
            <field name="last_maintenance">2024-09-01</field>
            <field name="next_maintenance">2025-03-01</field>
            <field name="status">active</field>
            <field name="building_id" ref="building_b"/>
            <field name="zone_id" ref="b_b_south_wing"/>
            <field name="floor_id" ref="b_b_z_south_wing_floor_warehouse_1"/>
            <field name="door_id" ref="door_service_entrance_gate"/>
            <field name="system_id" ref="system_gate_barrier"/>
        </record>


    </data>
</odoo>