<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Fire Event Demo Data -->
        
        <!-- Main Event Record -->
        <record id="fire_event_demo_main_1" model="nebular.event">
            <field name="name">Heat Detector Fault</field>
            <field name="building_id" ref="building_a"/>
            <field name="floor_id" ref="b_a_z_north_wing_floor_1"/>
            <field name="zone_id" ref="b_a_north_wing"/>
            <field name="event_type">fire</field>
            <field name="event_time">2024-01-25 10:15:00</field>
            <field name="description">Heat detector showing fault condition</field>
            <field name="source_event_code">12345678</field>
            <field name="source_state">fault</field>
            <field name="state">new</field>
            <field name="severity">critical</field>
            <field name="message">Please go to ground floor from west wing zone</field>
        </record>
        
        <!-- Fire Event Specific Record -->
        <record id="fire_event_demo_1" model="nebular.fire.event">
            <field name="event_id" ref="fire_event_demo_main_1"/>
            <field name="panel_code">FP-001</field>
            <field name="panel_name">Main Fire Panel</field>
            <field name="zone">Kitchen Zone</field>
            <field name="loop">Loop 1</field>
            <field name="node_id">108</field>
            <field name="node_code">FD-108</field>
            <field name="address">Address 08</field>
            <field name="alarm_type">heat</field>
            <field name="sensor_status">fault</field>
        </record>
        
    </data>
</odoo>