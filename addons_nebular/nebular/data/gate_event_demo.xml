<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Gate Event Demo Data -->
        <!-- 
        Example structure for gate events:
        <record id="gate_event_demo_1" model="nebular.gate.event">
            <field name="gate_id">1</field>
            <field name="gate_code">GATE001</field>
            <field name="name">Main Vehicle Gate</field>
            <field name="location">Building A - Main Entrance</field>
            <field name="status">open</field>
            <field name="vehicle_plate">ABC123</field>
            <field name="trigger">anpr</field>
            <field name="anpr_conf">0.95</field>
        </record>
        -->
    </data>
</odoo>