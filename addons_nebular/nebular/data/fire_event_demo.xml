<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Fire Event Demo Data -->
        <!-- 
        Example structure for fire events:
        <record id="fire_event_demo_1" model="nebular.fire.event">
            <field name="panel_code">FP001</field>
            <field name="panel_name">Main Fire Panel</field>
            <field name="zone">Zone A</field>
            <field name="loop">Loop 1</field>
            <field name="node_id">101</field>
            <field name="node_code">NODE001</field>
            <field name="address">Building A - Floor 1</field>
        </record>
        -->
    </data>
</odoo>