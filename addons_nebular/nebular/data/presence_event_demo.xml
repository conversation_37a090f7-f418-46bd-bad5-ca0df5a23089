<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Presence Event Demo Data -->
        <!-- 
        Example structure for presence events:
        <record id="presence_event_demo_1" model="nebular.presence.event">
            <field name="sensor_id">1</field>
            <field name="sensor_code">PRES001</field>
            <field name="sensor_name">Main Lobby Sensor</field>
            <field name="sensor_location">Building A - Main Lobby</field>
            <field name="vendor">Bosch</field>
            <field name="model">ISC-BPR2-W12</field>
            <field name="type">pir</field>
            <field name="count">1</field>
            <field name="occupancy">True</field>
        </record>
        -->
    </data>
</odoo>