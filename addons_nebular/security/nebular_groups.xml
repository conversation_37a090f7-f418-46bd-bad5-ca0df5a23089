<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Base Nebular Category -->
        <record id="module_category_nebular" model="ir.module.category">
            <field name="name">Nebular Dashboard</field>
            <field name="description">Nebular Dashboard System Access Rights</field>
            <field name="sequence">20</field>
        </record>
        
        <!-- Nebular User Group -->
        <record id="nebular_group_user" model="res.groups">
            <field name="name">Nebular User</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Basic access to Nebular Dashboard - can view buildings, zones, floors, rooms, doors, and devices</field>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <!-- Nebular Operator Group -->
        <record id="nebular_group_operator" model="res.groups">
            <field name="name">Nebular Operator</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Operator access to Nebular Dashboard - can manage events, alerts, and basic operations</field>
            <field name="implied_ids" eval="[(4, ref('nebular_group_user'))]"/>
        </record>
        
        <!-- Nebular Security Officer Group -->
        <record id="nebular_group_security" model="res.groups">
            <field name="name">Nebular Security Officer</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Security officer access - can manage access events, security alerts, and door controls</field>
            <field name="implied_ids" eval="[(4, ref('nebular_group_operator'))]"/>
        </record>
        
        <!-- Nebular Fire Safety Officer Group -->
        <record id="nebular_group_fire_safety" model="res.groups">
            <field name="name">Nebular Fire Safety Officer</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Fire safety officer access - can manage fire events, fire alerts, and emergency protocols</field>
            <field name="implied_ids" eval="[(4, ref('nebular_group_operator'))]"/>
        </record>
        
        <!-- Nebular Maintenance Group -->
        <record id="nebular_group_maintenance" model="res.groups">
            <field name="name">Nebular Maintenance</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Maintenance access - can manage devices, system metrics, and maintenance alerts</field>
            <field name="implied_ids" eval="[(4, ref('nebular_group_operator'))]"/>
        </record>
        
        <!-- Nebular Manager Group -->
        <record id="nebular_group_manager" model="res.groups">
            <field name="name">Nebular Manager</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Manager access to Nebular Dashboard - can create/edit buildings, zones, floors, rooms, and manage all operations</field>
            <field name="implied_ids" eval="[(4, ref('nebular_group_security')), (4, ref('nebular_group_fire_safety')), (4, ref('nebular_group_maintenance'))]"/>
        </record>
        
        <!-- Nebular Administrator Group -->
        <record id="nebular_group_admin" model="res.groups">
            <field name="name">Nebular Administrator</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Full administrative access to Nebular Dashboard - can manage all aspects including system configuration</field>
            <field name="implied_ids" eval="[(4, ref('nebular_group_manager'))]"/>
        </record>
        
        <!-- System Integration Group -->
        <record id="nebular_group_integration" model="res.groups">
            <field name="name">Nebular System Integration</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">System integration access - for API access and external system integration</field>
            <field name="implied_ids" eval="[(4, ref('nebular_group_user'))]"/>
        </record>
        
        <!-- Read-Only Viewer Group -->
        <record id="nebular_group_viewer" model="res.groups">
            <field name="name">Nebular Viewer</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Read-only access to Nebular Dashboard - can only view data, no modifications allowed</field>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <!-- Portal User Group for External Access -->
        <record id="nebular_group_portal" model="res.groups">
            <field name="name">Nebular Portal User</field>
            <field name="category_id" ref="module_category_nebular"/>
            <field name="comment">Portal access for external users - limited view of public areas and events</field>
            <field name="implied_ids" eval="[(4, ref('base.group_portal'))]"/>
        </record>
        
    </data>
</odoo>