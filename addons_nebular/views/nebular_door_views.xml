<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Door Form View -->
    <record id="nebular_door_view_form" model="ir.ui.view">
        <field name="name">nebular.door.form</field>
        <field name="model">nebular.door</field>
        <field name="arch" type="xml">
            <form string="Door">
                <header>
                    <button name="action_open" type="object" string="Open Door" 
                            class="btn-primary" invisible="status != 'closed'"/>
                    <button name="action_close" type="object" string="Close Door" 
                            class="btn-secondary" invisible="status != 'open'"/>
                    <button name="action_lock" type="object" string="Lock Door" 
                            class="btn-warning" invisible="status == 'locked'"/>
                    <button name="action_unlock" type="object" string="Unlock Door" 
                            class="btn-success" invisible="status != 'locked'"/>
                    <field name="status" widget="statusbar" statusbar_visible="closed,open,locked"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_events" type="object" class="oe_stat_button" icon="fa-bell">
                            <field name="event_count" widget="statinfo" string="Events"/>
                        </button>
                        <button name="action_view_access_logs" type="object" class="oe_stat_button" icon="fa-history">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="access_count"/>
                                </span>
                                <span class="o_stat_text">Access Logs</span>
                            </div>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                            invisible="active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Door Name"/>
                        </h1>
                        <h3>
                            <field name="display_name" readonly="1"/>
                        </h3>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="building_id" required="1"/>
                            <field name="floor_id" domain="[('building_id', '=', building_id)]"/>
                            <field name="room_id" domain="[('floor_id', '=', floor_id)]"/>
                            <field name="door_type"/>
                            <field name="door_number"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group name="status_info">
                            <field name="is_locked"/>
                            <field name="is_emergency_exit"/>
                            <field name="is_fire_door"/>
                            <field name="access_level"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="coordinates">
                            <field name="x_coordinate"/>
                            <field name="y_coordinate"/>
                            <field name="width"/>
                            <field name="height"/>
                        </group>
                        <group name="activity">
                            <field name="last_activity"/>
                            <field name="last_opened_by"/>
                            <field name="last_opened_at"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Door description..."/>
                    </group>
                    
                    <notebook>
                        <page string="Access Control" name="access_control">
                            <group>
                                <group name="access_settings">
                                    <field name="requires_card_access"/>
                                    <field name="requires_biometric"/>
                                    <field name="requires_pin"/>
                                    <field name="auto_lock_timeout"/>
                                </group>
                                <group name="access_restrictions">
                                    <field name="access_start_time" widget="float_time"/>
                                    <field name="access_end_time" widget="float_time"/>
                                    <field name="weekend_access"/>
                                    <field name="holiday_access"/>
                                </group>
                            </group>
                        </page>
                        <page string="Recent Events" name="events">
                            <field name="event_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="severity"/>
                                    <field name="timestamp"/>
                                    <field name="status"/>
                                    <field name="user_id"/>
                                </list>
                            </field>
                        </page>
                        <page string="Technical Details" name="technical">
                            <group>
                                <group name="hardware">
                                    <field name="manufacturer"/>
                                    <field name="model"/>
                                    <field name="serial_number"/>
                                    <field name="firmware_version"/>
                                </group>
                                <group name="connectivity">
                                    <field name="ip_address"/>
                                    <field name="mac_address"/>
                                    <field name="connection_type"/>
                                    <field name="signal_strength"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Door List View -->
    <record id="nebular_door_view_list" model="ir.ui.view">
        <field name="name">nebular.door.list</field>
        <field name="model">nebular.door</field>
        <field name="arch" type="xml">
            <list string="Doors" default_order="building_id, floor_id, room_id, door_number">
                <field name="name"/>
                <field name="door_number"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <field name="door_type"/>
                <field name="status" widget="badge" 
                       decoration-success="status == 'closed'"
                       decoration-warning="status == 'open'"
                       decoration-danger="status == 'locked'"/>
                <field name="access_level"/>
                <field name="is_locked" widget="boolean_toggle"/>
                <field name="is_emergency_exit" widget="boolean_toggle"/>
                <field name="is_fire_door" widget="boolean_toggle"/>
                <field name="last_activity"/>
                <field name="last_opened_by"/>
                <field name="event_count"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Door Kanban View -->
    <record id="nebular_door_view_kanban" model="ir.ui.view">
        <field name="name">nebular.door.kanban</field>
        <field name="model">nebular.door</field>
        <field name="arch" type="xml">
            <kanban default_group_by="door_type" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="door_number"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <field name="door_type"/>
                <field name="status"/>
                <field name="access_level"/>
                <field name="is_locked"/>
                <field name="is_emergency_exit"/>
                <field name="is_fire_door"/>
                <field name="last_activity"/>
                <field name="last_opened_by"/>
                <field name="event_count"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="door_number"/> - <field name="building_id"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <span t-if="record.status.raw_value == 'closed'" 
                                              class="badge badge-success">Closed</span>
                                        <span t-if="record.status.raw_value == 'open'" 
                                              class="badge badge-warning">Open</span>
                                        <span t-if="record.status.raw_value == 'locked'" 
                                              class="badge badge-danger">Locked</span>
                                        <span t-if="record.status.raw_value == 'malfunction'" 
                                              class="badge badge-dark">Malfunction</span>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-map-marker"/> <field name="floor_id"/> - <field name="room_id"/>
                                    </div>
                                    <div>
                                        <i class="fa fa-shield"/> Access: <field name="access_level"/>
                                    </div>
                                    <div class="o_kanban_tags_section">
                                        <span t-if="record.is_emergency_exit.raw_value" 
                                              class="badge badge-info">Emergency Exit</span>
                                        <span t-if="record.is_fire_door.raw_value" 
                                              class="badge badge-warning">Fire Door</span>
                                        <span t-if="record.is_locked.raw_value" 
                                              class="badge badge-secondary">Locked</span>
                                    </div>
                                    <div t-if="record.last_activity.raw_value">
                                        <i class="fa fa-clock-o"/> Last: <field name="last_activity"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="o_kanban_inline_block">
                                            <i class="fa fa-bell" title="Events"/> 
                                            <field name="event_count"/>
                                        </span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-if="record.last_opened_by.raw_value">
                                            <i class="fa fa-user"/> <field name="last_opened_by"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Door Search View -->
    <record id="nebular_door_view_search" model="ir.ui.view">
        <field name="name">nebular.door.search</field>
        <field name="model">nebular.door</field>
        <field name="arch" type="xml">
            <search string="Doors">
                <field name="name" string="Door" 
                       filter_domain="['|', ('name', 'ilike', self), ('door_number', 'ilike', self)]"/>
                <field name="door_number"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <field name="door_type"/>
                <field name="access_level"/>
                <field name="last_opened_by"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Closed" name="closed" domain="[('status', '=', 'closed')]"/>
                <filter string="Open" name="open" domain="[('status', '=', 'open')]"/>
                <filter string="Locked" name="locked" domain="[('status', '=', 'locked')]"/>
                <filter string="Malfunction" name="malfunction" domain="[('status', '=', 'malfunction')]"/>
                
                <separator/>
                <filter string="Entry Door" name="entry" domain="[('door_type', '=', 'entry')]"/>
                <filter string="Interior Door" name="interior" domain="[('door_type', '=', 'interior')]"/>
                <filter string="Emergency Exit" name="emergency" domain="[('door_type', '=', 'emergency')]"/>
                <filter string="Fire Door" name="fire" domain="[('door_type', '=', 'fire')]"/>
                <filter string="Security Door" name="security" domain="[('door_type', '=', 'security')]"/>
                <filter string="Automatic Door" name="automatic" domain="[('door_type', '=', 'automatic')]"/>
                
                <separator/>
                <filter string="Public Access" name="public" domain="[('access_level', '=', 'public')]"/>
                <filter string="Restricted Access" name="restricted" domain="[('access_level', '=', 'restricted')]"/>
                <filter string="Private Access" name="private" domain="[('access_level', '=', 'private')]"/>
                <filter string="Secure Access" name="secure" domain="[('access_level', '=', 'secure')]"/>
                <filter string="Critical Access" name="critical" domain="[('access_level', '=', 'critical')]"/>
                
                <separator/>
                <filter string="Currently Locked" name="currently_locked" 
                        domain="[('is_locked', '=', True)]"/>
                <filter string="Emergency Exits" name="emergency_exits" 
                        domain="[('is_emergency_exit', '=', True)]"/>
                <filter string="Fire Doors" name="fire_doors" 
                        domain="[('is_fire_door', '=', True)]"/>
                
                <separator/>
                <filter string="Card Access Required" name="card_access" 
                        domain="[('requires_card_access', '=', True)]"/>
                <filter string="Biometric Required" name="biometric" 
                        domain="[('requires_biometric', '=', True)]"/>
                <filter string="PIN Required" name="pin_required" 
                        domain="[('requires_pin', '=', True)]"/>
                
                <separator/>
                <filter string="Recent Activity" name="recent_activity" 
                        domain="[('last_activity', '>=', (context_today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                <filter string="Has Events" name="has_events" domain="[('event_count', '>', 0)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Building" name="group_building" 
                            context="{'group_by': 'building_id'}"/>
                    <filter string="Floor" name="group_floor" 
                            context="{'group_by': 'floor_id'}"/>
                    <filter string="Room" name="group_room" 
                            context="{'group_by': 'room_id'}"/>
                    <filter string="Door Type" name="group_door_type" 
                            context="{'group_by': 'door_type'}"/>
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'status'}"/>
                    <filter string="Access Level" name="group_access_level" 
                            context="{'group_by': 'access_level'}"/>
                    <filter string="Last Activity" name="group_last_activity" 
                            context="{'group_by': 'last_activity:day'}"/>
                    <filter string="Creation Date" name="group_create_date" 
                            context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Door Action -->
    <record id="nebular_door_action" model="ir.actions.act_window">
        <field name="name">Doors</field>
        <field name="res_model">nebular.door</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first door!
            </p>
            <p>
                Doors control access between spaces and can be monitored
                for security and safety purposes.
            </p>
        </field>
    </record>
</odoo>