<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Configuration Form View -->
    <record id="nebular_config_view_form" model="ir.ui.view">
        <field name="name">nebular.config.form</field>
        <field name="model">nebular.config</field>
        <field name="arch" type="xml">
            <form string="Nebular Configuration">
                <header>
                    <button name="action_apply_config" type="object" string="Apply Configuration" 
                            class="btn-primary"/>
                    <button name="action_reset_defaults" type="object" string="Reset to Defaults" 
                            class="btn-secondary"/>
                    <button name="action_backup_config" type="object" string="Backup Configuration" 
                            class="btn-info"/>
                    <button name="action_restore_config" type="object" string="Restore Configuration" 
                            class="btn-warning"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="config_type"/>
                            <field name="active"/>
                            <field name="is_default"/>
                        </group>
                        <group name="version_info">
                            <field name="version"/>
                            <field name="last_applied"/>
                            <field name="applied_by"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Configuration description..."/>
                    </group>
                    
                    <notebook>
                        <page string="General Settings" name="general" invisible="config_type != 'general'">
                            <group>
                                <group name="system_general">
                                    <field name="system_name"/>
                                    <field name="system_timezone"/>
                                    <field name="system_language"/>
                                    <field name="date_format"/>
                                    <field name="time_format"/>
                                </group>
                                <group name="logging_general">
                                    <field name="log_level"/>
                                    <field name="log_retention_days"/>
                                    <field name="enable_audit_log"/>
                                    <field name="enable_debug_mode"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Dashboard Settings" name="dashboard" invisible="config_type != 'dashboard'">
                            <group>
                                <group name="dashboard_display">
                                    <field name="dashboard_refresh_interval"/>
                                    <field name="dashboard_theme"/>
                                    <field name="enable_real_time_updates"/>
                                    <field name="max_dashboard_widgets"/>
                                </group>
                                <group name="dashboard_data">
                                    <field name="default_chart_type"/>
                                    <field name="data_aggregation_interval"/>
                                    <field name="enable_data_export"/>
                                    <field name="max_data_points"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Security Settings" name="security" invisible="config_type != 'security'">
                            <group>
                                <group name="authentication">
                                    <field name="session_timeout"/>
                                    <field name="password_policy"/>
                                    <field name="enable_two_factor"/>
                                    <field name="max_login_attempts"/>
                                </group>
                                <group name="access_control">
                                    <field name="enable_ip_whitelist"/>
                                    <field name="allowed_ip_ranges"/>
                                    <field name="enable_ssl_only"/>
                                    <field name="security_headers"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Alert Settings" name="alert" invisible="config_type != 'alert'">
                            <group>
                                <group name="alert_general">
                                    <field name="alert_retention_days"/>
                                    <field name="enable_alert_grouping"/>
                                    <field name="alert_escalation_timeout"/>
                                    <field name="max_escalation_level"/>
                                </group>
                                <group name="notifications">
                                    <field name="enable_email_alerts"/>
                                    <field name="enable_sms_alerts"/>
                                    <field name="enable_push_notifications"/>
                                    <field name="notification_throttle_minutes"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="System Monitoring" name="system_monitoring" invisible="config_type != 'system_monitoring'">
                            <group>
                                <group name="monitoring_general">
                                    <field name="metric_collection_interval"/>
                                    <field name="metric_retention_days"/>
                                    <field name="enable_predictive_analytics"/>
                                    <field name="anomaly_detection_sensitivity"/>
                                </group>
                                <group name="performance">
                                    <field name="performance_monitoring_enabled"/>
                                    <field name="resource_usage_alerts"/>
                                    <field name="health_check_interval"/>
                                    <field name="system_maintenance_window"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Integration Settings" name="integration" invisible="config_type != 'integration'">
                            <group>
                                <group name="api_settings">
                                    <field name="api_rate_limit"/>
                                    <field name="api_timeout"/>
                                    <field name="enable_api_versioning"/>
                                    <field name="api_documentation_url"/>
                                </group>
                                <group name="external_systems">
                                    <field name="enable_webhook_notifications"/>
                                    <field name="webhook_retry_attempts"/>
                                    <field name="external_system_timeout"/>
                                    <field name="data_sync_interval"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Device Integration" name="device_integration" invisible="config_type != 'device_integration'">
                            <group>
                                <group name="device_general">
                                    <field name="device_discovery_enabled"/>
                                    <field name="device_auto_registration"/>
                                    <field name="device_heartbeat_interval"/>
                                    <field name="device_offline_timeout"/>
                                </group>
                                <group name="communication">
                                    <field name="default_communication_protocol"/>
                                    <field name="enable_device_encryption"/>
                                    <field name="device_authentication_method"/>
                                    <field name="max_concurrent_connections"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Backup &amp; Maintenance" name="backup_maintenance" invisible="config_type != 'backup_maintenance'">
                            <group>
                                <group name="backup_settings">
                                    <field name="backup_enabled"/>
                                    <field name="backup_schedule"/>
                                    <field name="backup_retention_days"/>
                                    <field name="backup_location"/>
                                </group>
                                <group name="maintenance_settings">
                                    <field name="maintenance_mode_enabled"/>
                                    <field name="maintenance_schedule"/>
                                    <field name="auto_update_enabled"/>
                                    <field name="update_check_interval"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Configuration Data" name="config_data">
                            <group>
                                <field name="config_data" widget="ace" options="{'mode': 'json'}"/>
                            </group>
                        </page>
                        
                        <page string="Metadata" name="metadata">
                            <group>
                                <field name="metadata" widget="ace" options="{'mode': 'json'}"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Configuration List View -->
    <record id="nebular_config_view_list" model="ir.ui.view">
        <field name="name">nebular.config.list</field>
        <field name="model">nebular.config</field>
        <field name="arch" type="xml">
            <list string="Configurations" default_order="config_type, name">
                <field name="name"/>
                <field name="config_type"/>
                <field name="version"/>
                <field name="active" widget="boolean_toggle"/>
                <field name="is_default" widget="boolean_toggle"/>
                <field name="last_applied"/>
                <field name="applied_by"/>
            </list>
        </field>
    </record>

    <!-- Configuration Kanban View -->
    <record id="nebular_config_view_kanban" model="ir.ui.view">
        <field name="name">nebular.config.kanban</field>
        <field name="model">nebular.config</field>
        <field name="arch" type="xml">
            <kanban default_group_by="config_type" class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="config_type"/>
                <field name="version"/>
                <field name="active"/>
                <field name="is_default"/>
                <field name="last_applied"/>
                <field name="applied_by"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            Version <field name="version"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_record_title">
                                        <span t-if="record.is_default.raw_value" 
                                              class="badge badge-primary">Default</span>
                                        <span t-if="!record.active.raw_value" 
                                              class="badge badge-secondary">Inactive</span>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div t-if="record.last_applied.raw_value">
                                        <i class="fa fa-clock-o"/> Last Applied: <field name="last_applied"/>
                                    </div>
                                    <div t-if="record.applied_by.raw_value">
                                        <i class="fa fa-user"/> Applied by: <field name="applied_by"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Configuration Search View -->
    <record id="nebular_config_view_search" model="ir.ui.view">
        <field name="name">nebular.config.search</field>
        <field name="model">nebular.config</field>
        <field name="arch" type="xml">
            <search string="Configurations">
                <field name="name" string="Configuration" 
                       filter_domain="['|', ('name', 'ilike', self), ('description', 'ilike', self)]"/>
                <field name="config_type"/>
                <field name="version"/>
                <field name="applied_by"/>
                
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <filter string="Default" name="default" domain="[('is_default', '=', True)]"/>
                
                <separator/>
                <filter string="General" name="general" domain="[('config_type', '=', 'general')]"/>
                <filter string="Dashboard" name="dashboard" domain="[('config_type', '=', 'dashboard')]"/>
                <filter string="Security" name="security" domain="[('config_type', '=', 'security')]"/>
                <filter string="Alert" name="alert" domain="[('config_type', '=', 'alert')]"/>
                <filter string="System Monitoring" name="system_monitoring" domain="[('config_type', '=', 'system_monitoring')]"/>
                <filter string="Integration" name="integration" domain="[('config_type', '=', 'integration')]"/>
                <filter string="Device Integration" name="device_integration" domain="[('config_type', '=', 'device_integration')]"/>
                <filter string="Backup &amp; Maintenance" name="backup_maintenance" domain="[('config_type', '=', 'backup_maintenance')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Configuration Type" name="group_config_type" 
                            context="{'group_by': 'config_type'}"/>
                    <filter string="Version" name="group_version" 
                            context="{'group_by': 'version'}"/>
                    <filter string="Applied By" name="group_applied_by" 
                            context="{'group_by': 'applied_by'}"/>
                    <filter string="Last Applied" name="group_last_applied" 
                            context="{'group_by': 'last_applied:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Configuration Action -->
    <record id="nebular_config_action" model="ir.actions.act_window">
        <field name="name">System Configuration</field>
        <field name="res_model">nebular.config</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{
            'search_default_active': 1,
            'search_default_group_config_type': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first configuration!
            </p>
            <p>
                System configurations allow you to customize
                the behavior and settings of your Nebular system.
            </p>
        </field>
    </record>
</odoo>