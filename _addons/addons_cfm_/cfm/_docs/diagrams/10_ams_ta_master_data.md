
## Master Data:
    _template_model.py: Defines a template model.
    base_device_log.py: Manages device logging.
    base_timesheet.py: Handles timesheet functionalities.
    base_transaction.py: Manages transactions.
    department.py: Defines department-related functionalities.
    employee.py: Contains employee-related data and functionalities.
    policy_group.py: Manages policy groups.(user_group)
    res_user.py: Manages user-related data.
    schedule.py: Defines scheduling functionalities.
    schedule_day.py: Manages daily schedules.
    shift.py: Handles shift-related functionalities.
    shift_rule.py: Defines rules for shifts.
    shift_unit.py: Manages units of shifts.

# Operations
    punch_log.py: Handles punch log entries.
    timesheet.py: Contains core timesheet functionalities.
    timesheet_setting.py: Manages settings related to timesheets.
    timesheet_shift_unit.py: Handles shift units in timesheets.

# Class 
```mermaid
classDiagram
    class BaseAbstractModel {
        - _name = 'ams_base.abstract_model'
        - company_id : Many2One
    }
    class ActivateModel {
        - _name = 'ams_base.activate_model'
        - _inherit = 'ams_base.abstract_model'
    }
    
    class BaseCodeModel {
        - _name = 'ams_base.code_model'
        - _inherit = 'ams_base.activate_model'
    }
    
    class ShiftRule {
        - _name = 'ams_ta.shift_rule'
        - _inherit = 'ams_base.code_model'
    }
    
    BaseCodeModel <|--  ShiftRule :_inherit
    ActivateModel <|--  BaseCodeModel :_inherit
%%     BaseAbstractModel <|--  APIBaseModel :_inherit
%%    APIBaseModel <|--  BaseAccessGroup :_inherit
%%    APIBaseModel <|--  BaseAMSUser :_inherit
%%    APIBaseModel <|--  BaseDevice :_inherit
    
```