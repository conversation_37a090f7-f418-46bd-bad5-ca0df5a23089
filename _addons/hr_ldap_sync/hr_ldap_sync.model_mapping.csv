"id","attribute","create_uid/id","create_date","foreign_key_type","foreign_key_priority","foreign_key_field","foreign_key_of","is_unique_identifier","write_uid/id","write_date","ldap_attribute","model","model_priority"
"__export__.hr_ldap_sync_model_mapping_1","name","base.user_root","2018-03-23 10:12:00","","0","","","False","base.user_root","2018-03-23 10:12:58","name","res.users","1"
"__export__.hr_ldap_sync_model_mapping_2","login","base.user_root","2018-03-23 10:12:00","","0","","","True","base.user_root","2018-03-23 10:12:00","mail","res.users","0"
"__export__.hr_ldap_sync_model_mapping_4","name","base.user_root","2018-03-23 10:12:00","","0","","","True","base.user_root","2018-03-23 10:12:19","company","res.company","4"
"__export__.hr_ldap_sync_model_mapping_6","company_ids","base.user_root","2018-03-23 10:12:00","Many2*","1","name","res.company","False","base.user_root","2018-03-23 10:13:22","company","res.users","0"
"__export__.hr_ldap_sync_model_mapping_7","company_id","base.user_root","2018-03-23 10:12:00","One2*","0","name","res.company","False","base.user_root","2018-03-23 10:12:00","company","res.users","0"
"__export__.hr_ldap_sync_model_mapping_8","name","base.user_root","2018-03-23 10:12:00","","0","","","False","base.user_root","2018-03-23 10:12:00","name","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_9","identification_id","base.user_root","2018-03-23 10:12:00","","0","","","True","base.user_root","2018-03-23 10:12:00","mail","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_10","user_id","base.user_root","2018-03-23 10:12:00","One2*","0","login","res.users","False","base.user_root","2018-03-23 10:12:00","mail","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_11","address_id","base.user_root","2018-03-23 10:12:00","One2*","0","name","res.partner","False","base.user_root","2018-03-23 10:12:00","company","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_12","image","base.user_root","2018-03-23 10:12:00","","0","","","False","base.user_root","2018-03-23 10:12:00","thumbnailPhoto","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_13","ldap_id","base.user_root","2018-03-23 10:12:00","","0","","","False","base.user_root","2018-03-23 10:12:00","distinguishedName","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_14","parent_id","base.user_root","2018-03-23 10:12:00","One2*","0","ldap_id","hr.employee","False","base.user_root","2018-03-23 10:12:00","manager","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_14_1","work_location","base.user_root","2018-03-23 10:12:00","","0","","","False","base.user_root","2018-03-23 10:12:00","st","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_15","name","base.user_root","2018-03-23 10:12:00","","0","","","True","base.user_root","2018-03-23 10:12:43","department","hr.department","3"
"__export__.hr_ldap_sync_model_mapping_16","name","base.user_root","2018-03-23 10:12:00","","0","","","True","base.user_root","2018-03-23 10:12:52","title","hr.job","2"
"__export__.hr_ldap_sync_model_mapping_17","department_id","base.user_root","2018-03-23 10:12:00","One2*","0","name","hr.department","False","base.user_root","2018-03-23 10:12:00","department","hr.employee","0"
"__export__.hr_ldap_sync_model_mapping_18","job_id","base.user_root","2018-03-23 10:12:00","One2*","0","name","hr.job","False","base.user_root","2018-03-23 10:12:00","title","hr.employee","0"
