<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="kerbrose_hijri_widget.DateTimeFieldHijri" owl="1">
        <t t-if="props.readonly">
            <span t-esc="formattedValue" />
        </t>
        <t t-else="">
            <DateTimePickerHijri
                t-props="props.pickerOptions"
                date="props.value"
                inputId="props.id"
                placeholder="props.placeholder"
                onDateTimeChanged="(datetime) => this.onDateTimeChanged(datetime)"
                onInput.bind="onDatePickerInput"
                onUpdateInput.bind="onUpdateInput"
                revId="revId"
            />
        </t>
    </t>

</templates>
