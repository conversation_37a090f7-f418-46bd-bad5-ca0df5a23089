<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="DateHijriField">
        <t t-if="props.readonly">
            <span t-esc="formattedValue"/>
        </t>
        <t t-else="">
            <DateHijriPicker
                    t-props="props.pickerOptions"
                    date="date"
                    inputId="props.id"
                    placeholder="props.placeholder"
                    onDateTimeChanged="(date) => this.onDateTimeChanged(date)"
                    onInput.bind="onDatePickerInput"
                    onUpdateInput.bind="onUpdateInput"
                    revId="revId"
            />
        </t>

    </t>
</templates>