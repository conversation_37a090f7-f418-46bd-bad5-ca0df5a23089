/** @odoo-module **/

import {DateHijriPicker} from "@kerbrose_hijri_widget/date_hijri/date_hijri_picker";
import {areDatesEqual} from "@web/core/l10n/dates";
import {_lt} from "@web/core/l10n/translation";
import {registry} from "@web/core/registry";
import {useRecordObserver} from "@web/model/relational_model/utils";
import {standardFieldProps} from "@web/views/fields/standard_field_props";
import {Component, useState} from "@odoo/owl";

const {DateTime} = luxon;

export class DateHijriField extends Component {
    static template = 'DateHijriField'

    setup() {
        this.state = useState({});
        useRecordObserver((record) => {
            this.state.value = record.data[this.props.name];
        });
        this.lastSetValue = null;
        this.revId = 0;
        //onWillRender(() => this.triggerIsDirty());
    }

    get isDateTime() {
        return this.props.record.fields[this.props.name].type === "datetime";
    }

    get date() {
        //const props_value = this.props.value && this.props.value.startOf("day");
        //console.log("DateHijriField >> get date():",props_value);
        //const record_date_val = this.props.record.data[this.props.name];
        //const format_val = formatDate(date_val);
        return this.state.value;
    }

    get formattedValue() {
        // return this.isDateTime
        //     ? formatDateTime(this.props.value, { format: localization.dateFormat })
        //     : formatDate(this.props.value);
        //const date_val = this.props.record.data[this.props.name];
        if (this.state.value) {
            return this.state.value.setLocale("ar-SA-islamic-umalqura").toLocaleString(DateTime.DATE_FULL); //this.props.value;
        }
        // // const format_val = this.state.value.setLocale("ar-SA-islamic-umalqura").toLocaleString(DateTime.DATE_FULL); //this.props.value;
        // return format_val;
    }

    onDateTimeChanged(date) {
        console.log("DateHijriField >> onDateTimeChanged date:", date);
        if (!areDatesEqual(this.date || "", date)) {
            this.revId++;
            const changes = {[this.props.name]: date};
            if (this.props.update) {
                this.props.update(changes);
            } else {
                // Update props.value
                this.state.value = date;
                this.props.record.update(changes);
            }
            //this.props.update(date);
        }
    }

    onDatePickerInput(ev) {
        console.log("DateHijriField >> onDatePickerInput", ev);
        if (this.props.setDirty) {
            this.props.setDirty(ev.target.value !== this.lastSetValue);
        }
        // this.props.setDirty(ev.target.value !== this.lastSetValue);
    }

    onUpdateInput(date) {
        console.log("DateHijriField >> onUpdateInput", date);
        if (this.props.setDirty) {
            this.props.setDirty(false);
        }
        this.lastSetValue = date;

//        this.props.record.update(data.company);
//this.props.record.update({ [this.props.name]: barcode });
//        if (this.props.setDirty) {
//            this.props.setDirty(false);
//        }
    }
}

DateHijriField.template = "DateHijriField";
DateHijriField.components = {
    DateHijriPicker
};
DateHijriField.defaultProps = {
    pickerOptions: {},
};
DateHijriField.props = {
    // Components props
    ...standardFieldProps,
    pickerOptions: {type: Object, optional: true},
    placeholder: {type: String, optional: true},
};

export const date_hijri_field = {
    component: DateHijriField,
    supportedTypes: ["date"],
};
DateHijriField.displayName = _lt("Date");
DateHijriField.supportedTypes = ["date"];

registry.category("fields").add("date_hijri", date_hijri_field);