/** @odoo-module **/

import { Component, onWillRender, useState } from "@odoo/owl";
import { useDateTimePicker } from "@web/core/datetime/datetime_hook";
import { registry } from "@web/core/registry";

const { DateTime } = luxon;

export class HijriDatePicker extends Component {
    static template = "HijriDatePicker";

    setup() {
        this.state = useState({
            value: null,
        });

        // Log props for debugging
        console.log("Props:", this.props);

        // Check if required props are defined
        if (!this.props.record || !this.props.name) {
            console.error("Record or name is undefined");
            return; // Prevent further execution if required props are missing
        }

        // Initialize the DateTimePicker
        const dateTimePicker = useDateTimePicker({
            target: "root",
            get pickerProps() {
                return {
                    value: this.state.value,
                    type: "date",
                };
            },
            onChange: (value) => {
                this.state.value = value; // Update state with new date
            },
            onApply: () => {
                const hijriDate = this.convertToHijri(this.state.value);
                this.updateRecord(hijriDate); // Update the record with Hijri date
            },
        });

        // Handle rendering changes
        onWillRender(() => this.triggerIsDirty());
    }

    convertToHijri(gregorianDate) {
        // Convert the selected Gregorian date to Hijri date format
        return DateTime.fromJSDate(gregorianDate, { zone: 'utc' }).setLocale('ar-IQ').toFormat('yyyy/MM/dd');
    }

    updateRecord(hijriDate) {
        // Update the record with the Hijri date using the name from props
        this.props.record.update({ [this.props.name]: hijriDate });
    }

    triggerIsDirty(isDirty = true) {
        // Trigger dirty state for the record
        this.props.record.model.bus.trigger("FIELD_IS_DIRTY", isDirty);
    }
}

registry.category("fields").add("hijri_date", HijriDatePicker);
