/** @odoo-module **/

import {Component, onMounted, onWillUnmount, onWillUpdateProps, useExternalListener, useRef, useState} from "@odoo/owl";
import {isMobileOS} from "@web/core/browser/feature_detection";
import {formatDate, formatDateTime, parseDate, parseDateTime,} from "@web/core/l10n/dates";
import {localization} from "@web/core/l10n/localization"; // "Localization parameters not ready yet. Maybe add 'localization' to your dependencies?"
import {useAutofocus} from "@web/core/utils/hooks";
import {pick} from "@web/core/utils/objects";

const {DateTime} = luxon;

let datePickerId = 0;

const hijriMonths = {
    en: [
        'Muharram', 'Safar', 'Rabiʿ I', 'Rabiʿ II',
        'Jumada I', 'Jumada II', 'Rajab', '<PERSON>haʿban',
        'Ramadan', 'Shawwal', '<PERSON>huʾ<PERSON>-<PERSON>ʿdah', 'Dhuʾl-Hijjah'
    ],
    ar: [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الآخر',
        'جمادى الأولى', 'جمادى الآخرة', 'رجب', 'شعبان',
        'رمضان', 'شوّال', 'ذو القعدة', 'ذو الحجة'
    ]
};

const hijriDays = {
    en: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    ar: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
};

/**
 * @param {unknown} value1
 * @param {unknown} value2
 */
function areEqual(value1, value2) {
    if (value1 && value2) {
        // Only compare date values
        return Number(value1) === Number(value2);
    } else {
        return value1 === value2;
    }
}

function wrapError(fn, defaultValue) {
    return (...args) => {
        const result = [defaultValue, null];
        try {
            result[0] = fn(...args);
        } catch (_err) {
            result[1] = _err;
        }
        console.log("DateHijriPicker >> wrapErro,defaultValue", defaultValue, "result", result);
        return result;
    };
}

export class DateHijriPicker extends Component {
    hidePicker() {
        this.bootstrapTD("hide");
    }

    showPicker() {
        this.bootstrapTD("show");
    }

    getLocalization() {
        // const currentLang = localization.getCurrentLanguage().code || 'en'; // Get current language
        const currentLang = 'ar'; // Get current language

        return {
            today: currentLang === 'ar' ? "اليوم" : "Today",
            clear: currentLang === 'ar' ? "مسح" : "Clear",
            close: currentLang === 'ar' ? "إغلاق" : "Close",
            selectMonth: currentLang === 'ar' ? "اختر الشهر" : "Select Month",
            previousMonth: currentLang === 'ar' ? "الشهر السابق" : "Previous Month",
            nextMonth: currentLang === 'ar' ? "الشهر التالي" : "Next Month",
            selectYear: currentLang === 'ar' ? "اختر السنة" : "Select Year",
            previousYear: currentLang === 'ar' ? "العام السابق" : "Previous Year",
            nextYear: currentLang === 'ar' ? "العام التالي" : "Next Year",
            selectDecade: currentLang === 'ar' ? "اختر العقد" : "Select Decade",
            previousDecade: currentLang === 'ar' ? "العقد السابق" : "Previous Decade",
            nextDecade: currentLang === 'ar' ? "العقد التالي" : "Next Decade",
            previousCentury: currentLang === 'ar' ? "القرن السابق" : "Previous Century",
            nextCentury: currentLang === 'ar' ? "القرن التالي" : "Next Century",
            pickHour: currentLang === 'ar' ? "اختر الساعة" : "Pick Hour",
            incrementHour: currentLang === 'ar' ? "أضف ساعة" : "Add Hour",
            decrementHour: currentLang === 'ar' ? "أنقص ساعة" : "Subtract Hour",
            pickMinute: currentLang === 'ar' ? "اختر الدقيقة" : "Pick Minute",
            incrementMinute: currentLang === 'ar' ? "أضف دقيقة" : "Add Minute",
            decrementMinute: currentLang === 'ar' ? "أنقص دقيقة" : "Subtract Minute",
            pickSecond: currentLang === 'ar' ? "اختر الثانية" : "Pick Second",
            incrementSecond: currentLang === 'ar' ? "أضف ثانية" : "Add Second",
            decrementSecond: currentLang === 'ar' ? "أنقص ثانية" : "Subtract Second",
            toggleMeridiem: currentLang === 'ar' ? "تبديل الفترة" : "Toggle AM/PM",
            selectTime: currentLang === 'ar' ? "اختر الوقت" : "Select Time",
            selectDate: currentLang === 'ar' ? "اختر التاريخ" : "Select Date",
            dayViewHeaderFormat: {month: "long", year: "2-digit"},
            locale: currentLang === 'ar' ? 'ar-SA-islamic-umalqura' : 'en-US',
            startOfTheWeek: 0,

            // months: currentLang === 'ar' ? hijriMonths.ar : hijriMonths.en,
            // days: currentLang === 'ar' ? hijriDays.ar : hijriDays.en,
        };
    }

    setup() {
        console.log("HijriPicker >> setup , this.props:", this.props);


        this.rootRef = useRef("root");
        this.inputRef = useRef("input");
        this.hiddenInputRef = useRef("hiddenInput");
        this.state = useState({warning: false});

        this.datePickerId = `o_date_hijri_picker_${datePickerId++}`;
        this.isPickerOpen = false;
        this.isPickerChanged = false;
        /** @type {DateTime | null} */
        this.pickerDate = this.props.date ? this.props.date : DateTime.now(); // explicitly set to null if not provided // this.props.date || '';  // Initial value null if no date is provided

        console.log("HijriPicker >> setup , this.pickerDate:", this.pickerDate ,'this.props.date:', this.props.date);
        this.ignorePickerEvents = true;
        this.td = {};
        this.localeFormat = "ar-SA-islamic-umalqura";
        this.td_settings = {
            localization: this.getLocalization(),
            // defaultDate: this.pickerDate, //defaultDate: this.pickerDate || DateTime.now(),
            useCurrent: false, // Determines if the current date/time should be used as the default value when the picker is opened.
            // viewDate: this.pickerDate || DateTime.now(),
            display: {
                components: {
                    clock: false
                },
                icons: {
                    today: 'fa fa-calendar-o',
                    close: 'fa fa-calendar-times-o'
                },
                buttons: {
                    today: true,
                    clear: true,
                    close: true
                },
            },

        };



        this.initFormat();
        this.setDateAndFormat(this.props);

        // useAutofocus();
        useExternalListener(window, "click", this.onWindowClick, {capture: true});
        useExternalListener(window, "scroll", this.onWindowScroll, {capture: true});

        onMounted(this.onMounted);
        onWillUpdateProps(this.onWillUpdateProps);
        onWillUnmount(this.onWillUnmount);

    }

    onMounted() {
        this.bootstrapTD(this.props);
        this.updateInput(this.date);

        this.addPickerListener("show", () => {
            this.isPickerOpen = true;
            this.inputRef.el.select();
        });
        this.addPickerListener("change", ({date}) => {
            if (date && this.isPickerOpen) {
                const {locale} = this.getOptions();
                this.isPickerChanged = true;
                this.pickerDate = date.setLocale(locale);// momentToLuxon(date).setLocale(locale);
                this.updateInput(this.pickerDate);
            }
        });
        this.addPickerListener("hide", () => {
            this.isPickerOpen = false;
            this.onDateChange();
            this.isPickerChanged = false;
        });
        this.addPickerListener("error", () => false);
        this.ignorePickerEvents = false;
    }

    onWillUpdateProps(nextProps) {
        this.ignorePickerEvents = true;
        console.log("DateHijriPicker >> onWillUpdateProps ,this.date:", this.date, "nextProps.date:", nextProps.date);
        this.setDateAndFormat(nextProps);
        const shouldUpdate =
            this.props.revId !== nextProps.revId ||
            Object.entries(pick(nextProps, "date", "format")).some(
                ([key, val]) => !areEqual(this.props[key], val)
            );
        if (shouldUpdate && !areEqual(this.pickerDate, nextProps.date)) {
            if (nextProps.date) {
                //nextProps.date.toJSDate()
                this.bootstrapTD("date", nextProps.date);//luxonToMoment(nextProps.date)
            } else {
                this.bootstrapTD("clear");
            }
        }
        if (shouldUpdate) {
            this.updateInput(this.date);
        }
        if (this.isPickerOpen) {
            this.hidePicker();
            this.showPicker();
        }
        this.ignorePickerEvents = false;
    }

    onWillUnmount() {
        window.$(this.rootRef.el).off(); // Removes all jQuery events
        this.td.dispose();
    }

    addPickerListener(type, listener) {
        return window.$(this.rootRef.el).on(`${type}.datetimepicker`, (ev) => {
            if (this.ignorePickerEvents) {
                return false;
            }
            return listener(ev);
        });
    }

    getOptions() {
        return {
            format: this.format,
            locale: this.localeFormat,  // Use Hijri locale when necessary
        };
    }

    initFormat() {
        this.defaultFormat = localization.dateFormat;
        this.formatValue = wrapError(formatDate, "");
        this.parseValue = wrapError(parseDate,);
        this.isLocal = false;
    }

    setDateAndFormat({date, locale, format}) {
        console.log("DateHijriPicker >> setDateAndFormat({ date, locale, format })", date, locale, format);
        if (date && locale) {
            this.date = date.setLocale(locale);
        } else if (date) {
            this.date = date.setLocale(this.localeFormat);
        } else {
            this.date = '';  // If no date, set to empty string
        }
        // Fallback to default localization format in `@web/core/l10n/dates.js`.
        this.format = format || this.defaultFormat;
        this.staticFormat = "yyyy-MM-dd";  // Odoo default format
    }

    updateInput(value) {
        console.log("DateHijriPicker >> updateInput(value)", value,'this.td:',this.td);

        const options = this.getOptions();
        const [formattedValue, error] = this.formatValue(value, options);

        if (!error) {
            let input_format = value ? value.toLocaleString(DateTime.DATE_FULL) : "";

            if (this.props.isDateTime) {
                input_format = value ? value.toLocaleString({
                    weekday: 'short',
                    month: 'short',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                }) : "";
            }

            this.inputRef.el.value = input_format;
            [this.hiddenInputRef.el.value] = this.formatValue(value, {
                ...options,
                format: this.staticFormat,
            });
            this.props.onUpdateInput(formattedValue);
        }
        return formattedValue;

    }

    bootstrapTD(commandOrParams, ...commandArgs) {
        if (typeof commandOrParams === "object") {
            this.td = new tempusDominus.TempusDominus(this.rootRef.el, this.td_settings);
            const params = {
                ...commandOrParams,
                date: this.pickerDate ? this.pickerDate.toJSDate() : null, // Use pickerDate // this.date || null,
                format: this.staticFormat,//luxonToMomentFormat(this.staticFormat),// this.staticFormat,// luxonToMomentFormat(this.staticFormat),DD-MM-YYYY
                locale: commandOrParams.locale || (this.date && this.date.locale)
            };
            for (const prop in params) {
                if (params[prop] instanceof DateTime) {
                    params[prop] = params[prop].toJSDate();// luxonToMoment(params[prop]); //params[prop].toJSDate();//luxonToMoment(params[prop]);
                }
            }
            commandOrParams = params;
        }
        if (commandOrParams === "hide") {
            this.td.hide();
        }
        if (commandOrParams === "show") {
            this.td.show();
        }
        if (commandOrParams === "clear") {
            this.td.clear();
        }
    }

    onDateChange() {
        // let tdValue = this.td.value;
        //
        // // Convert to Hijri date
        // // let hijriDate = luxon.DateTime.fromJSDate(this.td.dates._dates[0], {locale: 'ar-SA-islamic-umalqura'});
        //
        // let hijriDate = luxon.DateTime.fromJSDate(this.td.value).reconfigure({
        //     outputCalendar: 'islamic',
        //     locale: 'ar'
        // });
        //
        // this.state.warning = hijriDate && hijriDate > DateTime.local();
        // let error = false;
        // if (error || areEqual(hijriDate, this.props.date)) {
        //     this.pickerDate = hijriDate;
        //     this.updateInput(hijriDate);  // Ensure input is updated with Hijri formatted date
        // }
        //
        // // Notify the Odoo system about the change
        // this.props.onUpdateInput(this.hiddenInputRef.el.value);

        let tdValue = this.td.value;
        let error = false;

        let value = luxon.DateTime.fromJSDate(this.td.dates._dates[0], {"locale": this.localeFormat});
        console.log("DateHijriPicker >> onDateChange value:", value);

        if (value.invalid) {
            console.log("DateHijriPicker >> onDateChange value.invalid:", value.invalid);
            value = '';
        }

        this.state.warning = value && value > DateTime.local();
        // let error = false;
        if (error || areEqual(this.date, value)) {
            // Force current value
            this.updateInput(value);
        } else {
            this.props.onDateTimeChanged(value);
        }

        // console.log("DateHijriPicker >> onDateChange,this.pickerDate:", this.pickerDate);
        // if (this.pickerDate) {
        //     this.inputRef.el.select();
        // }
    }

    onInputChange() {
        console.log("DateHijriPicker >> onInputChange");
        this.onDateChange();
    }

    onInputInput(ev) {
        this.isPickerChanged = false;
        return this.props.onInput(ev);
    }

    onInputKeydown(ev) {
        switch (ev.key) {
            case "Escape": {
                if (this.isPickerOpen) {
                    ev.preventDefault();
                    ev.stopPropagation();
                    this.hidePicker();
                    this.inputRef.el.select();
                }
                break;
            }
            case "Tab": {
                this.hidePicker();
                break;
            }
            case "Enter": {
                this.onInputChange();
                break;
            }
        }
    }

    onWindowClick({target}) {
        if (target.closest(".bootstrap-datetimepicker-widget") || target.closest(".tempus-dominus-widget")) {
            return;
        } else if (this.rootRef.el.contains(target)) {
            this.bootstrapTD("toggle");
        } else {
            this.hidePicker();
        }
    }

    onWindowScroll(ev) {
        if (!isMobileOS() && ev.target !== this.inputRef.el) {
            this.hidePicker();
        }
    }
}

DateHijriPicker.defaultProps = {
    calendarWeeks: true,
    buttons: {
        showClear: true,
        showClose: true,
        showToday: true
    },
    inputId: "",
    maxDate: DateTime.fromObject({year: 9999, month: 12, day: 31}),
    minDate: DateTime.fromObject({year: 1000}),
    useCurrent: false,
    widgetParent: "body",
    onInput: () => {
    },
    onUpdateInput: () => {
    },
    revId: 0,
    isDateTime: false,
};

DateHijriPicker
    .props = {
    // Components props
    onDateTimeChanged: Function,
    date: {type: [DateTime, {value: false}], optional: true},
    warn_future: {type: Boolean, optional: true},
    // Bootstrap datepicker options
    buttons: {
        type: Object,
        shape: {
            showClear: Boolean,
            showClose: Boolean,
            showToday: Boolean,
        },

        optional: true,
    },
    calendarWeeks: {type: Boolean, optional: true},
    format: {type: String, optional: true},
    inputId: {type: String, optional: true},
    keyBinds: {validate: (kb) => typeof kb === "object" || kb === null, optional: true},
    locale: {type: String, optional: true},
    maxDate: {type: DateTime, optional: true},
    minDate: {type: DateTime, optional: true},
    readonly: {type: Boolean, optional: true},
    useCurrent: {type: Boolean, optional: true},
    widgetParent: {type: String, optional: true},
    daysOfWeekDisabled: {type: Array, optional: true},
    placeholder: {type: String, optional: true},
    onInput: {type: Function, optional: true},
    onUpdateInput: {type: Function, optional: true},
    revId: {type: Number, optional: true},
    isDateTime: {type: Boolean, optional: true},

};

DateHijriPicker
    .template = "DateHijriPicker";

// ***********************************************************************************************************

export class DateTimeHijriPicker
    extends DateHijriPicker {

    setup() {
        super.setup();
        this.td_settings.display.components.clock = true;
        this.td_settings.display.components.seconds = true;
        console.log("isDateTime:", this.props.isDateTime);
    }

    /**
     * @override
     */
    initFormat() {
        console.log("HijriPicker >> initFormat  override, localization object", localization);
        this.defaultFormat = localization.dateTimeFormat;
        this.formatValue = wrapError(formatDateTime, "");
        this.parseValue = wrapError(parseDateTime, false);
        this.isLocal = true;
    }

    /**
     * @override
     */
    setDateAndFormat(nextProps) {
        console.log("HijriPicker >> setDateAndFormat override , nextProps", nextProps);
        super.setDateAndFormat(nextProps);
        this.staticFormat += ` ${/h/.test(this.format) ? "hh" : "HH"}:mm:ss`;
    }
}

DateTimeHijriPicker.defaultProps = {
    ...DateHijriPicker.defaultProps,
    isDateTime: true,
};