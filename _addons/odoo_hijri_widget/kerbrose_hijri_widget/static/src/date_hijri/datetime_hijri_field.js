/** @odoo-module **/

import {DateTimeHijriPicker} from "@kerbrose_hijri_widget/date_hijri/date_hijri_picker";
import {areDatesEqual} from "@web/core/l10n/dates";
import {_lt} from "@web/core/l10n/translation";
import {registry} from "@web/core/registry";
import {standardFieldProps} from "@web/views/fields/standard_field_props";
import {useRecordObserver} from "@web/model/relational_model/utils";
import {Component, useState} from "@odoo/owl";

const {DateTime} = luxon;

export class DateTimeHijriField extends Component {
    static template = 'DateTimeHijriField'

    setup() {
        console.log('Received props:', this.props);

        this.state = useState({});
        useRecordObserver((record) => {
            this.state.value = record.data[this.props.name];
        });

        this.lastSetValue = null;
        this.revId = 0;
    }

    get date() {
        return this.state.value;
    }

    get formattedValue() {
        // return formatDateTime(this.props.value);
        // return this.props.value;
        // if (this.state.value) {
        //     return this.state.value.setLocale("ar-SA-islamic-umalqura").toLocaleString(DateTime.DATE_FULL); //this.props.value;
        // }

        if (this.state.value) {
            return this.state.value.setLocale("ar-SA-islamic-umalqura").toLocaleString({
                ...DateTime.DATETIME_FULL,
                hour12: true
            });
        }
    }

    onDateTimeChanged(date) {
        const changes = {[this.props.name]: date};

        let is_date_equal = areDatesEqual(this.state.value || "", date);
        console.log("DateTimeFieldHijri >> onDateTimeChanged is_date_equal:", is_date_equal);
        if (!areDatesEqual(this.state.value || "", date)) {
            this.revId++;
            this.state.value = date;
            this.props.record.update(changes);
        }
    }

    onDatePickerInput(ev) {
        // this.props.setDirty(ev.target.value !== this.lastSetValue);
        if (typeof this.props.setDirty === 'function') {
            this.props.setDirty(ev.target.value !== this.lastSetValue);
        }
    }

    onUpdateInput(date) {
        console.log("DateTimeFieldHijri >> onUpdateInput date:", date);
        if (typeof this.props.setDirty === 'function') {
            this.props.setDirty(false);
        }
        // this.props.setDirty(false);
        this.lastSetValue = date;
    }
}

DateTimeHijriField.template = "DateTimeHijriField";
DateTimeHijriField.components = {
    DateTimeHijriPicker,
};
DateTimeHijriField.props = {
    ...standardFieldProps,
    pickerOptions: {type: Object, optional: true},
    placeholder: {type: String, optional: true},
};
DateTimeHijriField.defaultProps = {
    pickerOptions: {},
};

export const datetime_hijri_field = {
    component: DateTimeHijriField,
    supportedTypes: ["datetime"],
    setDirty: {type: Function, optional: true},

};

DateTimeHijriField.displayName = _lt("Date & Time");
DateTimeHijriField.supportedTypes = ["datetime"];


registry.category("fields").add("hijridt", datetime_hijri_field);