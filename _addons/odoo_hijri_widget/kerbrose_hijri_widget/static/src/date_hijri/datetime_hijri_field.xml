<?xml version="1.0" encoding="UTF-8"?>
<templates>

    <t t-name="DateTimeHijriField">
        <t t-if="props.readonly">
            <span t-esc="formattedValue" />
        </t>
        <t t-else="">
            <DateTimeHijriPicker
                t-props="props.pickerOptions"
                date="state.value"
                inputId="props.id"
                placeholder="props.placeholder"
                onDateTimeChanged="(datetime) => this.onDateTimeChanged(datetime)"
                onInput.bind="onDatePickerInput"
                onUpdateInput.bind="onUpdateInput"
                revId="revId"
            />
        </t>
    </t>

</templates>
