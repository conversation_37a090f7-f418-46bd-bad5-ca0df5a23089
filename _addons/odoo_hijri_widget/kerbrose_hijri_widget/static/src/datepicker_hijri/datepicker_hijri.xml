<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">


    <t t-name="kerbrose_hijri_widget.DatePickerHijri">
        <div class="o_datepicker" aria-atomic="true" t-att-data-target-input="'#' + datePickerId" t-ref="root">
            <input type="text"
                t-ref="input"
                t-att-id="props.inputId"
                class="o_datepicker_input o_input datetimepicker-input"
                t-att-name="props.name"
                t-att-placeholder="props.placeholder"
                t-att-readonly="props.readonly"
                autocomplete="off"
                t-on-change="onInputChange"
                t-on-input="onInputInput"
                t-on-keydown="onInputKeydown"
            />
            <input type="hidden" t-ref="hiddenInput" t-att-id="datePickerId" />
            <span
                t-if="props.warn_future and state.warning"
                class="fa fa-exclamation-triangle text-danger o_tz_warning o_datepicker_warning"
                data-tooltip="This date is on the future. Make sure it is what you expected."
            />
            <span class="o_datepicker_button" />
        </div>
    </t>

</templates>
