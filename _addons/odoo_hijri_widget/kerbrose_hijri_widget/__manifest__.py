{
    'name': 'Hi<PERSON>ri Date Widget',
    'version': '0.0.1',
    'summary': 'Um <PERSON><PERSON>ura Hijri date widget',
    'description': '''this module would enable hijri date widget across Odoo platform''',
    'author': '<PERSON><PERSON><PERSON> (kerbrose)',
    'website': 'https://kerbrose.github.io/',
    'category': 'Hidden/Tools',
    'depends': [
        'account',
        'base',
        'web'
    ],
    'data': [
        'views/account_move.xml',
        'views/res_partner.xml'
    ],
    'demo': [
        ''
    ],
    'auto_install': False,
    'application': False,
    'installable': True,
    'assets': {
        'web.assets_common': [
            # 'kerbrose_hijri_widget/static/lib/tempusdominus6/tempusdominus.css',
            # 'kerbrose_hijri_widget/static/lib/popper/popper.js',
            # 'kerbrose_hijri_widget/static/lib/fontawesome/fontawesome.js',
            # 'kerbrose_hijri_widget/static/lib/tempusdominus6/tempusdominus.js',
        ],
        'web.assets_backend': [
            'kerbrose_hijri_widget/static/lib/tempusdominus6/tempusdominus.css',
            # 'kerbrose_hijri_widget/static/lib/popper/popper.js',
            # 'kerbrose_hijri_widget/static/lib/fontawesome/fontawesome.js',
            'kerbrose_hijri_widget/static/lib/tempusdominus6/tempusdominus.js',

            # 'kerbrose_hijri_widget/static/src/datepicker_hijri/datepicker_hijri.js',
            'kerbrose_hijri_widget/static/src/datepicker_hijri/datepicker_hijri.xml',
            'kerbrose_hijri_widget/static/src/fields/date_field_hijri.js',
            'kerbrose_hijri_widget/static/src/fields/date_field_hijri.xml',
            # 'kerbrose_hijri_widget/static/src/fields/datetime_field_hijri.js',
            # 'kerbrose_hijri_widget/static/src/fields/datetime_field_hijri.xml',

            '/kerbrose_hijri_widget/static/src/multiple_dates/style.css',
            '/kerbrose_hijri_widget/static/src/multiple_dates/multiple_dates.js',
            '/kerbrose_hijri_widget/static/lib/bootstrap/bootstrap-datepicker.min.js',
            '/kerbrose_hijri_widget/static/src/multiple_dates/multiple_date.xml',

            # '/kerbrose_hijri_widget/static/lib/moment/moment-with-locales.min.js',
            '/kerbrose_hijri_widget/static/src/date_hijri/date_hijri_picker.js',
            '/kerbrose_hijri_widget/static/src/date_hijri/date_hijri_picker.xml',
            '/kerbrose_hijri_widget/static/src/date_hijri/date_hijri_field.js',
            '/kerbrose_hijri_widget/static/src/date_hijri/date_hijri_field.xml',

            '/kerbrose_hijri_widget/static/src/date_hijri/datetime_hijri_field.js',
            '/kerbrose_hijri_widget/static/src/date_hijri/datetime_hijri_field.xml',

            # '/kerbrose_hijri_widget/static/src/date_hijri/higri_widget.js',
            # '/kerbrose_hijri_widget/static/src/date_hijri/hijri_widget.xml',


        ]
    },
    'qweb': [],
    'images': ['static/description/icon.svg'],
    'license': 'OPL-1',
    'currency ': 'USD',
    'price': 10,
    'support': 'contact the developer',
}
