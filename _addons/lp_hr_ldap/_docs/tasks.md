
# Task Sync with LDAP
#### Goal:
sync user , employee , department data from ldap server based on settings

* override company model and views to add notebook contains 2 groups    

  * Setting group:
    - sync_user:Boolean
    - sync_employee:Boolean
    - sync_department:Boolean
       
  * Mapping Fields group:
    - department_name:Char
    - employee_name(username):Char
    - entry_uuid(GUID):Char
    - work_mobile:Char
    - work_phone:Char
    - email(login):Char
    - manager_uuid(GUID):Char
    - manager_name:Char
    - job_position:Char
    - employee_no:Char
    
  * add button to execute  action_sync_ldap()

    
   * override all sync models to add shared fields:

     * **Sync Models:**
        - user
        - employee
        - department

      * **Shared Fields:**
       - ldap_entry_uuid: Char # to determine crate or update
       - ldap_last_sync_date: Datetime



  
      

   