from dataclasses import dataclass
from typing import Any, Optional, List, TypeVar, Type, Callable, cast
from datetime import datetime
import dateutil.parser
from .utils import *

@dataclass
class ID:
    id: int
    name: str

    @staticmethod
    def from_dict(obj: Any) -> 'ID':
        assert isinstance(obj, dict)
        id = int(from_str(obj.get("id")))
        name = from_str(obj.get("name"))
        return ID(id, name)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["name"] = from_str(self.name)
        return result


@dataclass
class EventTypeID:
    code: int

    @staticmethod
    def from_dict(obj: Any) -> 'EventTypeID':
        assert isinstance(obj, dict)
        code = int(from_str(obj.get("code")))
        return EventTypeID(code)

    def to_dict(self) -> dict:
        result: dict = {}
        result["code"] = from_str(str(self.code))
        return result


@dataclass
class Timezone:
    half: int
    hour: int
    negative: int

    @staticmethod
    def from_dict(obj: Any) -> 'Timezone':
        assert isinstance(obj, dict)
        half = int(from_str(obj.get("half")))
        hour = int(from_str(obj.get("hour")))
        negative = int(from_str(obj.get("negative")))
        return Timezone(half, hour, negative)

    def to_dict(self) -> dict:
        result: dict = {}
        result["half"] = from_str(str(self.half))
        result["hour"] = from_str(str(self.hour))
        result["negative"] = from_str(str(self.negative))
        return result


@dataclass
class UserID:
    photo_exists: bool
    name: Optional[str] = None
    user_id: Optional[int] = None

    @staticmethod
    def from_dict(obj: Any) -> 'UserID':
        assert isinstance(obj, dict)
        user_id = from_union([from_none, lambda x: int(from_str(x))], obj.get("user_id"))
        photo_exists = from_stringified_bool(from_str(obj.get("photo_exists")))
        name = from_union([from_str, from_none], obj.get("name"))
        return UserID(user_id, photo_exists, name)

    def to_dict(self) -> dict:
        result: dict = {}
        if self.user_id is not None:
            result["user_id"] = from_union([lambda x: from_none((lambda x: is_type(type(None), x))(x)), lambda x: from_str((lambda x: str((lambda x: is_type(int, x))(x)))(x))], self.user_id)
        result["photo_exists"] = from_str(str(self.photo_exists).lower())
        if self.name is not None:
            result["name"] = from_union([from_str, from_none], self.name)
        return result


@dataclass
class Row:
    id: int
    server_datetime: datetime
    row_datetime: datetime
    index: int
    user_id: UserID
    user_group_id: ID
    device_id: ID
    event_type_id: EventTypeID
    is_dst: int
    timezone: Timezone
    user_update_by_device: bool
    hint: str
    user_id_name: Optional[str] = None
    door_id: Optional[List[ID]] = None
    tna_key: Optional[int] = None

    @staticmethod
    def from_dict(obj: Any) -> 'Row':
        assert isinstance(obj, dict)
        id = int(from_str(obj.get("id")))
        server_datetime = from_datetime(obj.get("server_datetime"))
        row_datetime = from_datetime(obj.get("datetime"))
        index = int(from_str(obj.get("index")))
        user_id = UserID.from_dict(obj.get("user_id"))
        user_group_id = ID.from_dict(obj.get("user_group_id"))
        device_id = ID.from_dict(obj.get("device_id"))
        event_type_id = EventTypeID.from_dict(obj.get("event_type_id"))
        tna_key = from_union([from_none, lambda x: int(from_str(x))], obj.get("tna_key"))
        is_dst = int(from_str(obj.get("is_dst")))
        timezone = Timezone.from_dict(obj.get("timezone"))
        user_update_by_device = from_stringified_bool(from_str(obj.get("user_update_by_device")))
        hint = from_str(obj.get("hint"))
        user_id_name = from_union([from_str, from_none], obj.get("user_id_name"))
        door_id = from_union([lambda x: from_list(ID.from_dict, x), from_none], obj.get("door_id"))
        return Row(id, server_datetime, row_datetime, index, user_id, user_group_id, device_id, event_type_id, tna_key, is_dst, timezone, user_update_by_device, hint, user_id_name, door_id)

    def to_dict(self) -> dict:
        result: dict = {}
        result["id"] = from_str(str(self.id))
        result["server_datetime"] = self.server_datetime.isoformat()
        result["datetime"] = self.row_datetime.isoformat()
        result["index"] = from_str(str(self.index))
        result["user_id"] = to_class(UserID, self.user_id)
        result["user_group_id"] = to_class(ID, self.user_group_id)
        result["device_id"] = to_class(ID, self.device_id)
        result["event_type_id"] = to_class(EventTypeID, self.event_type_id)
        if self.tna_key is not None:
            result["tna_key"] = from_union([lambda x: from_none((lambda x: is_type(type(None), x))(x)), lambda x: from_str((lambda x: str((lambda x: is_type(int, x))(x)))(x))], self.tna_key)
        result["is_dst"] = from_str(str(self.is_dst))
        result["timezone"] = to_class(Timezone, self.timezone)
        result["user_update_by_device"] = from_str(str(self.user_update_by_device).lower())
        result["hint"] = from_str(self.hint)
        if self.user_id_name is not None:
            result["user_id_name"] = from_union([from_str, from_none], self.user_id_name)
        if self.door_id is not None:
            result["door_id"] = from_union([lambda x: from_list(lambda x: to_class(ID, x), x), from_none], self.door_id)
        return result


@dataclass
class EventCollectionClass:
    rows: List[Row]

    @staticmethod
    def from_dict(obj: Any) -> 'EventCollectionClass':
        assert isinstance(obj, dict)
        rows = from_list(Row.from_dict, obj.get("rows"))
        return EventCollectionClass(rows)

    def to_dict(self) -> dict:
        result: dict = {}
        result["rows"] = from_list(lambda x: to_class(Row, x), self.rows)
        return result


@dataclass
class Response:
    code: int
    link: str
    message: str

    @staticmethod
    def from_dict(obj: Any) -> 'Response':
        assert isinstance(obj, dict)
        code = int(from_str(obj.get("code")))
        link = from_str(obj.get("link"))
        message = from_str(obj.get("message"))
        return Response(code, link, message)

    def to_dict(self) -> dict:
        result: dict = {}
        result["code"] = from_str(str(self.code))
        result["link"] = from_str(self.link)
        result["message"] = from_str(self.message)
        return result


@dataclass
class EventCollection:
    event_collection: EventCollectionClass
    response: Response

    @staticmethod
    def from_dict(obj: Any) -> 'EventCollection':
        assert isinstance(obj, dict)
        event_collection = EventCollectionClass.from_dict(obj.get("EventCollection"))
        response = Response.from_dict(obj.get("Response"))
        return EventCollection(event_collection, response)

    def to_dict(self) -> dict:
        result: dict = {}
        result["EventCollection"] = to_class(EventCollectionClass, self.event_collection)
        result["Response"] = to_class(Response, self.response)
        return result


def event_collection_from_dict(s: Any) -> EventCollection:
    return EventCollection.from_dict(s)


def event_collection_to_dict(x: EventCollection) -> Any:
    return to_class(EventCollection, x)
