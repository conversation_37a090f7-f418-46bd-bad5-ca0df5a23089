import json
import uuid
from base64 import b64encode

import werkzeug

from .config_keys import ConfigKeys as conf
import requests
import logging
# import pydantic
from odoo import api, fields, models, tools, _, http
from odoo.http import request, Response, serialize_exception
from odoo.modules import get_module_resource
from .event import *
from datetime import datetime, timedelta
# from odoo.http import JsonRequest
import time

from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class SupremaAPI(models.AbstractModel):
    _name = "ams_suprema.api"
    _description = "Suprema API "

    def get_config(self, key, default=''):
        config = self.env['ir.config_parameter'].sudo()
        return config.get_param(key, default)

    def set_config(self, key, value):
        self.env['ir.config_parameter'].sudo().set_param(key, value)

    # region helper methods
    def validate_token(self):
        # check expire 1 hour by default after called token
        token_date_text = self.get_config(conf.KEY_TOKEN_LAST_DATE.value)
        token = self.get_config(conf.KEY_TOKEN.value)
        if token_date_text:
            token_date = dateutil.parser.parse(token_date_text)
            expire_in = int(self.get_config(conf.KEY_TOKEN_EXPIRE_IN.value, '0'))
            seconds_to_validate = expire_in - 60 if expire_in > 1800 else expire_in - 1  # for safty session
            expire_in_date = token_date + timedelta(seconds=seconds_to_validate)

            if datetime.now() < expire_in_date and token:
                x = datetime.now()
                print("time now", x)
                print("expire_in", expire_in_date)
                return token
            else:
                return self.api_auth()  # auth again if expire or token empty
        else:
            return self.api_auth()  # auth in first time

    def _raise_error(self, error):
        raise UserError(_('Biostar2 API  Error:') + ' %s' % error)

    # endregion

    def api_auth(self):
        login_id = self.get_config(conf.KEY_LOGIN_ID.value)
        login_secret = self.get_config(conf.KEY_LOGIN_SECRET.value)
        # basic_params = b64encode(bytes(f'{client_id}:{client_secret}', "utf-8")).decode("ascii")  # Review Coding
        # headers = {'Authorization': 'Basic %s' % basic_params}
        headers = {'Content-Type': 'application/json'}
        payload = {
            "User": {
                "login_id": login_id,
                "password": login_secret
            }
        }
        # {"User": {"login_id": "admin", "password": "Admin@123456"}}
        data = json.loads(json.dumps(payload))
        url = f"{self.get_config(conf.KEY_API_BASE_URL.value)}{conf.URL_LOGIN}"
        try:
            response = requests.request("POST", url, headers=headers, data=payload)  #
            _logger.info(f'{url} called')

            if response.status_code == 200:
                token = response.headers.get(conf.HEADER_SESSION_KEY)
                # result = response.json()
                self.set_config(conf.CLIENT_TOKEN.value, token)
                self.set_config(conf.KEY_TOKEN_EXPIRE_IN.value, str(1800))
                self.set_config(conf.KEY_TOKEN_LAST_DATE.value, datetime.now().isoformat())
                return token

            else:
                _logger.error(
                    f'Request to get token failed.\nCode:{response.status_code} \nContent: {response.content}')
            return ''
            # self._raise_query_error(Exception(f"Authentication error for id:{client_id}"))
        except Exception as e:
            self._raise_error(e)

        # conn = http.client.HTTPSConnection("id.sit.eta.gov.eg")
        #
        # payload = "grant_type=client_credentials&client_id=&client_secret=&scope=InvoicingAPI"
        #
        # headers = {'Content-Type': "application/x-www-form-urlencoded"}
        #
        # conn.request("POST", "/connect/token", payload, headers)
        #
        # res = conn.getresponse()
        # data = res.read()
        #
        # print(data.decode("utf-8"))

        # https://stackoverflow.com/questions/6999565/python-https-get-with-basic-authentication

        # Content-Type:application/x-www-form-urlencoded
        # from http.client import HTTPSConnection
        # from base64 import b64encode
        # # This sets up the https connection
        # c = HTTPSConnection("www.google.com")
        # # we need to base 64 encode it
        # # and then decode it to acsii as python 3 stores it as a byte string
        # userAndPass = b64encode(b"username:password").decode("ascii")
        # headers = {'Authorization': 'Basic %s' % userAndPass}
        # # then connect
        # c.request('GET', '/', headers=headers)
        # # get the response back
        # res = c.getresponse()
        # # at this point you could check the status etc
        # # this gets the page text
        # data = res.read()

    # region API endpoint call
    @api.model
    def api_events(self, payload, auto=False):
        """
       {
    "Query": {
        "limit": 100,
        "conditions": [
            {
                "column": "datetime",
                "operator": 3,
                "values": [
                    "2023-03-01T00:00:00.000Z",
                    "2023-03-06T22:59:59.000Z"
                ]
            },
            {
                "column": "id",
                "operator": 5,
                "values": [
                    "1520"
                ]
            }

        ],
        "orders": [
            {
                "column": "datetime",
                "descending": false
            }
        ]
    }
}
        """
        url = f"{self.get_config(conf.KEY_API_BASE_URL.value)}{self.get_config(conf.URL_EVENTS.value)}"
        token = self.validate_token()
        if not token:
            self._raise_error("Not valid access token")
            return
        # payload = self.get_doc_payload(True)
        # headers = {'Authorization': f'bearer {token}'}
        headers = {'Content-Type': 'application/json', 'Authorization': f'bearer {token}'}

        try:
            response = requests.post(url, headers=headers, json=payload)  # data=payload,
            _logger.info(f'{url} called')

            if response.status_code == 200:
                rpc = response.json().get('jsonrpc')
                result = response.json().get('result') if rpc else response.json()

                if not result:
                    return

                response_data = EventCollection.from_dict(result)
                return response_data()

            else:
                _logger.error(
                    f'Request to get token failed.\nCode:{response.status_code} \nContent: {response.content}')


        except Exception as e:
            self._raise_error(e)

    # return with e-invoice api standard structure -------------------

    # endregion

    def api_get_test(self, filename):
        json_file_path = get_module_resource('ams_suprema', 'static/json/', f'{filename}.json')
        with open(json_file_path) as f:
            return json.load(f)

    # endregion

    # region  Name

    # endregion

    # region Automation Scheduled Task
    # https://www.youtube.com/watch?v=_P_AVSNr6uU
    # https://odoo-development.readthedocs.io/en/latest/odoo/models/ir.cron.html#:~:text=Schedulers%20are%20automated%20actions%20that,will%20execute%20it%20as%20defined.
    # endregion


class SupremaApiController(http.Controller):

    @http.route('/api/isalive', type='http', auth="public")
    def api_isalive(self, **kwargs):
        try:
            response = {'is_alive': 'OK'}
            # return response
            return http.Response(json.dumps(response), content_type='application/json', status=200)
        except Exception as e:
            _logger.exception("Fail to connect API")
            result = {'error': str(e)}
            return http.Response(json.dumps(result), content_type='application/json', status=400)

    # @http.route('/api/is_alive', type='http', auth='public', website=True, csrf=False)
    # def is_alive(self, **kwargs):
    #     response_data = {
    #         'is_alive': 'OK'
    #     }
    #     return http.http.Response(json.dumps(response_data), content_type='application/json')

    @http.route('/api/v1.0/documents/<string:uuid>/raw', type='http', auth="public")
    def get_document(self, uuid):
        try:
            response = self.api_get_response("api_get_document")
            return http.Response(json.dumps(response), content_type='application/json', status=200)

        except Exception as e:
            _logger.exception("Fail to Called get_document_details")
            result = {'error': str(e)}
            return http.Response(json.dumps(result), content_type='application/json', status=400)

    @http.route('/api/events/search', type='http', auth="public", methods=['POST'], website=True, csrf=False)
    def api_events(self, **kwargs):
        try:
            response = self.api_get_response("api_events")
            return http.Response(json.dumps(response), content_type='application/json', status=200)
        except Exception as e:
            _logger.exception("Fail to Called get_document_details")
            result = {'error': str(e)}
            return http.Response(json.dumps(result), content_type='application/json', status=400)

        # headers = [('Content-Type', 'application/json')]
        #
        # # request = http.request
        # # json = http.request.jsonrequest
        # # try:
        # #     docs = DocData.from_dict(body)
        # # except:
        # #     _logger.error("")
        # print(http.request.jsonrequest)
        # try:
        #     response = self.api_get_response("api_events")
        # except:
        #     error_response = {
        #         'code': '201',
        #         'message': 'test error 201',
        #         'target': f""
        #     }
        # return error_response
        # raise werkzeug.exceptions.BadRequest(json.dumps(error_response))
        # raise werkzeug.exceptions.BadRequest(description="try", response=json.dumps(error_response))
        # return Response(response=json.dumps(error_response), content_type='application/json',status=201)

        # return http.Response(json.dumps(response), status=400)
        # raise werkzeug.exceptions.BadRequest(json.dumps(response))

        # return response

        # return http.Response(json.dumps(response), content_type='application/json', status=200,headers=headers)

    # @http.route('/api/login', type='http', auth="public", methods=['Post'], csrf=False)
    # def api_login(self, **kwargs):
    #     test_token = f'{uuid.uuid4().hex}'
    #     headers = [('Content-Type', 'application/json'), ('bs-session-id', test_token)]
    #
    #     # request = http.request
    #     # print(http.request.jsonrequest)
    #     # json = http.request.jsonrequest
    #     # try:
    #     #     docs = DocData.from_dict(body)
    #     # except:
    #     #     _logger.error("/api/v1/documentsubmissions")
    #     #     print(body)
    #     # try:
    #     print(kwargs)
    #     print(f'access_test_token {test_token}')
    #     # response = {
    #     #     'access_token': f'{uuid.uuid4().hex}',
    #     #     'expires_in': 3600,
    #     #     'scope': 'InvoicingAPI',
    #     #     'token_type': 'client_credentials'
    #     # }
    #
    #     response = self.api_get_response("api_login_response")
    #     # return Response(response=json.dumps(response), content_type='application/json',status=400)
    #
    #     # return http.Response(json.dumps(response), status=400)
    #     # raise werkzeug.exceptions.BadRequest(json.dumps(response))
    #
    #     # return response
    #     """ Status: 200 OK
    #         bs-session-id: 9747c622498842d0bd651e3b77fab6be
    #         X-Content-Type-Options: nosniff
    #         X-XSS-Protection: 1
    #         Cache-Control: no-cache, no-store, max-age=0, must-revalidate
    #         Pragma: no-cache
    #         Expires: 0
    #         Strict-Transport-Security: max-age=31536000 ; includeSubDomains
    #         X-Frame-Options: SAMEORIGIN
    #         Content-Type: application/json;charset=UTF-8
    #         Content-Length: 1843
    #         Date: Mon, 06 Mar 2023 14:05:24 GMT
    #     """
    #
    #     return http.Response(json.dumps(response), content_type='application/json', status=200, headers=headers)

    @http.route('/api/login', type='json', auth='public', methods=['POST'], website=True, csrf=False)
    def login(self, **kwargs):
        # Parse request payload
        # payload = json.loads(request.httprequest.data)
        # login_id = payload.get('User', {}).get('login_id')
        # password = payload.get('User', {}).get('password')

        token = f'{uuid.uuid4().hex}'
        # ''.join(random.choices('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=32))

        # Set headers and response data
        # http.request.httprequest.headers['bs-session-id'] = '42d2350bd3fd42fca936a57d6d69cdde'

        # http.request.headers['Authorization'] = f'Token {token}'
        response_data = self.api_get_response("api_login_response")
        # response = http.Response(json.dumps(response_data), content_type='application/json',
        #                          headers={'X-My-Header': 'Custom value login'})
        # response.headers.add('bs-session-id', '42d2350bd3fd42fca936a57d6d69cdde')
        # response.status_code = 200
        return response_data

        # response = http.Response(json.dumps(response_data), content_type='application/json')
        # response.status = 401
        # return response

    @http.route('/api/test', type='json', auth='public', website=True, methods=['POST'], csrf=False)
    def my_endpoint(self, **kw):
        # Retrieve JSON payload from request body
        payload = json.loads(request.httprequest.data)

        # Do something with payload
        # ...

        # Prepare JSON response
        response_data = {
            "message": "Payload received successfully"
        }
        # response = http.Response(json.dumps(response_data), content_type='application/json', headers=headers)
        # response.headers.set('X-Custom-Header', '123456789')
        return response_data

    def api_get_response(self, filename):
        json_file_path = get_module_resource('ams_suprema', 'static/json/', f'{filename}.json')
        with open(json_file_path) as f:
            return json.load(f)

    @http.route('/api/v1.0/documents/<string:uuid>/raw', type='http', auth="public")
    def get_document(self, uuid):
        try:
            response = self.api_get_response("api_get_document")
            return http.Response(json.dumps(response), content_type='application/json', status=200)

        except Exception as e:
            _logger.exception("Fail to Called get_document_details")
            result = {'error': str(e)}
            return http.Response(json.dumps(result), content_type='application/json', status=400)

    # except Exception as e:
    #     _logger.exception("Fail to Called Token")
    #     result = {'error': str(e)}
    #     return json.dumps(result),400

    # Create authenticated session from outside odoo
    # https://stackoverflow.com/questions/52875925/how-to-connect-to-odoo-database-from-an-android-application


