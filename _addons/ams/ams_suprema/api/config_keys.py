from enum import Enum


class ConfigKeys(Enum):
    """
      Enum class that defines constant values for accessing configuration information
      and making API calls to a BioStar system.
      """

    KEY_LOGIN_ID = 'ta_bs.login_id'
    """Stores the user name used for logging in to the BioStar system"""

    KEY_LOGIN_SECRET = 'ta_bs.key_secret'
    """Stores the password used for logging in to the BioStar system"""

    KEY_TOKEN = 'ta_bs.key_token'
    """Stores the session value ID used to maintain the user's session"""

    KEY_TOKEN_EXPIRE_IN = 'ta_bs.key_token_expire_in'
    """Stores the expiration time in seconds for the session value ID"""

    KEY_TOKEN_LAST_DATE = 'ta_bs.key_token_last_date'
    """Stores the last date and time the session value ID was updated"""

    KEY_API_BASE_URL = 'ta_bs.key_base_url'
    """Stores the IP address and port number for the BioStar API server"""

    HEADER_SESSION_KEY = 'bs-session-id'
    """Stores the name of the HTTP header key field used to transmit the session value ID to the BioStar API server"""

    URL_LOGIN = '/api/login'
    """The URL for the BioStar API login endpoint"""

    URL_USERS = '/api/users'
    """The URL for the BioStar API users endpoint"""

    URL_EVENTS = '/api/events/search'
    """The URL for the BioStar API events search endpoint."""


# region configuration
# Todo : add the following keys in config parameter
"""
{
	"biostar_svr": "https://*************:555",
	"session_key": "bs-session-id",
	"session_id": "42d2350bd3fd42fca936a57d6d69cdde"
}"""
# endregion
