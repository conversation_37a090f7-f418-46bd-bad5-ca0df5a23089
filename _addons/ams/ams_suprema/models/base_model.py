# -*- coding: utf-8 -*-


from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class SupremaBaseModel(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.base_model"
    _inherit = ['ams_base.abstract_model']
    _description = "Suprema Base Model"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # error = fields.Boolean()
    # error_msg = fields.Char(string="Error Message")
    # response_code = fields.Char()
    # endregion

    # region  Special
    # endregion

    # region  Relational
    company_id = fields.Many2one(comodel_name="res.company", string="Company", help="",
                                 default=lambda self: self.env.user.company_id)

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # def assign_error(self, response):
    #     """
    #         this method to store error message, code and disconnect device when connection loss
    #     """
    #     self.error_msg = response.error_message
    #     self.response_code = response.response_code
    #
    # def reset_error(self):
    #     self.error = False
    #     self.error_msg = False
    #     self.response_code = '0'
    #
    # def _notify_success(self):
    #     """return client action notification to indicate success push"""
    #     return {
    #         'type': 'ir.actions.client',
    #         'tag': 'display_notification',
    #         'params': {
    #             'title': _('Success'),
    #             'message': _('Success updates'),
    #
    #         }
    #
    #     }
    #
    # def _notify_error(self):
    #     """return client action notification to indicate error push"""
    #     return {
    #         'type': 'ir.actions.client',
    #         'tag': 'display_notification',
    #         'params': {
    #             'title': _('Error'),
    #             'message': _('Error updates'),
    #
    #         }
    #
    #     }
    # endregion
