# -*- coding: utf-8 -*-
import json

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class BaseAuthModeAbstract(models.AbstractModel):
    _name = "ams_suprema.base_user_auth_mode"
    _description = "Base User Auth Mode"

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # def _default_face_auth_mode_id(self):
    #     return self.env.ref('ams_suprema.auth_ext_mode_face_only', raise_if_not_found=False)

    def _default_fingerprint_auth_mode_id(self):
        """override in user to set default auth mode for biometrics"""
        return False  # self.env.ref('ams_suprema.auth_mode_biometric_only', raise_if_not_found=False)

    def _default_card_auth_mode_id(self):
        """override in user to set default auth mode for card"""
        return False  # self.env.ref('ams_suprema.auth_mode_card_only', raise_if_not_found=False)

    # def _default_id_auth_mode_id(self):
    #     return self.env.ref('ams_suprema.auth_ext_mode_id_only', raise_if_not_found=False)

    # endregion

    ext_auth_supported = fields.Boolean('ExtAuth Supported', default=False)  # to be override and compute

    face_auth_mode_id = fields.Many2one('ams_suprema.auth_mode', string='Face Auth Mode')
    face_auth_mode_domain = fields.Char(compute='_compute_face_auth_mode_domain')

    fingerprint_auth_mode_id = fields.Many2one('ams_suprema.auth_mode', string='Fingerprint Auth Mode'
                                               , default=lambda self: self._default_fingerprint_auth_mode_id())
    fingerprint_auth_mode_domain = fields.Char(compute='_compute_fingerprint_auth_mode_domain')

    card_auth_mode_id = fields.Many2one('ams_suprema.auth_mode', string='Card Auth Mode',
                                        default=lambda self: self._default_card_auth_mode_id())
    card_auth_mode_domain = fields.Char(compute='_compute_card_auth_mode_domain')

    id_auth_mode_id = fields.Many2one('ams_suprema.auth_mode', string='ID Auth Mode')
    id_auth_mode_domain = fields.Char(compute='_compute_id_auth_mode_domain')

    def _get_auth_domain(self, default_domain):
        """return web domain for auth mode
        default_domain: list example domain = [('category_type', '=', 'id')]"""
        domain = default_domain or []
        domain.append(('activate', '=', True))
        if self.ext_auth_supported:
            domain.append(('extension_supported', '=', True))
        else:
            domain.append(('extension_supported', '=', False))
        return json.dumps(domain)

    @api.depends('ext_auth_supported')
    def _compute_id_auth_mode_domain(self):
        for rec in self:
            rec.id_auth_mode_domain = rec._get_auth_domain([('category_type', '=', 'id')])

    @api.depends('ext_auth_supported')
    def _compute_card_auth_mode_domain(self):
        for rec in self:
            rec.card_auth_mode_domain = rec._get_auth_domain([('category_type', '=', 'card')])

    @api.depends('ext_auth_supported')
    def _compute_fingerprint_auth_mode_domain(self):
        for rec in self:
            cat_type = 'fingerprint' if rec.ext_auth_supported else 'biometric'
            rec.fingerprint_auth_mode_domain = rec._get_auth_domain([('category_type', '=', cat_type)])

    @api.depends('ext_auth_supported')
    def _compute_face_auth_mode_domain(self):
        for rec in self:
            rec.face_auth_mode_domain = rec._get_auth_domain([('category_type', '=', 'face')])
