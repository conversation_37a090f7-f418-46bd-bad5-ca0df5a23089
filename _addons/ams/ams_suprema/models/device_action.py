# -*- coding: utf-8 -*-
import base64

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError

from datetime import datetime


class DeviceAction(models.Model):
    """
       This model represents an device actions queue to add/delete users on devices
        by cron job and track actions error when adding or  deleting.
    """
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.device_action"
    _inherit = ['ams_suprema.base_model', "ams_suprema.connector"]
    _description = "Device Action"
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char('Name', copy=False, required=True, readonly=True,
                       default=lambda self: self.env['ir.sequence'].next_by_code('ams_suprema.device_action'))
    user_name = fields.Char()
    enroll_number = fields.Char(string="Enroll No")
    employee_number = fields.Char()
    device_no = fields.Char(related="device_id.device_id", string="Device ID", store=True)
    execute_date = fields.Datetime()
    error = fields.Boolean()
    comment = fields.Char()
    batch_no = fields.Char(help="Batch No is  grouping of some actions created together 202503250114")
    state = fields.Selection(selection=[('pending', 'Pending'), ('executed', 'Executed'), ('fail', 'Fail')],
                             default='pending')
    action_type = fields.Selection(selection=[
        ('add', 'Add'), ('delete', 'Delete'), ('unlink', 'Unlink')], default='add',
        help="add: Add user to device,"
             " delete: Delete user from device,"
             " unlink: remove partial data of user from device like remove access group or card ...")

    user_enrolled = fields.Boolean()
    card_enrolled = fields.Boolean()
    finger_enrolled = fields.Boolean()
    face_enrolled = fields.Boolean()
    ac_group_enrolled = fields.Boolean()
    command_type = fields.Selection(selection=
                                    [('enroll_user', 'Enroll'),
                                     ('set_card', 'Set Card'),
                                     ('set_finger', 'Set Finger'),
                                     ('set_face', 'Set Face'),
                                     ('set_ac_group', 'Set AC Group')]
                                    )

    # endregion
    # region  Special
    # endregion

    # region  Relational
    employee_id = fields.Many2one('hr.employee')
    device_id = fields.Many2one('ams_suprema.device')
    user_enroll_id = fields.Many2one('ams_suprema.user')
    ac_group_id = fields.Many2one('ams_base.ac_group')

    # TODO ask abdr where's user_id field
    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    @property
    def execute_set_card(self):
        if self.device_id:
            if not self.device_id.device_id_info.card_input_supported:
                return False

        execute = self.command_type == 'set_card' or self.command_type is False
        return execute

    @property
    def execute_set_finger(self):
        if self.device_id:
            if not self.device_id.device_id_info.fingerprint_input_supported:
                return False
        execute = self.command_type == 'set_finger' or self.command_type is False
        return execute

    @property
    def execute_set_face(self):
        if self.device_id:
            if not self.device_id.device_id_info.face_input_supported:
                return False
        execute = self.command_type == 'set_face' or self.command_type is False
        return execute

    @property
    def execute_set_ac_group(self):
        execute = self.command_type == 'set_ac_group' or self.command_type is False
        return execute

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_execute(self):
        for device_action in self:
            response = False
            device_info = device_action.device_id.device_id_info
            if device_action.action_type == 'add' and device_action.user_enroll_id:
                response = device_action.user_enroll_id.enroll_user(device_info, device_action)

            elif device_action.action_type == 'delete' and device_action.user_enroll_id:
                response = device_action.user_enroll_id.delete_user(device_info)

            elif device_action.action_type == 'unlink' and device_action.user_enroll_id:
                response = device_action.user_enroll_id.unlink_user(device_info, device_action)

            else:
                pass

            if response.response_code == '1':
                device_action.state = 'executed'
                device_action.execute_date = datetime.now()
                device_action.reset_error()
            else:
                device_action.state = 'fail'
                device_action.error = True
                device_action.assign_error(response)

            self.env['ams_suprema.device_user'].sync_device_user(device_action)

    def auto_execute_device_actions(self):
        """ run by cron job to execute pending device actions """

        device_actions = self.sudo().env['ams_suprema.device_action'].search(
            [('state', '=', 'pending')], order='create_date asc')
        self.logger.info(f"starting to execute ({len(device_actions)}) device actions")
        device_actions.sudo().action_execute()

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _create_device_action(self, devices, users, new_vals={}, execute=True):
        """create batch of device action to enroll or delete users on devices
        or add | remove  access group for users
        new_vals like:
        {
        'action_type': 'add',
        'command_type': 'enroll_user',
        'ac_group_id': 1,
        }
        pass execute=False if you don't want to execute device actions immediately
        """
        new_vals = new_vals or {'action_type': 'add'}
        time_now = fields.Datetime.now()
        batch_no = fields.Datetime.context_timestamp(self, time_now).strftime("%Y%m%d%H%M%S")

        devices = self.env['ams_suprema.device'] if not devices else devices
        users = self.env['ams_suprema.user'] if not users else users
        pending_device_action_list = []
        dedicated_device_action_list = []
        for device in devices:
            for user in users:
                vals = {
                    'device_id': device.id,
                    'batch_no': batch_no,
                    'employee_id': user.employee_id.id if user.employee_id else False,
                    'enroll_number': user.enroll_number,
                    'employee_number': user.employee_id.employee_number if user.employee_id else False,
                    'user_name': user.name,
                    'user_enroll_id': user.id,
                }
                vals.update(new_vals)
                # device_action = self.env['ams_suprema.device_action'].create([vals])
                if device.dedicated:
                    dedicated_device_action_list.append(vals)
                else:
                    pending_device_action_list.append(vals)
            # if device.dedicated:
            #     device_action.action_execute()

        if dedicated_device_action_list:
            res_real = self.env['ams_suprema.device_action'].create(dedicated_device_action_list)
            if execute:
                res_real.action_execute()

        if pending_device_action_list:
            res_pending = self.env['ams_suprema.device_action'].create(pending_device_action_list)

    # endregion
