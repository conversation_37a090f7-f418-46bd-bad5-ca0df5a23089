# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from . import user_auth_mode as auth_mode


class ResConfigSettings(models.TransientModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = 'res.config.settings'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    getway_ca_file_path = fields.Char(config_parameter="ams_suprema.getway_ca_file_path")
    gateway_ip = fields.Char(config_parameter="ams_suprema.gateway_ip")
    gateway_port = fields.Char(config_parameter="ams_suprema.gateway_port")
    use_ssl = fields.Boolean(config_parameter="ams_suprema.use_ssl")
    finger_template_formate = fields.Char(config_parameter="ams_suprema.finger_template_formate")
    min_templates = fields.Integer(config_parameter="ams_suprema.min_templates", default=2)
    auth_mode = fields.Selection(auth_mode.AuthMode.get_auth_modes(),
                                 config_parameter="ams_suprema.auth_mode", default='0')

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_test_connection(self):
        ...

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
