# -*- coding: utf-8 -*-
import datetime

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class BaseDeviceLog(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.device_log"
    _inherit = ["ams_base.device_log", 'ams_suprema.base_model', 'ams_suprema.connector']
    _description = "Device Log"
    _order = "log_time desc"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    card_data = fields.Char()
    # endregion

    # region  Special
    # endregion

    # region  Relational
    event_device_id = fields.Many2one('ams_suprema.device', string="Device")
    company_id = fields.Many2one(comodel_name="res.company", string="Company", help="",
                                 default=lambda self: self.env.user.company_id)

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model
    def create(self, vals_list):
        res = super(BaseDeviceLog, self).create(vals_list)
        res.action_execute_log()
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_execute_log(self):
        for rec in self:
            if rec.is_ta:
                rec._create_punch_log(rec.enroll_number, rec.device_id, rec.log_time)
                rec.state = 'executed'
                rec.executed_datetime = datetime.datetime.now()

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _prepare_device_log(self, log_data):
        log_time = datetime.datetime.utcfromtimestamp(log_data.timestamp)
        unknown = "Unknown code(%#X)" % (log_data.eventCode | log_data.subCode)
        event_entry = self.client_gateway.get_event_entry(log_data.eventCode, log_data.subCode)
        # desc = self.client_gateway.get_event_name(log_data.eventCode, log_data.subCode)
        device = self.env['ams_suprema.device'].search([('device_id', '=', log_data.deviceID)], limit=1)
        enroll_user = self.env['ams_suprema.user'].search([('enroll_number', '=', log_data.userID)], limit=1)
        return {
            'event_device_id': device.id if device else False,
            'enroll_name': enroll_user.name if enroll_user else False,
            'device_id': log_data.deviceID,
            'event_id': log_data.ID,
            'name': f'{log_data.deviceID},{log_data.ID}',
            'event_code': log_data.eventCode,
            'code_name': event_entry.get('event_code_str', unknown),
            'sub_code': log_data.subCode,
            'description': event_entry.get('desc', unknown),
            # TODO map event code to name from json file  data/event_code.json
            'timestamp': log_data.timestamp,
            'enroll_number': log_data.userID,
            'has_image': log_data.hasImage,
            'changed_on_device': log_data.changedOnDevice,
            'entity_id': log_data.entityID,
            'temperature': log_data.temperature,
            'is_ta': self.client_gateway.is_success_punch(log_data.eventCode),
            # 'card_data': log_data.cardData,

            'log_time': log_time,
            'log_date': fields.Datetime.context_timestamp(self, log_time).date(),
            # 'is_ta':# TODO implement based in event code (user attendance)
            # 'event_datetime': datetime.datetime.now(),
            # 'executed_datetime': datetime.datetime.now(),

        }

    def _collect_device_logs(self, device):
        """ collect logs from specific device and create event log """
        if device.state == 'online':
            self.logger.info(f"collecting logs for device {device.device_label}")
            get_logs_res = self.client_gateway.get_log(device.device_id_info, device.last_log_id + 1, 0)
            if get_logs_res.response_code == '1':
                device.reset_error()
                logs_result = get_logs_res.result
                last_event_id = device.last_log_id
                for log in logs_result:
                    if log.ID > last_event_id:
                        last_event_id = log.ID

                    device_log = self._prepare_device_log(log)
                    self.env['ams_suprema.device_log'].sudo().create(device_log)

                device.last_log_id = last_event_id

            else:
                device.handle_device_state(get_logs_res)
                device.assign_error(get_logs_res)
        else:
            self.logger.info(f"cannot collecting logs from device {device.device_label} because state is offline")

    def auto_collect_logs(self):
        """ collect logs from all active devices and create event log in background """

        devices = self.env['ams_suprema.device'].search(
            [('activate', '=', True),
             ('log_active', '=', True),
             ('state', '=', 'online')])
        self.logger.info(f"starting to collect logs form {len(devices)} active devices")
        for device in devices:
            self.sudo()._collect_device_logs(device)

    # endregion
