# -*- coding: utf-8 -*-
import base64
import datetime
import time

from odoo import api, fields, models, _
from odoo.addons.ams_suprema.device_info import DeviceInfo
from odoo.exceptions import UserError, ValidationError

from . import user_auth_mode as auth_mode


class User(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.user"
    _inherit = ["ams_base.user", "ams_suprema.connector", "ams_suprema.base_model", 'ams_suprema.base_user_auth_mode'
        , 'mail.thread', 'mail.activity.mixin']
    _description = "Suprema User"
    _sql_constraints = [('enroll_number_unique', 'unique(enroll_number)', 'Enroll number must be unique!'),
                        ('employee_number_unique', 'unique(employee_number)', 'Employee number must be unique!'),
                        ('employee_id_unique', 'unique(employee_id)', 'Employee ID must be unique!')]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    def _default_fingerprint_auth_mode_id(self):
        return self.env.ref('ams_suprema.auth_mode_biometric_only', raise_if_not_found=False)

    def _default_card_auth_mode_id(self):
        return self.env.ref('ams_suprema.auth_mode_card_only', raise_if_not_found=False)

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(required=True, tracking=True)
    error_msg = fields.Text(string="Error")
    response_code = fields.Char()
    card_no = fields.Char(tracking=True)
    card_data = fields.Binary()
    card_type = fields.Integer()
    card_size = fields.Integer()
    finger_quality = fields.Selection([('80', 'QUALITY_HIGHEST'), ('60', 'QUALITY_HIGH'),
                                       ('40', 'QUALITY_STANDARD'), ('20', 'QUALITY_LOW')], required=True, default='80')

    finger_template_format = fields.Char(default="TEMPLATE_FORMAT_SUPREMA")
    finger1_template1 = fields.Binary()
    finger1_template2 = fields.Binary()
    finger2_template1 = fields.Binary()
    finger2_template2 = fields.Binary()

    face_enroll_threshold = fields.Char(default="BS2_FACE_ENROLL_THRESHOLD_DEFAULT")
    face_data = fields.Binary(help="Face Templates from scan device encoded bytes in base64")
    # auth_mode = fields.Char(default="AUTH_MODE_BIOMETRIC_ONLY")  # TODO (convert this field to be selection field)
    # auth_mode = fields.Selection(auth_mode.AuthMode.get_auth_modes(),
    #                              default=lambda self: self.env['ir.config_parameter'].get_param('ams_suprema.auth_mode',
    #                                                                                             '0'))
    enrollment_done = fields.Boolean(compute='_compute_enrollment_done', store=True)
    finger1_saved = fields.Boolean(compute='_compute_finger1_saved', store=True)
    finger2_saved = fields.Boolean(compute='_compute_finger2_saved', store=True)
    face_saved = fields.Boolean(compute='_compute_face_saved', store=True)
    sync_user = fields.Boolean(help="True if automatic sync user from device without manual enroll")
    last_batch_no = fields.Char(help="Last batch number of sync user from device to identify list of users "
                                     "modified")

    # user_enrolled = fields.Boolean()
    # card_enrolled = fields.Boolean()
    # finger_enrolled = fields.Boolean()
    # face_enrolled = fields.Boolean()

    # endregion

    # region  Special
    # endregion

    # region  Relational
    employee_id = fields.Many2one('hr.employee', tracking=True)
    device_ids = fields.Many2many('ams_suprema.device', string="Devices")
    enroll_device_id = fields.Many2one('ams_suprema.device', tracking=True
                                       , help="Device in which user will be enroll from")
    device_group_id = fields.Many2one('ams_base.device_group', tracking=True, index=True,
                                      help="Enroll user to all devices inside this group as default group")

    ac_groups_ids = fields.Many2many('ams_base.ac_group', 'ams_suprema_user_ac_group_rel',
                                     'user_id', 'group_id', 'Groups',
                                     default=lambda self: [(4, self.env.ref('ams_base.default_ac_group',
                                                                            raise_if_not_found=False).id)]
                                     )
    activate = fields.Boolean(default=True)

    # endregion

    # region  Computed
    # endregion
    # endregion

    # region -------------------------------------- TODO[IMP]: properties -------------------------
    @property
    def devices(self):
        """
            return public and current devices
        """
        public_devices = self.env['ams_suprema.device'].search([('is_public', '=', True)])
        return list(set(self.device_ids + public_devices))

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('finger1_template1', 'finger1_template2')
    def _compute_finger1_saved(self):
        for rec in self:
            rec.finger1_saved = True if rec.finger1_template1 and rec.finger1_template2 else False

    @api.depends('finger2_template1', 'finger2_template2')
    def _compute_finger2_saved(self):
        for rec in self:
            rec.finger2_saved = True if rec.finger2_template1 and rec.finger2_template2 else False

    @api.depends('face_data')
    def _compute_face_saved(self):
        for rec in self:
            rec.face_saved = True if rec.face_data else False

    @api.depends('finger1_saved', 'finger2_saved', 'face_saved')
    def _compute_enrollment_done(self):
        for rec in self:
            rec.enrollment_done = True if any(
                [rec.finger1_saved, rec.finger2_saved, rec.face_saved]) else False

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        # set name from employee username if employee linked with user
        if self.employee_id:
            if self.employee_id.user_id:
                self.name = self.employee_id.user_id.name
            if self.employee_id.enroll_number:
                self.enroll_number = self.employee_id.enroll_number
            if self.employee_id.employee_number:
                self.employee_number = self.employee_id.employee_number
            if self.employee_id.device_group_id:
                self.device_group_id = self.employee_id.device_group_id.id

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model
    def create(self, vals_list):
        res = super(User, self).create(vals_list)
        if res.device_group_id:
            res.device_ids = [(4, device.id) for device in res.device_group_id.suprema_device_ids]
        return res

    def write(self, values):
        res = super(User, self).write(values)

        if values.get('device_group_id'):
            device_group = self.env['ams_base.device_group'].browse(values.get('device_group_id'))
            if device_group:
                self.device_ids = [(4, device.id) for device in device_group.suprema_device_ids]

        # if values.get('employee_id'):
        #     emp = self.env['hr.employee'].browse(values.get('employee_id'))
        #     if emp.device_group_id:
        #         self.device_ids = [(4, device.id) for device in emp.device_group_id.suprema_device_ids]
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_scan(self):
        if not self.enroll_device_id:
            raise ValidationError(_('Please select device'))

        if self._context.get('type') == 'face':
            self._scan_face()
        elif self._context.get('type') == 'finger1':
            self._scan_finger(finger_code=1)
        elif self._context.get('type') == 'finger2':
            self._scan_finger(finger_code=2)
        elif self._context.get('type') == 'card':
            self._scan_card()
        else:
            pass

    def action_remove(self):
        # TODO user context.get('type') to check if remove face or finger
        if self._context.get('type') == 'face':
            # TODO get face data
            self.face_data = False

        elif self._context.get('type') == 'finger1':
            self.finger1_template1 = self.finger1_template2 = False

        else:
            self.finger2_template1 = self.finger2_template2 = False

    def action_open_transfer_wizard(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Transfer'),
            'res_model': 'ams_suprema.transfer_wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_user_id': self.id, 'default_device_ids': self.device_ids.ids, },
            'views': [(self.env.ref('ams_suprema.transfer_wizard_view_form').id, 'form')]
        }

    def action_enroll(self):
        """create device actions to enroll or update user to selected devices by transfer wizard"""
        for user in self:
            user._create_device_action('add', self.devices)

        # return self._create_device_action('add', self.devices)

    def action_delete_user(self):
        """ create device actions to delete user from selected devices by transfer wizard
        """
        for user in self:
            user._create_device_action('delete', self.devices)

        # return self._create_device_action('delete', self.devices)

    def action_open_device_action_views(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Device Actions'),
            'res_model': 'ams_suprema.device_action',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': [('user_enroll_id', '=', self.id)]
        }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _create_device_action(self, action_type, devices):
        batch_no = fields.Datetime.context_timestamp(self, datetime.datetime.now()).strftime("%Y%m%d%H%M%S")
        devices = self.env['ams_suprema.device'] if not devices else devices
        for device in devices:
            vals = {
                'device_id': device.id,
                'employee_id': self.employee_id.id,
                'execute_date': datetime.datetime.now(),
                'batch_no': batch_no,
                'action_type': action_type,
                'enroll_number': self.enroll_number,
                'employee_number': self.employee_id.employee_number,
                'user_name': self.name,
                'user_enroll_id': self.id,

            }
            device_action = self.env['ams_suprema.device_action'].create([vals])
            if device_action and device.dedicated and device.state == 'online':
                device_action.action_execute()

        return {
            'effect': {
                'fadeout': 'slow',
                'message': "Actions added to the device action queue ,please check status",
                'img_url': '/web/static/img/smile.svg',
                'type': 'rainbow_man',
            }
        }

    def _scan_face(self):
        """ used to scan face"""
        if self.enroll_device_id:
            device_info = self.enroll_device_id.device_id_info
            if not device_info.face_input_supported:
                raise ValidationError(_('Face  is not supported in enroll device'))
        else:
            raise ValidationError(_('Please select enroll device'))

        face_response = self.client_gateway.scan_face(device_info)
        if face_response.response_code == '1':
            self.reset_error()
            serialized_data = face_response.result.SerializeToString()  # serialize face_pb2.FaceData
            # face_data_bytes = bytes().join(face_response.result.templates)
            self.face_data = base64.b64encode(serialized_data)
        else:
            self.assign_error(face_response)

    def _scan_finger(self, finger_code=1):
        """
            used to scan finger1 if finger_code is 1 or finger2 if scan_code is 2
        """
        if self.enroll_device_id:
            device_info = self.enroll_device_id.device_id_info
            if not device_info.fingerprint_input_supported:
                raise ValidationError(_('Fingerprint  is not supported in enroll device'))
        else:
            raise ValidationError(_('Please select enroll device'))

        def scan(self, finger_code, temp_index):
            scan_response_temp = self.client_gateway.scan_finger(device_info,
                                                                 int(self.finger_quality))
            if scan_response_temp.response_code == '1':
                self.reset_error()
                temp_field = f'finger{finger_code}_template{temp_index + 1}'
                if temp_field in self._fields:
                    self[temp_field] = base64.b64encode(scan_response_temp.result)  # str(scan_response_temp.result)
                    # template = str(scan_response_temp.result)
            else:
                self.assign_error(scan_response_temp)

        max_temp = int(self.env['ir.config_parameter'].get_param('ams_suprema.min_templates', '2'))
        temp_index = 0
        while temp_index < max_temp:
            scan(self, finger_code, temp_index)
            temp_index = temp_index + 1

        # self.enroll_device_id.action_get_logs()

    def _scan_card(self):
        """ used to scan card """
        if self.enroll_device_id:
            device_info = self.enroll_device_id.device_id_info
            if not device_info.card_input_supported:
                raise ValidationError(_('Card  is not supported in enroll device'))
        else:
            raise ValidationError(_('Please select enroll device'))

        scan_card_response = self.client_gateway.scan_card(device_info)
        if scan_card_response.response_code == '1':
            self.reset_error()
            if scan_card_response.result.CSNCardData:
                self.card_data = base64.b64encode(scan_card_response.result.CSNCardData.data)
                self.card_size = scan_card_response.result.CSNCardData.size
                self.card_type = scan_card_response.result.CSNCardData.type
                self.card_no = self.client_gateway.get_card_number(scan_card_response.result.CSNCardData.data)
            self.card_type = scan_card_response.result.type
        else:
            self.assign_error(scan_card_response)

    def _get_auth_mode_config(self, device_info: DeviceInfo) -> dict:
        """
        Return dictionary mapping of authentication mode config.

        Example return: {'card_auth_mode_id': '', 'fingerprint_auth_mode_id': '', 'face_auth_mode_id':''}
        """
        auth_mode_config = {}

        def update_auth_mode(mode, mode_type):
            if mode:
                if device_info.extended_auth_supported:
                    if mode.extension_supported:
                        auth_mode_config[mode_type] = mode.code
                    elif mode.mapping_auth_mode_id and mode.mapping_auth_mode_id.extension_supported:
                        auth_mode_config[mode_type] = mode.mapping_auth_mode_id.code
                else:
                    if not mode.extension_supported:
                        auth_mode_config[mode_type] = mode.code
                    elif mode.mapping_auth_mode_id and not mode.mapping_auth_mode_id.extension_supported:
                        auth_mode_config[mode_type] = mode.mapping_auth_mode_id.code

        update_auth_mode(self.card_auth_mode_id, 'card_auth_mode_id')
        update_auth_mode(self.fingerprint_auth_mode_id, 'fingerprint_auth_mode_id')
        update_auth_mode(self.face_auth_mode_id, 'face_auth_mode_id')

        return auth_mode_config

    def enroll_user(self, device_info: DeviceInfo, device_action):
        device_action = device_action or self.env['ams_suprema.device_action']

        error_msg = ''
        response_code = '0'  # last response error code
        response = False
        user_enroll = self
        auth_mode_config = self._get_auth_mode_config(device_info)
        start_date_time_unix = int(time.mktime(self.start_datetime.timetuple()))  # convert datetime to unix time
        end_date_time_unix = int(time.mktime(self.end_datetime.timetuple()))  # convert datetime to unix time
        # device_info = self.device_id.device_id_info

        enroll_response = self.client_gateway.enroll_user(device_info,
                                                          user_enroll.enroll_number,
                                                          user_enroll.name,
                                                          start_date_time_unix=start_date_time_unix,
                                                          end_date_time_unix=end_date_time_unix,
                                                          auth_mode_config=auth_mode_config
                                                          )
        if enroll_response.response_code == '1':
            if device_action:
                device_action.user_enrolled = True

            # set finger
            if device_action.execute_set_finger:

                # decode all fingers binary fields from base64 to bytes
                finger1_temp = []
                finger2_temp = []
                if self.finger1_template1:
                    finger1_temp.append(bytes(base64.b64decode(self.finger1_template1)))
                if self.finger1_template2:
                    finger1_temp.append(bytes(base64.b64decode(self.finger1_template2)))

                if self.finger2_template1:
                    finger2_temp.append(bytes(base64.b64decode(self.finger2_template1)))
                if self.finger2_template2:
                    finger2_temp.append(bytes(base64.b64decode(self.finger2_template2)))

                fingers_temp = []
                if len(finger1_temp) > 0:
                    fingers_temp.append(finger1_temp)
                if len(finger2_temp) > 0:
                    fingers_temp.append(finger2_temp)

                # templates = [bytes(base64.b64decode(temp)) for temp in templates_arr if temp]

                if len(fingers_temp) > 0:
                    set_finger_res = self.client_gateway.set_finger(device_info,
                                                                    self.enroll_number,
                                                                    fingers_temp)
                    if set_finger_res.response_code == '1':
                        if device_action:
                            device_action.finger_enrolled = True
                        # self.reset_error()
                    else:
                        enroll_response.error_message = f"{set_finger_res.error_message}"
                        enroll_response.response_code = set_finger_res.response_code
                    # self.assign_error(set_finger_res)

            # set face
            if device_action.execute_set_face and user_enroll.face_data:
                face_templates = base64.b64decode(self.face_data)
                set_face_res = self.client_gateway.set_face(device_info,
                                                            self.enroll_number,
                                                            face_templates)
                if set_face_res.response_code == '1':
                    if device_action:
                        device_action.face_enrolled = True
                # self.reset_error()
                else:
                    enroll_response.error_message = f"{error_msg},{set_face_res.error_message}"
                    enroll_response.response_code = set_face_res.response_code
                    # self.assign_error(set_face_res)

            # set card
            if device_action.execute_set_card and user_enroll.card_data:
                card_data = base64.b64decode(self.card_data)
                card_data_obj = self.client_gateway.create_csn_card_data(self.card_size, card_data,
                                                                         self.card_type)
                set_card_res = self.client_gateway.set_card(device_info,
                                                            self.enroll_number,
                                                            card_data_obj)
                if set_card_res.response_code == '1':
                    if device_action:
                        device_action.card_enrolled = True
                else:
                    enroll_response.error_message = f"{error_msg},{set_card_res.error_message}"
                    enroll_response.response_code = set_card_res.response_code
                    # self.assign_error(set_card_res)

            # set access group
            if device_action.execute_set_ac_group:
                if device_action.ac_group_id:
                    self.ac_groups_ids += device_action.ac_group_id  # link action access group with user access groups

                ac_group_ids = self.ac_groups_ids.mapped('group_number')
                set_ac_group_res = self.client_gateway.set_ac_groups(device_info, user_enroll.enroll_number,
                                                                     ac_group_ids)
                if set_ac_group_res.response_code == '1':
                    if device_action:
                        device_action.ac_group_enrolled = True
                        device_action.ac_group_id.apply_user(user_enroll)

                else:
                    enroll_response.error_message = f"{error_msg},{set_ac_group_res.error_message}"
                    enroll_response.response_code = set_ac_group_res.response_code
                    # self.assign_error(set_ac_group_res)

        return enroll_response

    def delete_user(self, device_info: DeviceInfo):
        delete_user_response = self.client_gateway.delete_user(device_info, [self.enroll_number])
        return delete_user_response

    def unlink_user(self, device_info: DeviceInfo, device_action):
        user_enroll = self
        device_action = device_action or self.env['ams_suprema.device_action']

        if (device_action.command_type == 'set_ac_group'
                and device_action.action_type == 'unlink'
                and device_action.ac_group_id):

            ac_group_ids = set(self.ac_groups_ids.mapped('group_number'))
            new_group_ids = list(ac_group_ids - {device_action.ac_group_id.group_number})
            response = self.client_gateway.set_ac_groups(device_info, self.enroll_number, new_group_ids)
            if response.response_code == '1':
                self.ac_groups_ids -= device_action.ac_group_id
                device_action.ac_group_id.apply_user(user_enroll, False)

            return response

        # TODO:  unlink other user information her

        return self.client_gateway.UNKNOWN_RESPONSE

    def _prepare_user_vals(self, device, user_data, user):
        """ prepare user vals based on user data read from device and pass user if exist for data validation"""
        user = user or self.env['ams_suprema.user']
        """ user_data reference :
            https://supremainc.github.io/g-sdk/api/user/#user-information

             setting reference :
             https://supremainc.github.io/g-sdk/api/user/#user-setting
        }
        """
        # device = self.env['ams_suprema.device'].browse(device.id)
        if user_data is None:
            return {}

        user_vals = {}
        enroll_number = user_data.hdr.ID
        name = user_data.name
        user_vals.update({'enroll_number': enroll_number, 'name': name,
                          'enroll_device_id': device.id, 'sync_user': True,
                          'device_group_id': device.device_group_id.id,
                          'last_sync_time': datetime.datetime.now()})

        employee = self.env['hr.employee'].search([('enroll_number', '=', enroll_number)], limit=1)
        if employee:
            user_vals.update({'employee_id': employee.id, 'employee_number': employee.employee_number,
                              'user_type': 'employee'})

        if len(user_data.cards) > 0 and not user.card_data:
            csn_card = user_data.cards[0]
            card_data = base64.b64encode(csn_card.data)
            card_size = csn_card.size
            card_type = csn_card.type
            card_no = self.client_gateway.get_card_number(csn_card.data)
            user_vals.update(
                {'card_data': card_data, 'card_size': card_size,
                 'card_type': card_type, 'card_no': card_no})
        if len(user_data.fingers) > 0 and not user.finger1_saved:
            fp1 = user_data.fingers[0]
            fp2 = user_data.fingers[1] if len(user_data.fingers) > 1 else None
            if len(fp1.templates) > 0:
                fp1_temp1 = base64.b64encode(fp1.templates[0])
                fp1_temp2 = base64.b64encode(fp1.templates[1]) if len(fp1.templates) > 1 else None
                user_vals.update({'finger1_template1': fp1_temp1, 'finger1_template2': fp1_temp2})

            if fp2 and len(fp2.templates) > 0:
                fp2_temp1 = base64.b64encode(fp2.templates[0])
                fp2_temp2 = base64.b64encode(fp2.templates[1]) if len(fp2.templates) > 1 else None
                user_vals.update({'finger2_template1': fp2_temp1, 'finger2_template2': fp2_temp2})
        if len(user_data.faces) > 0 and not user.face_saved:
            face1 = user_data.faces[0]
            serialized_data = face1.SerializeToString()  # serialize face_pb2.FaceData
            face_data = base64.b64encode(serialized_data)
            user_vals.update({'face_data': face_data})

        if user_data.setting and not user:
            start_date = datetime.datetime.utcfromtimestamp(user_data.setting.startTime)
            end_date = datetime.datetime.utcfromtimestamp(user_data.setting.endTime)
            user_vals.update({'start_datetime': start_date, 'end_datetime': end_date})
            if device.ext_auth_supported:
                face_auth_mode_id = self.get_auth_mode_id(user_data.setting.faceAuthExtMode)
                fingerprint_auth_mode_id = self.get_auth_mode_id(user_data.setting.fingerAuthExtMode)
                card_auth_mode_id = self.get_auth_mode_id(user_data.setting.cardAuthExtMode)

                user_vals.update({'face_auth_mode_id': face_auth_mode_id,
                                  'fingerprint_auth_mode_id': fingerprint_auth_mode_id,
                                  'card_auth_mode_id': card_auth_mode_id})
            else:
                fingerprint_auth_mode_id = self.get_auth_mode_id(user_data.setting.biometricAuthMode)
                card_auth_mode_id = self.get_auth_mode_id(user_data.setting.cardAuthMode)
                # id_auth_mode_id = user_data.setting.IDAuthMode
                user_vals.update({'fingerprint_auth_mode_id': fingerprint_auth_mode_id,
                                  'card_auth_mode_id': card_auth_mode_id})

        return user_vals

    def get_auth_mode_id(self, mode_enum):
        """ get auth mode from enum value integer"""

        if mode_enum is None or mode_enum == 255:  # 255 undefined
            return False

        code = self.client_gateway.get_auth_mode_str(mode_enum)
        if code is None:
            return False

        auth_mode = self.env['ams_suprema.auth_mode'].search([('code', '=', code)], limit=1)
        if auth_mode:
            return auth_mode.id

        return False

    # endregion
