# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class AuthMode(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.auth_mode"
    _description = "Suprema Auth Mode"
    _sql_constraints = [('code_unique', 'unique(code)', 'Code must be unique!'),
                        ('name_unique', 'check(1=1)', 'Name must be unique!')]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    code = fields.Char(string='Code', required=True, help="Code", index=True)
    name = fields.Char(string='Auth Mode', required=True, help="Auth Mode")
    extension_supported = fields.Boolean(string='Ext Supported', help="Extension Supported")

    # selection card , fingerprint , face , id , biometric
    category_type = fields.Selection([('card', 'Card'), ('fingerprint', 'Fingerprint'),
                                      ('face', 'Face'), ('id', 'ID'), ('biometric', 'Biometric')])

    activate = fields.Boolean(string='Activate', help="Activate")
    mapping_auth_mode_id = fields.Many2one('ams_suprema.auth_mode', string='Mapping Auth Mode',
                                           help="Use to map authentication from extension support | Not supported")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
