class AuthMode(object):
    AUTH_MODES = [
        ('0', 'AUTH_MODE_BIOMETRIC_ONLY'), ('1', 'AUTH_MODE_BIOMETRIC_PIN'),
        ('2', 'AUTH_MODE_CARD_ONLY'), ('3', 'AUTH_MODE_CARD_BIOMETRIC'),
        ('4', 'AUTH_MODE_CARD_PIN'), ('5', 'AUTH_MODE_CARD_BIOMETRIC_OR_PIN'),
        ('6', 'AUTH_MODE_CARD_BIOMETRIC_PIN'), ('7', 'AUTH_MODE_ID_BIOMETRIC'),
        ('8', 'AUTH_MODE_ID_PIN'), ('9', 'AUTH_MODE_ID_BIOMETRIC_OR_PIN'),
        ('10', 'AUTH_MODE_ID_BIOMETRIC_PIN'), ('11', 'AUTH_MODE_NONE'),
        ('12', 'AUTH_MODE_PROHIBITED'), ('13', 'AUTH_EXT_MODE_FACE_ONLY'),
        ('14', 'AUTH_EXT_MODE_FACE_FINGERPRINT'), ('15', 'AUTH_EXT_MODE_FACE_PIN'),
        ('16', 'AUTH_EXT_MODE_FACE_FINGERPRINT_OR_PIN'), ('17', 'AUTH_EXT_MODE_FACE_FINGERPRINT_PIN'),
        ('18', 'AUTH_EXT_MODE_FINGERPRINT_ONLY'), ('19', 'AUTH_EXT_MODE_FINGERPRINT_FACE'),
        ('20', 'AUTH_EXT_MODE_FINGERPRINT_PIN'), ('21', 'AUTH_EXT_MODE_FINGERPRINT_FACE_OR_PIN'),
        ('22', 'AUTH_EXT_MODE_FINGERPRINT_FACE_PIN'), ('23', 'AUTH_EXT_MODE_CARD_ONLY'),
        ('25', 'AUTH_EXT_MODE_CARD_FINGERPRINT'), ('26', 'AUTH_EXT_MODE_CARD_PIN'),
        ('27', 'AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT'), ('28', 'AUTH_EXT_MODE_CARD_FACE_OR_PIN'),
        ('29', 'AUTH_EXT_MODE_CARD_FINGERPRINT_OR_PIN'), ('30', 'AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT_OR_PIN'),
        ('31', 'AUTH_EXT_MODE_CARD_FACE_FINGERPRINT'), ('32', 'AUTH_EXT_MODE_CARD_FACE_PIN'),
        ('33', 'AUTH_EXT_MODE_CARD_FINGERPRINT_FACE'), ('34', 'AUTH_EXT_MODE_CARD_FINGERPRINT_PIN'),
        ('35', 'AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT_PIN'), ('36', 'AUTH_EXT_MODE_CARD_FACE_FINGERPRINT_OR_PIN'),
        ('37', 'AUTH_EXT_MODE_CARD_FINGERPRINT_FACE_OR_PIN'), ('38', 'AUTH_EXT_MODE_ID_FACE'),
        ('39', 'AUTH_EXT_MODE_ID_FINGERPRINT'), ('40', 'AUTH_EXT_MODE_ID_PIN'),
        ('41', 'AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT'), ('42', 'AUTH_EXT_MODE_ID_FACE_OR_PIN'),
        ('43', 'AUTH_EXT_MODE_ID_FINGERPRINT_OR_PIN'), ('44', 'AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT_OR_PIN'),
        ('45', 'AUTH_EXT_MODE_ID_FACE_FINGERPRINT'), ('46', 'AUTH_EXT_MODE_ID_FACE_PIN'),
        ('47', 'AUTH_EXT_MODE_ID_FINGERPRINT_FACE'), ('48', 'AUTH_EXT_MODE_ID_FINGERPRINT_PIN'),
        ('49', 'AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT_PIN'), ('50', 'AUTH_EXT_MODE_ID_FACE_FINGERPRINT_OR_PIN'),
        ('51', 'AUTH_EXT_MODE_ID_FINGERPRINT_FACE_OR_PIN')
    ]

    # region AUTH_MODES
    AUTH_MODES_BIO_SELECTION = [
                    ('AUTH_MODE_BIOMETRIC_ONLY', 'Fingerprint or Face'),
                    ('AUTH_MODE_BIOMETRIC_PIN', '(Fingerprint or Face) + PIN')
            ]
    AUTH_MODES_CARD_SELECTION = [
            ('AUTH_MODE_CARD_ONLY', 'Card'),
            ('AUTH_MODE_CARD_BIOMETRIC', 'Card + (Fingerprint or Face)'),
            ('AUTH_MODE_CARD_PIN', 'Card + PIN'),
            ('AUTH_MODE_CARD_BIOMETRIC_OR_PIN', 'Card + (Fingerprint or Face or PIN)'),
            ('AUTH_MODE_CARD_BIOMETRIC_PIN', 'Card + (Fingerprint or Face) + PIN')
    ]

    AUTH_MODES_ID_SELECTION = [
            ('AUTH_MODE_ID_BIOMETRIC', 'ID + (Fingerprint or Face)'),
            ('AUTH_MODE_ID_PIN', 'ID + PIN'),
            ('AUTH_MODE_ID_BIOMETRIC_OR_PIN', 'ID + (Fingerprint or Face or PIN)'),
            ('AUTH_MODE_ID_BIOMETRIC_PIN', 'ID + (Fingerprint or Face) + PIN'),
            ('0xFE', 'Not permitted'),
            ('0xFF', 'Undefined. Use the settings of AuthConfig')
    ]

    # endregion
    # region AUTH_EXT_MODE for recent device F2 or BioStation3
    AUTH_MODES_EXT_SELECTION = [
        ('AUTH_EXT_MODE_FACE_ONLY', 'Face'),
        ('AUTH_EXT_MODE_FACE_FINGERPRINT', 'Face + Fingerprint'),
        ('AUTH_EXT_MODE_FACE_PIN', 'Face + PIN'),
        ('AUTH_EXT_MODE_FACE_FINGERPRINT_OR_PIN', 'Face + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_FACE_FINGERPRINT_PIN', 'Face + Fingerprint + PIN'),
        ('AUTH_EXT_MODE_FINGERPRINT_ONLY', 'Fingerprint'),
        ('AUTH_EXT_MODE_FINGERPRINT_FACE', 'Fingerprint + Face'),
        ('AUTH_EXT_MODE_FINGERPRINT_PIN', 'Fingerprint + PIN'),
        ('AUTH_EXT_MODE_FINGERPRINT_FACE_OR_PIN', 'Fingerprint + (Face or PIN)'),
        ('AUTH_EXT_MODE_FINGERPRINT_FACE_PIN', 'Fingerprint + Face + PIN'),
        ('AUTH_EXT_MODE_CARD_ONLY', 'Card'),
        ('AUTH_EXT_MODE_CARD_FACE', 'Card + Face'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT', 'Card + Fingerprint'),
        ('AUTH_EXT_MODE_CARD_PIN', 'Card + PIN'),
        ('AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT', 'Card + (Face or Fingerprint)'),
        ('AUTH_EXT_MODE_CARD_FACE_OR_PIN', 'Card + (Face or PIN)'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT_OR_PIN', 'Card + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT_OR_PIN', 'Card + (Face or Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_CARD_FACE_FINGERPRINT', 'Card + Face + Fingerprint'),
        ('AUTH_EXT_MODE_CARD_FACE_PIN', 'Card + Face + PIN'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT_FACE', 'Card + Fingerprint + Face'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT_PIN', 'Card + Fingerprint + PIN'),
        ('AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT_PIN', 'Card + (Face or Fingerprint) + PIN'),
        ('AUTH_EXT_MODE_CARD_FACE_FINGERPRINT_OR_PIN', 'Card + Face + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT_FACE_OR_PIN', 'Card + Fingerprint + (Face or PIN)'),
        ('AUTH_EXT_MODE_ID_FACE', 'ID + Face'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT', 'ID + Fingerprint'),
        ('AUTH_EXT_MODE_ID_PIN', 'ID + PIN'),
        ('AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT', 'ID + (Face or Fingerprint)'),
        ('AUTH_EXT_MODE_ID_FACE_OR_PIN', 'ID + (Face or PIN)'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT_OR_PIN', 'ID + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT_OR_PIN', 'ID + (Face or Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_ID_FACE_FINGERPRINT', 'ID + Face + Fingerprint'),
        ('AUTH_EXT_MODE_ID_FACE_PIN', 'ID + Face + PIN'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT_FACE', 'ID + Fingerprint + Face'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT_PIN', 'ID + Fingerprint + PIN'),
        ('AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT_PIN', 'ID + (Face or Fingerprint) + PIN'),
        ('AUTH_EXT_MODE_ID_FACE_FINGERPRINT_OR_PIN', 'ID + Face + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT_FACE_OR_PIN', 'ID + Fingerprint + (Face or PIN)'),
        ('0xFE', 'Not permitted'),
        ('0xFF', 'Undefined. Use the settings of AuthConfig'),
    ]

    AUTH_MODES_EXT_FACE_SELECTION = [
        ('AUTH_EXT_MODE_FACE_ONLY', 'Face'),
        ('AUTH_EXT_MODE_FACE_FINGERPRINT', 'Face + Fingerprint'),
        ('AUTH_EXT_MODE_FACE_PIN', 'Face + PIN'),
        ('AUTH_EXT_MODE_FACE_FINGERPRINT_OR_PIN', 'Face + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_FACE_FINGERPRINT_PIN', 'Face + Fingerprint + PIN'),
        ('0xFE', 'Not permitted'),
        ('0xFF', 'Undefined. Use the settings of AuthConfig'),
    ]

    AUTH_MODES_EXT_FINGER_SELECTION = [
        ('AUTH_EXT_MODE_FINGERPRINT_ONLY', 'Fingerprint'),
        ('AUTH_EXT_MODE_FINGERPRINT_FACE', 'Fingerprint + Face'),
        ('AUTH_EXT_MODE_FINGERPRINT_PIN', 'Fingerprint + PIN'),
        ('AUTH_EXT_MODE_FINGERPRINT_FACE_OR_PIN', 'Fingerprint + (Face or PIN)'),
        ('AUTH_EXT_MODE_FINGERPRINT_FACE_PIN', 'Fingerprint + Face + PIN'),
        ('0xFE', 'Not permitted'),
        ('0xFF', 'Undefined. Use the settings of AuthConfig'),
    ]

    AUTH_MODES_EXT_CARD_SELECTION = [
        ('AUTH_EXT_MODE_CARD_ONLY', 'Card'),
        ('AUTH_EXT_MODE_CARD_FACE', 'Card + Face'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT', 'Card + Fingerprint'),
        ('AUTH_EXT_MODE_CARD_PIN', 'Card + PIN'),
        ('AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT', 'Card + (Face or Fingerprint)'),
        ('AUTH_EXT_MODE_CARD_FACE_OR_PIN', 'Card + (Face or PIN)'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT_OR_PIN', 'Card + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT_OR_PIN', 'Card + (Face or Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_CARD_FACE_FINGERPRINT', 'Card + Face + Fingerprint'),
        ('AUTH_EXT_MODE_CARD_FACE_PIN', 'Card + Face + PIN'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT_FACE', 'Card + Fingerprint + Face'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT_PIN', 'Card + Fingerprint + PIN'),
        ('AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT_PIN', 'Card + (Face or Fingerprint) + PIN'),
        ('AUTH_EXT_MODE_CARD_FACE_FINGERPRINT_OR_PIN', 'Card + Face + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_CARD_FINGERPRINT_FACE_OR_PIN', 'Card + Fingerprint + (Face or PIN)'),
        ('0xFE', 'Not permitted'),
        ('0xFF', 'Undefined. Use the settings of AuthConfig'),
    ]

    AUTH_MODES_EXT_ID_SELECTION = [
        ('AUTH_EXT_MODE_ID_FACE', 'ID + Face'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT', 'ID + Fingerprint'),
        ('AUTH_EXT_MODE_ID_PIN', 'ID + PIN'),
        ('AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT', 'ID + (Face or Fingerprint)'),
        ('AUTH_EXT_MODE_ID_FACE_OR_PIN', 'ID + (Face or PIN)'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT_OR_PIN', 'ID + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT_OR_PIN', 'ID + (Face or Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_ID_FACE_FINGERPRINT', 'ID + Face + Fingerprint'),
        ('AUTH_EXT_MODE_ID_FACE_PIN', 'ID + Face + PIN'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT_FACE', 'ID + Fingerprint + Face'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT_PIN', 'ID + Fingerprint + PIN'),
        ('AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT_PIN', 'ID + (Face or Fingerprint) + PIN'),
        ('AUTH_EXT_MODE_ID_FACE_FINGERPRINT_OR_PIN', 'ID + Face + (Fingerprint or PIN)'),
        ('AUTH_EXT_MODE_ID_FINGERPRINT_FACE_OR_PIN', 'ID + Fingerprint + (Face or PIN)'),
        ('0xFE', 'Not permitted'),
        ('0xFF', 'Undefined. Use the settings of AuthConfig'),
    ]
    # endregion
    @classmethod
    def get_auth_modes(cls):
        return cls.AUTH_MODES
