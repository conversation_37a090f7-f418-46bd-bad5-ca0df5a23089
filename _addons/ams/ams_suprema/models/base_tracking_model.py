# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class TrackingModel(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.tracking_model"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Suprema Tracking Model"
    # endregion


