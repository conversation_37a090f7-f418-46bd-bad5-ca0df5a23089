# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BasePunchLog(models.AbstractModel):
    _name = "ams_base.base_punch_log"
    _description = "Base Punch Log"
    _abstract = True

    device_number = fields.Char(string="Device No", index=True)
    suprema_device_id = fields.Many2one('ams_suprema.device', string="Device" ,store=True,
                                        compute='_compute_device_id')

    @api.depends('device_number')
    def _compute_device_id(self):
        for rec in self:
            rec.suprema_device_id = self.env['ams_suprema.device'].search([('device_id', '=', rec.device_number)], limit=1)


