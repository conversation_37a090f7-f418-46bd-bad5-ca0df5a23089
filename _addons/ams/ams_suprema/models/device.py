# -*- coding: utf-8 -*-
import datetime
import json

from odoo import api, fields, models, _
from odoo.addons.ams_suprema import suprema_client as sc
from odoo.exceptions import UserError, ValidationError


class Device(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.device"
    _inherit = ["ams_base.device", "ams_suprema.connector", 'ams_suprema.base_model', 'ams_suprema.base_user_auth_mode',
                'mail.thread', 'mail.activity.mixin']
    _description = "Device"

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    dedicated = fields.Boolean(default=False, help="in case checked open connection without close a previous one"
                                                   " in case unchecked open connection and close after execute task")
    is_public = fields.Boolean(default=False, help="if checked all users in system will enrolled on this device")
    is_enroll = fields.Boolean(default=False, help="checked to allow this device for enroll process")

    mac_addr = fields.Char(string="Mac Address")
    model_name = fields.Char()
    type = fields.Char()
    firmware_version = fields.Char(string="Firmware Version")
    kernel_version = fields.Char(string="Kernel Version")
    bs_core_version = fields.Char(string="BS Core Version")
    board_version = fields.Char()

    # device_cap_data = fields.Json(string='Device Capabilities')
    device_cap_json = fields.Text(string='Device Capabilities', readonly=True)

    ext_auth_supported = fields.Boolean('ExtAuth Supported', store=True, readonly=False,
                                        compute='_compute_ext_auth_supported')  # to be override and compute
    use_private_auth = fields.Boolean()
    face_supported = fields.Boolean(compute='_compute_face_supported', store=True)
    fingerprint_supported = fields.Boolean(compute='_compute_fingerprint_supported', store=True)
    card_supported = fields.Boolean(compute='_compute_card_supported', store=True)
    id_supported = fields.Boolean(compute='_compute_id_supported', store=True)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    device_group_id = fields.Many2one('ams_base.device_group', string="Device Group",
                                      default=lambda self: [(4, self.env.ref('ams_base.default_device_group',
                                                                             raise_if_not_found=False).id)]
                                      )

    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ------------- TODO[IMP]:Default Methods and Properties ----------------------
    @property
    def _device_id(self) -> int:
        """return device serial number"""
        return int(self.device_id) if self.device_id else 0

    @property
    def device_id_info(self) -> sc.DeviceInfo:
        """return DeviceInfo object contain  ip, port,device id, and validate device id is required"""
        if not self.device_id:
            raise UserError(_('Device ID is required to open connection with device'))
        return sc.DeviceInfo(ip_address=self.ip, device_id=self._device_id, port=self.port, name=self.name,
                             dedicated=self.dedicated, cab_info_json_data=self.cap_info_json_data)

    @property
    def device_ip_info(self) -> sc.DeviceInfo:
        """return DeviceInfo object contain  ip, port,device id, and validate device ip and port is required"""
        if not self.ip or not self.port:
            raise UserError(_('IP and Port are required to open connection'))

        return sc.DeviceInfo(ip_address=self.ip, device_id=self._device_id, port=self.port, name=self.name,
                             dedicated=self.dedicated, cab_info_json_data=self.cap_info_json_data)

    @property
    def device_label(self):
        return f"[{self.device_id}] {self.name}"

    @property
    def cap_info_json_data(self):
        # return json date from device_cap_json string
        if self.device_cap_json:
            try:
                return json.loads(self.device_cap_json)
            except:
                return {}
        return {}

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('device_cap_json')
    def _compute_ext_auth_supported(self):
        for rec in self:
            if rec.device_id and rec.device_cap_json:
                rec.ext_auth_supported = rec.device_id_info.extended_auth_supported

    @api.depends('device_cap_json')
    def _compute_face_supported(self):
        for rec in self:
            if rec.device_id and rec.device_cap_json:
                rec.face_supported = rec.device_id_info.face_input_supported

    @api.depends('device_cap_json')
    def _compute_fingerprint_supported(self):
        for rec in self:
            if rec.device_id and rec.device_cap_json:
                rec.fingerprint_supported = rec.device_id_info.fingerprint_input_supported

    @api.depends('device_cap_json')
    def _compute_card_supported(self):
        for rec in self:
            if rec.device_id and rec.device_cap_json:
                rec.card_supported = rec.device_id_info.card_input_supported

    @api.depends('device_cap_json')
    def _compute_id_supported(self):
        for rec in self:
            if rec.device_id and rec.device_cap_json:
                rec.id_supported = rec.device_id_info.id_input_supported

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_connect(self):
        """ trying to connect device to check status  """
        device_connection_res = self.client_gateway.connect_device(device_info=self.device_ip_info)

        if device_connection_res.response_code == '1':
            self.state = 'online'
            self.reset_error()
            self.status = "tcp_connected"
        else:
            self.handle_device_state(device_connection_res)
            self.assign_error(device_connection_res)

    def action_check_status(self):
        """ trying to connect device to check status and get device info"""
        device_connection_res = self.client_gateway.connect_device(device_info=self.device_ip_info)

        if device_connection_res.response_code == '1':
            self.state = 'online'
            self.reset_error()
            self.status = "tcp_connected"
            # get_device_info
            self.device_id = device_connection_res.result
            device_info_res = self.client_gateway.get_device_info(self.device_id_info)
            if device_info_res.response_code == '1':
                info = device_info_res.result
                self.mac_addr = info.MACAddr
                self.model_name = info.modelName
                self.firmware_version = info.firmwareVersion
                self.kernel_version = info.kernelVersion
                self.bs_core_version = info.BSCoreVersion
                self.board_version = info.boardVersion
            else:
                self.assign_error(device_info_res)
            # get_device_cap_info -- not all devices support GetCapability
            cap_res = self.client_gateway.get_device_cap(device_info=self.device_id_info)

            if cap_res.response_code == '1':
                self.device_cap_json = self.client_gateway.message_to_json(cap_res.result)
                self.create_default_ports()
            else:
                cap_info_res = self.client_gateway.get_device_cap_info(device_info=self.device_id_info)
                if cap_info_res.response_code == '1':
                    self.device_cap_json = self.client_gateway.message_to_json(cap_info_res.result)
                    self.create_default_ports()
                else:
                    self.assign_error(cap_info_res)
                    # self.assign_error(cap_res)

            # call get_device_info() and get_device_capability
        else:
            self.handle_device_state(device_connection_res)
            self.assign_error(device_connection_res)

    def action_disconnect(self):
        disconnect_response = self.client_gateway.disconnect_device(device_info=self.device_id_info)
        if disconnect_response.response_code == '1':
            self.reset_error()
            self.status = 'tcp_disconnected'
            self.state = 'offline'

        else:
            self.assign_error(disconnect_response)

    def handle_device_state(self, response):
        if response.response_code == '1100':  # 1100 is connection error
            self.state = 'offline'
            self.status = 'tcp_disconnected'
        else:
            self.state = 'online'
            self.status = 'tcp_connected'

    def action_get_logs(self):
        """collect logs from selected devices"""
        for device in self:
            self.env['ams_suprema.device_log'].sudo()._collect_device_logs(device)

    def action_clear_logs(self):
        """clear device logs"""
        if not self.device_id:
            raise UserError(_('No device'))

        clear_logs_res = self.client_gateway.clear_log(device_info=self.device_id_info)
        if clear_logs_res == '1':
            self.reset_error()
            return {
                'effect': {
                    'fadeout': 'slow',
                    'message': "Logs Deleted",
                    'img_url': '/web/static/img/smile.svg',
                    'type': 'rainbow_man',
                }
            }
        else:
            self.handle_device_state(clear_logs_res)
            self.assign_error(clear_logs_res)

    def action_close_gateway_channel(self):
        close_response = self.client_gateway.close_gateway_channel()
        if close_response.response_code != '1':
            self.assign_error(close_response)
        else:
            self.reset_error()

    def action_start_gateway_channel(self):
        start_response = self.client_gateway.start_gateway_channel()
        if start_response.response_code != '1':
            self.assign_error(start_response)
        else:
            self.reset_error()

    def action_open_device_logs_views(self):
        return {
            'name': 'Device Logs',
            'view_mode': 'list,form',
            'res_model': 'ams_suprema.device_log',
            'type': 'ir.actions.act_window',
            'domain': [('device_id', '=', self.device_id)],
        }

    def action_open_device_action_views(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Device Actions'),
            'res_model': 'ams_suprema.device_action',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': [('device_id', '=', self.id)]
        }

    def action_open_device_users_views(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Device Users'),
            'res_model': 'ams_suprema.device_user',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': [('device_id', '=', self.id)]
        }

    def action_set_config(self):
        """ set configuration for device like default authentication mode"""

        default_auth_modes = []
        if self.card_auth_mode_id:
            default_auth_modes.append(self.card_auth_mode_id.code)
        if self.fingerprint_auth_mode_id:
            default_auth_modes.append(self.fingerprint_auth_mode_id.code)
        if self.face_auth_mode_id:
            default_auth_modes.append(self.face_auth_mode_id.code)

        set_config_res = self.client_gateway.set_config(self.device_id_info,
                                                        use_private_auth=self.use_private_auth,
                                                        default_auth_modes=default_auth_modes)
        if set_config_res.response_code != '1':
            self.assign_error(set_config_res)
        else:
            self.reset_error()
            self.handle_device_state(set_config_res)
            return self._notify_success()

    def action_sync_users(self):
        """ read users from device and create/update them in system """
        response = self.client_gateway.get_users(self.device_id_info)
        if response.response_code == '1':
            self.reset_error()
            users = response.result
            new_users_ids = []
            # get new users ids from device
            for user_data in users:
                if user_data.ID != '1':  # 1 reserved for administrator
                    new_users_ids.append(user_data.ID)

                # if not user:
                #     new_users_ids.append(user_data.ID)

            # get users info from device
            user_info_res = self.client_gateway.get_users_info(self.device_id_info, new_users_ids)
            if user_info_res.response_code == '1':
                self.reset_error()
                users_info = user_info_res.result
                users = self.env['ams_suprema.user']  # list users created or updated
                device = self
                batch_no = fields.Datetime.context_timestamp(self, datetime.datetime.now()).strftime("%Y%m%d%H%M%S")
                for user_info in users_info:
                    user = self.env['ams_suprema.user'].search([('enroll_number', '=', user_info.hdr.ID)], limit=1)
                    user_vals = self.env['ams_suprema.user']._prepare_user_vals(device, user_info, user)
                    user_vals.update({'last_batch_no': batch_no})
                    if user:
                        # TODO : don't override biometric info if exists
                        user.update(user_vals)
                    else:
                        user = self.env['ams_suprema.user'].create(user_vals)

                    users += user  #

                self.env['ams_suprema.device_user'].save_device_users(device, users)  #

        else:
            self.assign_error(response)

    def action_upload_users(self):
        """upload users to device based on default device group"""

        if not self.device_group_id:
            raise UserError(_("Please select device group"))

        if self.state == 'offline':
            raise UserError(_("Device is offline , please connect device first"))

        # search about suprema user based on device group
        users = self.env['ams_suprema.user'].search([
            ('device_group_id', '=', self.device_group_id.id)
        ])

        if not users:
            raise UserError(_("No users found in device group"))

        # create device action for enrollment
        self.env['ams_suprema.device_action']._create_device_action(self, users)

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def create_default_ports(self):
        """ override to create default ports for device based on device capability"""
        pass

    def auto_retry_devices_connect(self):
        """used by cron job to check devices connection every some minutes"""

        # read active offline devices and has error
        devices = self.env['ams_suprema.device'].sudo().search([('state', '=', 'offline'),
                                                               ('error', '=', True),
                                                               ('activate', '=', True)])
        if devices:
            self.logger.info("Auto retry connect for %s devices" % len(devices))
            for device in devices:
                device.sudo().action_connect()
    # endregion
