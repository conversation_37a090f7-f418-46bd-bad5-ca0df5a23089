# -*- coding: utf-8 -*-

from collections import defaultdict
from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class DeviceUser(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.device_user"
    _description = "Device User"
    _sql_constraints = [('unique_device_user', 'unique(device_id,user_enroll_id)', 'Device User must be unique')]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    user_enrolled = fields.Boolean()
    card_enrolled = fields.Bo<PERSON>an()
    finger_enrolled = fields.<PERSON><PERSON><PERSON>()
    face_enrolled = fields.Boolean()
    ac_group_enrolled = fields.Boolean()
    error = fields.Boolean()
    error_msg = fields.Char(string="Error Message")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    device_id = fields.Many2one('ams_suprema.device', string="Device", required=True, index=True, ondelete='cascade')
    user_enroll_id = fields.Many2one('ams_suprema.user', string="User", required=True, index=True, ondelete='cascade')
    device_no = fields.Char(string="Device ID", related="device_id.device_id", store=True)
    enroll_number = fields.Char(string="Enroll No", related="user_enroll_id.enroll_number", store=True)

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    def _compute_display_name(self):
        for record in self:
            name = "New User"
            if record.user_enroll_id and record.device_id:
                name = f"{record.user_enroll_id.name} - {record.device_id.name}"

            record.display_name = name

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # def unlink(self):
    #     """delete only success users deleted from device"""
    #     device_users = self.env['ams_suprema.device_user']
    #     users = self.env['ams_suprema.user']
    #     devices = self.env['ams_suprema.device']
    #     for record in self:
    #         if record.device_id and record.user_enroll_id:
    #             response = record.user_enroll_id.delete_user(record.device_id.device_id_info)
    #             if response.response_code == '1':
    #                 device_users += record
    #                 users += record.user_enroll_id
    #                 devices += record.device_id
    #
    #             else:
    #                 record.error = True
    #                 record.error_msg = response.error_message
    #
    #     if device_users:
    #         new_vals = {'action_type': 'delete', 'state': 'executed'}
    #         self.env['ams_suprema.device_action']._create_device_action(devices, users, new_vals, execute=False)
    #         res = device_users.unlink()
    #         return res
    #
    #     return True

    # return super(DeviceUser, self).unlink()

    def action_delete_device_users(self):
        """delete only success users deleted from device"""
        device_users = self.env['ams_suprema.device_user']
        # users = self.env['ams_suprema.user']
        # devices = self.env['ams_suprema.device']
        device_groups = defaultdict(list)  # contain grouping per device  with related users
        for record in self:
            if record.device_id and record.user_enroll_id:
                response = record.user_enroll_id.delete_user(record.device_id.device_id_info)
                if response.response_code == '1':
                    device_users += record
                    # users += record.user_enroll_id
                    # devices += record.device_id
                    # Add record to the corresponding device group
                    device_groups[record.device_id].append(record.user_enroll_id)

                else:
                    record.error = True
                    record.error_msg = response.error_message

        if device_users:
            new_vals = {'action_type': 'delete', 'state': 'executed'}
            for device, grouped_users in device_groups.items():
                self.env['ams_suprema.device_action']._create_device_action(device, grouped_users, new_vals,
                                                                           execute=False)
            res = device_users.unlink()
            return res

        return True

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def sync_device_user(self, device_action):
        """add or update device user to sync data when device action executed"""
        device_action = device_action or self.env['ams_suprema.device_action']
        if device_action:
            if device_action.state == 'executed':
                device_user = self.env['ams_suprema.device_user'].search(
                    [('device_id', '=', device_action.device_id.id),
                     ('user_enroll_id', '=', device_action.user_enroll_id.id)], limit=1)
                # update device user info
                if device_action.action_type in ['add', 'unlink']:
                    if not device_user:
                        device_user = self.env['ams_suprema.device_user'].create({
                            'device_id': device_action.device_id.id,
                            'user_enroll_id': device_action.user_enroll_id.id
                        })

                    # update enrollment data status
                    syn_data = self.prepare_vals_to_sync(device_action)
                    device_user.update(syn_data)
                # delete device user
                elif device_action.action_type == 'delete':
                    if device_user:
                        device_user.unlink()

    def save_device_users(self, device, users):
        """used when sync user from device if  device user not exists create new one to link device with user"""
        users = users or self.env['ams_suprema.user']
        device = device or self.env['ams_suprema.device']
        for user in users:
            if device:
                device_user = self.env['ams_suprema.device_user'].search([
                    ('device_id', '=', device.id),
                    ('user_enroll_id', '=', user.id)
                ], limit=1)
                device_user_vals = self.prepare_vals_to_save(user, device)
                if device_user:
                    device_user.update(device_user_vals)
                else:
                    device_user = self.env['ams_suprema.device_user'].create(device_user_vals)

        # delete device users where users not found in actually sync users list in device
        device_users_to_delete = self.env['ams_suprema.device_user'].search([
            ('device_id', '=', device.id), ('user_enroll_id', 'not in', users.ids)
        ])
        if device_users_to_delete:
            device_users_to_delete.unlink()

    def prepare_vals_to_sync(self, device_action):
        """prepare data for sync device user to update enrollment data status"""
        device_action = device_action or self.env['ams_suprema.device_action']
        if not device_action.command_type:
            return {
                'user_enrolled': device_action.user_enrolled,
                'card_enrolled': device_action.card_enrolled,
                'finger_enrolled': device_action.finger_enrolled,
                'face_enrolled': device_action.face_enrolled,
                'ac_group_enrolled': device_action.ac_group_enrolled
            }
        if device_action.command_type == 'set_ac_group':
            return {
                'ac_group_enrolled': device_action.ac_group_enrolled
            }
        if device_action.command_type == 'set_face':
            return {
                'face_enrolled': device_action.face_enrolled
            }
        if device_action.command_type == 'set_card':
            return {
                'card_enrolled': device_action.card_enrolled
            }
        if device_action.command_type == 'set_finger':
            return {
                'finger_enrolled': device_action.finger_enrolled
            }

    def prepare_vals_to_save(self, user, device):
        """used when sync user from device  to prepare data for save"""
        user = user or self.env['ams_suprema.user']
        device = device or self.env['ams_suprema.device']
        if user and device:
            return {
                'user_enroll_id': user.id,
                'device_id': device.id,
                'user_enrolled': True,
                'card_enrolled': True if user.card_data else False,
                'finger_enrolled': True if user.finger1_saved else False,
                'face_enrolled': True if user.face_saved else False,
            }
        # 'ac_group_enrolled': user.ac_group_enrolled

    # endregion
