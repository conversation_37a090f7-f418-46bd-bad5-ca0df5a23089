{"EventCollection": {"rows": [{"id": "1521", "server_datetime": "2023-03-05T12:43:28.00Z", "datetime": "2023-03-05T09:43:27.00Z", "index": "144069", "user_id_name": "203(<PERSON>)", "user_id": {"user_id": "203", "name": "<PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1", "name": "All Users"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800940705441687630000144069"}, {"id": "1522", "server_datetime": "2023-03-05T12:43:28.00Z", "datetime": "2023-03-05T09:43:27.00Z", "index": "144070", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800940705441687630000144070", "user_id": {"photo_exists": "false"}}, {"id": "1523", "server_datetime": "2023-03-05T12:43:31.00Z", "datetime": "2023-03-05T09:43:30.00Z", "index": "144071", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800941005441687630000144071", "user_id": {"photo_exists": "false"}}, {"id": "1524", "server_datetime": "2023-03-05T12:43:34.00Z", "datetime": "2023-03-05T09:43:34.00Z", "index": "144072", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800941405441687630000144072"}, {"id": "1525", "server_datetime": "2023-03-05T12:45:55.00Z", "datetime": "2023-03-05T09:45:56.00Z", "index": "47090", "user_id_name": "15(<PERSON><PERSON>)", "user_id": {"user_id": "15", "name": "Calim", "photo_exists": "false"}, "user_group_id": {"id": "1", "name": "All Users"}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "4865"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800955605416248960000047090"}, {"id": "1526", "server_datetime": "2023-03-05T12:45:55.00Z", "datetime": "2023-03-05T09:45:56.00Z", "index": "47091", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800955605416248960000047091", "user_id": {"photo_exists": "false"}}, {"id": "1527", "server_datetime": "2023-03-05T12:45:58.00Z", "datetime": "2023-03-05T09:45:59.00Z", "index": "47092", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800955905416248960000047092", "user_id": {"photo_exists": "false"}}, {"id": "1528", "server_datetime": "2023-03-05T12:48:29.00Z", "datetime": "2023-03-05T09:48:28.00Z", "index": "144073", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800970805441687630000144073", "user_id": {"photo_exists": "false"}}, {"id": "1529", "server_datetime": "2023-03-05T12:48:31.00Z", "datetime": "2023-03-05T09:48:31.00Z", "index": "144074", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800971105441687630000144074", "user_id": {"photo_exists": "false"}}, {"id": "1530", "server_datetime": "2023-03-05T12:48:34.00Z", "datetime": "2023-03-05T09:48:33.00Z", "index": "144075", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167800971305441687630000144075"}, {"id": "1531", "server_datetime": "2023-03-05T12:58:10.00Z", "datetime": "2023-03-05T09:58:09.00Z", "index": "144076", "user_id_name": "7(<PERSON><PERSON><PERSON>)", "user_id": {"user_id": "7", "name": "<PERSON><PERSON><PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1029", "name": "Genius Valley"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801028905441687630000144076"}, {"id": "1532", "server_datetime": "2023-03-05T12:58:10.00Z", "datetime": "2023-03-05T09:58:09.00Z", "index": "144077", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801028905441687630000144077", "user_id": {"photo_exists": "false"}}, {"id": "1533", "server_datetime": "2023-03-05T12:58:13.00Z", "datetime": "2023-03-05T09:58:12.00Z", "index": "144078", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801029205441687630000144078", "user_id": {"photo_exists": "false"}}, {"id": "1534", "server_datetime": "2023-03-05T12:58:16.00Z", "datetime": "2023-03-05T09:58:15.00Z", "index": "144079", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801029505441687630000144079"}, {"id": "1535", "server_datetime": "2023-03-05T13:02:18.00Z", "datetime": "2023-03-05T10:02:17.00Z", "index": "144080", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801053705441687630000144080", "user_id": {"photo_exists": "false"}}, {"id": "1536", "server_datetime": "2023-03-05T13:02:21.00Z", "datetime": "2023-03-05T10:02:20.00Z", "index": "144081", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801054005441687630000144081", "user_id": {"photo_exists": "false"}}, {"id": "1537", "server_datetime": "2023-03-05T13:02:23.00Z", "datetime": "2023-03-05T10:02:22.00Z", "index": "144082", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801054205441687630000144082"}, {"id": "1538", "server_datetime": "2023-03-05T13:07:21.00Z", "datetime": "2023-03-05T10:07:20.00Z", "index": "144083", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "5124"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801084005441687630000144083", "user_id": {"photo_exists": "false"}}, {"id": "1539", "server_datetime": "2023-03-05T13:07:25.00Z", "datetime": "2023-03-05T10:07:24.00Z", "index": "144084", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "5124"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801084405441687630000144084", "user_id": {"photo_exists": "false"}}, {"id": "1540", "server_datetime": "2023-03-05T13:07:29.00Z", "datetime": "2023-03-05T10:07:28.00Z", "index": "144085", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "5124"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801084805441687630000144085", "user_id": {"photo_exists": "false"}}, {"id": "1541", "server_datetime": "2023-03-05T13:07:32.00Z", "datetime": "2023-03-05T10:07:31.00Z", "index": "144086", "user_id_name": "17(<PERSON><PERSON>)", "user_id": {"user_id": "17", "name": "<PERSON><PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1", "name": "All Users"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801085105441687630000144086"}, {"id": "1542", "server_datetime": "2023-03-05T13:07:32.00Z", "datetime": "2023-03-05T10:07:31.00Z", "index": "144087", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801085105441687630000144087", "user_id": {"photo_exists": "false"}}, {"id": "1543", "server_datetime": "2023-03-05T13:07:34.00Z", "datetime": "2023-03-05T10:07:34.00Z", "index": "144088", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801085405441687630000144088", "user_id": {"photo_exists": "false"}}, {"id": "1544", "server_datetime": "2023-03-05T13:07:38.00Z", "datetime": "2023-03-05T10:07:38.00Z", "index": "144089", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801085805441687630000144089"}, {"id": "1545", "server_datetime": "2023-03-05T13:10:21.00Z", "datetime": "2023-03-05T10:10:20.00Z", "index": "144090", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801102005441687630000144090", "user_id": {"photo_exists": "false"}}, {"id": "1546", "server_datetime": "2023-03-05T13:10:24.00Z", "datetime": "2023-03-05T10:10:23.00Z", "index": "144091", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801102305441687630000144091", "user_id": {"photo_exists": "false"}}, {"id": "1547", "server_datetime": "2023-03-05T13:10:26.00Z", "datetime": "2023-03-05T10:10:25.00Z", "index": "144092", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801102505441687630000144092"}, {"id": "1548", "server_datetime": "2023-03-05T13:12:01.00Z", "datetime": "2023-03-05T10:12:00.00Z", "index": "144093", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "5124"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801112005441687630000144093", "user_id": {"photo_exists": "false"}}, {"id": "1549", "server_datetime": "2023-03-05T13:12:03.00Z", "datetime": "2023-03-05T10:12:02.00Z", "index": "144094", "user_id_name": "202(<PERSON>)", "user_id": {"user_id": "202", "name": "<PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1", "name": "All Users"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801112205441687630000144094"}, {"id": "1550", "server_datetime": "2023-03-05T13:12:03.00Z", "datetime": "2023-03-05T10:12:02.00Z", "index": "144095", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801112205441687630000144095", "user_id": {"photo_exists": "false"}}, {"id": "1551", "server_datetime": "2023-03-05T13:12:06.00Z", "datetime": "2023-03-05T10:12:05.00Z", "index": "144096", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801112505441687630000144096", "user_id": {"photo_exists": "false"}}, {"id": "1552", "server_datetime": "2023-03-05T13:12:09.00Z", "datetime": "2023-03-05T10:12:08.00Z", "index": "144097", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801112805441687630000144097"}, {"id": "1553", "server_datetime": "2023-03-05T13:19:33.00Z", "datetime": "2023-03-05T10:19:32.00Z", "index": "144098", "user_id_name": "4(<PERSON><PERSON>)", "user_id": {"user_id": "4", "name": "<PERSON><PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1029", "name": "Genius Valley"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4102"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801157205441687630000144098"}, {"id": "1554", "server_datetime": "2023-03-05T13:19:33.00Z", "datetime": "2023-03-05T10:19:32.00Z", "index": "144099", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801157205441687630000144099", "user_id": {"photo_exists": "false"}}, {"id": "1555", "server_datetime": "2023-03-05T13:19:36.00Z", "datetime": "2023-03-05T10:19:35.00Z", "index": "144100", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801157505441687630000144100", "user_id": {"photo_exists": "false"}}, {"id": "1556", "server_datetime": "2023-03-05T13:19:38.00Z", "datetime": "2023-03-05T10:19:38.00Z", "index": "144101", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801157805441687630000144101"}, {"id": "1557", "server_datetime": "2023-03-05T13:24:59.00Z", "datetime": "2023-03-05T10:24:58.00Z", "index": "144102", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801189805441687630000144102", "user_id": {"photo_exists": "false"}}, {"id": "1558", "server_datetime": "2023-03-05T13:25:02.00Z", "datetime": "2023-03-05T10:25:01.00Z", "index": "144103", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801190105441687630000144103", "user_id": {"photo_exists": "false"}}, {"id": "1559", "server_datetime": "2023-03-05T13:25:03.00Z", "datetime": "2023-03-05T10:25:02.00Z", "index": "144104", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801190205441687630000144104"}, {"id": "1560", "server_datetime": "2023-03-05T13:26:13.00Z", "datetime": "2023-03-05T10:26:12.00Z", "index": "144105", "user_id_name": "3(<PERSON>)", "user_id": {"user_id": "3", "name": "<PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1", "name": "All Users"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801197205441687630000144105"}, {"id": "1561", "server_datetime": "2023-03-05T13:26:13.00Z", "datetime": "2023-03-05T10:26:12.00Z", "index": "144106", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801197205441687630000144106", "user_id": {"photo_exists": "false"}}, {"id": "1562", "server_datetime": "2023-03-05T13:26:16.00Z", "datetime": "2023-03-05T10:26:15.00Z", "index": "144107", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801197505441687630000144107", "user_id": {"photo_exists": "false"}}, {"id": "1563", "server_datetime": "2023-03-05T13:26:19.00Z", "datetime": "2023-03-05T10:26:18.00Z", "index": "144108", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801197805441687630000144108"}, {"id": "1564", "server_datetime": "2023-03-05T13:27:38.00Z", "datetime": "2023-03-05T10:27:37.00Z", "index": "144109", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801205705441687630000144109", "user_id": {"photo_exists": "false"}}, {"id": "1565", "server_datetime": "2023-03-05T13:27:41.00Z", "datetime": "2023-03-05T10:27:40.00Z", "index": "144110", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801206005441687630000144110", "user_id": {"photo_exists": "false"}}, {"id": "1566", "server_datetime": "2023-03-05T13:27:42.00Z", "datetime": "2023-03-05T10:27:41.00Z", "index": "144111", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801206105441687630000144111"}, {"id": "1567", "server_datetime": "2023-03-05T13:29:07.00Z", "datetime": "2023-03-05T10:29:06.00Z", "index": "144112", "user_id_name": "16(<PERSON>hams)", "user_id": {"user_id": "16", "name": "<PERSON>hams", "photo_exists": "false"}, "user_group_id": {"id": "1", "name": "All Users"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4102"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801214605441687630000144112"}, {"id": "1568", "server_datetime": "2023-03-05T13:29:07.00Z", "datetime": "2023-03-05T10:29:06.00Z", "index": "144113", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801214605441687630000144113", "user_id": {"photo_exists": "false"}}, {"id": "1569", "server_datetime": "2023-03-05T13:29:10.00Z", "datetime": "2023-03-05T10:29:09.00Z", "index": "144114", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801214905441687630000144114", "user_id": {"photo_exists": "false"}}, {"id": "1570", "server_datetime": "2023-03-05T13:29:14.00Z", "datetime": "2023-03-05T10:29:13.00Z", "index": "144115", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801215305441687630000144115"}, {"id": "1571", "server_datetime": "2023-03-05T13:31:31.00Z", "datetime": "2023-03-05T10:31:30.00Z", "index": "144116", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801229005441687630000144116", "user_id": {"photo_exists": "false"}}, {"id": "1572", "server_datetime": "2023-03-05T13:31:34.00Z", "datetime": "2023-03-05T10:31:33.00Z", "index": "144117", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801229305441687630000144117", "user_id": {"photo_exists": "false"}}, {"id": "1573", "server_datetime": "2023-03-05T13:31:36.00Z", "datetime": "2023-03-05T10:31:35.00Z", "index": "144118", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801229505441687630000144118"}, {"id": "1574", "server_datetime": "2023-03-05T13:33:21.00Z", "datetime": "2023-03-05T10:33:22.00Z", "index": "47093", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801240205416248960000047093", "user_id": {"photo_exists": "false"}}, {"id": "1575", "server_datetime": "2023-03-05T13:33:24.00Z", "datetime": "2023-03-05T10:33:25.00Z", "index": "47094", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801240505416248960000047094", "user_id": {"photo_exists": "false"}}, {"id": "1576", "server_datetime": "2023-03-05T13:34:42.00Z", "datetime": "2023-03-05T10:34:43.00Z", "index": "47095", "user_id_name": "8(<PERSON><PERSON><PERSON>)", "user_id": {"user_id": "8", "name": "<PERSON><PERSON><PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1029", "name": "Genius Valley"}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "4865"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801248305416248960000047095"}, {"id": "1577", "server_datetime": "2023-03-05T13:34:42.00Z", "datetime": "2023-03-05T10:34:43.00Z", "index": "47096", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801248305416248960000047096", "user_id": {"photo_exists": "false"}}, {"id": "1578", "server_datetime": "2023-03-05T13:34:45.00Z", "datetime": "2023-03-05T10:34:46.00Z", "index": "47097", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801248605416248960000047097", "user_id": {"photo_exists": "false"}}, {"id": "1579", "server_datetime": "2023-03-05T13:35:08.00Z", "datetime": "2023-03-05T10:35:07.00Z", "index": "144119", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801250705441687630000144119", "user_id": {"photo_exists": "false"}}, {"id": "1580", "server_datetime": "2023-03-05T13:35:11.00Z", "datetime": "2023-03-05T10:35:10.00Z", "index": "144120", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801251005441687630000144120", "user_id": {"photo_exists": "false"}}, {"id": "1581", "server_datetime": "2023-03-05T13:35:11.00Z", "datetime": "2023-03-05T10:35:10.00Z", "index": "144121", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801251005441687630000144121"}, {"id": "1582", "server_datetime": "2023-03-05T13:37:41.00Z", "datetime": "2023-03-05T10:37:41.00Z", "index": "144122", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801266105441687630000144122", "user_id": {"photo_exists": "false"}}, {"id": "1583", "server_datetime": "2023-03-05T13:37:44.00Z", "datetime": "2023-03-05T10:37:44.00Z", "index": "144123", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801266405441687630000144123", "user_id": {"photo_exists": "false"}}, {"id": "1584", "server_datetime": "2023-03-05T13:37:46.00Z", "datetime": "2023-03-05T10:37:45.00Z", "index": "144124", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801266505441687630000144124"}, {"id": "1585", "server_datetime": "2023-03-05T13:38:12.00Z", "datetime": "2023-03-05T10:38:11.00Z", "index": "144125", "user_id_name": "7(<PERSON><PERSON><PERSON>)", "user_id": {"user_id": "7", "name": "<PERSON><PERSON><PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1029", "name": "Genius Valley"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801269105441687630000144125"}, {"id": "1586", "server_datetime": "2023-03-05T13:38:12.00Z", "datetime": "2023-03-05T10:38:11.00Z", "index": "144126", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801269105441687630000144126", "user_id": {"photo_exists": "false"}}, {"id": "1587", "server_datetime": "2023-03-05T13:38:15.00Z", "datetime": "2023-03-05T10:38:14.00Z", "index": "144127", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801269405441687630000144127", "user_id": {"photo_exists": "false"}}, {"id": "1588", "server_datetime": "2023-03-05T13:38:17.00Z", "datetime": "2023-03-05T10:38:16.00Z", "index": "144128", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801269605441687630000144128"}, {"id": "1589", "server_datetime": "2023-03-05T13:39:04.00Z", "datetime": "2023-03-05T10:39:03.00Z", "index": "144129", "user_id_name": "25(<PERSON><PERSON><PERSON>)", "user_id": {"user_id": "25", "name": "Gamal Atia", "photo_exists": "false"}, "user_group_id": {"id": "1029", "name": "Genius Valley"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801274305441687630000144129"}, {"id": "1590", "server_datetime": "2023-03-05T13:39:04.00Z", "datetime": "2023-03-05T10:39:03.00Z", "index": "144130", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801274305441687630000144130", "user_id": {"photo_exists": "false"}}, {"id": "1591", "server_datetime": "2023-03-05T13:39:08.00Z", "datetime": "2023-03-05T10:39:06.00Z", "index": "144131", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801274605441687630000144131", "user_id": {"photo_exists": "false"}}, {"id": "1592", "server_datetime": "2023-03-05T13:39:13.00Z", "datetime": "2023-03-05T10:39:12.00Z", "index": "144132", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801275205441687630000144132"}, {"id": "1593", "server_datetime": "2023-03-05T13:54:32.00Z", "datetime": "2023-03-05T10:54:33.00Z", "index": "47098", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801367305416248960000047098", "user_id": {"photo_exists": "false"}}, {"id": "1594", "server_datetime": "2023-03-05T13:54:35.00Z", "datetime": "2023-03-05T10:54:36.00Z", "index": "47099", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801367605416248960000047099", "user_id": {"photo_exists": "false"}}, {"id": "1595", "server_datetime": "2023-03-05T13:58:06.00Z", "datetime": "2023-03-05T10:58:05.00Z", "index": "144133", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801388505441687630000144133", "user_id": {"photo_exists": "false"}}, {"id": "1596", "server_datetime": "2023-03-05T13:58:09.00Z", "datetime": "2023-03-05T10:58:08.00Z", "index": "144134", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801388805441687630000144134", "user_id": {"photo_exists": "false"}}, {"id": "1597", "server_datetime": "2023-03-05T13:58:10.00Z", "datetime": "2023-03-05T10:58:10.00Z", "index": "144135", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801389005441687630000144135"}, {"id": "1598", "server_datetime": "2023-03-05T13:58:20.00Z", "datetime": "2023-03-05T10:58:19.00Z", "index": "144136", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801389905441687630000144136", "user_id": {"photo_exists": "false"}}, {"id": "1599", "server_datetime": "2023-03-05T13:58:23.00Z", "datetime": "2023-03-05T10:58:22.00Z", "index": "144137", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801390205441687630000144137", "user_id": {"photo_exists": "false"}}, {"id": "1600", "server_datetime": "2023-03-05T13:58:25.00Z", "datetime": "2023-03-05T10:58:24.00Z", "index": "144138", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801390405441687630000144138"}, {"id": "1601", "server_datetime": "2023-03-05T14:07:52.00Z", "datetime": "2023-03-05T11:07:52.00Z", "index": "144139", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801447205441687630000144139", "user_id": {"photo_exists": "false"}}, {"id": "1602", "server_datetime": "2023-03-05T14:07:55.00Z", "datetime": "2023-03-05T11:07:55.00Z", "index": "144140", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801447505441687630000144140", "user_id": {"photo_exists": "false"}}, {"id": "1603", "server_datetime": "2023-03-05T14:07:57.00Z", "datetime": "2023-03-05T11:07:56.00Z", "index": "144141", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801447605441687630000144141"}, {"id": "1604", "server_datetime": "2023-03-05T14:08:00.00Z", "datetime": "2023-03-05T11:07:59.00Z", "index": "144142", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801447905441687630000144142", "user_id": {"photo_exists": "false"}}, {"id": "1605", "server_datetime": "2023-03-05T14:08:03.00Z", "datetime": "2023-03-05T11:08:02.00Z", "index": "144143", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801448205441687630000144143", "user_id": {"photo_exists": "false"}}, {"id": "1606", "server_datetime": "2023-03-05T14:08:05.00Z", "datetime": "2023-03-05T11:08:04.00Z", "index": "144144", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801448405441687630000144144"}, {"id": "1607", "server_datetime": "2023-03-05T14:08:32.00Z", "datetime": "2023-03-05T11:08:33.00Z", "index": "47100", "user_id_name": "5(<PERSON>)", "user_id": {"user_id": "5", "name": "<PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1028", "name": "<PERSON><PERSON><PERSON>"}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "4102"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801451305416248960000047100"}, {"id": "1608", "server_datetime": "2023-03-05T14:08:32.00Z", "datetime": "2023-03-05T11:08:33.00Z", "index": "47101", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801451305416248960000047101", "user_id": {"photo_exists": "false"}}, {"id": "1609", "server_datetime": "2023-03-05T14:08:35.00Z", "datetime": "2023-03-05T11:08:36.00Z", "index": "47102", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801451605416248960000047102", "user_id": {"photo_exists": "false"}}, {"id": "1610", "server_datetime": "2023-03-05T14:11:15.00Z", "datetime": "2023-03-05T11:11:15.00Z", "index": "47103", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801467505416248960000047103", "user_id": {"photo_exists": "false"}}, {"id": "1611", "server_datetime": "2023-03-05T14:11:18.00Z", "datetime": "2023-03-05T11:11:18.00Z", "index": "47104", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "541624896", "name": "BioEntry P2 541624896 (192.168.10.18)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "2", "name": "Tech"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801467805416248960000047104", "user_id": {"photo_exists": "false"}}, {"id": "1612", "server_datetime": "2023-03-05T14:11:49.00Z", "datetime": "2023-03-05T11:11:47.00Z", "index": "144145", "user_id_name": "5(<PERSON>)", "user_id": {"user_id": "5", "name": "<PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1028", "name": "<PERSON><PERSON><PERSON>"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4102"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801470705441687630000144145"}, {"id": "1613", "server_datetime": "2023-03-05T14:11:49.00Z", "datetime": "2023-03-05T11:11:47.00Z", "index": "144146", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801470705441687630000144146", "user_id": {"photo_exists": "false"}}, {"id": "1614", "server_datetime": "2023-03-05T14:11:52.00Z", "datetime": "2023-03-05T11:11:50.00Z", "index": "144147", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801471005441687630000144147", "user_id": {"photo_exists": "false"}}, {"id": "1615", "server_datetime": "2023-03-05T14:11:53.00Z", "datetime": "2023-03-05T11:11:52.00Z", "index": "144148", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801471205441687630000144148"}, {"id": "1616", "server_datetime": "2023-03-05T14:12:52.00Z", "datetime": "2023-03-05T11:12:51.00Z", "index": "144149", "user_id_name": "202(<PERSON>)", "user_id": {"user_id": "202", "name": "<PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1", "name": "All Users"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801477105441687630000144149"}, {"id": "1617", "server_datetime": "2023-03-05T14:12:52.00Z", "datetime": "2023-03-05T11:12:51.00Z", "index": "144150", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20480"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801477105441687630000144150", "user_id": {"photo_exists": "false"}}, {"id": "1618", "server_datetime": "2023-03-05T14:12:55.00Z", "datetime": "2023-03-05T11:12:54.00Z", "index": "144151", "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "20736"}, "door_id": [{"id": "1", "name": "main"}], "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801477405441687630000144151", "user_id": {"photo_exists": "false"}}, {"id": "1619", "server_datetime": "2023-03-05T14:12:57.00Z", "datetime": "2023-03-05T11:12:56.00Z", "index": "144152", "user_id_name": "Port:0(Input0)(-)", "user_id": {"user_id": "Port:0(Input0)", "photo_exists": "false"}, "user_group_id": {"id": "0", "name": ""}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "16128"}, "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801477605441687630000144152"}, {"id": "1620", "server_datetime": "2023-03-05T14:13:25.00Z", "datetime": "2023-03-05T11:13:25.00Z", "index": "144153", "user_id_name": "10(<PERSON>)", "user_id": {"user_id": "10", "name": "<PERSON>", "photo_exists": "false"}, "user_group_id": {"id": "1029", "name": "Genius Valley"}, "device_id": {"id": "544168763", "name": "BioEntry W2 544168763 (192.168.10.17)"}, "event_type_id": {"code": "4865"}, "tna_key": "1", "is_dst": "0", "timezone": {"half": "0", "hour": "3", "negative": "0"}, "user_update_by_device": "false", "hint": "167801480505441687630000144153"}]}, "Response": {"code": "0", "link": "https://support.supremainc.com/en/support/home", "message": "Success"}}