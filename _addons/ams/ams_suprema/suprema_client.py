import copy
import json
import os

import grpc
# import event_pb2

import sys

from odoo.addons.ams_suprema.device_info import DeviceInfo
from odoo.tools import config

from pprint import pprint

# sys.path.append(r"E:\LP_GOOGLE_DRIVE\LP\100_TA\poc\sources\python_client_sdk\example")

# sys.path.append(r"E:\LP_GOOGLE_DRIVE\LP\100_TA\poc")
# sys.path.append(r"E:\LP_GOOGLE_DRIVE\LP\100_TA\poc\biostar\service")
# sys.path.append(r"E:\LP_GOOGLE_DRIVE\LP\100_TA\poc\venv\Lib\site-packages")

# pprint(sys.path)

import datetime, time
from datetime import timedelta
import threading

# import biostar as client

from gsdk.client.client import GatewayClient
# from gsdk.master.master import MasterClient
from gsdk.connect.connect import ConnectSvc
from gsdk.connectMaster.connectMaster import ConnectMasterSvc
from gsdk.login.login import LoginSvc
from gsdk.device.device import DeviceSvc
from gsdk.finger.finger import FingerSvc
from gsdk.face.face import FaceSvc
from gsdk.card.card import CardSvc
from gsdk.user.user import UserSvc
from gsdk_custom.event.event import EventSvc
from gsdk.auth.auth import AuthSvc
from gsdk.door.door import DoorSvc
from gsdk.access.access import AccessSvc

# from gsdk.tenant.tenant import TenantSvc
# from gsdk.user.test.testCard import TestCard

# import biostarPython as client
import logging

from biostar.service import (connect_pb2, user_pb2, finger_pb2, auth_pb2,
                             device_pb2, face_pb2, card_pb2, door_pb2, access_pb2)

# from suprema import SupremaClient
from logging.handlers import TimedRotatingFileHandler

# region  Logger Config --------------------------------------------------
# setup logging
logger = logging.getLogger(__name__)
log_path = os.path.join(os.path.dirname(__file__), 'logs', 'gsdk.log')
handler = TimedRotatingFileHandler(filename=log_path, when='D', interval=1, backupCount=45,
                                   encoding='utf-8', delay=False)
formatter = logging.Formatter(fmt='%(asctime)s %(levelname)s %(lineno)d %(name)s %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)
logger.info('Start G-SDK gateway logging ...')
# endregion logging  --------------------------------------------------

# region Gateway Config -------------------------------------------
# GATEWAY_CA_FILE = '../../../cert/gateway/ca.crt'
# log_path = os.path.join(os.path.dirname(__file__), 'cert', 'ca.crt')
GATEWAY_CA_FILE = os.path.join(os.path.dirname(__file__), 'cert', 'ca.crt')
# '.\cert\ca.crt'  # conf(add physical_path)

EVENT_CODE_FILE = os.path.join(os.path.dirname(__file__), 'data', 'event_code.json')
# the ip address of the gateway
GATEWAY_IP = '**************'  # conf
# GATEWAY_IP = '0.0.0.0'
GATEWAY_PORT = 4000  # conf

# the ip address of the target device
DEVICE_IP = '*************'
DEVICE_ID = 546832418
DEVICE_PORT = 51211
USE_SSL = False  # conf
FINGER_TEMPLATE_FORMAT = finger_pb2.TEMPLATE_FORMAT_SUPREMA  # conf
FINGER_QUALITY = 80
# endregion Gateway-----------------------------------------

# region Other Config
EVENT_DICT_FILE = os.path.join(os.path.dirname(__file__), 'data', 'event_code.json')  # '.\data\event_code.json'


# endregion


def run_test():
    test_device = True  # get_device_info,get_device_info
    test_user = False  # enroll
    test_card = False  # scan_card , set_card
    test_finger = False  # scan_finger , set_finger
    test_user_delete = False  # delete_user

    test_get_logs = True  # get_log
    test_clear_logs = False  # clear_log
    test_get_logs_realtime = False  # event subscribe

    # region start gateway channel --------------------------------------------

    sup_client = SupremaClient()
    sup_client.connect()
    device_connection_res = sup_client.connect_device_retry([DEVICE_ID], DEVICE_IP, DEVICE_PORT, USE_SSL)
    device_id = None
    if device_connection_res.result:
        device_id = device_connection_res.result

    device_list_res = sup_client.get_device_list()
    if device_list_res.response_code == '1':
        devices_list = device_list_res.result
        print(devices_list)
    else:
        print(device_list_res.error_message)
    # endregion start gateway channel --------------------------------------------

    # region test variables ------------------------------------------------------
    card = None
    finger_temp1 = None
    finger_temp2 = None
    face = None
    logs = []
    test_user_name = 'gsdk_abadr'
    test_user_id = '556677'
    start_date_time = datetime.datetime.now() + timedelta(days=-1)
    end_date_time = start_date_time + timedelta(days=365 * 5)
    # endregion test variables ------------------------------------------------------

    # TODO: Test here ---------
    if device_id:
        # region device ---------------------------------------------------------
        if test_device:
            device_info_res = sup_client.get_device_info(device_id=device_id)
            if device_info_res.response_code == '1':
                info = device_info_res.result
                print(info)
            else:
                print(device_info_res.error_message)

            device_cap_info_res = sup_client.get_device_cap_info(device_id=device_id)
            if device_cap_info_res.response_code == '1':
                cap_info = device_cap_info_res.result
                print(cap_info)
            else:
                print(device_cap_info_res.error_message)

        # endregion
        # region card usage------------------------------------------------------
        if test_card:
            card_res = sup_client.scan_card(device_id=device_id)
            if card_res.response_code == '1':
                card = card_res.result
                # print(card)
            else:
                print(card_res.error_message)
        # endregion

        # region finger usage------------------------------------------------------
        if test_finger:
            finger_res1 = sup_client.scan_finger(device_id=device_id, template_formate=FINGER_TEMPLATE_FORMAT,
                                                 quality=FINGER_QUALITY)
            if finger_res1.response_code == '1':
                finger_temp1 = finger_res1.result
                # print(finger_temp1)
            else:
                print(finger_res1.error_message)

            finger_res2 = sup_client.scan_finger(device_id=device_id, template_formate=FINGER_TEMPLATE_FORMAT,
                                                 quality=FINGER_QUALITY)
            if finger_res2.response_code == '1':
                finger_temp2 = finger_res2.result
                # print(finger_temp2)
            else:
                print(finger_res2.error_message)
        # endregion

        # region enroll user usage------------------------------------------------------
        if test_user:
            enroll_res = sup_client.enroll_user(device_id, test_user_id, test_user_name, start_date_time, end_date_time)
            if enroll_res.response_code == '1':
                # print(enroll_res.result)
                print('Success enroll')
            else:
                print(enroll_res.error_message)
        if test_user_delete:
            delete_res = sup_client.delete_user(device_id, [test_user_id])
            if delete_res.response_code == '1':
                # print(enroll_res.result)
                print('Success delete')
            else:
                print(delete_res.error_message)
        # endregion

        # region enroll finger user usage------------------------------------------------------

        if finger_temp1 and finger_temp2 and test_finger:
            print(finger_temp1)
            finger_templates = [finger_temp1, finger_temp2]  # .templateData
            enroll_finger_res = sup_client.set_finger(device_id, test_user_id, finger_templates)
            if enroll_finger_res.response_code == '1':
                # print(enroll_res.result)
                print('Success enroll finger')
            else:
                print(enroll_finger_res.error_message)
        # endregion

        # region enroll card user usage------------------------------------------------------
        if card and test_card:
            enroll_card_res = sup_client.set_card(device_id, test_user_id, card.CSNCardData)
            if enroll_card_res.response_code == '1':
                # print(enroll_res.result)
                print('Success enroll card')
            else:
                print(enroll_card_res.error_message)
        # endregion

        # region event log usage------------------------------------------------------
        # TODO: add last_event_log in database in device table
        if test_get_logs:
            log_res = sup_client.get_log(device_id=device_id)
            if log_res.response_code == '1':
                logs = log_res.result
                # sup_client.print_to_file('.\data\device_log2.txt',logs)
                print('logs count:', len(logs))
                # sup_client.print_events(logs)
                sup_client.print_events(logs[-5:])  # last events
                # print(logs)
            else:
                print(log_res.error_message)
        # endregion

        # region event clear logs usage------------------------------------------------------
        if test_clear_logs:
            clear_log_res = sup_client.clear_log(device_id=device_id)
            if clear_log_res.response_code == '1':
                # res=clear_log_res.result
                # sup_client.print_to_file('.\data\device_log2.txt',logs)
                print('success claer logs count:', len(logs))
                # print(logs)
            else:
                print(clear_log_res.error_message)
        # endregion

        # region event realtime logs usage------------------------------------------------------
        if test_get_logs_realtime:
            print('>> testRealtimeLog')
            sup_client.subscribe_log(device_id=device_id, queue_size=10, max_logs=5)

        # endregion

        # region------------------------------------------------------
        test1 = 1

        # endregion

    else:
        print(device_connection_res.error_message)

    # Finally disconnect
    if device_id:
        sup_client.disconnect_device([device_id])


class ResponseMessage:
    def __init__(self, result=None, response_code='1', error_message=''):
        self.result = result
        """if success response return 1 or return error code"""
        self.response_code = response_code
        self.error_message = error_message


from odoo.http import request
from odoo import http


# class CustomeWebRequest:
#     def __init__(self):
#         self._cr = None
#         self._uid = None
#         self._context = None
#         self._env = None
# 
#     @property
#     def cr(self):
#         if not self.db:
#             raise RuntimeError('request not bound to a database')
#         if not self._cr:
#             self._cr = self.registry.cursor()
#         return self._cr

class SupremaClient:
    # _name = 'ams_suprema.cl'
    connected = False
    code_map = None
    event_channel = None  # to listen on event logs stream at realtime
    EVENT_QUEUE_SIZE = 16  # to be configurable

    @property
    def UNKNOWN_RESPONSE(self):
        """initialize unknown response message"""
        return ResponseMessage(response_code='0', error_message='Unknown response')

    def __init__(self):
        self.event_svc: EventSvc
        self.auth_svc: AuthSvc
        self.user_svc: UserSvc
        self.face_svc: FaceSvc
        self.finger_svc: FingerSvc
        self.card_svc: CardSvc
        self.device_svc: DeviceSvc
        self.connect_svc: ConnectSvc

        self.door_svc: DoorSvc
        self.access_svc: AccessSvc

        self.channel = None
        self.gateway: GatewayClient

        logger.debug('__init__ SupremaClient')
        self.gateway_ip = self.gateway_port = self.ca_file = False

        # read configuration by default from odoo.conf file
        # because at this moment config parameters cannot access direct access
        self.gateway_ip = config.get('gsdk_gateway_ip', '')
        self.gateway_port = config.get('gsdk_gateway_port', '')
        self.ca_file = config.get('gsdk_ca_file', False) or GATEWAY_CA_FILE or ''
        self.events_file = config.get('gsdk_events_file', False) or EVENT_CODE_FILE or ''

        logger.info(f"SupremaClient: gateway_ip={self.gateway_ip},"
                    f" gateway_port={self.gateway_port}, ca_file={self.ca_file}, events_file={self.events_file}")

        # self.gateway_ip = self.env['ir.config_parameter'].sudo().get_param('ams_suprema.gateway_ip')
        # self.gateway_port = self.env['ir.config_parameter'].sudo().get_param('ams_suprema.gateway_port')
        # request.env['ir.config_parameter'].sudo().get_param('ams_suprema.getway_ca_file_path')

    def connect(self, gateway_ip=False, gateway_port=False, ca_file=False, events_file=False):
        """connect to G-SDK gateway and initialize all suprema services"""
        try:
            logger.info('TODO connect Suprema Client')
            if gateway_ip:
                self.gateway_ip = gateway_ip
            if gateway_port:
                self.gateway_port = gateway_port
            if ca_file:
                self.ca_file = ca_file
            if events_file:
                self.events_file = events_file

            self.gateway = GatewayClient(self.gateway_ip, self.gateway_port, self.ca_file)
            self.channel = self.gateway.getChannel()
            self.connect_svc = ConnectSvc(self.channel)
            self.device_svc = DeviceSvc(self.channel)
            self.card_svc = CardSvc(self.channel)
            self.finger_svc = FingerSvc(self.channel)
            self.face_svc = FaceSvc(self.channel)
            self.user_svc = UserSvc(self.channel)
            self.auth_svc = AuthSvc(self.channel)
            self.event_svc = EventSvc(self.channel)

            self.door_svc = DoorSvc(self.channel)
            self.access_svc = AccessSvc(self.channel)

            self.ca_file = GATEWAY_CA_FILE

            if EVENT_CODE_FILE:
                self.init_code_map(EVENT_CODE_FILE)  # initialize events dictionary names & description

            if self.gateway_ip and self.gateway_port and self.ca_file:
                self.connected = True
                logger.info(f"success connected: gateway_ip={self.gateway_ip}, gateway_port={self.gateway_port}")
            else:
                logger.error(f"failed to connect: gateway_ip={self.gateway_ip}, gateway_port={self.gateway_port}")

            return self.connected
        except Exception as e:
            logger.error(f"failed to connect: gateway_ip={self.gateway_ip}, gateway_port={self.gateway_port}")
            return False
            # self.event_svc.initCodeMap(events_file)

    def close_gateway_channel(self):
        devices = None
        try:
            devices = self.connect_svc.getDeviceList()
            devices_arr = []
            for device in devices:
                devices_arr.append(device.deviceID)

            if len(devices_arr) > 0:
                self.connect_svc.disconnect(devices_arr)  # disconnect all devices before close channel
            # if device.status == 1 and device.IPAddr == device_id_info.ip_address:  # TCP_CONNECTED
            #     return device.deviceID

            if self.channel:
                self.channel.close()

        except Exception as e:
            err_msg = f'Cannot close gateway channel,{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='1005', error_message=err_msg)

        return ResponseMessage()

    def start_gateway_channel(self):
        try:
            self.connect()
        except Exception as e:
            err_msg = f'Cannot start gateway channel,{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='1000', error_message=err_msg)
        return ResponseMessage()

    @property
    def client_logger(self):
        return logger

    # region -------------------------------------------
    # endregion-----------------------------------------

    # region device api-----------------------------------------------

    def connect_device(self, device_info: DeviceInfo) -> ResponseMessage:
        """
        Connect to a device using its IP address and port.
        https://supremainc.github.io/g-sdk/api/connect/#connect
        """
        logger.info(f'Connecting to device with IP [{device_info.ip_address}], port [{device_info.port}]')

        try:
            # Check if the device is already connected
            device_id = self.get_device_connected(device_info)
            if device_id:
                return ResponseMessage(result=device_id)

            # If not connected, attempt to connect
            connInfo = connect_pb2.ConnectInfo(IPAddr=device_info.ip_address, port=device_info.port,
                                               useSSL=device_info.useSSL)
            device_id = self.connect_svc.connect(connInfo)
            # device_id_info = self.device_svc.getCapInfo(deviceID=device_id)  # Uncomment if needed
            return ResponseMessage(result=device_id)

        except Exception as e:
            err_msg = f'Failed to connect to device IP {device_info.ip_address}: {e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='1100', error_message=err_msg)

    def connect_device_retry(self, device_ids, ip_adress, device_port, useSSL) -> ResponseMessage:
        """if no connection try disconnect device and connect again"""

        connect_res = self.connect_device(ip_adress, device_port, useSSL)
        device_id = connect_res.result
        if not device_id:
            # try reconnect again
            logger.info('retry to connect device ...')
            try:
                self.connect_svc.disconnect(device_ids)
                connect_res = self.connect_device(ip_adress, device_port, useSSL)
            except Exception as e:
                err_msg = f'Cannot retry connect device_ip:{ip_adress},device_id:{device_ids},{e}'
                logger.error(err_msg)
                return ResponseMessage(result='', response_code='1101', error_message=err_msg)

        return connect_res

    def set_config(self, device_info: DeviceInfo, **kwargs) -> ResponseMessage:
        """ set config for device
        https://supremainc.github.io/g-sdk/api/auth/#setconfig
        """
        device_id = device_info.device_id
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res
            use_private_auth = kwargs.get('use_private_auth', False)
            config = auth_pb2.AuthConfig(matchTimeout=10, authTimeout=10, usePrivateAuth=use_private_auth)
            default_auth_modes = kwargs.get('default_auth_modes', [])
            for mode in default_auth_modes:
                mode_enum = self.string_to_enum(auth_pb2.AuthMode, mode)
                config.authSchedules.add(mode=mode_enum, scheduleID=1)

            logger.info(f'Set config to device  IP [{device_info.ip_address}], name [{device_info.name}]')

            self.auth_svc.setConfig(deviceID=device_id, config=config)

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot set config  on device:{device_info.device_label}, name:{device_info.name},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='110', error_message=err_msg)

        return ResponseMessage()

    def disconnect_device(self, device_info: DeviceInfo):
        try:
            if not device_info.dedicated:
                self.connect_svc.disconnect([device_info.device_id])
        except Exception as e:
            err_msg = f'Cannot disconnect device_ids:{device_info.device_id}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='1102', error_message=err_msg)

        return ResponseMessage()

    def get_device_list(self):
        """ get device list
        https://biostar-dev.github.io/g-sdk/api/connect/#getdevicelist
        """
        devices = None
        try:
            devices = self.connect_svc.getDeviceList()
        except Exception as e:
            err_msg = f'Cannot get device list,{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='1103', error_message=err_msg)

        return ResponseMessage(result=devices)

    def get_device_connected(self, device_info: DeviceInfo):
        """
        Search by device IP if the device is already connected with the server; no connection is opened again.
        Return device ID if connected, else return False.
        https://supremainc.github.io/g-sdk/api/connect/#getdevicelist
        """
        try:
            devices = self.connect_svc.getDeviceList()
            for device in devices:
                if device.status == 1 and device.IPAddr == device_info.ip_address:  # TCP_CONNECTED
                    return device.deviceID
        except Exception as e:
            err_msg = f'Cannot get device list: {e}'
            logger.error(err_msg)

        return False

    def get_device_info_old(self, device_id):
        """ get device information
        https://biostar-dev.github.io/g-sdk/api/device/#getinfo
        """
        device_info = None
        try:
            device_info = self.device_svc.getInfo(deviceID=device_id)
        except Exception as e:
            err_msg = f'Cannot get device info device:{device_info.device_label},{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='121', error_message=err_msg)

        return ResponseMessage(result=device_info)

    def get_device_info(self, device_info: DeviceInfo) -> ResponseMessage:
        """ get device information based on device id
        https://supremainc.github.io/g-sdk/api/device/#getinfo
        """
        device_id = device_info.device_id
        device_info_res = None
        try:
            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                logger.error(f"Cannot get device info device:{device_info.device_label} , ip:{device_info.ip_address}")
                return conn_response  # return fail connection response

            # execute action
            device_info_res = self.device_svc.getInfo(deviceID=device_id)

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get device info device:{device_info.device_label},{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='121', error_message=err_msg)

        return ResponseMessage(result=device_info_res)

    def get_device_cap_info(self, device_info) -> ResponseMessage:
        """ get device capability information
        https://supremainc.github.io/g-sdk/api/device/#getcapabilityinfo
        """
        device_id = device_info.device_id
        try:

            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                return conn_response  # return fail connection response
            # execute action
            device_cap_res = self.device_svc.getCapInfo(deviceID=device_id)

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get device cap info device:{device_info.device_label},{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='122', error_message=err_msg)

        return ResponseMessage(result=device_cap_res)

    def get_device_cap(self, device_info) -> ResponseMessage:
        """ get device capability information
        https://supremainc.github.io/g-sdk/api/device/#getcapability
        """
        device_id = device_info.device_id
        try:

            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                return conn_response  # return fail connection response
            # execute action
            device_cap_res = self.device_svc.getCapability(deviceID=device_id)

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get device capability  device:{device_info.device_label},{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='123', error_message=err_msg)

        return ResponseMessage(result=device_cap_res)

    def message_to_json(self, message):
        """ convert message response to json"""
        from google.protobuf.json_format import MessageToJson
        return MessageToJson(message)

    # TODO implement device lock , unlock

    # endregion connect_svc api

    def scan_card(self, device_info: DeviceInfo) -> ResponseMessage:
        """ scan card
        https://biostar-dev.github.io/g-sdk/api/card/#scan"""
        card = None
        device_id = device_info.device_id
        try:
            # connection
            connection_response = self.connect_device(device_info=device_info)
            if connection_response.response_code != '1':
                return connection_response

            # execute action
            card = self.card_svc.scan(deviceID=device_id)

            # disconnect
            if not device_info.dedicated:
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != '1':
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot scan card on device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='110', error_message=err_msg)

            # message CardData {
            # Type type;
            # CSNCardData CSNCardData; // null if it is a smartcard
            # SmartCardData smartCardData; // null if it is a CSN card
            # }
            # type
            # CSNCardData
            # Valid only with CARD_TYPE_CSN or CARD_TYPE_QR.
            # smartCardData
            # Valid only with CARD_TYPE_SECURE or CARD_TYPE_ACCESS.
            # message CSNCardData {
            # Type type;
            # int32 size;
            # bytes data;
            # }
            # message SmartCardData {
            # SmartCardHeader header;
            # bytes cardID;
            # SmartCardCredential credential;
            # AccessOnCardData accessOnData;
            # }
        return ResponseMessage(result=card)

    def get_card_number(self, card_data):
        card_no = None
        if card_data:
            # print(card)
            card_bytes = bytes(
                card_data)  # bytes(str(card.CSNCardData.data),'utf-8') # bytes.fromhex(str(card.CSNCardData.data))#
            # print(card_bytes)
            # b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x04<N\xb2\xd3/\x80'
            card_no = int(card_bytes.hex(), 16)  # display number on devices , biostar 2
            print('Success Card_No:', card_no)

        return card_no

    def create_csn_card_data(self, size, data, type=False) -> card_pb2.CSNCardData:
        csn_card_data = card_pb2.CSNCardData()
        csn_card_data.data = data
        csn_card_data.size = size
        csn_card_data.type = type  # or card_pb2.CARD_TYPE_UNKNOWN
        return csn_card_data

    def scan_finger(self, device_info, quality=80, template_format=finger_pb2.TEMPLATE_FORMAT_SUPREMA):
        """ scan finger
        https://biostar-dev.github.io/g-sdk/api/finger/#scan"""
        finger = None
        device_id = device_info.device_id
        try:

            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            # finger_config = self.finger_svc.getConfig(device_id)

            finger = self.finger_svc.scan(deviceID=device_id, templateFormat=template_format,
                                          qualityThreshold=quality)

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)
        except Exception as e:
            err_msg = f'Cannot scan finger on device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='130', error_message=err_msg)

        return ResponseMessage(result=finger)

    def scan_face(self, device_info, enroll_threshold=face_pb2.BS2_FACE_ENROLL_THRESHOLD_DEFAULT):
        """ scan face
        https://biostar-dev.github.io/g-sdk/api/face/#scan"""
        face = None
        device_id = device_info.device_id
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            face = self.face_svc.scan(deviceID=device_id, enrollThreshold=enroll_threshold)

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)
        except Exception as e:
            err_msg = f'Cannot scan face on device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='140', error_message=err_msg)

        return ResponseMessage(result=face)

    # region user_svc api -----------------------------------------------------------------
    def create_user_setting(self, device_info: DeviceInfo, user_id, name, **kwargs):
        """ prepare  user setting user_pb2.UserSetting() to used in enroll user"""
        user_setting = user_pb2.UserSetting()

        # Set default start and end times
        start_time = datetime.datetime.now()
        end_time = start_time + datetime.timedelta(days=(365 * 12))  # + 12 Years

        # Convert datetime to unix time
        default_start_date_time_unix = int(time.mktime(start_time.timetuple()))
        default_end_date_time_unix = int(time.mktime(end_time.timetuple()))

        # Set start and end times using provided or default values
        user_setting.startTime = int(kwargs.get('start_date_time_unix', f'{default_start_date_time_unix}'))
        user_setting.endTime = int(kwargs.get('end_date_time_unix', f'{default_end_date_time_unix}'))

        # Handle authentication mode configuration
        auth_mode_config = kwargs.get('auth_mode_config', {})
        card_auth_mode = auth_mode_config.get('card_auth_mode_id', 'AUTH_MODE_NONE')
        finger_auth_mode = auth_mode_config.get('fingerprint_auth_mode_id', 'AUTH_MODE_NONE')
        face_auth_mode = auth_mode_config.get('face_auth_mode_id', 'AUTH_MODE_NONE')

        # Set authentication modes based on device support
        if device_info.extended_auth_supported:
            user_setting.cardAuthExtMode = self.get_auth_mode(card_auth_mode)
            user_setting.fingerAuthExtMode = self.get_auth_mode(finger_auth_mode)
            user_setting.faceAuthExtMode = self.get_auth_mode(face_auth_mode)
        else:
            user_setting.cardAuthMode = self.get_auth_mode(card_auth_mode)
            user_setting.biometricAuthMode = self.get_auth_mode(finger_auth_mode)



        # biometricAuthMode = 3;
        # cardAuthMode = 4;
        # IDAuthMode = 5;
        # securityLevel = 6;
        #
        # // for F2 and BS3 only
        # faceAuthExtMode = 7;
        # fingerAuthExtMode = 8;
        # cardAuthExtMode = 9;
        # IDAuthExtMode = 10;
        return user_setting

    def enroll_user(self, device_info: DeviceInfo, user_id, name, **kwargs):
        """ enroll user
        https://supremainc.github.io/g-sdk/api/user/#enroll-1
        auth_mode_config
        """

        device_id = device_info.device_id
        try:
            user_hdr = user_pb2.UserHdr(ID=user_id)
            # region  user setting
            user_setting = self.create_user_setting(device_info, user_id, name, **kwargs)
            # endregion

            # read user info if exist then  override data if exist else create new user info
            users_info = self.user_svc.getUser(deviceID=device_id, userIDs=[user_id])  # return  UserInfo[]
            if len(users_info) > 0:
                user_info = users_info[0]
                user_info.setting.CopyFrom(user_setting)
                user_info.name = name
            else:
                user_info = user_info = user_pb2.UserInfo(hdr=user_hdr, setting=user_setting, name=name)

            # user_info = user_pb2.UserInfo(hdr=user_hdr, setting=user_setting, name=name)

            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res
            users = [user_info]
            self.user_svc.enroll(deviceID=device_id, users=users, overwrite=True)
            logger.info(f'Success enroll user :{name},user_id:{user_id}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot enroll the user {user_id} on device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='150', error_message=err_msg)

        return ResponseMessage(result=users)

    def delete_user(self, device_info, user_ids):
        """ delete user
        https://supremainc.github.io/g-sdk/api/user/#delete-1"""
        device_id = device_info.device_id
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(f'deleting user_ids {user_ids} on device:{device_info.device_label}')
            self.user_svc.delete(deviceID=device_id, userIDs=user_ids)
            logger.info(f'Success delete card for selected users on device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot delete users card on device:{device_info.device_label}, user_ids:{user_ids},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='151', error_message=err_msg)

        return ResponseMessage()

    def set_card(self, device_info, user_id, card_data):
        """ set card
        https://supremainc.github.io/g-sdk/api/user/#setcard
        """
        user_cards = None
        device_id = device_info.device_id
        try:

            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(f'set card for user_id {user_id} on device:{device_info.device_label}')
            user_cards = user_pb2.UserCard(userID=user_id, cards=[card_data])  # card.CSNCardData
            self.user_svc.setCard(deviceID=device_id, userCards=[user_cards])
            logger.info(f'Success enroll card for user_id:{user_id} on device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot set card on device:{device_info.device_label}, user_id:{user_id},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='155', error_message=err_msg)

        return ResponseMessage(result=user_cards)

    def set_finger(self, device_info, user_id, finger_templates):
        """ set finger
        https://supremainc.github.io/g-sdk/api/user/#setfinger
        """
        device_id = device_info.device_id
        user_finger = None
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(f'set finger for user_id {user_id} on device:{device_info.device_label}')
            # finger_templates =[[],[]] -- map to each nested array is  FingerData
            fingers = []
            for finger_temp in finger_templates:
                # finger_temp is templates bytes for one finger
                fingers.append(finger_pb2.FingerData(templates=finger_temp))

            # finger_data = finger_pb2.FingerData(templates=finger_templates)  # template1.templateData

            user_finger = user_pb2.UserFinger(userID=user_id, fingers=fingers)
            self.user_svc.setFinger(deviceID=device_id, userFingers=[user_finger])
            logger.info(f'Success enroll finger for user_id:{user_id} on device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot set finger templates on device:{device_info.device_label} , user_id:{user_id},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='156', error_message=err_msg)

        return ResponseMessage(result=user_finger)

    def set_face(self, device_info, user_id, face_templates):
        """ set Face
        https://supremainc.github.io/g-sdk/api/user/#setface
        """
        user_face = None
        device_id = device_info.device_id
        try:

            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(
                f'[set_face] deserialize FaceData object user_id {user_id} on device:{device_info.device_label}')

            # Parse bytes to FaceData object
            deserialized_face_data = face_pb2.FaceData()
            deserialized_face_data.ParseFromString(face_templates)

            user_face = user_pb2.UserFace(userID=user_id, faces=[deserialized_face_data])

            self.user_svc.setFace(deviceID=device_id, userFaces=[user_face])

            logger.debug(f'[set_face] Success enroll face for user_id:{user_id} on device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'[set_face] Cannot set face  on device:{device_info.device_label} , user_id:{user_id},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='157', error_message=err_msg)

        return ResponseMessage(result=user_face)

    def get_users(self, device_info: DeviceInfo):
        """get users from device return result of  UserHdr[]
        https://supremainc.github.io/g-sdk/api/user/#getlist"""
        users = None
        device_id = device_info.device_id
        try:
            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                logger.error(
                    f"Cannot get logs from device:{device_info.device_label}, {conn_response.error_message}")
                return conn_response  # return fail connection response

            # execute action getLog
            logger.debug(f'start get users from device:{device_info.device_label}')
            users = self.user_svc.getList(deviceID=device_id)  # return  UserHdr[]
            logger.info(f'Success get users from device:{device_info.device_label}')

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get users from device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='160', error_message=err_msg)

        return ResponseMessage(result=users)

    def get_users_info(self, device_info: DeviceInfo, user_ids=[]):
        """get users info detail from device
        https://supremainc.github.io/g-sdk/api/user/#get"""
        user = None
        device_id = device_info.device_id
        try:
            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                logger.error(
                    f"Cannot get users info from device:{device_info.device_label} {conn_response.error_message}")
                return conn_response  # return fail connection response

            # execute action getLog
            logger.debug(f'start get users from device:{device_info.device_label}')
            # self.user_svc = UserSvc(self.channel)  #
            users = self.user_svc.getUser(deviceID=device_id, userIDs=user_ids)  # return  UserInfo[]
            logger.info(f'Success get users from device:{device_info.device_label}')

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get users from device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='161', error_message=err_msg)

        return ResponseMessage(result=users)

    # endregion user_svc api -----------------------------------------------------------------

    # region event_svc api ------------------------------------------------------------------
    def get_log(self, device_info: DeviceInfo, start_event_id=0, max_logs=0):
        """get log by device id
        https://supremainc.github.io/g-sdk/api/event/#getlog"""
        logs = None
        device_id = device_info.device_id
        try:
            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                logger.error(
                    f"Cannot get logs from device:{device_info.device_label}, {conn_response.error_message}")
                return conn_response  # return fail connection response

            # execute action getLog
            logger.debug(f'start get logs from device:{device_info.device_label}')
            logs = self.event_svc.getLog(deviceID=device_id, startEventID=start_event_id, maxNumOfLog=max_logs)
            logger.info(f'Success get logs from device:{device_info.device_label}')

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get logs from device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='1200', error_message=err_msg)

        return ResponseMessage(result=logs)

    def hgagy_test_generic(self, device_info: DeviceInfo, method, **kwargs):
        """
            generic pattern which starts with
            1)connect
            2)excute action
            3)disconnect
        """
        logs = None
        device_id = device_info.device_id
        try:
            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                return conn_response  # return fail connection response

            # execute action getLog
            if method == 'get_log':
                logger.debug(f'start get logs from device:{device_info.device_label}')
                logs = self.event_svc.getLog(deviceID=device_id, startEventID=kwargs.get('start_event_id'),
                                             maxNumOfLog=kwargs.get('max_logs'))
                logger.info(f'Success get logs from device:{device_info.device_label}')

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get logs from device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='1200', error_message=err_msg)

        return ResponseMessage(result=logs)

    def clear_log(self, device_info: DeviceInfo) -> ResponseMessage:
        logs = None
        device_id = device_info.device_id
        try:
            # connect
            connection_res = self.connect_device(device_info)
            if connection_res.response_code != '1':
                return connection_res

            # execute action
            logger.debug(f'start clear logs from device:{device_info.device_label}')
            logs = self.event_svc.clearLog(deviceID=device_id)
            logger.info(f'Success clear logs  from device:{device_info.device_label}')

            # disconnect
            if not device_info.dedicated:
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != '1':
                    logger.error(disconn_response.error_message)


        except Exception as e:
            err_msg = f'Cannot clear logs from device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='1201', error_message=err_msg)

        return ResponseMessage(result=logs)

    def subscribe_log(self, device_id, queue_size=16, max_logs=5):
        """for test only subscribe -- to monitoring in real time  """
        try:
            self.event_svc.enableMonitoring(device_id)

            print(f'\n>>> Generate {max_logs} events...', flush=True)
            eventCh = self.event_svc.subscribe(queue_size)
            eventCount = 0

            for event in eventCh:
                logger.debug(f'Event: {event}')
                print(f'Event: {event}', flush=True)
                eventCount += 1
                if eventCount >= max_logs:
                    eventCh.cancel()

            self.event_svc.disableMonitoring(device_id)

        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.CANCELLED:
                print('Subscription is cancelled', flush=True)
            else:
                print(f'Cannot complete the realtime event test: {e}', flush=True)

    def subscribe_event(self, device_id, queue_size=16):
        """subscribe event and use threading to handel  """
        try:
            self.event_svc.enableMonitoring(device_id)
            self.event_channel = self.event_svc.subscribe(queue_size)

            # ---- usage in testing ----
            # statusThread = threading.Thread(target=self.handel_event)
            # statusThread.start()

            # --- handel_event : used to save event to database



        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.CANCELLED:
                print('Subscription is cancelled', flush=True)
            else:
                print(f'Cannot complete the realtime event test: {e}', flush=True)

    def start_monitoring(self, deviceID):
        try:
            self.event_svc.enableMonitoring(deviceID)
            self.event_channel = self.event_svc.subscribe(self.EVENT_QUEUE_SIZE)

            statusThread = threading.Thread(target=self.handel_event)
            statusThread.start()

        except grpc.RpcError as e:
            print(f'Cannot start monitoring: {e}', flush=True)

    def handel_event(self):
        try:
            for event in self.event_channel:
                if self.firstEventID == 0:
                    self.firstEventID = event.ID
                    print(f'\nEvent: {event}', flush=True)

        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.CANCELLED:
                print('Monitoring is cancelled', flush=True)
            else:
                print(f'Cannot get realtime events: {e}')

    def stopMonitoring(self, deviceID):
        try:
            self.event_svc.disableMonitoring(deviceID)
            self.event_channel.cancel()

        except grpc.RpcError as e:
            print(f'Cannot stop monitoring: {e}', flush=True)

    # region helper-----------------------------------------------

    def init_code_map(self, filename):
        "helper method to load all events dictionary in current client instance"
        try:
            with open(filename) as f:
                self.code_map = json.load(f)
        except:
            e = sys.exc_info()[0]
            print(f'Cannot init the event code map: {e}')

    def get_event_entry(self, code, sub_code):
        """return entry from json datasource event_code.json"""
        if self.code_map == None:
            return "No code map(%#X)" % (code | sub_code)
        else:
            for entry in self.code_map['entries']:
                if code == entry['event_code'] and sub_code == entry['sub_code']:
                    return entry

        return {}

    def get_event_name(self, code, sub_code):
        """return name from json datasource event_code.json"""
        if self.code_map == None:
            return "No code map(%#X)" % (code | sub_code)
        else:
            for entry in self.code_map['entries']:
                if code == entry['event_code'] and sub_code == entry['sub_code']:
                    return entry['event_code_str']

        return "Unknown code(%#X)" % (code | sub_code)

    def get_event_desc(self, code, sub_code):
        """return description from json datasource event_code.json"""
        if self.code_map == None:
            return "No code map(%#X)" % (code | sub_code)
        else:
            for entry in self.code_map['entries']:
                if code == entry['event_code'] and sub_code == entry['sub_code']:
                    return entry['desc']

        return "Unknown code(%#X)" % (code | sub_code)

    def is_success_punch(self, code):
        """check if code is a success punch inside ranges
            (4096, 4111),  # VERIFY_SUCCESS
            (4112, 4117),  # VERIFY_SUCCESS_CARD_FACE_FINGER
            (4118, 4127),  # VERIFY_SUCCESS_MOBILE_CARD
            (4610, 4615),  # VERIFY_DURESS_ID_FINGERPRINT
            (4616, 4623),  # VERIFY_DURESS_CARD_FINGERPRINT
            (4624, 4627),  # VERIFY_DURESS_CARD_FACE_FINGER
            (4632, 4639),  # VERIFY_DURESS_MOBILE_CARD_FINGER
            (4640, 4643),  # VERIFY_DURESS_MOBILE_CARD_FACE_FINGER
            (4864, 4875),  # IDENTIFY_SUCCESS
            (5376, 5380),  # IDENTIFY_DURESS
            (5381, 5384)  # IDENTIFY_DURESS_FACE_FINGER"""
        ranges = [
            (4096, 4111),  # VERIFY_SUCCESS
            (4112, 4117),  # VERIFY_SUCCESS_CARD_FACE_FINGER
            (4118, 4127),  # VERIFY_SUCCESS_MOBILE_CARD
            (4610, 4615),  # VERIFY_DURESS_ID_FINGERPRINT
            (4616, 4623),  # VERIFY_DURESS_CARD_FINGERPRINT
            (4624, 4627),  # VERIFY_DURESS_CARD_FACE_FINGER
            (4632, 4639),  # VERIFY_DURESS_MOBILE_CARD_FINGER
            (4640, 4643),  # VERIFY_DURESS_MOBILE_CARD_FACE_FINGER
            (4864, 4875),  # IDENTIFY_SUCCESS
            (5376, 5380),  # IDENTIFY_DURESS
            (5381, 5384)  # IDENTIFY_DURESS_FACE_FINGER
        ]

        for start, end in ranges:
            if start <= code <= end:
                return True
        return False

    def print_events(self, events=[]):
        for event in events:
            event_time = datetime.datetime.utcfromtimestamp(event.timestamp)
            event_name = self.get_event_desc(event.eventCode, event.subCode)
            print(f'{event_time}: Device {event.deviceID}, User {event.userID}, {event_name}', flush=True)

    # endregion helper

    # endregion event_svc

    # region access control
    # error code start with 2000
    def prepare_door_info_proto(self, door_info_dict):
        door_info_proto = door_pb2.DoorInfo()
        door_info_proto.doorID = door_info_dict.get("door_id", 0)
        door_info_proto.name = door_info_dict.get("name", "")
        door_info_proto.entryDeviceID = door_info_dict.get("entry_device_id", 0)
        door_info_proto.exitDeviceID = door_info_dict.get("exit_device_id", 0)
        door_info_proto.autoLockTimeout = door_info_dict.get("auto_lock_timeout", 0)
        door_info_proto.heldOpenTimeout = door_info_dict.get("held_open_timeout", 0)
        door_info_proto.instantLock = door_info_dict.get("instant_lock", False)
        door_info_proto.unlockFlags = door_info_dict.get("unlock_flags", 0)
        door_info_proto.lockFlags = door_info_dict.get("lock_flags", 0)
        door_info_proto.unconditionalLock = door_info_dict.get("unconditional_lock", False)

        # If relay details exist in the dictionary, add them to the proto message
        relay_dict = door_info_dict.get("relay", {})
        if relay_dict:
            relay = door_pb2.Relay(
                deviceID=relay_dict.get("device_id", 0),
                port=relay_dict.get("port", 0)
            )
            door_info_proto.relay.CopyFrom(relay)
            # door_info_proto.relay = relay

        # If sensor details exist in the dictionary, add them to the proto message
        sensor_dict = door_info_dict.get("sensor", {})
        if sensor_dict:
            sensor = door_pb2.Sensor()  # door_info_proto.sensor
            sensor.deviceID = sensor_dict.get("device_id", 0)
            sensor.port = sensor_dict.get("port", 0)
            switch_type = sensor_dict.get("type", "NORMALLY_OPEN")
            sensor.type = self.string_to_enum(device_pb2.SwitchType, switch_type.upper())
            door_info_proto.sensor.CopyFrom(sensor)
            # door_info_proto.sensor = sensor

        # If exit button details exist in the dictionary, add them to the proto message
        button_dict = door_info_dict.get("button", {})
        if button_dict:
            button = door_pb2.ExitButton()  # door_info_proto.button
            button.deviceID = button_dict.get("device_id", 0)
            button.port = button_dict.get("port", 0)
            switch_type = button_dict.get("type", "NORMALLY_OPEN")
            button.type = self.string_to_enum(device_pb2.SwitchType, switch_type.upper())
            door_info_proto.button.CopyFrom(button)
            # door_info_proto.button = button

        # Set default values for other fields
        door_info_proto.dualAuthScheduleID = door_info_dict.get("dual_auth_schedule_id", 0)
        door_info_proto.dualAuthDevice = door_info_dict.get("dual_auth_device", 0)
        door_info_proto.dualAuthType = door_info_dict.get("dual_auth_type", 0)
        door_info_proto.dualAuthTimeout = door_info_dict.get("dual_auth_timeout", 15)
        door_info_proto.dualAuthGroupIDs.extend(door_info_dict.get("dual_auth_group_ids", []))

        return door_info_proto

    def add_door(self, device_info: DeviceInfo, door_name, door_info_dict):
        """ add door
             https://supremainc.github.io/g-sdk/api/door/#add
             """
        device_id = device_info.device_id
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(f'[add_door] Trying to add door  {door_name} on device:{device_info.device_label}')
            door_info_proto = self.prepare_door_info_proto(door_info_dict)
            self.door_svc.add(device_id, [door_info_proto])

            logger.debug(f'[add_door] Success adding door:{door_name} on device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'[add_door] Cannot add door  on device:{device_info.device_label} , door:{door_name},{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='2000', error_message=err_msg)

        return ResponseMessage(result=door_info_proto)

    def delete_doors(self, device_info: DeviceInfo, door_ids=[]):
        """ delete doors
        https://supremainc.github.io/g-sdk/api/user/#delete-1"""
        device_id = device_info.device_id
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(f'deleting door_ids {door_ids} on device:{device_info.device_label}')
            if len(door_ids) > 0:
                self.door_svc.delete(deviceID=device_id, doorIDs=door_ids)
                logger.info(f'Success delete door from device:{device_info.device_label}')
            else:
                self.door_svc.deleteAll(deviceID=device_id)
                logger.info(f'Success delete all doors from device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot delete doors  from device:{device_info.device_label},{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='2005', error_message=err_msg)

        return ResponseMessage()

    def get_doors(self, device_info: DeviceInfo):
        """get doors from device return result of  DoorInfo[]
        https://supremainc.github.io/g-sdk/api/door/#getlist"""
        doors = None
        device_id = device_info.device_id
        try:
            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                return conn_response  # return fail connection response

            # execute action getLog
            logger.debug(f'start get doors from device:{device_info.device_label}')
            doors = self.door_svc.getList(deviceID=device_id)  # return  UserHdr[]
            logger.info(f'Success get doors from device:{device_info.device_label}')

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get doors from device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='2010', error_message=err_msg)

        return ResponseMessage(result=doors)

    def prepare_ac_level_proto(self, ac_group_dict):
        """ prepare access level proto"""
        levels = []
        for level in ac_group_dict.get('levels', []):
            door_schedules = []
            for door_schedule in level.get('door_schedules', []):
                door_schedule_proto = access_pb2.DoorSchedule(
                    doorID=door_schedule.get('door_id', 0),
                    scheduleID=door_schedule.get('schedule_id', 0),
                )
                door_schedules.append(door_schedule_proto)

            ac_level_proto = access_pb2.AccessLevel(
                ID=level.get('id', 0),
                name=level.get('name', ''),
                doorSchedules=door_schedules
            )
            levels.append(ac_level_proto)

        return levels

    def add_ac_group(self, device_info: DeviceInfo, group_name, ac_group_dict, delete=False):
        """ add AC group
             if delete = true , delete all group | levels in device before add new ones
             https://supremainc.github.io/g-sdk/api/access/#add
             """
        # testing ref  testAccessGroup(self, deviceID)

        device_id = device_info.device_id
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            # Add AC Levels
            logger.debug(f'[add_ac_group] Trying to add ac_levels on device:{device_info.device_label}')
            if delete:
                self.access_svc.deleteAllLevel(deviceID=device_id)

            levels = self.prepare_ac_level_proto(ac_group_dict)
            self.access_svc.addLevel(deviceID=device_id, levels=levels)
            logger.debug(f'[add_ac_group] Success adding levels ({len(levels)})'
                         f' of {group_name} on device:{device_info.device_label}')

            # Add AC groups
            logger.debug(f'[add_ac_group] Trying to add ac_group  {group_name} on device:{device_info.device_label}')
            if delete:
                self.access_svc.deleteAll(deviceID=device_id)  # delete all groups first

            levels_ids = [level.ID for level in levels]
            ac_group_proto = access_pb2.AccessGroup(
                ID=ac_group_dict.get('id', 0),
                name=ac_group_dict.get('name', ''),
                levelIDs=levels_ids
            )
            self.access_svc.add(device_id, [ac_group_proto])

            logger.debug(f'[add_ac_group] Success adding ac group:{group_name} on device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = (f'[add_ac_group] Cannot add AC level | group  on device:{device_info.device_label} ,'
                       f' group:{group_name},{e}')
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='2020', error_message=err_msg)

        return ResponseMessage(result=ac_group_proto)

    def delete_ac_group(self, device_info: DeviceInfo, ac_group_ids=[]):
        """ delete AC group
        if ac_group_ids is empty - delete all ac_group_ids
        https://supremainc.github.io/g-sdk/api/access/#delete"""
        device_id = device_info.device_id
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(f'deleting ac_group_ids {ac_group_ids} on device:{device_info.device_label}')
            if len(ac_group_ids) > 0:
                self.access_svc.delete(deviceID=device_id, groupIDs=ac_group_ids)
                logger.info(f'Success delete ac_groups from device:{device_info.device_label}')
            else:
                self.access_svc.deleteAll(deviceID=device_id)
                logger.info(f'Success delete all ac_groups from device:{device_info.device_label}')

                self.access_svc.deleteAllLevel(deviceID=device_id)
                logger.info(f'Success delete all ac_groups level from device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot delete ac_groups  from device:{device_info.device_label},{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='2025', error_message=err_msg)

        return ResponseMessage()

    def delete_ac_level(self, device_info: DeviceInfo, ac_level_ids=[]):
        """ delete AC group
        https://supremainc.github.io/g-sdk/api/access/#delete"""
        device_id = device_info.device_id
        try:
            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(f'deleting ac_levels {ac_level_ids} on device:{device_info.device_label}')
            if len(ac_level_ids) > 0:
                self.access_svc.deleteLevel(deviceID=device_id, levelIDs=ac_level_ids)
                logger.info(f'Success delete ac_levels from device:{device_info.device_label}')
            else:
                self.access_svc.deleteAllLevel(deviceID=device_id)
                logger.info(f'Success delete all ac_levels from device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot delete ac_levels  from device:{device_info.device_label},{e}'
            logger.error(err_msg)
            return ResponseMessage(result='', response_code='2026', error_message=err_msg)

        return ResponseMessage()

    def get_ac_groups(self, device_info: DeviceInfo):
        """get groups from device return result of  AccessGroup[]
        https://supremainc.github.io/g-sdk/api/access/#getlist
         """
        ac_groups = None
        device_id = device_info.device_id
        try:
            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                return conn_response  # return fail connection response

            # execute action getLog
            logger.debug(f'start get ac_groups from device:{device_info.device_label}')
            ac_groups = self.access_svc.getList(deviceID=device_id)
            logger.info(f'Success get ac_groups from device:{device_info.device_label}')

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get ac_groups from device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='2030', error_message=err_msg)

        return ResponseMessage(result=ac_groups)

    def get_ac_levels(self, device_info: DeviceInfo):
        """get groups from device return result of  AccessGroup[]
        https://supremainc.github.io/g-sdk/api/access/#getlist
         """
        ac_levels = None
        device_id = device_info.device_id
        try:
            # connect
            conn_response = self.connect_device(device_info)
            if conn_response.response_code != "1":
                return conn_response  # return fail connection response

            # execute action getLog
            logger.debug(f'start get ac_levels from device:{device_info.device_label}')
            ac_levels = self.access_svc.getLevelList(deviceID=device_id)
            logger.info(f'Success get ac_levels from device:{device_info.device_label}')

            # disconnect
            if not device_info.dedicated:  # if device dedicated don't disconnect device
                disconn_response = self.disconnect_device(device_info)
                if disconn_response.response_code != "1":
                    logger.error(disconn_response.error_message)

        except Exception as e:
            err_msg = f'Cannot get ac_levels from device:{device_info.device_label},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='2035', error_message=err_msg)

        return ResponseMessage(result=ac_levels)

    def set_ac_groups(self, device_info, user_id, ac_group_ids=[]):
        """ set access group
        https://supremainc.github.io/g-sdk/api/user/#setaccessgroup
        """
        user_groups = None
        device_id = device_info.device_id
        try:

            conn_res = self.connect_device(device_info=device_info)
            if conn_res.response_code != '1':
                return conn_res

            logger.debug(f'set ac_groups for user_id {user_id} on device:{device_info.device_label}')
            user_groups = user_pb2.UserAccessGroup(userID=user_id, accessGroupIDs=ac_group_ids)
            self.user_svc.setAccessGroup(deviceID=device_id, userAccessGroups=[user_groups])
            logger.info(f'Success set ac_groups for user_id:{user_id} on device:{device_info.device_label}')

            if not device_info.dedicated:
                disconn_res = self.disconnect_device(device_info=device_info)
                if disconn_res.response_code != '1':
                    logger.error(disconn_res.error_message)

        except Exception as e:
            err_msg = f'Cannot set ac_groups on device:{device_info.device_label}, user_id:{user_id},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='2040', error_message=err_msg)

        return ResponseMessage(result=user_groups)

    # TODO complete api
    def suprema_callback(self, device_info: DeviceInfo, response: ResponseMessage):
        """
         A callback function that is invoked when a response is received from a Suprema device.

         Parameters:
         - device_id_info (DeviceInfo): Information about the device that sent the response.
         - response (ResponseMessage): The response message received from the device.

         Returns:
         - Optional[ResponseMessage]: If the connection to the device was successful,
         """
        conn_response = self.connect_device(device_info.ip_address, device_info.port, device_info.useSSL)
        if conn_response.response_code != "1":
            return conn_response  # return fail connection response

        if not device_info.dedicated:  # if device dedicated don't disconnect device
            disconn_response = self.disconnect_device([device_info.device_id])
            # TODO: use logger to log disconnection fail

        return response

    def print_to_file(self, file, result):
        try:
            # open the file for writing
            file = open(file, "w")

            # write the response object to the file 
            file.write(str(result))

            # close the file after writing is complete 
            file.close()
        except Exception as e:
            err_msg = f'Cannot write result to file:{file},{e}'
            logger.error(err_msg)

            return ResponseMessage(result='', response_code='1300', error_message=err_msg)

        return ResponseMessage(result=result)

    def get_auth_mode(self, mode):
        if mode:
            return self.string_to_enum(auth_pb2.AuthMode, mode)
        else:
            return None

    def get_auth_mode_str(self, mode_enum):
        if mode_enum:
            return self.enum_to_string(auth_pb2.AuthMode, mode_enum)
        else:
            return None

    def enum_to_string(self, enum_type, enum):
        try:
            return enum_type.keys()[enum_type.values().index(enum)]
        except ValueError:
            return None
            # raise ValueError(f"{enum} is not a valid member of {enum_type.__name__}")

    def string_to_enum(self, enum_type, enum_str):
        try:
            return getattr(enum_type, enum_str)
        except AttributeError:
            return None
            # raise ValueError(f"{enum_str} is not a valid member of {enum_type.__name__}")

        # # Example usage:
        # mode_enum = string_to_enum(auth_pb2.AuthMode, 'AUTH_EXT_MODE_CARD_ONLY')
        # print(mode_enum)

    # endregion ------------------------------------------------------------

# if __name__ == '__main__':
# run_test()

# def run_event_test(self,device_id):
#     print('>> Start Read Log ....')
#     logs= event_svc.getLog(deviceID=device_id)
#     print(logs)

# def run_enroll_test(self,device_id):
#     devID=device_id
#     #info=device_svc.getInfo(devID)
#     device_id_info=device_svc.getCapInfo(devID)
#     #print(device_id_info)


#     # card ---------------------------------------------------
#     print('>> Scan Card ....')
#     card= card_svc.scan(devID)
#     if card:
#         #print(card)
#         card_bytes=bytes(card.CSNCardData.data)    #bytes(str(card.CSNCardData.data),'utf-8') # bytes.fromhex(str(card.CSNCardData.data))#
#         #print(card_bytes)
#         #b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x04<N\xb2\xd3/\x80'
#         card_no=int(card_bytes.hex(),16) # display number on devices , biostar 2
#         print('Success Card_No:',card_no)

#     #Finger ---------------------------------------------------
#     print('>> Scan Finger ....')
#     template1=finger_svc.scan(deviceID=devID,templateFormat='TEMPLATE_FORMAT_SUPREMA',qualityThreshold=80)
#     template2=finger_svc.scan(deviceID=devID,templateFormat='TEMPLATE_FORMAT_SUPREMA',qualityThreshold=80)
#     if template1:
#         print('Success Read finger',template1)
#     #print(finger)

#     # region user enroll --------------------------------------
#     users=[]
#     USER_ID='556677'
#     user_svc.delete(devID,[USER_ID])

#     start_date_time = datetime.datetime.now() + timedelta(days=-1)
#     end_date_time = start_date_time + timedelta(days=365*5)
#     start_date_time_unix=int(time.mktime(start_date_time.timetuple())) # convert datetime to unix time
#     end_date_time_unix = int(time.mktime(end_date_time.timetuple())) # convert datetime to unix time
#     print("Given Date:",start_date_time,end_date_time)
#     print("UNIX timestamp:",start_date_time_unix,end_date_time_unix)


#     user_hdr = user_pb2.UserHdr(ID=USER_ID)
#     user_setting = user_pb2.UserSetting(startTime=start_date_time_unix,endTime=end_date_time_unix)

#     if device_id_info.type == device_pb2.FACESTATION_F2 or device_id_info.type == device_pb2.FACESTATION_F2_FP:
#         user_setting.cardAuthExtMode = auth_pb2.AUTH_EXT_MODE_CARD_ONLY
#         user_setting.fingerAuthExtMode = auth_pb2.AUTH_EXT_MODE_FINGERPRINT_ONLY
#         user_setting.faceAuthExtMode = auth_pb2.AUTH_EXT_MODE_FACE_ONLY
#     else:
#         user_setting.cardAuthMode = auth_pb2.AUTH_MODE_CARD_ONLY
#         user_setting.biometricAuthMode = auth_pb2.AUTH_MODE_BIOMETRIC_ONLY


#     userCards = user_pb2.UserCard(userID=USER_ID, cards=[card.CSNCardData])

#     #fingerData = finger_pb2.FingerData(templates=[finger.templateData])
#     #userFingers = [user_pb2.UserFinger(userID=USER_ID, fingers=[fingerData])]

#     fingerData = finger_pb2.FingerData(templates=[template1.templateData, template2.templateData])
#     userFingers = [user_pb2.UserFinger(userID=USER_ID, fingers=[fingerData])]


#     # cards = [card.CSNCardData],
#     # fingers = [fingerData],

#     userInfo = user_pb2.UserInfo(
#         hdr = user_hdr,
#         setting = user_setting,
#         name='gsdk_abadr')

#     users.append(userInfo)

#     # config=auth_svc.getConfig(deviceID=devID)
#     # print(config)


#     user_svc.enroll(deviceID=devID,users=users,overwrite=True)
#     print('Success user enroll')
#     user_svc.setFinger(deviceID=devID,userFingers=userFingers)
#     print('Success register user fingers')
#     user_svc.setCard(deviceID=devID,userCards=[userCards])
#     print('Success register user cards')

#     users_list=user_svc.getUser(deviceID=devID,userIDs=[USER_ID])
#     print('current user setting:',users_list[0].setting)


#   Message UserInfo {
#   UserHdr hdr;
#   UserSetting setting;
#   string name;
#   repeated card.CSNCardData cards;
#   repeated finger.FingerData fingers;
#   repeated face.FaceData faces;
#   repeated uint32 accessGroupIDs;
#   repeated tna.JobCode jobCodes;
#   bytes PIN;
#   bytes photo;
# }
# setting {
#   biometricAuthMode: 255
#   cardAuthMode: 255
#   IDAuthMode: 255
#   securityLevel: 2
#   faceAuthExtMode: 255
#   fingerAuthExtMode: 255
#   cardAuthExtMode: 255
#   IDAuthExtMode: 255
# }

# setting {
#   startTime: 1673028392
#   endTime: 1830708392
#   faceAuthExtMode: 255
#   fingerAuthExtMode: 255
#   cardAuthExtMode: 255
#   IDAuthExtMode: 255
# }

# if deviceType == device_pb2.FACESTATION_F2 or deviceType == device_pb2.FACESTATION_F2_FP:
#     newUser.setting.cardAuthExtMode = auth_pb2.AUTH_EXT_MODE_CARD_ONLY
#     newUser.setting.fingerAuthExtMode = auth_pb2.AUTH_EXT_MODE_FINGERPRINT_ONLY
#     newUser.setting.faceAuthExtMode = auth_pb2.AUTH_EXT_MODE_FACE_ONLY
#   else:
#     newUser.setting.cardAuthMode = auth_pb2.AUTH_MODE_CARD_ONLY
#     newUser.setting.biometricAuthMode = auth_pb2.AUTH_MODE_BIOMETRIC_ONLY


# Helper -------------------------------------------------------
# import datetime
# import time
# date_time = datetime.datetime(2022, 6, 3, 12, 0, 50)
# print("Given Date:",date_time)
# print("UNIX timestamp:",
# (time.mktime(date_time.timetuple())))

# region add current path to python path ----------------------------

# sys.path.append(r"E:\LP_GOOGLE_DRIVE\LP\100_TA\poc\sources\python_client_sdk")
# sys.path.append(r"E:\LP_GOOGLE_DRIVE\LP\100_TA\poc\sources\python_client_sdk\example")
# sys.path.append(r"E:\LP_GOOGLE_DRIVE\LP\100_TA\poc\sources\python_client_sdk\biostar\service")
# os.environ['PYTHONPATH']+=r";E:\LP_GOOGLE_DRIVE\LP\100_TA\poc\sources\python_client_sdk\example;"
##pprint(sys.path)
# endregion ---------------------------------------------------------------
