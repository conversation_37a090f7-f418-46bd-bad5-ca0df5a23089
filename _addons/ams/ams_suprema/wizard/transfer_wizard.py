# -*- coding: utf-8 -*-


from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class MakeTransfer(models.TransientModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.transfer_wizard"
    _description = "Device Transfer Wizard"
    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    user_id = fields.Many2one('ams_suprema.user')
    device_ids = fields.Many2many('ams_suprema.device')
    device_group_ids = fields.Many2many('ams_base.device_group')

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.onchange('device_group_ids')
    def _onchange_device_ids(self):
        group_devices = self.env['ams_suprema.device'].search([('device_group_id', 'in', self.device_group_ids.ids)])
        if group_devices:
            self.device_ids = [(4, device.id) for device in group_devices]

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_transfer(self):
        """create device action for new devices added to enroll user or create device action
        for deleted devices to delete user"""
        deleted_devices = self.user_id.device_ids - self.device_ids
        self.user_id._create_device_action('delete', deleted_devices)

        added_devices = self.device_ids - self.user_id.device_ids
        self.user_id._create_device_action('add', added_devices)

        self.user_id.device_ids = [(6, 0, list(set(self.device_ids.ids)))]

        # return notification with deleted devices or added devices count
        title = _('Success')
        message = _(
            f'enroll action in progress on new devices: {len(added_devices)}'
            f' and delete action in progress on deleted devices: {len(deleted_devices)}'
        )
        if len(added_devices) == 0 and len(deleted_devices) == 0:
            title = _('Warning')
            message = _('No  new actions created')

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': title,
                'message': message,
            }
        }
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
