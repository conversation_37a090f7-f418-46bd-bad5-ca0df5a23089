
## 1- Device ----------------------------------------------------
### create basic views for device (tree,form,search)
_inherit = "ams_base.device"
1)action_check_status() 
2)action_get_logs() #TODO add cron job
3)action_clear_logs() #TODO add cron job
### Basics 
device_id:"546832418"
ip_adress :"************"
port:"51211"
state:[pending,online,offline] # online if last log collected without error, else offline
status: [TCP_CONNECTED,TCP_DISCONNECTED] # used gsdk to know connected or not
last_event_id:10007

### Info Page (replace fields name with python rules)
MACAddr: "00:17:FC:98:00:22"
modelName: "BS2-OMPW"
firmwareVersion: "1.6.0 2018/03/04 04:25:24"
kernelVersion: "1.3.0 2018/01/02 09:04:54"
BSCoreVersion: "2.6.0 Mar  4 2018 04:22:11"
boardVersion: "1.0.0"

type: "BIOSTATION_2"

### Capability Page
maxNumOfUser: 500000
PINSupported: true
cardSupported: true
card1xSupported: true
SEOSSupported: true
fingerSupported: true
userNameSupported: true
alphanumericIDSupported: true
WLANSupported: true
TNASupported: true
wiegandSupported: true
wiegandMultiSupported: true
triggerActionSupported: true
DSTSupported: true
DNSSupported: true
OSDPKeySupported: true

faceSupported
imageLogSupported
userPhotoSupported
userPhraseSupported
VOIPSupported
QRSupported
RS485ExtSupported
jobCodeSupported

## 2- User ------------------------------------------------------
name="ams_suprema.user"

1)action_open_transfer_wizard()
2)action_enroll()
3)action_delete_user() #delete user from device
### Odoo fields
    user_type:[employee,visitor]
    employee_id: 1 #Many2one hr.employee --> enable search with employee_number , enroll_number
    enroll_number:"5000"
    name: "Abdelrazek Badr"
   
    device_ids :[] #Many2many ams_suprema.device

    number_of_year:1
    start_datetime:
    end_datetime:

    card_no:""
    card_data:""
    card_type:"CARD_TYPE_CSN"

    finger_quality:[20,40,60,80] # field selction [low,standard,high,highest]
    finger_template_format:"TEMPLATE_FORMAT_SUPREMA" <=default_val
    finger1_template1:"" # not appear in xml , replace by static image if has data
    finger1_template2:""
    finger2_template1:""
    finger2_template2:""

    face_enroll_threshold:"BS2_FACE_ENROLL_THRESHOLD_DEFAULT" <=default_val
    face_data ="" # not appear in xml , replace by static face image if has data

    auth_mode:"AUTH_MODE_BIOMETRIC_ONLY" # selection private authentication  
    
    

### Actual response user info fields
    name: "Abdullrazzaq Alsaid"
    cards {
    type: CARD_TYPE_CSN
    size: 32
    data: "\000\..222\357\2223"
    }
    fingers {
    templates: "E+.....377\377\000"
    templates: "D8U.....\000\"
    }
    fingers {
    templates: ""
    }
    accessGroupIDs: 1
    , hdr {
    ID: "13"
    numOfCard: 1
    numOfFinger: 1
    }
    setting {
    startTime: 978307200
    endTime: 1924991940
    biometricAuthMode: 255
    cardAuthMode: 255
    IDAuthMode: 255
    faceAuthExtMode: 255
    fingerAuthExtMode: 255
    cardAuthExtMode: 255
    IDAuthExtMode: 255
    }

## 3- Event log -------------------------------------------------
### odoo fields
    event_id: 69460
    timestamp: 1673096375
    event_datetime:2023-01-01 15:00:00
    device_id: 546832418   1
    event_code: 4352 1
    sub_code: 2
    card_data: "1192208612142976"
    state:[pending,executed]
    executed_datetime:2023-01-01 15:15:00 
    has_image:False
    temperature:30;
    ----------------------------------
    actual fields name from device to map from:
    ID: 69460
    timestamp: 1673096375
    deviceID: 546832418 
    eventCode: 4352
    subCode: 2
    cardData: "1192208612142976"
    bool hasImage;
    bool changedOnDevice;
    uint32 temperature;

## 4- Device action
define queue of user action [add,delete,update] on devices 