### biometricAuthMode -------------------------------------------------
AUTH_MODE_BIOMETRIC_ONLY	Fingerprint or Face
AUTH_MODE_BIOMETRIC_PIN	(Fingerprint or Face) + PIN
0xFE	Not permitted
0xFF	Undefined. Use the settings of AuthConfig

### cardAuthMode -------------------------------------------------
* AUTH_MODE_CARD_ONLY	Card

AUTH_MODE_CARD_BIOMETRIC	Card + (Fingerprint or Face)
AUTH_MODE_CARD_PIN	Card + PIN
AUTH_MODE_CARD_BIOMETRIC_OR_PIN	Card + (Fingerprint or Face or PIN)
AUTH_MODE_CARD_BIOMETRIC_PIN	Card + (Fingerprint or Face) + PIN
0xFE	Not permitted
0xFF	Undefined. Use the settings of AuthConfig

### idAuthMode -------------------------------------------------
AUTH_MODE_ID_BIOMETRIC	ID + (Fingerprint or Face)
AUTH_MODE_ID_PIN	ID + PIN
AUTH_MODE_ID_BIOMETRIC_OR_PIN	ID + (Fingerprint or Face or PIN)
AUTH_MODE_ID_BIOMETRIC_PIN	ID + (Fingerprint or Face) + PIN
0xFE	Not permitted
0xFF	Undefined. Use the settings of AuthConfig

### securityLevel -------------------------------------------------
Specify the security level for fingerprint and face verification.
0	Undefined. Use the settings of FingerConfig and FaceConfig
1	Least secure
2	Less secure
3	Normal
4	More secure
5	Most secure

### faceAuthExtMode -------------------------------------------------
* FaceStation F2 and BioStation 3 only

AUTH_EXT_MODE_FACE_ONLY	Face
AUTH_EXT_MODE_FACE_FINGERPRINT	Face + Fingerprint
AUTH_EXT_MODE_FACE_PIN	Face + PIN
AUTH_EXT_MODE_FACE_FINGERPRINT_OR_PIN	Face + (Fingerprint or PIN)
AUTH_EXT_MODE_FACE_FINGERPRINT_PIN	Face + Fingerprint + PIN
0xFE	Not permitted
0xFF	Undefined. Use the settings of AuthConfig

### fingerAuthExtMode -------------------------------------------------
* FaceStation F2 and BioStation 3 only

AUTH_EXT_MODE_FINGERPRINT_ONLY	Fingerprint
AUTH_EXT_MODE_FINGERPRINT_FACE	Fingerprint + Face
AUTH_EXT_MODE_FINGERPRINT_PIN	Fingerprint + PIN
AUTH_EXT_MODE_FINGERPRINT_FACE_OR_PIN	Fingerprint + (Face or PIN)
AUTH_EXT_MODE_FINGERPRINT_FACE_PIN	Fingerprint + Face + PIN
0xFE	Not permitted
0xFF	Undefined. Use the settings of AuthConfig

### cardAuthExtMode -------------------------------------------------
* FaceStation F2 and BioStation 3 only

AUTH_EXT_MODE_CARD_ONLY	Card
AUTH_EXT_MODE_CARD_FACE	Card + Face
AUTH_EXT_MODE_CARD_FINGERPRINT	Card + Fingerprint
AUTH_EXT_MODE_CARD_PIN	Card + PIN
AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT	Card + (Face or Fingerprint)
AUTH_EXT_MODE_CARD_FACE_OR_PIN	Card + (Face or PIN)e
AUTH_EXT_MODE_CARD_FINGERPRINT_OR_PIN	Card + (Fingerprint or PIN)
AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT_OR_PIN	Card + (Face or Fingerprint or PIN)
AUTH_EXT_MODE_CARD_FACE_FINGERPRINT	Card + Face + Fingerprint
AUTH_EXT_MODE_CARD_FACE_PIN	Card + Face + PIN
AUTH_EXT_MODE_CARD_FINGERPRINT_FACE	Card + Fingerprint + Face
AUTH_EXT_MODE_CARD_FINGERPRINT_PIN	Card + Fingerprint + PIN
AUTH_EXT_MODE_CARD_FACE_OR_FINGERPRINT_PIN	Card + (Face or Fingerprint) + PIN
AUTH_EXT_MODE_CARD_FACE_FINGERPRINT_OR_PIN	Card + Face + (Fingerprint or PIN)
AUTH_EXT_MODE_CARD_FINGERPRINT_FACE_OR_PIN	Card + Fingerprint + (Face or PIN)
0xFE	Not permitted
0xFF	Undefined. Use the settings of AuthConfig

### idAuthExtMode -------------------------------------------------
* FaceStation F2 and BioStation 3 only

AUTH_EXT_MODE_ID_FACE	ID + Face
AUTH_EXT_MODE_ID_FINGERPRINT	ID + Fingerprint
AUTH_EXT_MODE_ID_PIN	ID + PIN
AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT	ID + (Face or Fingerprint)
AUTH_EXT_MODE_ID_FACE_OR_PIN	ID + (Face or PIN)e
AUTH_EXT_MODE_ID_FINGERPRINT_OR_PIN	ID + (Fingerprint or PIN)
AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT_OR_PIN	ID + (Face or Fingerprint or PIN)
AUTH_EXT_MODE_ID_FACE_FINGERPRINT	ID + Face + Fingerprint
AUTH_EXT_MODE_ID_FACE_PIN	ID + Face + PIN
AUTH_EXT_MODE_ID_FINGERPRINT_FACE	ID + Fingerprint + Face
AUTH_EXT_MODE_ID_FINGERPRINT_PIN	ID + Fingerprint + PIN
AUTH_EXT_MODE_ID_FACE_OR_FINGERPRINT_PIN	ID + (Face or Fingerprint) + PIN
AUTH_EXT_MODE_ID_FACE_FINGERPRINT_OR_PIN	ID + Face + (Fingerprint or PIN)
AUTH_EXT_MODE_ID_FINGERPRINT_FACE_OR_PIN	ID + Fingerprint + (Face or PIN)
0xFE	Not permitted
0xFF	Undefined. Use the settings of AuthConfig