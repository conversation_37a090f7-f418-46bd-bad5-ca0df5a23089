## Installation

### update 2024 & rebuild from suprema 
    >> python -m pip install grpcio
    >> python -m pip install grpcio-status
    >> pip install deprecated

    *For Windows - activate venv
    odoo>>.\.venv\Scripts\activate.ps1
    
    *For Linux - activate venv
    odoo>> source ./.venv/bin/activate

### downgrade in case  old version for compatibility suprema old lib
pip install grpcio==1.42.0
pip install grpcio-status==1.42.0
pip install protobuf==3.20.*
pip install biostarPython -- this a third party not recommended
pip3 freeze > requirements.txt -- to extract libraries dependencies

### Create Client & Server  certificates  
* copy device gateway to python installation 
* add path in system environment 
Windows:
* create self sign certificate
  >>  device_gateway_x64 -c
* run server
  >>  device_gateway_x64
* run server based on Suprema documentation 
   >>  device_gateway

Linux:
* move device gateway to /opt/gsdk & run 
  >> chmod +x /opt/gsdk/device_gateway_linux_x64

* create self sign certificate
  >>  sudo /opt/gsdk/device_gateway_linux_x64 -c
* run server
  >>  sudo /opt/gsdk/device_gateway_linux_x64

Create Service:---------------------------------
  >> sudo nano /opt/gsdk/gsdk.service
  
[Unit]
Description=Suprema Device Gateway
After=network.target

[Service]
Type=simple
WorkingDirectory=/opt/gsdk  
ExecStart=/opt/gsdk/device_gateway_linux_x64

[Install]
WantedBy=multi-user.target

Run Service in background-----------------
sudo systemctl enable /opt/gsdk/gsdk.service
sudo systemctl start gsdk.service
sudo systemctl stop gsdk.service

sudo systemctl daemon-reload
sudo systemctl restart gsdk.service
---------------------------------------



* copy certificate to client path to open gateway channel 
* Move biostar,gsdk,gsdk_custom folder to python package root folder and add to python path
in system environment 

### log
- enable server log device_gateway/config.json
- enable client log ams_suprema/logs -- recommend  to move outside
### References 
release
https://github.com/supremainc/g-sdk/releases/

development
https://supremainc.github.io/g-sdk/python/install/
https://supremainc.github.io/g-sdk/python/quick/

### Run GSDK Server on Local PC:
cd C:\Python310\biostar\device_gateway
C:\Python310\biostar\device_gateway>device_gateway_x64