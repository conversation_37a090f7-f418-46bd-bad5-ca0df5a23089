    

    # max_user_num = fields.Integer(string="Max Users")
    # 
    # pin_supported = fields.<PERSON><PERSON><PERSON>(string="PIN Supported")
    # card_supported = fields.Boolean()
    # card1x_supported = fields.Boolean()
    # seos_supported = fields.Boolean(string="SEOS Supported")
    # finger_supported = fields.Bo<PERSON>an(string="Finger Supported")
    # username_supported = fields.Boolean()
    # alphanumeric_id_supported = fields.Boolean()
    # wlan_supported = fields.<PERSON><PERSON><PERSON>(string="WLAN Supported")
    # tna_supported = fields.<PERSON><PERSON><PERSON>(string="TNA Supported")
    # wiegand_supported = fields.Boolean(string="Wiegand Supported")
    # wiegand_multi_supported = fields.Boolean(string="Wiegand Multi Supported")
    # trigger_action_supported = fields.Boolean(string="Trigger Action Supported")
    # dts_supported = fields.<PERSON><PERSON><PERSON>(string="DST Supported")
    # dns_supported = fields.Boolean(string="DNS Supported")
    # osdp_key_supported = fields.Bo<PERSON>an(string="OSDP Key Supported")
    # face_supported = fields.Boolean()
    # image_log_supported = fields.<PERSON>olean()
    # user_photo_supported = fields.Boolean()
    # user_phrase_supported = fields.<PERSON>olean()
    # voice_supported = fields.<PERSON>olean()
    # qr_supported = fields.Boolean(string="QR Supported")
    # rs485_ext_supported = fields.Boolean(string="RS485 Ext Supported")
    # job_code_supported = fields.Boolean()
    # 
    # ext_auth_supported = fields.Boolean(help="extAuthSupported")