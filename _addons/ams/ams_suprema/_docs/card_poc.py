
def deserialize_card_number(card_data):
    card_no = None
    if card_data:
        card_bytes = bytes(card_data)
        card_no = int(card_bytes.hex(), 16)
        return card_no



def serialize_card_number(card_number):
    if card_number is None:
        return None

    hex_string = hex(card_number)[2:]  # Remove '0x' prefix
    print('hex_string:' ,hex_string)
    if len(hex_string) % 2 != 0:
        hex_string = '0' + hex_string  # Ensure even length

    byte_string = bytes.fromhex(hex_string)
    return byte_string

# Example input data
card_data = b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x04<N\xb2\xd3/\x80'

card_data2 =b'\x04<N\xb2\xd3/\x80'
# Calling the method
card_number = deserialize_card_number(card_data2)
print("Card Number:", card_number)
# Output :1192208612142976


# Original card number
card_number = 1192208612142976
card_number = 155556

# Calling the function to get the byte array
reversed_data = serialize_card_number(card_number)

print("Reversed Data:", reversed_data)

