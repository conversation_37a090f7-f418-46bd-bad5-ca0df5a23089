
###
today action for ts,punch logs , device_log
### Device
- add action delete user from device
- smart action to view all users related to device 
- add action to sync users (download user info from selected device) [done]
- add action transfer device from group to another group
- add fields:
  * is_enroll:Boolean # to be filter enrollment device in user form view 
- Realtime logs view event date > current -1 minutes 
- implement auto refresh

https://supremainc.github.io/g-sdk/api/auth/#setconfig

### ams_base
- add basic views to make ability to inherit in suprema user
### Device logs
  - execute device logs to move time sheet [Done]

### Device Action


### User
- delete user from devices list wizard
- enroll with mobile card

suprema.user > action_transfer:
when execute action_transfer user on devices create device action 
if new devices linked to user -> create device action records to add user on devices (action_type='add')
if new devices unlinked to user -> create device action records to delete user on devices (action_type='delete')

suprema.user > action_enroll :
when execute action_enroll create device action records to add user on devices (action_type='add')

suprema.user > action_delete :
when execute action_delete create device action records to delete user on devices (action_type='delete')


TODO: later
search if device found in dedicated list -->
execute action directly else cron job will be executed or execute manual
 
  