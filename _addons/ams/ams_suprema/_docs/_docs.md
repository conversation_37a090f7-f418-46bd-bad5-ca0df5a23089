
### Implementation ------------------------------------
* device gateway setup
    - certificate
    - client 
    - server

* device 
    - connect
    - disconnect
    - search
    - realtime status (connected,disconnected)

* user
    - scan_card 
    - scan_finger 
    - scan_face 
    - user enroll,delete ,set_card,set_finger,set_face
* event
    - get_log
    - clear_log

### Implementation Access Control ----------------------


#### Notes
Covert Bytes to number 
>> bytes_d=b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x04<N\xb2\xd3/\x80'
>> print(int(bytes_d.hex(),16)) #1192208612142976


from odoo import api,  registry,SUPERUSER_ID
import os
from odoo.tools import config
from odoo.modules import get_modules
print(get_modules())
def post_init_hook(cr, registry):
    # Define the Odoo environment
    env = api.Environment(cr, SUPERUSER_ID, {})
    # Get the value of the configuration parameter
    my_parameter = env['ir.config_parameter'].sudo().get_param('ams_suprema.gateway_ip', default=False)


def pre_init_hook(cr):
    # Define the Odoo environment
    env = api.Environment(cr, SUPERUSER_ID, {})
    # Get the value of the configuration parameter
    my_parameter = env['ir.config_parameter'].sudo().get_param('ams_suprema.gateway_ip', default=False)

[b'\x00\x00\x03\x00\xfa\x00\x93\x01\x06\x01^\x01c\x0c\xfe\x0b$H]>\x00\x00x\x00\x00\x00\x00\x00\x00\x00\x00\x00w|\x86\x9ejyvq\x85u\x8cxz\x8f\x8e\x81~s\x7f|w{\x82utu\x84qm\x8bk\x80\x7fn\x84\x86{\x82\x87\x88\x84\x82\x85\x85\x87~xs\x85\x8fUy\x89xn~y~\x88}xh\x91t{\x98\x95vsv\x8a\x9c\x92\x82\x88maoyps\x85\x83\x83\x94g\x7f\x89t\x7f\x85~\x84ik}\x8ae\x9d_\x89|\x92_\x91\x93\x80\x82~w\x8e\x84\xa3i\x88w\x86im\x88rvfxir}\x89\x8fm\x8a\x8cT~\x7fv\x7f\x82\x87\x7fqrxwd\x81\x85\x85x\x7fq{~v\x86ys\x84\x7f\x89\x93u\x9a\x7fv\x8cps\x84z\x85\x80\x80\x8e\x89\x8d\x7fv\x98o\x95p{p\x84\x92s\x9d\x7f\x7f\x82\x89u}u~\x7fq\x8c|w\x97s\x9dut\x8c]\x83\x8a\x88bzyt\x8ey|\x92\x7fp\x86vo\x8c\x82\x88\x89\x97\x98rz\x7f\x91\x91mvz\x9a\x7f\x87r\x7f\x87z\x84p\x80~\x7fqvs\x97\x97\x89|\x84\x82\x92\x8fzi\x92\x99y\x97z\x7fyv\x80s\x8a\x94tx\x85\x8e}\x7f\x82yy\x95\x80\x83oet}\x82f\x89\x97|m\x97\x86\x8dgu\x89{y~~\x83vx\x91\x81u\x87tqwsjx\x8fakplo\x7f\x85\x84~s\x91}\x81\x88{y\x88{y\x96\x96lto|m\x8cx\x83\x90\x88\x8bx\x87\xa2tx\x98\x82z\x86~\x7fuo\x8eos\x81y\x85\x7f{xm\x85\xaa[\x7f\x84mqo\x96\x89t|\x81\x8e\x8a\x9em\x93\x87\x8fsvm`\x8a\x93\x7ftu\x8d\x8dop\x8c{rl\x94\x82hz\x92\x88\x85a\x8f|\x99\x83wv\x8b\x9dsx\x87\x93\x82\x8e|i\x80v\x90\x8e{\x9aw\x84sw}u\x9ax\x8e\x81\x7fu\x82\x8d\x82hY\x81\x8b|unys|\x86\x8b\x91{z\x8a\x7f}\x92k\x86\x8a\x89\x83\x98j|\x85w\x8f\x9cvtb\xa6}\x81x\x82\x8c\x81uwm\x84j\x87\x88k\x90~\x88\x9a\x92\x00\x00\x00\x00\x00\x00\x00\x00']
b'\x00\x00\x03\x00\xfa\x00\x93\x01\x06\x01^\x01c\x0c\xfe\x0b$H]>\x00\x00x\x00\x00\x00\x00\x00\x00\x00\x00\x00w|\x86\x9ejyvq\x85u\x8cxz\x8f\x8e\x81~s\x7f|w{\x82utu\x84qm\x8bk\x80\x7fn\x84\x86{\x82\x87\x88\x84\x82\x85\x85\x87~xs\x85\x8fUy\x89xn~y~\x88}xh\x91t{\x98\x95vsv\x8a\x9c\x92\x82\x88maoyps\x85\x83\x83\x94g\x7f\x89t\x7f\x85~\x84ik}\x8ae\x9d_\x89|\x92_\x91\x93\x80\x82~w\x8e\x84\xa3i\x88w\x86im\x88rvfxir}\x89\x8fm\x8a\x8cT~\x7fv\x7f\x82\x87\x7fqrxwd\x81\x85\x85x\x7fq{~v\x86ys\x84\x7f\x89\x93u\x9a\x7fv\x8cps\x84z\x85\x80\x80\x8e\x89\x8d\x7fv\x98o\x95p{p\x84\x92s\x9d\x7f\x7f\x82\x89u}u~\x7fq\x8c|w\x97s\x9dut\x8c]\x83\x8a\x88bzyt\x8ey|\x92\x7fp\x86vo\x8c\x82\x88\x89\x97\x98rz\x7f\x91\x91mvz\x9a\x7f\x87r\x7f\x87z\x84p\x80~\x7fqvs\x97\x97\x89|\x84\x82\x92\x8fzi\x92\x99y\x97z\x7fyv\x80s\x8a\x94tx\x85\x8e}\x7f\x82yy\x95\x80\x83oet}\x82f\x89\x97|m\x97\x86\x8dgu\x89{y~~\x83vx\x91\x81u\x87tqwsjx\x8fakplo\x7f\x85\x84~s\x91}\x81\x88{y\x88{y\x96\x96lto|m\x8cx\x83\x90\x88\x8bx\x87\xa2tx\x98\x82z\x86~\x7fuo\x8eos\x81y\x85\x7f{xm\x85\xaa[\x7f\x84mqo\x96\x89t|\x81\x8e\x8a\x9em\x93\x87\x8fsvm`\x8a\x93\x7ftu\x8d\x8dop\x8c{rl\x94\x82hz\x92\x88\x85a\x8f|\x99\x83wv\x8b\x9dsx\x87\x93\x82\x8e|i\x80v\x90\x8e{\x9aw\x84sw}u\x9ax\x8e\x81\x7fu\x82\x8d\x82hY\x81\x8b|unys|\x86\x8b\x91{z\x8a\x7f}\x92k\x86\x8a\x89\x83\x98j|\x85w\x8f\x9cvtb\xa6}\x81x\x82\x8c\x81uwm\x84j\x87\x88k\x90~\x88\x9a\x92\x00\x00\x00\x00\x00\x00\x00\x00'