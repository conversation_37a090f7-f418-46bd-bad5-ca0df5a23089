
### Introduction

This document provides an overview of the component diagram for all custom modules within the system.

- **ams_base**:
  This module serves as a shared foundation among all custom modules. All modules are required to depend on `ams_base`. The core model within this module is `ams_base.abstract_model`, which serves as the base model for all other models across the modules.

- **ta**:
  The `ta` module is responsible for implementing the business logic related to time attendance. It encompasses policies governing timesheet management, including rules for handling delays, shortages, overtime, working hours, and generating reports.

- **ta_hr_att**:
  This module facilitates the integration between our custom `ta` module and the `hr_attendance` module in Odoo. It ensures seamless synchronization of punch records, especially when clients opt to utilize specific devices for punch-ins while leveraging Odoo's standard attendance module.

- **ta_time_off**:
  The `ta_time_off` module streamlines integration between our custom `ta` module and the `hr_holidays` module in Odoo. Its primary function is to synchronize time off records when employees are on leave, ensuring accurate and up-to-date records.

- **ams_suprema**:
  Within the `ams_suprema` module, the focus lies on device management, user enrollment, event logging, and communication with the G-SDK API. It encapsulates the business logic required for efficient management of devices used within the system.

- **ams_suprema_ac**:
  This module specializes in access control, encompassing features such as door management, access level management, access group management, and user access group management. It ensures robust security measures and streamlined access management within the system.

### Component Diagram:
this diagrame represent modules and dependencies
```mermaid
graph BT;
    ams_base[ams_base <br>Base Module]
    ta[ta<br>Time Attendance]
    ams_suprema[ams_suprema <br> Device Manager]
    ams_suprema_ac[ams_suprema_ac <br> Access Control]
    ta_hr_att[ta_hr_att <br> HR- Attendance<br>Integration]
    ta_time_off
    hr_attendance[hr_attendance <Br> Attendance - Odoo]
    hr_holidays[hr_holidays <Br> Time Off - Odoo]
    hr[hr <Br> Employee - Odoo]
    
     ta --> ams_base
     ta --> hr
     ta_hr_att --> ta
     ta_hr_att --> hr_attendance
     
     ta_time_off --> ta
     ta_time_off --> hr_holidays
     
     ams_suprema --> ams_base
     ams_suprema_ac --> ams_suprema
     
    
