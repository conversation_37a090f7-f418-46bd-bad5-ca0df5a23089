class DeviceInfo:
    # def __init__(self, cab_info_json_data):
    #     self.cab_info_json_data = cab_info_json_data

    def __init__(self, ip_address, device_id, port=51211, name='',
                 dedicated=False, useSSL=False, cab_info_json_data=None):
        self.ip_address = ip_address
        self.device_id = device_id
        self.port = port
        self.dedicated = dedicated
        self.useSSL = useSSL
        self.name = name
        self.cab_info_json_data = cab_info_json_data
        # self.cap_info_type todo assined from device

    @property
    def device_label(self):
        return f"[{self.device_id}] {self.name}"

    @property
    def max_users(self):
        return int(self.cab_info_json_data.get('maxUsers', 0))

    @property
    def max_event_logs(self):
        return int(self.cab_info_json_data.get('maxEventLogs', 0))

    @property
    def max_image_logs(self):
        return int(self.cab_info_json_data.get('maxImageLogs', 0))

    @property
    def max_blacklists(self):
        return int(self.cab_info_json_data.get('maxBlacklists', 0))

    @property
    def max_operators(self):
        return int(self.cab_info_json_data.get('maxOperators', 0))

    @property
    def max_cards(self):
        return int(self.cab_info_json_data.get('maxCards', 0))

    @property
    def max_faces(self):
        return int(self.cab_info_json_data.get('maxFaces', 0))

    @property
    def max_fingerprints(self):
        return int(self.cab_info_json_data.get('maxFingerprints', 0))

    @property
    def max_user_names(self):
        return int(self.cab_info_json_data.get('maxUserNames', 0))

    @property
    def max_user_images(self):
        return int(self.cab_info_json_data.get('maxUserImages', 0))

    @property
    def max_user_jobs(self):
        return int(self.cab_info_json_data.get('maxUserJobs', 0))

    @property
    def max_user_phrases(self):
        return int(self.cab_info_json_data.get('maxUserPhrases', 0))

    @property
    def max_cards_per_user(self):
        return int(self.cab_info_json_data.get('maxCardsPerUser', 0))

    @property
    def max_faces_per_user(self):
        return int(self.cab_info_json_data.get('maxFacesPerUser', 0))

    @property
    def max_fingerprints_per_user(self):
        return int(self.cab_info_json_data.get('maxFingerprintsPerUser', 0))

    @property
    def max_input_ports(self):
        return int(self.cab_info_json_data.get('maxInputPorts', 0))

    @property
    def max_output_ports(self):
        return int(self.cab_info_json_data.get('maxOutputPorts', 0))

    @property
    def max_relays(self):
        return int(self.cab_info_json_data.get('maxRelays', 0))

    @property
    def max_rs485_channels(self):
        return int(self.cab_info_json_data.get('maxRS485Channels', 0))

    @property
    def camera_supported(self):
        return self.cab_info_json_data.get('cameraSupported', False)

    @property
    def tamper_supported(self):
        return self.cab_info_json_data.get('tamperSupported', False)

    @property
    def wlan_supported(self):
        return self.cab_info_json_data.get('wlanSupported', False)

    @property
    def display_supported(self):
        return self.cab_info_json_data.get('displaySupported', False)

    @property
    def thermal_supported(self):
        return self.cab_info_json_data.get('thermalSupported', False)

    @property
    def mask_supported(self):
        return self.cab_info_json_data.get('maskSupported', False)

    @property
    def face_ex_supported(self):
        return self.cab_info_json_data.get('faceExSupported', False)

    @property
    def em_card_supported(self):
        return self.cab_info_json_data.get('EMCardSupported', False)

    @property
    def hid_prox_card_supported(self):
        return self.cab_info_json_data.get('HIDProxCardSupported', False)

    @property
    def mifare_felica_card_supported(self):
        return self.cab_info_json_data.get('MifareFelicaCardSupported', False)

    @property
    def iclass_card_supported(self):
        return self.cab_info_json_data.get('iClassCardSupported', False)

    @property
    def classic_plus_card_supported(self):
        return self.cab_info_json_data.get('ClassicPlusCardSupported', False)

    @property
    def desfire_ev1_card_supported(self):
        return self.cab_info_json_data.get('DesFireEV1CardSupported', False)

    @property
    def srse_card_supported(self):
        return self.cab_info_json_data.get('SRSECardSupported', False)

    @property
    def seos_card_supported(self):
        return self.cab_info_json_data.get('SEOSCardSupported', False)

    @property
    def nfc_supported(self):
        return self.cab_info_json_data.get('NFCSupported', False)

    @property
    def ble_supported(self):
        return self.cab_info_json_data.get('BLESupported', False)

    @property
    def use_card_operation(self):
        return self.cab_info_json_data.get('useCardOperation', False)

    @property
    def extended_auth_supported(self):
        return self.cab_info_json_data.get('extendedAuthSupported', False)

    @property
    def card_input_supported(self):
        # cardInputSupported
        cap1 = self.cab_info_json_data.get('cardInputSupported', False)
        cap2 = self.cab_info_json_data.get('cardSupported', False)  # for old devices compatibility
        cap3 = self.cab_info_json_data.get('card1xSupported', False)  # for old devices compatibility
        return cap1 or cap2 or cap3

    @property
    def fingerprint_input_supported(self):
        # fingerSupported
        cap1 = self.cab_info_json_data.get('fingerprintInputSupported', False)
        cap2 = self.cab_info_json_data.get('fingerSupported', False)  # for old devices compatibility
        return cap1 or cap2

    @property
    def face_input_supported(self):
        # faceSupported
        cap1 = self.cab_info_json_data.get('faceInputSupported', False)
        cap2 = self.cab_info_json_data.get('faceSupported', False)  # for old devices compatibility
        return cap1 or cap2

    @property
    def id_input_supported(self):
        cap1 = self.cab_info_json_data.get('idInputSupported', False)
        cap2 = self.cab_info_json_data.get('idSupported', False)  # for old devices compatibility
        return cap1 or cap2

    @property
    def pin_input_supported(self):
        return self.cab_info_json_data.get('PINInputSupported', False)

    @property
    def biometric_only_supported(self):
        return self.cab_info_json_data.get('biometricOnlySupported', False)

    @property
    def biometric_pin_supported(self):
        return self.cab_info_json_data.get('biometricPINSupported', False)

    @property
    def card_only_supported(self):
        return self.cab_info_json_data.get('cardOnlySupported', False)

    @property
    def card_biometric_supported(self):
        return self.cab_info_json_data.get('cardBiometricSupported', False)

    @property
    def card_pin_supported(self):
        return self.cab_info_json_data.get('cardPINSupported', False)

    @property
    def card_biometric_or_pin_supported(self):
        return self.cab_info_json_data.get('cardBiometricOrPINSupported', False)

    @property
    def card_biometric_pin_supported(self):
        return self.cab_info_json_data.get('cardBiometricPINSupported', False)

    @property
    def id_biometric_supported(self):
        return self.cab_info_json_data.get('idBiometricSupported', False)

    @property
    def id_pin_supported(self):
        return self.cab_info_json_data.get('idPINSupported', False)

    @property
    def id_biometric_or_pin_supported(self):
        return self.cab_info_json_data.get('idBiometricOrPINSupported', False)

    @property
    def id_biometric_pin_supported(self):
        return self.cab_info_json_data.get('idBiometricPINSupported', False)

    @property
    def extended_face_only_supported(self):
        return self.cab_info_json_data.get('extendedFaceOnlySupported', False)

    @property
    def extended_face_fingerprint_supported(self):
        return self.cab_info_json_data.get('extendedFaceFingerprintSupported', False)

    @property
    def extended_face_pin_supported(self):
        return self.cab_info_json_data.get('extendedFacePINSupported', False)

    @property
    def extended_face_fingerprint_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedFaceFingerprintOrPINSupported', False)

    @property
    def extended_face_fingerprint_pin_supported(self):
        return self.cab_info_json_data.get('extendedFaceFingerprintPINSupported', False)

    @property
    def extended_fingerprint_only_supported(self):
        return self.cab_info_json_data.get('extendedFingerprintOnlySupported', False)

    @property
    def extended_fingerprint_face_supported(self):
        return self.cab_info_json_data.get('extendedFingerprintFaceSupported', False)

    @property
    def extended_fingerprint_pin_supported(self):
        return self.cab_info_json_data.get('extendedFingerprintPINSupported', False)

    @property
    def extended_fingerprint_face_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedFingerprintFaceOrPINSupported', False)

    @property
    def extended_fingerprint_face_pin_supported(self):
        return self.cab_info_json_data.get('extendedFingerprintFacePINSupported', False)

    @property
    def extended_card_only_supported(self):
        return self.cab_info_json_data.get('extendedCardOnlySupported', False)

    @property
    def extended_card_face_supported(self):
        return self.cab_info_json_data.get('extendedCardFaceSupported', False)

    @property
    def extended_card_fingerprint_supported(self):
        return self.cab_info_json_data.get('extendedCardFingerprintSupported', False)

    @property
    def extended_card_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardPINSupported', False)

    @property
    def extended_card_face_or_fingerprint_supported(self):
        return self.cab_info_json_data.get('extendedCardFaceOrFingerprintSupported', False)

    @property
    def extended_card_face_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardFaceOrPINSupported', False)

    @property
    def extended_card_fingerprint_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardFingerprintOrPINSupported', False)

    @property
    def extended_card_face_or_fingerprint_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardFaceOrFingerprintOrPINSupported', False)

    @property
    def extended_card_face_fingerprint_supported(self):
        return self.cab_info_json_data.get('extendedCardFaceFingerprintSupported', False)

    @property
    def extended_card_face_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardFacePINSupported', False)

    @property
    def extended_card_fingerprint_face_supported(self):
        return self.cab_info_json_data.get('extendedCardFingerprintFaceSupported', False)

    @property
    def extended_card_fingerprint_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardFingerprintPINSupported', False)

    @property
    def extended_card_face_or_fingerprint_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardFaceOrFingerprintPINSupported', False)

    @property
    def extended_card_face_fingerprint_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardFaceFingerprintOrPINSupported', False)

    @property
    def extended_card_fingerprint_face_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedCardFingerprintFaceOrPINSupported', False)

    @property
    def extended_id_face_supported(self):
        return self.cab_info_json_data.get('extendedIdFaceSupported', False)

    @property
    def extended_id_fingerprint_supported(self):
        return self.cab_info_json_data.get('extendedIdFingerprintSupported', False)

    @property
    def extended_id_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdPINSupported', False)

    @property
    def extended_id_face_or_fingerprint_supported(self):
        return self.cab_info_json_data.get('extendedIdFaceOrFingerprintSupported', False)

    @property
    def extended_id_face_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdFaceOrPINSupported', False)

    @property
    def extended_id_fingerprint_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdFingerprintOrPINSupported', False)

    @property
    def extended_id_face_or_fingerprint_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdFaceOrFingerprintOrPINSupported', False)

    @property
    def extended_id_face_fingerprint_supported(self):
        return self.cab_info_json_data.get('extendedIdFaceFingerprintSupported', False)

    @property
    def extended_id_face_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdFacePINSupported', False)

    @property
    def extended_id_fingerprint_face_supported(self):
        return self.cab_info_json_data.get('extendedIdFingerprintFaceSupported', False)

    @property
    def extended_id_fingerprint_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdFingerprintPINSupported', False)

    @property
    def extended_id_face_or_fingerprint_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdFaceOrFingerprintPINSupported', False)

    @property
    def extended_id_face_fingerprint_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdFaceFingerprintOrPINSupported', False)

    @property
    def extended_id_fingerprint_face_or_pin_supported(self):
        return self.cab_info_json_data.get('extendedIdFingerprintFaceOrPINSupported', False)

    @property
    def intelligent_pd_supported(self):
        return self.cab_info_json_data.get('intelligentPDSupported', False)

    @property
    def update_user_supported(self):
        return self.cab_info_json_data.get('updateUserSupported', False)

    @property
    def simulated_unlock_supported(self):
        return self.cab_info_json_data.get('simulatedUnlockSupported', False)

    @property
    def smart_card_byte_order_supported(self):
        return self.cab_info_json_data.get('smartCardByteOrderSupported', False)

    @property
    def qr_as_csn_supported(self):
        return self.cab_info_json_data.get('qrAsCSNSupported', False)

# Example usage:
# json_data = {
#   "maxUsers": 0,
#   "maxEventLogs": 0,
#   "maxImageLogs": 0,
#   "maxBlacklists": 0,
#   "maxOperators": 0,
#   "maxCards": 0,
#   "maxFaces": 0,
#   "maxFingerprints": 0,
#   # other fields ...
# }
#
# device_info = DeviceInfo(json_data)
#
# # Accessing properties
# print(device_info.max_users)
# print(device_info.max_event_logs)
# Access other properties similarly
