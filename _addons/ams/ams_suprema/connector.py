from logging import Logger

from odoo import models, api, registry, fields, _

from . import suprema_client as sc
from odoo.http import request


class Connector(models.AbstractModel):
    _name = 'ams_suprema.connector'
    _description = 'Suprema Connector'

    conn_registry = {}  # save connection as static object in dictionary to be shared across pages
    error = fields.Boolean()
    error_msg = fields.Text(string="Error Message")
    response_code = fields.Char()
    sync = fields.Boolean(tracking=True, readonly=False,
                          help="Sync indicate changes is applied to g-sdk server devices")
    last_sync_time = fields.Datetime()

    @classmethod
    def register_connection(cls):
        """ register suprema connection object to be shared across pages call ones when module loaded at first time"""
        suprema_client = sc.SupremaClient()
        connected = suprema_client.connect()
        conn_registry_dict = {'connection': suprema_client, 'connected': connected}
        cls.conn_registry.update(conn_registry_dict)

    @property
    def client_gateway(self) -> sc.SupremaClient:
        # if not connected or register try to connect again
        client_conn = self.client_connection
        if not client_conn.connected:
            client_conn = self.update_connection()
        return client_conn

    @property
    def client_connection(self) -> sc.SupremaClient:
        """return g-sdk shared client connection if exist or create new one"""
        client_conn = self.conn_registry.get('connection') or sc.SupremaClient()
        return client_conn

    @property
    def logger(self) -> Logger:
        return self.client_gateway.client_logger

    def update_connection(self) -> sc.SupremaClient:
        """update G-SDK server connection from ir.config_parameter"""
        gateway_ip = self.env['ir.config_parameter'].sudo().get_param('ams_suprema.gateway_ip')
        gateway_port = self.env['ir.config_parameter'].sudo().get_param('ams_suprema.gateway_port')
        ca_file = self.env['ir.config_parameter'].sudo().get_param('ams_suprema.getway_ca_file_path')

        client_conn = self.client_connection

        # override default connection in odoo.conf with connection from in ir.config_parameter if exist
        connected = client_conn.connect(gateway_ip, gateway_port, ca_file)
        conn_registry_dict = {'connection': client_conn, 'connected': connected}
        Connector.conn_registry.update(conn_registry_dict)
        return client_conn

    def assign_error(self, response):
        """
            this method to store error message, code and disconnect device when connection loss
        """
        # call write with context
        self.with_context(error=True).write({
            'error': True,
            'error_msg': response.error_message,
            'response_code': response.response_code
        })

    def reset_error(self):
        self.with_context(error=True).write({
            'error': False,
            'error_msg': False,
            'response_code': '0'
        })

    def _notify_success(self):
        """return client action notification to indicate success push"""
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Success updates'),

            }

        }

    def _notify_error(self):
        """return client action notification to indicate error push"""
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Error'),
                'message': _('Error updates'),

            }

        }
