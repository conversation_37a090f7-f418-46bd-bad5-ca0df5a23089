# -*- coding: utf-8 -*-

{
    "name": "ams_suprema",
    "version": "1.0.2",
    "depends": [
        'base', 'ams_base', 'hr'
    ],
    "author": "laplacesoftware",
    "category": "TA",
    "website": "https://www.laplacesoftware.com/",
    "images": ["static/description/images/main_screenshot.jpg"],
    "price": "0",
    "license": "OPL-1",
    "currency": "USD",
    "summary": "Integration  with suprema devices",
    "description": """

Information
======================================================================

* created menus
* created objects
* created views
* logics

""",
    "data": [
        # "security/groups.xml",
        "security/ir.model.access.csv",
        "data/ir_sequence_data.xml",
        "data/auth_mode_data.xml",

        "views/auth_mode_views.xml",
        "wizard/transfer_wizard.xml",

        "views/device_views.xml",
        "views/device_log_views.xml",
        "views/device_group_views.xml",
        "views/device_action_views.xml",
        "views/device_users_views.xml",

        "views/user_views.xml",
        "views/employee_views.xml",
        "views/setting_views.xml",

        "views/menu.xml",
        "data/device_cron.xml"
    ],
    "installable": True,
    "auto_install": False,
    "application": True,
    "external_dependencies": {"python": ["grpcio", "grpcio-status", "deprecated"]},
    # 'post_init': 'pre_init_suprema',
    # 'Post_load': '_open_connection_hook',
}
