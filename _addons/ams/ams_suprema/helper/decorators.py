def connected(func):
    """
    A decorator that checks whether the connection is initialized and the device is connected.
    """

    def wrapper(self, *args, **kwargs):
        suprema_client = self.client_gateway
        is_initialized = self.suprema_client.get('is_initialized')
        if suprema_client and is_initialized and self.device_id is not None:
            return func(self, *args, **kwargs)
        else:
            # TODO stop raise exception and switch to standby connection becouse we will use (active standby) [Hgagy]
            raise Exception('Connection is not initialized or device is not connected.')

    return wrapper
