# -*- coding: utf-8 -*-
from odoo import api, SUPERUSER_ID


def migrate(cr, version):
    # drop table ams_base.device_group if exists and not record found;

    cr.execute(
        "SELECT table_name FROM information_schema.tables where table_name = 'ams_suprema_device_group'")
    suprema_dg_exists = bool(cr.fetchone())
    if suprema_dg_exists:
        cr.execute(
            "SELECT table_name FROM information_schema.tables where table_name = 'ams_base_device_group'")
        base_dg_exists = bool(cr.fetchone())
        if base_dg_exists:
            cr.execute("DROP TABLE ams_base_device_group")


    # 1. Update the ir.model table
    cr.execute("""
        UPDATE ir_model
        SET model = 'ams_base.device_group'
        WHERE model =  'ams_suprema.device_group'
    """)

    # 2. Update the ir.model.data table
    cr.execute("""
        UPDATE ir_model_data
        SET model = 'ams_base.device_group'
        WHERE model =  'ams_suprema.device_group'
    """)

    # 3. Update the ir.model.fields table (if needed, for fields like One2many)
    cr.execute("""
        UPDATE ir_model_fields
        SET relation = 'ams_base.device_group'
        WHERE relation = 'ams_suprema.device_group'
    """)

    cr.execute("""
        UPDATE ir_model_fields
        SET model = 'ams_base.device_group'
        WHERE model = 'ams_suprema.device_group'
    """)

    # 4. Rename the table itself
    cr.execute("""
        ALTER TABLE ams_suprema_device_group
        RENAME TO ams_base_device_group;
    """)

    # TODO Rename the column  --------------------------------------
    # cr.execute(
    #     "SELECT column_name FROM information_schema.columns WHERE table_name='employee' AND column_name='device_group_id'")
    # employee_column_exists = bool(cr.fetchone())
    # if not employee_column_exists:
    #     cr.execute("ALTER TABLE account_move RENAME COLUMN device_group_id TO device_group_id;")

    # Update field name in ir.model.fields

    # cr.execute("""
    #     UPDATE ir_model_fields
    #     SET name = 'claim_batch_id'
    #     WHERE model = 'account.move' AND name = 'claim_request_id'
    # """)

    #
    # # Update field name in ir.model.fields
    # cr.execute("""
    #     UPDATE ir_model_fields
    #     SET name = 'claim_batch_id'
    #     WHERE model = 'account.payment' AND name = 'claim_request_id'
    # """)

# def migrate(cr, version):
#     from openupgradelib import openupgrade
#
#     openupgrade.rename_models(cr, [('emr_coverage.claim_request', 'emr_coverage.claim_batch')])
