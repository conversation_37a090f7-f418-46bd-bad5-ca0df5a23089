syntax = "proto3";

package gsdk.event;

option go_package = "biostar/service/event";
option java_package = "com.supremainc.sdk.event";
option java_multiple_files = true;

import "err.proto";
import "tna.proto";

service Event {
  rpc EnableMonitoring(EnableMonitoringRequest) returns (EnableMonitoringResponse); 
  rpc EnableMonitoringMulti(EnableMonitoringMultiRequest) returns (EnableMonitoringMultiResponse); 

  rpc DisableMonitoring(DisableMonitoringRequest) returns (DisableMonitoringResponse);
  rpc DisableMonitoringMulti(DisableMonitoringMultiRequest) returns (DisableMonitoringMultiResponse);

  rpc SubscribeRealtimeLog(SubscribeRealtimeLogRequest) returns (stream EventLog); 

  rpc GetLog(GetLogRequest) returns (GetLogResponse);
  rpc GetLogWithFilter(GetLogWithFilterRequest) returns (GetLogWithFilterResponse);

  rpc GetImageLog(GetImageLogRequest) returns (GetImageLogResponse);

  rpc GetImageFilter(GetImageFilterRequest) returns (GetImageFilterResponse);
  rpc SetImageFilter(SetImageFilterRequest) returns (SetImageFilterResponse);
  rpc SetImageFilterMulti(SetImageFilterMultiRequest) returns (SetImageFilterMultiResponse);

  rpc ClearLog(ClearLogRequest) returns (ClearLogResponse);
  rpc ClearLogMulti(ClearLogMultiRequest) returns (ClearLogMultiResponse);
}

enum Enum {
  FIRST_ENUM_VALUE_MUST_BE_ZERO = 0;

  MAX_EVENT_FILTERS = 32;
}

enum PortValue {
	OPEN = 0;
	CLOSED = 1;
	SUPERVISED_SHORT = 2;
	SUPERVISED_OPEN = 3;
};

message DetectInputInfo {
  uint32 ioDeviceID = 1;
  uint32 port = 2;
  PortValue value = 3;
}

message AlarmZoneInfo {
  uint32 zoneID = 1;
  uint32 doorID = 2;
  uint32 ioDeviceID = 3;
  uint32 port = 4;
}

message InterlockZoneInfo {
  uint32 zoneID = 1;
  repeated uint32 doorIDs = 2; 
}

message EventLog {
  uint32 ID = 1;
  uint32 timestamp = 2;
  uint32 deviceID = 3;
  string userID = 4;
  uint32 entityID = 5;
  uint32 eventCode = 6;
  uint32 subCode = 7;
  tna.Key TNAKey = 8;
  bool hasImage = 9;
  bool changedOnDevice = 10;

  // for Thermal Camera only
  uint32 temperature = 11;

  // only for verification failure with a card
  bytes cardData = 12;

  // only for events of detecting inputs
  DetectInputInfo inputInfo = 13;

  // only for alarm zone
  AlarmZoneInfo alarmZoneInfo = 14;

  // only for interlock zone
  InterlockZoneInfo interlockZoneInfo = 15;
}


message EventFilter {
  string userID = 1;
  uint32 startTime = 2;
  uint32 endTime = 3;
  uint32 eventCode = 4;
  tna.Key TNAKey = 5;
}


message EnableMonitoringRequest {
  uint32 deviceID = 1;
}

message EnableMonitoringResponse {
}


message EnableMonitoringMultiRequest {
  repeated uint32 deviceIDs = 1;
}

message EnableMonitoringMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;
}


message DisableMonitoringRequest {
  uint32 deviceID = 1;
}

message DisableMonitoringResponse {
}

message DisableMonitoringMultiRequest {
  repeated uint32 deviceIDs = 1;
}

message DisableMonitoringMultiResponse {
  repeated err.ErrorResponse deviceErrors= 1;
}

message SubscribeRealtimeLogRequest {
  int32 queueSize = 1;
  repeated uint32 deviceIDs = 2;
  repeated uint32 eventCodes = 3;
}

message SubscribeRealtimeLogResponse { // for gateway
  string logChanID = 1;
}

message GetLogRequest {
  uint32 deviceID = 1;
  uint32 startEventID = 2;
  uint32 maxNumOfLog = 3;
}

message GetLogResponse {
  repeated EventLog events = 1;
}


message GetLogWithFilterRequest {
  uint32 deviceID = 1;
  uint32 startEventID = 2;
  uint32 maxNumOfLog = 3;
  repeated EventFilter filters = 4;
}

message GetLogWithFilterResponse {
  repeated EventLog events = 1;
}


message ImageLog {
  uint32 ID = 1;
  uint32 timestamp = 2;
  uint32 deviceID = 3;
  string userID = 4;
  uint32 eventCode = 5;
  uint32 subCode = 6;
  bytes JPGImage = 7;
}

message GetImageLogRequest {
  uint32 deviceID = 1;
  uint32 startEventID = 2;
  uint32 maxNumOfLog = 3;
}


message GetImageLogResponse {
  repeated ImageLog imageEvents = 1;
}


message ImageFilter {
  uint32 eventCode = 1; 
  uint32 scheduleID = 2;
}


message GetImageFilterRequest {
  uint32 deviceID = 1;
}

message GetImageFilterResponse {
  repeated ImageFilter filters = 1;
}


message SetImageFilterRequest {
  uint32 deviceID= 1;
  repeated ImageFilter filters = 2;
}

message SetImageFilterResponse {
}

message SetImageFilterMultiRequest {
  repeated uint32 deviceIDs = 1;
  repeated ImageFilter filters = 2;
}

message SetImageFilterMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;
}


message ClearLogRequest {
  uint32 deviceID = 1;
}

message ClearLogResponse {
}


message ClearLogMultiRequest {
  repeated uint32 deviceIDs = 1;
}

message ClearLogMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;
}
