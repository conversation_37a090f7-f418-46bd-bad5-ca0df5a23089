syntax = "proto3";

package gsdk.system;

option go_package = "biostar/service/system";
option java_package = "com.supremainc.sdk.system";
option java_multiple_files = true;

import "err.proto";

service System {
  rpc GetConfig(GetConfigRequest) returns (GetConfigResponse);
  rpc SetConfig(SetConfigRequest) returns (SetConfigResponse);
  rpc SetConfigMulti(SetConfigMultiRequest) returns (SetConfigMultiResponse);    
}

enum CameraFrequency {
  FREQ_NONE = 0x00;
  FREQ_50HZ = 1;
  FREQ_60HZ = 2;
}

enum CardOperationMask {
  CARD_OPERATION_MASK_NONE = 0x00000000;
  // CARD_OPERATION_MASK_DEFAULT = 0xFFFFFFFF;
  // CARD_OPERATION_MASK_USE = 0x80000000;
  CARD_OPERATION_MASK_BLE = 0x00000200;
  CARD_OPERATION_MASK_NFC = 0x00000100;
  CARD_OPERATION_MASK_SEOS = 0x00000080;
  CARD_OPERATION_MASK_SR_SE = 0x00000040;
  CARD_OPERATION_MASK_DESFIRE_EV1 = 0x00000020;
  CARD_OPERATION_MASK_CLASSIC_PLUS = 0x00000010;
  CARD_OPERATION_MASK_ICLASS = 0x00000008;
  CARD_OPERATION_MASK_MIFARE_FELICA = 0x00000004;
  CARD_OPERATION_MASK_HIDPROX = 0x00000002;
  CARD_OPERATION_MASK_EM = 0x00000001;
}

message SystemConfig {
  int32 timeZone = 1;
  bool syncTime = 2;
  bool isLocked = 3; 
  bool useInterphone = 4;
  bool OSDPKeyEncrypted = 5;
  bool useJobCode = 6;
  bool useAlphanumericID = 7;
  CameraFrequency cameraFrequency = 8;
  bool useSecureTamper = 9;
  uint32 useCardOperationMask = 10;
}


message GetConfigRequest {
  uint32 deviceID = 1;
}

message GetConfigResponse {
  SystemConfig config = 1;
}


message SetConfigRequest {
  uint32 deviceID = 1;
  SystemConfig config = 2;
}

message SetConfigResponse {
}

message SetConfigMultiRequest {
  repeated uint32 deviceIDs = 1;
  SystemConfig config = 2;
}

message SetConfigMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;
}

