syntax = "proto3";

package gsdk.intrusion_zone;

option go_package = "biostar/service/intrusionZone";
option java_package = "com.supremainc.sdk.intrusion_zone";
option java_multiple_files = true;

import "zone.proto";
import "action.proto";
import "device.proto";
import "card.proto";

service IntrusionAlarmZone {
  rpc Get(GetRequest) returns (GetResponse);
  rpc GetStatus(GetStatusRequest) returns (GetStatusResponse);
  rpc Add(AddRequest) returns (AddResponse);
  rpc Delete(DeleteRequest) returns (DeleteResponse);
  rpc DeleteAll(DeleteAllRequest) returns (DeleteAllResponse);
  rpc SetArm(SetArmRequest) returns (SetArmResponse);
  rpc SetAlarm(SetAlarmRequest) returns (SetAlarmResponse);
}

enum Enum {
  option allow_alias = true;

  FIRST_ENUM_VALUE_MUST_BE_ZERO = 0;

  DEFAULT_ARM_DELAY = 10;
  DEFAULT_ALALM_DELAY = 5;

  MAX_ALARMS = 5;
  MAX_ACCESS_GROUPS = 128;
  MAX_DOORS = 64;
  MAX_MEMBERS = 64;
  MAX_CARDS = 128;
  MAX_INPUTS = 128;
  MAX_OUTPUTS = 128;
  MAX_NAME_LENGTH = 144;
  MAX_ARM_DELAY = 255;
  MAX_ALARM_DELAY = 255;
}

enum InputType {
	INPUT_NONE = 0x00;
	INPUT_CARD = 0x01;
	INPUT_KEY = 0x02;
	INPUT_ALL = 0xFF;	  
}

enum OperationType {
	OPERATION_NONE = 0x00;
	OPERATION_ARM = 0x01;
	OPERATION_DISARM = 0x02;
	OPERATION_TOGGLE = 0x03;
	OPERATION_ALARM = 0x04;
	OPERATION_CLEAR_ALARM = 0x08;  
}

message Member {
  uint32 deviceID = 1;
  uint32 input = 2;
  uint32 operation = 3;
}

message Input {
  uint32 deviceID = 1;
  uint32 port = 2;
  device.SwitchType switchType = 3;
  uint32 duration = 4;
  uint32 operation = 5;
}

message Output {
  uint32 event = 1;
  action.Action action = 2;
}

message ZoneInfo {
  uint32 zoneID = 1;
  string name = 2;

  bool disabled = 3;
  uint32 armDelay = 4;
  uint32 alarmDelay = 5;

  repeated uint32 doorIDs = 6;
  repeated uint32 groupIDs = 7;

  repeated card.CSNCardData cards = 8;
  repeated Member members = 9;
  repeated Input inputs = 10;
  repeated Output outputs = 11;
}

message GetRequest {
  uint32 deviceID = 1;
}

message GetResponse {
  repeated ZoneInfo zones = 1;
}

message GetStatusRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
}

message GetStatusResponse {
  repeated zone.ZoneStatus status = 1;
}

message AddRequest {
  uint32 deviceID = 1;
  repeated ZoneInfo zones = 2;
}

message AddResponse {
}

message DeleteRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
}

message DeleteResponse {
}

message DeleteAllRequest {
  uint32 deviceID = 1;
}

message DeleteAllResponse {
}


message SetArmRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
  bool armed = 3;
}

message SetArmResponse {
}

message SetAlarmRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
  bool alarmed = 3;
}

message SetAlarmResponse {
}