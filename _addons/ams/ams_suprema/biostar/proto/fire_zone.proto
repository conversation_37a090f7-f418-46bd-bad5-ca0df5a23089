syntax = "proto3";

package gsdk.fire_zone;

option go_package = "biostar/service/fireZone";
option java_package = "com.supremainc.sdk.fire_zone";
option java_multiple_files = true;

import "zone.proto";
import "action.proto";
import "device.proto";


service FireAlarmZone {
  rpc Get(GetRequest) returns (GetResponse);
  rpc GetStatus(GetStatusRequest) returns (GetStatusResponse);
  rpc Add(AddRequest) returns (AddResponse);
  rpc Delete(DeleteRequest) returns (DeleteResponse);
  rpc DeleteAll(DeleteAllRequest) returns (DeleteAllResponse);
  rpc SetAlarm(SetAlarmRequest) returns (SetAlarmResponse);
}

enum Enum {
  FIRST_ENUM_VALUE_MUST_BE_ZERO = 0;

  MAX_ALARMS = 5;
  MAX_SENSORS = 8;
  MAX_DOOR_OR_LIFTS = 32;
  MAX_NAME_LENGTH = 144;
}

message FireSensor {
  uint32 deviceID = 1;
  uint32 port = 2;
  device.SwitchType type = 3;
  uint32 duration = 4;
}

message ZoneInfo {
  uint32 zoneID = 1;
  string name = 2;

  bool disabled = 3;
  bool alarmed = 4;

  repeated uint32 doorIDs = 5;
  repeated FireSensor sensors = 6;
  repeated action.Action actions = 7;
}


message GetRequest {
  uint32 deviceID = 1;
}

message GetResponse {
  repeated ZoneInfo zones = 1;
}

message GetStatusRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
}

message GetStatusResponse {
  repeated zone.ZoneStatus status = 1;
}

message AddRequest {
  uint32 deviceID = 1;
  repeated ZoneInfo zones = 2;
}

message AddResponse {
}

message DeleteRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
}

message DeleteResponse {
}

message DeleteAllRequest {
  uint32 deviceID = 1;
}

message DeleteAllResponse {
}

message SetAlarmRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
  bool alarmed = 3;
}

message SetAlarmResponse {
}