syntax = "proto3";

package gsdk.access;

option go_package = "biostar/service/access";
option java_package = "com.supremainc.sdk.access";
option java_multiple_files = true;

import "err.proto";

service Access {
  rpc GetList(GetListRequest) returns (GetListResponse);
  rpc Add(AddRequest) returns (AddResponse);
  rpc AddMulti(AddMultiRequest) returns (AddMultiResponse);
  rpc Delete(DeleteRequest) returns (DeleteResponse);
  rpc DeleteMulti(DeleteMultiRequest) returns (DeleteMultiResponse);
  rpc DeleteAll(DeleteAllRequest) returns (DeleteAllResponse); 
  rpc DeleteAllMulti(DeleteAllMultiRequest) returns (DeleteAllMultiResponse); 

  rpc GetLevelList(GetLevelListRequest) returns (GetLevelListResponse);
  rpc AddLevel(AddLevelRequest) returns (AddLevelResponse);
  rpc AddLevelMulti(AddLevelMultiRequest) returns (AddLevelMultiResponse);
  rpc DeleteLevel(DeleteLevelRequest) returns (DeleteLevelResponse);
  rpc DeleteLevelMulti(DeleteLevelMultiRequest) returns (DeleteLevelMultiResponse);
  rpc DeleteAllLevel(DeleteAllLevelRequest) returns (DeleteAllLevelResponse);
  rpc DeleteAllLevelMulti(DeleteAllLevelMultiRequest) returns (DeleteAllLevelMultiResponse);  

  rpc GetFloorLevelList(GetFloorLevelListRequest) returns (GetFloorLevelListResponse);
  rpc AddFloorLevel(AddFloorLevelRequest) returns (AddFloorLevelResponse);
  rpc AddFloorLevelMulti(AddFloorLevelMultiRequest) returns (AddFloorLevelMultiResponse);
  rpc DeleteFloorLevel(DeleteFloorLevelRequest) returns (DeleteFloorLevelResponse);
  rpc DeleteFloorLevelMulti(DeleteFloorLevelMultiRequest) returns (DeleteFloorLevelMultiResponse);
  rpc DeleteAllFloorLevel(DeleteAllFloorLevelRequest) returns (DeleteAllFloorLevelResponse);
  rpc DeleteAllFloorLevelMulti(DeleteAllFloorLevelMultiRequest) returns (DeleteAllFloorLevelMultiResponse);   
}

enum Enum {
  option allow_alias = true;

  FIRST_ENUM_VALUE_MUST_BE_ZERO = 0;

  MAX_LEVELS_IN_GROUP = 128;
  MAX_SCHEDULES_IN_LEVEL = 128;
  MAX_NAME_LENGTH = 144;
}

message GetListRequest {
  uint32 deviceID = 1;
}

message AccessGroup {
  uint32 ID = 1;
  string name = 2;
  repeated uint32 levelIDs = 3;
}

message GetListResponse {
  repeated AccessGroup groups = 1;
}


message AddRequest {
  uint32 deviceID = 1;
  repeated AccessGroup groups = 2;
}

message AddResponse {
}

message AddMultiRequest {
  repeated uint32 deviceIDs = 1;
  repeated AccessGroup groups = 2;
}

message AddMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;
}

message DeleteRequest {
  uint32 deviceID = 1;
  repeated uint32 groupIDs = 2;
}

message DeleteResponse {
}

message DeleteMultiRequest {
  repeated uint32 deviceIDs = 1;
  repeated uint32 groupIDs = 2;
}

message DeleteMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;  
}

message DeleteAllRequest {
  uint32 deviceID = 1;
}

message DeleteAllResponse {
}

message DeleteAllMultiRequest {
  repeated uint32 deviceIDs = 1;
}

message DeleteAllMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;    
}


message GetLevelListRequest {
  uint32 deviceID = 1;
}

message DoorSchedule {
  uint32 doorID = 1;
  uint32 scheduleID = 2;
}

message AccessLevel {
  uint32 ID = 1;
  string name = 2;
  repeated DoorSchedule doorSchedules = 3;
}

message GetLevelListResponse {
  repeated AccessLevel levels = 1;
}

message AddLevelRequest {
  uint32 deviceID = 1;
  repeated AccessLevel levels = 2;
}

message AddLevelResponse {
}

message AddLevelMultiRequest {
  repeated uint32 deviceIDs = 1;
  repeated AccessLevel levels = 2;
}

message AddLevelMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;
}

message DeleteLevelRequest {
  uint32 deviceID = 1;
  repeated uint32 levelIDs = 2;
}

message DeleteLevelResponse {
}

message DeleteLevelMultiRequest {
  repeated uint32 deviceIDs = 1;
  repeated uint32 levelIDs = 2;
}

message DeleteLevelMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;  
}

message DeleteAllLevelRequest {
  uint32 deviceID = 1;
}

message DeleteAllLevelResponse {
}

message DeleteAllLevelMultiRequest {
  repeated uint32 deviceIDs = 1;
}

message DeleteAllLevelMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;    
}


message FloorSchedule {
  uint32 liftID = 1;
  uint32 floorIndex = 2;
  uint32 scheduleID = 3;
}

message FloorLevel {
  uint32 ID = 1;
  string name = 2;
  repeated FloorSchedule floorSchedules = 3;
}

message GetFloorLevelListRequest {
  uint32 deviceID = 1;
}

message GetFloorLevelListResponse {
  repeated FloorLevel levels = 1;
}

message AddFloorLevelRequest {
  uint32 deviceID = 1;
  repeated FloorLevel levels = 2;
}

message AddFloorLevelResponse {
}

message AddFloorLevelMultiRequest {
  repeated uint32 deviceIDs = 1;
  repeated FloorLevel levels = 2;
}

message AddFloorLevelMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;
}

message DeleteFloorLevelRequest {
  uint32 deviceID = 1;
  repeated uint32 levelIDs = 2;
}

message DeleteFloorLevelResponse {
}

message DeleteFloorLevelMultiRequest {
  repeated uint32 deviceIDs = 1;
  repeated uint32 levelIDs = 2;
}

message DeleteFloorLevelMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;  
}

message DeleteAllFloorLevelRequest {
  uint32 deviceID = 1;
}

message DeleteAllFloorLevelResponse {
}

message DeleteAllFloorLevelMultiRequest {
  repeated uint32 deviceIDs = 1;
}

message DeleteAllFloorLevelMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;    
}
