syntax = "proto3";

package gsdk.lift_zone;

option go_package = "biostar/service/liftZone";
option java_package = "com.supremainc.sdk.lift_zone";
option java_multiple_files = true;

import "zone.proto";
import "action.proto";

service LiftZone {
  rpc Get(GetRequest) returns (GetResponse);
  rpc GetStatus(GetStatusRequest) returns (GetStatusResponse);
  rpc Add(AddRequest) returns (AddResponse);
  rpc Delete(DeleteRequest) returns (DeleteResponse);
  rpc DeleteAll(DeleteAllRequest) returns (DeleteAllResponse);
  rpc SetAlarm(SetAlarmRequest) returns (SetAlarmResponse);
}

enum Enum {
  option allow_alias = true;

  FIRST_ENUM_VALUE_MUST_BE_ZERO = 0;

  MAX_ALARMS = 5;
  MAX_BYPASS_GROUPS = 16;
  MAX_UNLOCK_GROUPS = 16;
  MAX_LIFTS = 32;
  MAX_NAME_LENGTH = 144;
}

message Lift {
  uint32 liftID = 1;
  repeated uint32 floorIndexes = 3;
}

message ZoneInfo {
  uint32 zoneID = 1;
  string name = 2;

  uint32 activateScheduleID = 3;
  uint32 deactivateScheduleID = 4;
  
  bool disabled = 5;
  bool alarmed = 6;

  repeated Lift lifts = 7;

  repeated action.Action actions = 8;
  repeated uint32 bypassGroupIDs = 9;
  repeated uint32 unlockGroupIDs = 10;
}


message GetRequest {
  uint32 deviceID = 1;
}

message GetResponse {
  repeated ZoneInfo zones = 1;
}

message GetStatusRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
}

message GetStatusResponse {
  repeated zone.ZoneStatus status = 1;
}

message AddRequest {
  uint32 deviceID = 1;
  repeated ZoneInfo zones = 2;
}

message AddResponse {
}

message DeleteRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
}

message DeleteResponse {
}

message DeleteAllRequest {
  uint32 deviceID = 1;
}

message DeleteAllResponse {
}


message SetAlarmRequest {
  uint32 deviceID = 1;
  repeated uint32 zoneIDs = 2;
  bool alarmed = 3;
}

message SetAlarmResponse {
}