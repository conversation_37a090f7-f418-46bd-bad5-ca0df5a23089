syntax = "proto3";

package gsdk.voip;

option go_package = "biostar/service/voip";
option java_package = "com.supremainc.sdk.voip";
option java_multiple_files = true;

import "err.proto";

service VOIP {
  rpc GetConfig(GetConfigRequest) returns (GetConfigResponse);
  rpc SetConfig(SetConfigRequest) returns (SetConfigResponse);
  rpc SetConfigMulti(SetConfigMultiRequest) returns (SetConfigMultiResponse);    
}


enum Enum {
  option allow_alias = true;

  FIRST_ENUM_VALUE_MUST_BE_ZERO = 0;

  DEFAULT_VOIP_SERVER_PORT = 5060;

  MAX_VOIP_ID_LENGTH = 32;
  MAX_VOIP_PASSWORD_LENGTH = 32;
  MAX_PHONEBOOK_ITEMS = 32;
  MAX_PHONE_NUMBER_LENGTH = 32;
  MAX_PHONE_DESCRIPTION_LENGTH = 144;
  MAX_VOIP_URL_LENGTH = 256;
}

message UserPhone {
  string phoneNumber = 1;
  string description = 2;
}


message VOIPConfig {
  string serverURL = 1;
  uint32 serverPort = 2;
  string userID = 3;
  string userPW = 4;

  bool enabled = 5;

  uint32 exitButton = 6;
  uint32 DTMFMode = 7;

  uint32 registrationDuration = 9;
  uint32 speakerVolume = 10;
  uint32 micVolume = 11;

  string authorizationCode = 12;

  bool showExtensionNumber = 13;
  bool useOutboundProxy = 14;
  string proxyURL = 15;
  uint32 proxyPort = 16;

  repeated UserPhone phones = 8;
}


message GetConfigRequest {
  uint32 deviceID = 1;
}

message GetConfigResponse {
  VOIPConfig config = 1;
}


message SetConfigRequest {
  uint32 deviceID= 1;
  VOIPConfig config = 2;
}

message SetConfigResponse {
}

message SetConfigMultiRequest {
  repeated uint32 deviceIDs = 1;
  VOIPConfig config = 2;
}

message SetConfigMultiResponse {
  repeated err.ErrorResponse deviceErrors = 1;
}
