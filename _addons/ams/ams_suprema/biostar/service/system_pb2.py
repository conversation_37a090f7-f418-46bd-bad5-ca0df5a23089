# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: system.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import err_pb2 as err__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0csystem.proto\x12\x0bgsdk.system\x1a\terr.proto\"\x92\x02\n\x0cSystemConfig\x12\x10\n\x08timeZone\x18\x01 \x01(\x05\x12\x10\n\x08syncTime\x18\x02 \x01(\x08\x12\x10\n\x08isLocked\x18\x03 \x01(\x08\x12\x15\n\ruseInterphone\x18\x04 \x01(\x08\x12\x18\n\x10OSDPKeyEncrypted\x18\x05 \x01(\x08\x12\x12\n\nuseJobCode\x18\x06 \x01(\x08\x12\x19\n\x11useAlphanumericID\x18\x07 \x01(\x08\x12\x35\n\x0f\x63\x61meraFrequency\x18\x08 \x01(\x0e\x32\x1c.gsdk.system.CameraFrequency\x12\x17\n\x0fuseSecureTamper\x18\t \x01(\x08\x12\x1c\n\x14useCardOperationMask\x18\n \x01(\r\"$\n\x10GetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\">\n\x11GetConfigResponse\x12)\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x19.gsdk.system.SystemConfig\"O\n\x10SetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12)\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x19.gsdk.system.SystemConfig\"\x13\n\x11SetConfigResponse\"U\n\x15SetConfigMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12)\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x19.gsdk.system.SystemConfig\"G\n\x16SetConfigMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse*>\n\x0f\x43\x61meraFrequency\x12\r\n\tFREQ_NONE\x10\x00\x12\r\n\tFREQ_50HZ\x10\x01\x12\r\n\tFREQ_60HZ\x10\x02*\xfa\x02\n\x11\x43\x61rdOperationMask\x12\x1c\n\x18\x43\x41RD_OPERATION_MASK_NONE\x10\x00\x12\x1c\n\x17\x43\x41RD_OPERATION_MASK_BLE\x10\x80\x04\x12\x1c\n\x17\x43\x41RD_OPERATION_MASK_NFC\x10\x80\x02\x12\x1d\n\x18\x43\x41RD_OPERATION_MASK_SEOS\x10\x80\x01\x12\x1d\n\x19\x43\x41RD_OPERATION_MASK_SR_SE\x10@\x12#\n\x1f\x43\x41RD_OPERATION_MASK_DESFIRE_EV1\x10 \x12$\n CARD_OPERATION_MASK_CLASSIC_PLUS\x10\x10\x12\x1e\n\x1a\x43\x41RD_OPERATION_MASK_ICLASS\x10\x08\x12%\n!CARD_OPERATION_MASK_MIFARE_FELICA\x10\x04\x12\x1f\n\x1b\x43\x41RD_OPERATION_MASK_HIDPROX\x10\x02\x12\x1a\n\x16\x43\x41RD_OPERATION_MASK_EM\x10\x01\x32\xfb\x01\n\x06System\x12J\n\tGetConfig\x12\x1d.gsdk.system.GetConfigRequest\x1a\x1e.gsdk.system.GetConfigResponse\x12J\n\tSetConfig\x12\x1d.gsdk.system.SetConfigRequest\x1a\x1e.gsdk.system.SetConfigResponse\x12Y\n\x0eSetConfigMulti\x12\".gsdk.system.SetConfigMultiRequest\x1a#.gsdk.system.SetConfigMultiResponseB5\n\x19\x63om.supremainc.sdk.systemP\x01Z\x16\x62iostar/service/systemb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'system_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\031com.supremainc.sdk.systemP\001Z\026biostar/service/system'
  _CAMERAFREQUENCY._serialized_start=681
  _CAMERAFREQUENCY._serialized_end=743
  _CARDOPERATIONMASK._serialized_start=746
  _CARDOPERATIONMASK._serialized_end=1124
  _SYSTEMCONFIG._serialized_start=41
  _SYSTEMCONFIG._serialized_end=315
  _GETCONFIGREQUEST._serialized_start=317
  _GETCONFIGREQUEST._serialized_end=353
  _GETCONFIGRESPONSE._serialized_start=355
  _GETCONFIGRESPONSE._serialized_end=417
  _SETCONFIGREQUEST._serialized_start=419
  _SETCONFIGREQUEST._serialized_end=498
  _SETCONFIGRESPONSE._serialized_start=500
  _SETCONFIGRESPONSE._serialized_end=519
  _SETCONFIGMULTIREQUEST._serialized_start=521
  _SETCONFIGMULTIREQUEST._serialized_end=606
  _SETCONFIGMULTIRESPONSE._serialized_start=608
  _SETCONFIGMULTIRESPONSE._serialized_end=679
  _SYSTEM._serialized_start=1127
  _SYSTEM._serialized_end=1378
# @@protoc_insertion_point(module_scope)
