# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: admin.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0b\x61\x64min.proto\x12\ngsdk.admin\"\x10\n\x0eGetInfoRequest\"5\n\x0fGetInfoResponse\x12\x0f\n\x07version\x18\x01 \x01(\t\x12\x11\n\tbuildDate\x18\x02 \x01(\t2K\n\x05\x41\x64min\x12\x42\n\x07GetInfo\x12\x1a.gsdk.admin.GetInfoRequest\x1a\x1b.gsdk.admin.GetInfoResponseB3\n\x18\x63om.supremainc.sdk.adminP\x01Z\x15\x62iostar/service/adminb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'admin_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030com.supremainc.sdk.adminP\001Z\025biostar/service/admin'
  _GETINFOREQUEST._serialized_start=27
  _GETINFOREQUEST._serialized_end=43
  _GETINFORESPONSE._serialized_start=45
  _GETINFORESPONSE._serialized_end=98
  _ADMIN._serialized_start=100
  _ADMIN._serialized_end=175
# @@protoc_insertion_point(module_scope)
