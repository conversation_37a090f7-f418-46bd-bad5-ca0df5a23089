# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import operator_pb2 as operator__pb2


class OperatorStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetList = channel.unary_unary(
                '/gsdk.operator.Operator/GetList',
                request_serializer=operator__pb2.GetListRequest.SerializeToString,
                response_deserializer=operator__pb2.GetListResponse.FromString,
                )
        self.Add = channel.unary_unary(
                '/gsdk.operator.Operator/Add',
                request_serializer=operator__pb2.AddRequest.SerializeToString,
                response_deserializer=operator__pb2.AddResponse.FromString,
                )
        self.AddMulti = channel.unary_unary(
                '/gsdk.operator.Operator/AddMulti',
                request_serializer=operator__pb2.AddMultiRequest.SerializeToString,
                response_deserializer=operator__pb2.AddMultiResponse.FromString,
                )
        self.Delete = channel.unary_unary(
                '/gsdk.operator.Operator/Delete',
                request_serializer=operator__pb2.DeleteRequest.SerializeToString,
                response_deserializer=operator__pb2.DeleteResponse.FromString,
                )
        self.DeleteMulti = channel.unary_unary(
                '/gsdk.operator.Operator/DeleteMulti',
                request_serializer=operator__pb2.DeleteMultiRequest.SerializeToString,
                response_deserializer=operator__pb2.DeleteMultiResponse.FromString,
                )
        self.DeleteAll = channel.unary_unary(
                '/gsdk.operator.Operator/DeleteAll',
                request_serializer=operator__pb2.DeleteAllRequest.SerializeToString,
                response_deserializer=operator__pb2.DeleteAllResponse.FromString,
                )
        self.DeleteAllMulti = channel.unary_unary(
                '/gsdk.operator.Operator/DeleteAllMulti',
                request_serializer=operator__pb2.DeleteAllMultiRequest.SerializeToString,
                response_deserializer=operator__pb2.DeleteAllMultiResponse.FromString,
                )


class OperatorServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Add(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Delete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAll(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAllMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OperatorServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetList,
                    request_deserializer=operator__pb2.GetListRequest.FromString,
                    response_serializer=operator__pb2.GetListResponse.SerializeToString,
            ),
            'Add': grpc.unary_unary_rpc_method_handler(
                    servicer.Add,
                    request_deserializer=operator__pb2.AddRequest.FromString,
                    response_serializer=operator__pb2.AddResponse.SerializeToString,
            ),
            'AddMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.AddMulti,
                    request_deserializer=operator__pb2.AddMultiRequest.FromString,
                    response_serializer=operator__pb2.AddMultiResponse.SerializeToString,
            ),
            'Delete': grpc.unary_unary_rpc_method_handler(
                    servicer.Delete,
                    request_deserializer=operator__pb2.DeleteRequest.FromString,
                    response_serializer=operator__pb2.DeleteResponse.SerializeToString,
            ),
            'DeleteMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteMulti,
                    request_deserializer=operator__pb2.DeleteMultiRequest.FromString,
                    response_serializer=operator__pb2.DeleteMultiResponse.SerializeToString,
            ),
            'DeleteAll': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAll,
                    request_deserializer=operator__pb2.DeleteAllRequest.FromString,
                    response_serializer=operator__pb2.DeleteAllResponse.SerializeToString,
            ),
            'DeleteAllMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAllMulti,
                    request_deserializer=operator__pb2.DeleteAllMultiRequest.FromString,
                    response_serializer=operator__pb2.DeleteAllMultiResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.operator.Operator', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Operator(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.operator.Operator/GetList',
            operator__pb2.GetListRequest.SerializeToString,
            operator__pb2.GetListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Add(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.operator.Operator/Add',
            operator__pb2.AddRequest.SerializeToString,
            operator__pb2.AddResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.operator.Operator/AddMulti',
            operator__pb2.AddMultiRequest.SerializeToString,
            operator__pb2.AddMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Delete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.operator.Operator/Delete',
            operator__pb2.DeleteRequest.SerializeToString,
            operator__pb2.DeleteResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.operator.Operator/DeleteMulti',
            operator__pb2.DeleteMultiRequest.SerializeToString,
            operator__pb2.DeleteMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAll(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.operator.Operator/DeleteAll',
            operator__pb2.DeleteAllRequest.SerializeToString,
            operator__pb2.DeleteAllResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAllMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.operator.Operator/DeleteAllMulti',
            operator__pb2.DeleteAllMultiRequest.SerializeToString,
            operator__pb2.DeleteAllMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
