# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: input.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import err_pb2 as err__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0binput.proto\x12\ngsdk.input\x1a\terr.proto\":\n\x14SupervisedInputRange\x12\x10\n\x08MinValue\x18\x01 \x01(\r\x12\x10\n\x08MaxValue\x18\x02 \x01(\r\"\xd5\x01\n\x15SupervisedInputConfig\x12/\n\x05short\x18\x01 \x01(\x0b\x32 .gsdk.input.SupervisedInputRange\x12.\n\x04open\x18\x02 \x01(\x0b\x32 .gsdk.input.SupervisedInputRange\x12,\n\x02on\x18\x03 \x01(\x0b\x32 .gsdk.input.SupervisedInputRange\x12-\n\x03off\x18\x04 \x01(\x0b\x32 .gsdk.input.SupervisedInputRange\"\x92\x01\n\x0fSupervisedInput\x12\x11\n\tportIndex\x18\x01 \x01(\r\x12\x39\n\nregistance\x18\x03 \x01(\x0e\x32%.gsdk.input.SupervisedRegistanceValue\x12\x31\n\x06\x63onfig\x18\x04 \x01(\x0b\x32!.gsdk.input.SupervisedInputConfig\"v\n\x0bInputConfig\x12\x12\n\nnumOfInput\x18\x01 \x01(\r\x12\x1c\n\x14numOfSupervisedInput\x18\x02 \x01(\r\x12\x35\n\x10supervisedInputs\x18\x03 \x03(\x0b\x32\x1b.gsdk.input.SupervisedInput\"$\n\x10GetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"<\n\x11GetConfigResponse\x12\'\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x17.gsdk.input.InputConfig\"M\n\x10SetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\'\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x17.gsdk.input.InputConfig\"\x13\n\x11SetConfigResponse\"S\n\x15SetConfigMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\'\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x17.gsdk.input.InputConfig\"G\n\x16SetConfigMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse*\x98\x01\n\x19SupervisedRegistanceValue\x12\x15\n\x11SUPERVISED_REG_1K\x10\x00\x12\x17\n\x13SUPERVISED_REG_2_2K\x10\x01\x12\x17\n\x13SUPERVISED_REG_4_7K\x10\x02\x12\x16\n\x12SUPERVISED_REG_10K\x10\x03\x12\x1a\n\x15SUPERVISED_REG_CUSTOM\x10\xff\x01\x32\xf4\x01\n\x05Input\x12H\n\tGetConfig\x12\x1c.gsdk.input.GetConfigRequest\x1a\x1d.gsdk.input.GetConfigResponse\x12H\n\tSetConfig\x12\x1c.gsdk.input.SetConfigRequest\x1a\x1d.gsdk.input.SetConfigResponse\x12W\n\x0eSetConfigMulti\x12!.gsdk.input.SetConfigMultiRequest\x1a\".gsdk.input.SetConfigMultiResponseB3\n\x18\x63om.supremainc.sdk.inputP\x01Z\x15\x62iostar/service/inputb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'input_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030com.supremainc.sdk.inputP\001Z\025biostar/service/input'
  _SUPERVISEDREGISTANCEVALUE._serialized_start=942
  _SUPERVISEDREGISTANCEVALUE._serialized_end=1094
  _SUPERVISEDINPUTRANGE._serialized_start=38
  _SUPERVISEDINPUTRANGE._serialized_end=96
  _SUPERVISEDINPUTCONFIG._serialized_start=99
  _SUPERVISEDINPUTCONFIG._serialized_end=312
  _SUPERVISEDINPUT._serialized_start=315
  _SUPERVISEDINPUT._serialized_end=461
  _INPUTCONFIG._serialized_start=463
  _INPUTCONFIG._serialized_end=581
  _GETCONFIGREQUEST._serialized_start=583
  _GETCONFIGREQUEST._serialized_end=619
  _GETCONFIGRESPONSE._serialized_start=621
  _GETCONFIGRESPONSE._serialized_end=681
  _SETCONFIGREQUEST._serialized_start=683
  _SETCONFIGREQUEST._serialized_end=760
  _SETCONFIGRESPONSE._serialized_start=762
  _SETCONFIGRESPONSE._serialized_end=781
  _SETCONFIGMULTIREQUEST._serialized_start=783
  _SETCONFIGMULTIREQUEST._serialized_end=866
  _SETCONFIGMULTIRESPONSE._serialized_start=868
  _SETCONFIGMULTIRESPONSE._serialized_end=939
  _INPUT._serialized_start=1097
  _INPUT._serialized_end=1341
# @@protoc_insertion_point(module_scope)
