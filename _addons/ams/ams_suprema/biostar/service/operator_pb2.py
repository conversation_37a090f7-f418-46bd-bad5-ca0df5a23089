# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: operator.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import err_pb2 as err__pb2
import auth_pb2 as auth__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0eoperator.proto\x12\rgsdk.operator\x1a\terr.proto\x1a\nauth.proto\"\"\n\x0eGetListRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"9\n\x0fGetListResponse\x12&\n\toperators\x18\x01 \x03(\x0b\x32\x13.gsdk.auth.Operator\"F\n\nAddRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12&\n\toperators\x18\x02 \x03(\x0b\x32\x13.gsdk.auth.Operator\"\r\n\x0b\x41\x64\x64Response\"L\n\x0f\x41\x64\x64MultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12&\n\toperators\x18\x02 \x03(\x0b\x32\x13.gsdk.auth.Operator\"A\n\x10\x41\x64\x64MultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"6\n\rDeleteRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x13\n\x0boperatorIDs\x18\x02 \x03(\t\"\x10\n\x0e\x44\x65leteResponse\"<\n\x12\x44\x65leteMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x13\n\x0boperatorIDs\x18\x02 \x03(\t\"D\n\x13\x44\x65leteMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"$\n\x10\x44\x65leteAllRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x13\n\x11\x44\x65leteAllResponse\"*\n\x15\x44\x65leteAllMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"G\n\x16\x44\x65leteAllMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse2\xab\x04\n\x08Operator\x12H\n\x07GetList\x12\x1d.gsdk.operator.GetListRequest\x1a\x1e.gsdk.operator.GetListResponse\x12<\n\x03\x41\x64\x64\x12\x19.gsdk.operator.AddRequest\x1a\x1a.gsdk.operator.AddResponse\x12K\n\x08\x41\x64\x64Multi\x12\x1e.gsdk.operator.AddMultiRequest\x1a\x1f.gsdk.operator.AddMultiResponse\x12\x45\n\x06\x44\x65lete\x12\x1c.gsdk.operator.DeleteRequest\x1a\x1d.gsdk.operator.DeleteResponse\x12T\n\x0b\x44\x65leteMulti\x12!.gsdk.operator.DeleteMultiRequest\x1a\".gsdk.operator.DeleteMultiResponse\x12N\n\tDeleteAll\x12\x1f.gsdk.operator.DeleteAllRequest\x1a .gsdk.operator.DeleteAllResponse\x12]\n\x0e\x44\x65leteAllMulti\x12$.gsdk.operator.DeleteAllMultiRequest\x1a%.gsdk.operator.DeleteAllMultiResponseB9\n\x1b\x63om.supremainc.sdk.operatorP\x01Z\x18\x62iostar/service/operatorb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'operator_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\033com.supremainc.sdk.operatorP\001Z\030biostar/service/operator'
  _GETLISTREQUEST._serialized_start=56
  _GETLISTREQUEST._serialized_end=90
  _GETLISTRESPONSE._serialized_start=92
  _GETLISTRESPONSE._serialized_end=149
  _ADDREQUEST._serialized_start=151
  _ADDREQUEST._serialized_end=221
  _ADDRESPONSE._serialized_start=223
  _ADDRESPONSE._serialized_end=236
  _ADDMULTIREQUEST._serialized_start=238
  _ADDMULTIREQUEST._serialized_end=314
  _ADDMULTIRESPONSE._serialized_start=316
  _ADDMULTIRESPONSE._serialized_end=381
  _DELETEREQUEST._serialized_start=383
  _DELETEREQUEST._serialized_end=437
  _DELETERESPONSE._serialized_start=439
  _DELETERESPONSE._serialized_end=455
  _DELETEMULTIREQUEST._serialized_start=457
  _DELETEMULTIREQUEST._serialized_end=517
  _DELETEMULTIRESPONSE._serialized_start=519
  _DELETEMULTIRESPONSE._serialized_end=587
  _DELETEALLREQUEST._serialized_start=589
  _DELETEALLREQUEST._serialized_end=625
  _DELETEALLRESPONSE._serialized_start=627
  _DELETEALLRESPONSE._serialized_end=646
  _DELETEALLMULTIREQUEST._serialized_start=648
  _DELETEALLMULTIREQUEST._serialized_end=690
  _DELETEALLMULTIRESPONSE._serialized_start=692
  _DELETEALLMULTIRESPONSE._serialized_end=763
  _OPERATOR._serialized_start=766
  _OPERATOR._serialized_end=1321
# @@protoc_insertion_point(module_scope)
