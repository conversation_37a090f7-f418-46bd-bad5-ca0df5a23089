# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: access.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import err_pb2 as err__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x61\x63\x63\x65ss.proto\x12\x0bgsdk.access\x1a\terr.proto\"\"\n\x0eGetListRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"9\n\x0b\x41\x63\x63\x65ssGroup\x12\n\n\x02ID\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08levelIDs\x18\x03 \x03(\r\";\n\x0fGetListResponse\x12(\n\x06groups\x18\x01 \x03(\x0b\x32\x18.gsdk.access.AccessGroup\"H\n\nAddRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12(\n\x06groups\x18\x02 \x03(\x0b\x32\x18.gsdk.access.AccessGroup\"\r\n\x0b\x41\x64\x64Response\"N\n\x0f\x41\x64\x64MultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12(\n\x06groups\x18\x02 \x03(\x0b\x32\x18.gsdk.access.AccessGroup\"A\n\x10\x41\x64\x64MultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"3\n\rDeleteRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x10\n\x08groupIDs\x18\x02 \x03(\r\"\x10\n\x0e\x44\x65leteResponse\"9\n\x12\x44\x65leteMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x10\n\x08groupIDs\x18\x02 \x03(\r\"D\n\x13\x44\x65leteMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"$\n\x10\x44\x65leteAllRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x13\n\x11\x44\x65leteAllResponse\"*\n\x15\x44\x65leteAllMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"G\n\x16\x44\x65leteAllMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"\'\n\x13GetLevelListRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"2\n\x0c\x44oorSchedule\x12\x0e\n\x06\x64oorID\x18\x01 \x01(\r\x12\x12\n\nscheduleID\x18\x02 \x01(\r\"Y\n\x0b\x41\x63\x63\x65ssLevel\x12\n\n\x02ID\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x30\n\rdoorSchedules\x18\x03 \x03(\x0b\x32\x19.gsdk.access.DoorSchedule\"@\n\x14GetLevelListResponse\x12(\n\x06levels\x18\x01 \x03(\x0b\x32\x18.gsdk.access.AccessLevel\"M\n\x0f\x41\x64\x64LevelRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12(\n\x06levels\x18\x02 \x03(\x0b\x32\x18.gsdk.access.AccessLevel\"\x12\n\x10\x41\x64\x64LevelResponse\"S\n\x14\x41\x64\x64LevelMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12(\n\x06levels\x18\x02 \x03(\x0b\x32\x18.gsdk.access.AccessLevel\"F\n\x15\x41\x64\x64LevelMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"8\n\x12\x44\x65leteLevelRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x10\n\x08levelIDs\x18\x02 \x03(\r\"\x15\n\x13\x44\x65leteLevelResponse\">\n\x17\x44\x65leteLevelMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x10\n\x08levelIDs\x18\x02 \x03(\r\"I\n\x18\x44\x65leteLevelMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\")\n\x15\x44\x65leteAllLevelRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x18\n\x16\x44\x65leteAllLevelResponse\"/\n\x1a\x44\x65leteAllLevelMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"L\n\x1b\x44\x65leteAllLevelMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"G\n\rFloorSchedule\x12\x0e\n\x06liftID\x18\x01 \x01(\r\x12\x12\n\nfloorIndex\x18\x02 \x01(\r\x12\x12\n\nscheduleID\x18\x03 \x01(\r\"Z\n\nFloorLevel\x12\n\n\x02ID\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x32\n\x0e\x66loorSchedules\x18\x03 \x03(\x0b\x32\x1a.gsdk.access.FloorSchedule\",\n\x18GetFloorLevelListRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"D\n\x19GetFloorLevelListResponse\x12\'\n\x06levels\x18\x01 \x03(\x0b\x32\x17.gsdk.access.FloorLevel\"Q\n\x14\x41\x64\x64\x46loorLevelRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\'\n\x06levels\x18\x02 \x03(\x0b\x32\x17.gsdk.access.FloorLevel\"\x17\n\x15\x41\x64\x64\x46loorLevelResponse\"W\n\x19\x41\x64\x64\x46loorLevelMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\'\n\x06levels\x18\x02 \x03(\x0b\x32\x17.gsdk.access.FloorLevel\"K\n\x1a\x41\x64\x64\x46loorLevelMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"=\n\x17\x44\x65leteFloorLevelRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x10\n\x08levelIDs\x18\x02 \x03(\r\"\x1a\n\x18\x44\x65leteFloorLevelResponse\"C\n\x1c\x44\x65leteFloorLevelMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x10\n\x08levelIDs\x18\x02 \x03(\r\"N\n\x1d\x44\x65leteFloorLevelMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\".\n\x1a\x44\x65leteAllFloorLevelRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x1d\n\x1b\x44\x65leteAllFloorLevelResponse\"4\n\x1f\x44\x65leteAllFloorLevelMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"Q\n DeleteAllFloorLevelMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse*z\n\x04\x45num\x12!\n\x1d\x46IRST_ENUM_VALUE_MUST_BE_ZERO\x10\x00\x12\x18\n\x13MAX_LEVELS_IN_GROUP\x10\x80\x01\x12\x1b\n\x16MAX_SCHEDULES_IN_LEVEL\x10\x80\x01\x12\x14\n\x0fMAX_NAME_LENGTH\x10\x90\x01\x1a\x02\x10\x01\x32\xd2\x0e\n\x06\x41\x63\x63\x65ss\x12\x44\n\x07GetList\x12\x1b.gsdk.access.GetListRequest\x1a\x1c.gsdk.access.GetListResponse\x12\x38\n\x03\x41\x64\x64\x12\x17.gsdk.access.AddRequest\x1a\x18.gsdk.access.AddResponse\x12G\n\x08\x41\x64\x64Multi\x12\x1c.gsdk.access.AddMultiRequest\x1a\x1d.gsdk.access.AddMultiResponse\x12\x41\n\x06\x44\x65lete\x12\x1a.gsdk.access.DeleteRequest\x1a\x1b.gsdk.access.DeleteResponse\x12P\n\x0b\x44\x65leteMulti\x12\x1f.gsdk.access.DeleteMultiRequest\x1a .gsdk.access.DeleteMultiResponse\x12J\n\tDeleteAll\x12\x1d.gsdk.access.DeleteAllRequest\x1a\x1e.gsdk.access.DeleteAllResponse\x12Y\n\x0e\x44\x65leteAllMulti\x12\".gsdk.access.DeleteAllMultiRequest\x1a#.gsdk.access.DeleteAllMultiResponse\x12S\n\x0cGetLevelList\x12 .gsdk.access.GetLevelListRequest\x1a!.gsdk.access.GetLevelListResponse\x12G\n\x08\x41\x64\x64Level\x12\x1c.gsdk.access.AddLevelRequest\x1a\x1d.gsdk.access.AddLevelResponse\x12V\n\rAddLevelMulti\x12!.gsdk.access.AddLevelMultiRequest\x1a\".gsdk.access.AddLevelMultiResponse\x12P\n\x0b\x44\x65leteLevel\x12\x1f.gsdk.access.DeleteLevelRequest\x1a .gsdk.access.DeleteLevelResponse\x12_\n\x10\x44\x65leteLevelMulti\x12$.gsdk.access.DeleteLevelMultiRequest\x1a%.gsdk.access.DeleteLevelMultiResponse\x12Y\n\x0e\x44\x65leteAllLevel\x12\".gsdk.access.DeleteAllLevelRequest\x1a#.gsdk.access.DeleteAllLevelResponse\x12h\n\x13\x44\x65leteAllLevelMulti\x12\'.gsdk.access.DeleteAllLevelMultiRequest\x1a(.gsdk.access.DeleteAllLevelMultiResponse\x12\x62\n\x11GetFloorLevelList\x12%.gsdk.access.GetFloorLevelListRequest\x1a&.gsdk.access.GetFloorLevelListResponse\x12V\n\rAddFloorLevel\x12!.gsdk.access.AddFloorLevelRequest\x1a\".gsdk.access.AddFloorLevelResponse\x12\x65\n\x12\x41\x64\x64\x46loorLevelMulti\x12&.gsdk.access.AddFloorLevelMultiRequest\x1a\'.gsdk.access.AddFloorLevelMultiResponse\x12_\n\x10\x44\x65leteFloorLevel\x12$.gsdk.access.DeleteFloorLevelRequest\x1a%.gsdk.access.DeleteFloorLevelResponse\x12n\n\x15\x44\x65leteFloorLevelMulti\x12).gsdk.access.DeleteFloorLevelMultiRequest\x1a*.gsdk.access.DeleteFloorLevelMultiResponse\x12h\n\x13\x44\x65leteAllFloorLevel\x12\'.gsdk.access.DeleteAllFloorLevelRequest\x1a(.gsdk.access.DeleteAllFloorLevelResponse\x12w\n\x18\x44\x65leteAllFloorLevelMulti\x12,.gsdk.access.DeleteAllFloorLevelMultiRequest\x1a-.gsdk.access.DeleteAllFloorLevelMultiResponseB5\n\x19\x63om.supremainc.sdk.accessP\x01Z\x16\x62iostar/service/accessb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'access_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\031com.supremainc.sdk.accessP\001Z\026biostar/service/access'
  _ENUM._options = None
  _ENUM._serialized_options = b'\020\001'
  _ENUM._serialized_start=2741
  _ENUM._serialized_end=2863
  _GETLISTREQUEST._serialized_start=40
  _GETLISTREQUEST._serialized_end=74
  _ACCESSGROUP._serialized_start=76
  _ACCESSGROUP._serialized_end=133
  _GETLISTRESPONSE._serialized_start=135
  _GETLISTRESPONSE._serialized_end=194
  _ADDREQUEST._serialized_start=196
  _ADDREQUEST._serialized_end=268
  _ADDRESPONSE._serialized_start=270
  _ADDRESPONSE._serialized_end=283
  _ADDMULTIREQUEST._serialized_start=285
  _ADDMULTIREQUEST._serialized_end=363
  _ADDMULTIRESPONSE._serialized_start=365
  _ADDMULTIRESPONSE._serialized_end=430
  _DELETEREQUEST._serialized_start=432
  _DELETEREQUEST._serialized_end=483
  _DELETERESPONSE._serialized_start=485
  _DELETERESPONSE._serialized_end=501
  _DELETEMULTIREQUEST._serialized_start=503
  _DELETEMULTIREQUEST._serialized_end=560
  _DELETEMULTIRESPONSE._serialized_start=562
  _DELETEMULTIRESPONSE._serialized_end=630
  _DELETEALLREQUEST._serialized_start=632
  _DELETEALLREQUEST._serialized_end=668
  _DELETEALLRESPONSE._serialized_start=670
  _DELETEALLRESPONSE._serialized_end=689
  _DELETEALLMULTIREQUEST._serialized_start=691
  _DELETEALLMULTIREQUEST._serialized_end=733
  _DELETEALLMULTIRESPONSE._serialized_start=735
  _DELETEALLMULTIRESPONSE._serialized_end=806
  _GETLEVELLISTREQUEST._serialized_start=808
  _GETLEVELLISTREQUEST._serialized_end=847
  _DOORSCHEDULE._serialized_start=849
  _DOORSCHEDULE._serialized_end=899
  _ACCESSLEVEL._serialized_start=901
  _ACCESSLEVEL._serialized_end=990
  _GETLEVELLISTRESPONSE._serialized_start=992
  _GETLEVELLISTRESPONSE._serialized_end=1056
  _ADDLEVELREQUEST._serialized_start=1058
  _ADDLEVELREQUEST._serialized_end=1135
  _ADDLEVELRESPONSE._serialized_start=1137
  _ADDLEVELRESPONSE._serialized_end=1155
  _ADDLEVELMULTIREQUEST._serialized_start=1157
  _ADDLEVELMULTIREQUEST._serialized_end=1240
  _ADDLEVELMULTIRESPONSE._serialized_start=1242
  _ADDLEVELMULTIRESPONSE._serialized_end=1312
  _DELETELEVELREQUEST._serialized_start=1314
  _DELETELEVELREQUEST._serialized_end=1370
  _DELETELEVELRESPONSE._serialized_start=1372
  _DELETELEVELRESPONSE._serialized_end=1393
  _DELETELEVELMULTIREQUEST._serialized_start=1395
  _DELETELEVELMULTIREQUEST._serialized_end=1457
  _DELETELEVELMULTIRESPONSE._serialized_start=1459
  _DELETELEVELMULTIRESPONSE._serialized_end=1532
  _DELETEALLLEVELREQUEST._serialized_start=1534
  _DELETEALLLEVELREQUEST._serialized_end=1575
  _DELETEALLLEVELRESPONSE._serialized_start=1577
  _DELETEALLLEVELRESPONSE._serialized_end=1601
  _DELETEALLLEVELMULTIREQUEST._serialized_start=1603
  _DELETEALLLEVELMULTIREQUEST._serialized_end=1650
  _DELETEALLLEVELMULTIRESPONSE._serialized_start=1652
  _DELETEALLLEVELMULTIRESPONSE._serialized_end=1728
  _FLOORSCHEDULE._serialized_start=1730
  _FLOORSCHEDULE._serialized_end=1801
  _FLOORLEVEL._serialized_start=1803
  _FLOORLEVEL._serialized_end=1893
  _GETFLOORLEVELLISTREQUEST._serialized_start=1895
  _GETFLOORLEVELLISTREQUEST._serialized_end=1939
  _GETFLOORLEVELLISTRESPONSE._serialized_start=1941
  _GETFLOORLEVELLISTRESPONSE._serialized_end=2009
  _ADDFLOORLEVELREQUEST._serialized_start=2011
  _ADDFLOORLEVELREQUEST._serialized_end=2092
  _ADDFLOORLEVELRESPONSE._serialized_start=2094
  _ADDFLOORLEVELRESPONSE._serialized_end=2117
  _ADDFLOORLEVELMULTIREQUEST._serialized_start=2119
  _ADDFLOORLEVELMULTIREQUEST._serialized_end=2206
  _ADDFLOORLEVELMULTIRESPONSE._serialized_start=2208
  _ADDFLOORLEVELMULTIRESPONSE._serialized_end=2283
  _DELETEFLOORLEVELREQUEST._serialized_start=2285
  _DELETEFLOORLEVELREQUEST._serialized_end=2346
  _DELETEFLOORLEVELRESPONSE._serialized_start=2348
  _DELETEFLOORLEVELRESPONSE._serialized_end=2374
  _DELETEFLOORLEVELMULTIREQUEST._serialized_start=2376
  _DELETEFLOORLEVELMULTIREQUEST._serialized_end=2443
  _DELETEFLOORLEVELMULTIRESPONSE._serialized_start=2445
  _DELETEFLOORLEVELMULTIRESPONSE._serialized_end=2523
  _DELETEALLFLOORLEVELREQUEST._serialized_start=2525
  _DELETEALLFLOORLEVELREQUEST._serialized_end=2571
  _DELETEALLFLOORLEVELRESPONSE._serialized_start=2573
  _DELETEALLFLOORLEVELRESPONSE._serialized_end=2602
  _DELETEALLFLOORLEVELMULTIREQUEST._serialized_start=2604
  _DELETEALLFLOORLEVELMULTIREQUEST._serialized_end=2656
  _DELETEALLFLOORLEVELMULTIRESPONSE._serialized_start=2658
  _DELETEALLFLOORLEVELMULTIRESPONSE._serialized_end=2739
  _ACCESS._serialized_start=2866
  _ACCESS._serialized_end=4740
# @@protoc_insertion_point(module_scope)
