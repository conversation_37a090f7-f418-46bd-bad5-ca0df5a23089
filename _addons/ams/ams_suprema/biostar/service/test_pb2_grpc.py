# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import test_pb2 as test__pb2


class TestStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.DetectCard = channel.unary_unary(
                '/gsdk.test.Test/DetectCard',
                request_serializer=test__pb2.DetectCardRequest.SerializeToString,
                response_deserializer=test__pb2.DetectCardResponse.FromString,
                )
        self.DetectFace = channel.unary_unary(
                '/gsdk.test.Test/DetectFace',
                request_serializer=test__pb2.DetectFaceRequest.SerializeToString,
                response_deserializer=test__pb2.DetectFaceResponse.FromString,
                )
        self.DetectFingerprint = channel.unary_unary(
                '/gsdk.test.Test/DetectFingerprint',
                request_serializer=test__pb2.DetectFingerprintRequest.SerializeToString,
                response_deserializer=test__pb2.DetectFingerprintResponse.FromString,
                )
        self.EnterKey = channel.unary_unary(
                '/gsdk.test.Test/EnterKey',
                request_serializer=test__pb2.EnterKeyRequest.SerializeToString,
                response_deserializer=test__pb2.EnterKeyResponse.FromString,
                )


class TestServicer(object):
    """Missing associated documentation comment in .proto file."""

    def DetectCard(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DetectFace(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DetectFingerprint(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnterKey(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TestServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'DetectCard': grpc.unary_unary_rpc_method_handler(
                    servicer.DetectCard,
                    request_deserializer=test__pb2.DetectCardRequest.FromString,
                    response_serializer=test__pb2.DetectCardResponse.SerializeToString,
            ),
            'DetectFace': grpc.unary_unary_rpc_method_handler(
                    servicer.DetectFace,
                    request_deserializer=test__pb2.DetectFaceRequest.FromString,
                    response_serializer=test__pb2.DetectFaceResponse.SerializeToString,
            ),
            'DetectFingerprint': grpc.unary_unary_rpc_method_handler(
                    servicer.DetectFingerprint,
                    request_deserializer=test__pb2.DetectFingerprintRequest.FromString,
                    response_serializer=test__pb2.DetectFingerprintResponse.SerializeToString,
            ),
            'EnterKey': grpc.unary_unary_rpc_method_handler(
                    servicer.EnterKey,
                    request_deserializer=test__pb2.EnterKeyRequest.FromString,
                    response_serializer=test__pb2.EnterKeyResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.test.Test', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Test(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def DetectCard(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.test.Test/DetectCard',
            test__pb2.DetectCardRequest.SerializeToString,
            test__pb2.DetectCardResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DetectFace(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.test.Test/DetectFace',
            test__pb2.DetectFaceRequest.SerializeToString,
            test__pb2.DetectFaceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DetectFingerprint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.test.Test/DetectFingerprint',
            test__pb2.DetectFingerprintRequest.SerializeToString,
            test__pb2.DetectFingerprintResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnterKey(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.test.Test/EnterKey',
            test__pb2.EnterKeyRequest.SerializeToString,
            test__pb2.EnterKeyResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
