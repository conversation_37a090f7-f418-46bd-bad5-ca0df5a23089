# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: cert.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\ncert.proto\x12\tgsdk.cert\"~\n\x07PKIName\x12\x0f\n\x07\x63ountry\x18\x01 \x01(\t\x12\x10\n\x08province\x18\x02 \x01(\t\x12\x0c\n\x04\x63ity\x18\x03 \x01(\t\x12\x14\n\x0corganization\x18\x04 \x01(\t\x12\x18\n\x10organizationUnit\x18\x05 \x01(\t\x12\x12\n\ncommonName\x18\x06 \x01(\t\"\x85\x01\n\x1e\x43reateServerCertificateRequest\x12#\n\x07subject\x18\x01 \x01(\x0b\x32\x12.gsdk.cert.PKIName\x12\x13\n\x0b\x64omainNames\x18\x02 \x03(\t\x12\x0f\n\x07IPAddrs\x18\x03 \x03(\t\x12\x18\n\x10\x65xpireAfterYears\x18\x04 \x01(\x05\"H\n\x1f\x43reateServerCertificateResponse\x12\x12\n\nserverCert\x18\x01 \x01(\t\x12\x11\n\tserverKey\x18\x02 \x01(\t\"D\n\x1bSetServerCertificateRequest\x12\x12\n\nserverCert\x18\x01 \x01(\t\x12\x11\n\tserverKey\x18\x02 \x01(\t\"\x1e\n\x1cSetServerCertificateResponse\"G\n\x1cSetGatewayCertificateRequest\x12\x13\n\x0bgatewayCert\x18\x01 \x01(\t\x12\x12\n\ngatewayKey\x18\x02 \x01(\t\"\x1f\n\x1dSetGatewayCertificateResponse2\xcd\x02\n\x04\x43\x65rt\x12p\n\x17\x43reateServerCertificate\x12).gsdk.cert.CreateServerCertificateRequest\x1a*.gsdk.cert.CreateServerCertificateResponse\x12g\n\x14SetServerCertificate\x12&.gsdk.cert.SetServerCertificateRequest\x1a\'.gsdk.cert.SetServerCertificateResponse\x12j\n\x15SetGatewayCertificate\x12\'.gsdk.cert.SetGatewayCertificateRequest\x1a(.gsdk.cert.SetGatewayCertificateResponseB1\n\x17\x63om.supremainc.sdk.certP\x01Z\x14\x62iostar/service/certb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'cert_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\027com.supremainc.sdk.certP\001Z\024biostar/service/cert'
  _PKINAME._serialized_start=25
  _PKINAME._serialized_end=151
  _CREATESERVERCERTIFICATEREQUEST._serialized_start=154
  _CREATESERVERCERTIFICATEREQUEST._serialized_end=287
  _CREATESERVERCERTIFICATERESPONSE._serialized_start=289
  _CREATESERVERCERTIFICATERESPONSE._serialized_end=361
  _SETSERVERCERTIFICATEREQUEST._serialized_start=363
  _SETSERVERCERTIFICATEREQUEST._serialized_end=431
  _SETSERVERCERTIFICATERESPONSE._serialized_start=433
  _SETSERVERCERTIFICATERESPONSE._serialized_end=463
  _SETGATEWAYCERTIFICATEREQUEST._serialized_start=465
  _SETGATEWAYCERTIFICATEREQUEST._serialized_end=536
  _SETGATEWAYCERTIFICATERESPONSE._serialized_start=538
  _SETGATEWAYCERTIFICATERESPONSE._serialized_end=569
  _CERT._serialized_start=572
  _CERT._serialized_end=905
# @@protoc_insertion_point(module_scope)
