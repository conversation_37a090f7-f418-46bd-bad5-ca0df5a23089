# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: schedule.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import err_pb2 as err__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0eschedule.proto\x12\rgsdk.schedule\x1a\terr.proto\"\xc6\x01\n\x0cScheduleInfo\x12\n\n\x02ID\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12-\n\x05\x64\x61ily\x18\x03 \x01(\x0b\x32\x1c.gsdk.schedule.DailyScheduleH\x00\x12/\n\x06weekly\x18\x04 \x01(\x0b\x32\x1d.gsdk.schedule.WeeklyScheduleH\x00\x12\x30\n\x08holidays\x18\x05 \x03(\x0b\x32\x1e.gsdk.schedule.HolidayScheduleB\n\n\x08ordinary\"9\n\x0b\x44\x61ySchedule\x12*\n\x07periods\x18\x01 \x03(\x0b\x32\x19.gsdk.schedule.TimePeriod\"0\n\nTimePeriod\x12\x11\n\tstartTime\x18\x01 \x01(\x05\x12\x0f\n\x07\x65ndTime\x18\x02 \x01(\x05\"B\n\x0eWeeklySchedule\x12\x30\n\x0c\x64\x61ySchedules\x18\x01 \x03(\x0b\x32\x1a.gsdk.schedule.DaySchedule\"T\n\rDailySchedule\x12\x11\n\tstartDate\x18\x01 \x01(\r\x12\x30\n\x0c\x64\x61ySchedules\x18\x02 \x03(\x0b\x32\x1a.gsdk.schedule.DaySchedule\"S\n\x0fHolidaySchedule\x12\x0f\n\x07groupID\x18\x01 \x01(\r\x12/\n\x0b\x64\x61ySchedule\x18\x02 \x01(\x0b\x32\x1a.gsdk.schedule.DaySchedule\"R\n\x0cHolidayGroup\x12\n\n\x02ID\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12(\n\x08holidays\x18\x03 \x03(\x0b\x32\x16.gsdk.schedule.Holiday\"M\n\x07Holiday\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x34\n\nrecurrence\x18\x02 \x01(\x0e\x32 .gsdk.schedule.HolidayRecurrence\"\"\n\x0eGetListRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"A\n\x0fGetListResponse\x12.\n\tschedules\x18\x01 \x03(\x0b\x32\x1b.gsdk.schedule.ScheduleInfo\"N\n\nAddRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12.\n\tschedules\x18\x02 \x03(\x0b\x32\x1b.gsdk.schedule.ScheduleInfo\"\r\n\x0b\x41\x64\x64Response\"T\n\x0f\x41\x64\x64MultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12.\n\tschedules\x18\x02 \x03(\x0b\x32\x1b.gsdk.schedule.ScheduleInfo\"A\n\x10\x41\x64\x64MultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"6\n\rDeleteRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x13\n\x0bscheduleIDs\x18\x02 \x03(\r\"\x10\n\x0e\x44\x65leteResponse\"<\n\x12\x44\x65leteMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x13\n\x0bscheduleIDs\x18\x02 \x03(\r\"D\n\x13\x44\x65leteMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"$\n\x10\x44\x65leteAllRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x13\n\x11\x44\x65leteAllResponse\"*\n\x15\x44\x65leteAllMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"G\n\x16\x44\x65leteAllMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\")\n\x15GetHolidayListRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"E\n\x16GetHolidayListResponse\x12+\n\x06groups\x18\x01 \x03(\x0b\x32\x1b.gsdk.schedule.HolidayGroup\"R\n\x11\x41\x64\x64HolidayRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12+\n\x06groups\x18\x02 \x03(\x0b\x32\x1b.gsdk.schedule.HolidayGroup\"\x14\n\x12\x41\x64\x64HolidayResponse\"X\n\x16\x41\x64\x64HolidayMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12+\n\x06groups\x18\x02 \x03(\x0b\x32\x1b.gsdk.schedule.HolidayGroup\"H\n\x17\x41\x64\x64HolidayMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\":\n\x14\x44\x65leteHolidayRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x10\n\x08groupIDs\x18\x02 \x03(\r\"\x17\n\x15\x44\x65leteHolidayResponse\"@\n\x19\x44\x65leteHolidayMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x10\n\x08groupIDs\x18\x02 \x03(\r\"K\n\x1a\x44\x65leteHolidayMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"+\n\x17\x44\x65leteAllHolidayRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x1a\n\x18\x44\x65leteAllHolidayResponse\"1\n\x1c\x44\x65leteAllHolidayMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"N\n\x1d\x44\x65leteAllHolidayMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse*+\n\x12PredefinedSchedule\x12\t\n\x05NEVER\x10\x00\x12\n\n\x06\x41LWAYS\x10\x01*s\n\x07WeekDay\x12\n\n\x06SUNDAY\x10\x00\x12\n\n\x06MONDAY\x10\x01\x12\x0b\n\x07TUESDAY\x10\x02\x12\r\n\tWEDNESDAY\x10\x03\x12\x0c\n\x08THURSDAY\x10\x04\x12\n\n\x06\x46RIDAY\x10\x05\x12\x0c\n\x08SATURDAY\x10\x06\x12\x0c\n\x08WEEKDAYS\x10\x07*\x86\x01\n\x04\x45num\x12!\n\x1d\x46IRST_ENUM_VALUE_MUST_BE_ZERO\x10\x00\x12\x16\n\x12MAX_HOLIDAY_GROUPS\x10\x04\x12\x14\n\x10MAX_TIME_PERIODS\x10\x05\x12\x17\n\x13MAX_DAILY_SCHEDULES\x10Z\x12\x14\n\x0fMAX_NAME_LENGTH\x10\x90\x01*\\\n\x11HolidayRecurrence\x12\x10\n\x0c\x44O_NOT_RECUR\x10\x00\x12\x10\n\x0cRECUR_YEARLY\x10\x01\x12\x11\n\rRECUR_MONTHLY\x10\x02\x12\x10\n\x0cRECUR_WEEKLY\x10\x03\x32\xdf\t\n\x08Schedule\x12H\n\x07GetList\x12\x1d.gsdk.schedule.GetListRequest\x1a\x1e.gsdk.schedule.GetListResponse\x12<\n\x03\x41\x64\x64\x12\x19.gsdk.schedule.AddRequest\x1a\x1a.gsdk.schedule.AddResponse\x12K\n\x08\x41\x64\x64Multi\x12\x1e.gsdk.schedule.AddMultiRequest\x1a\x1f.gsdk.schedule.AddMultiResponse\x12\x45\n\x06\x44\x65lete\x12\x1c.gsdk.schedule.DeleteRequest\x1a\x1d.gsdk.schedule.DeleteResponse\x12T\n\x0b\x44\x65leteMulti\x12!.gsdk.schedule.DeleteMultiRequest\x1a\".gsdk.schedule.DeleteMultiResponse\x12N\n\tDeleteAll\x12\x1f.gsdk.schedule.DeleteAllRequest\x1a .gsdk.schedule.DeleteAllResponse\x12]\n\x0e\x44\x65leteAllMulti\x12$.gsdk.schedule.DeleteAllMultiRequest\x1a%.gsdk.schedule.DeleteAllMultiResponse\x12]\n\x0eGetHolidayList\x12$.gsdk.schedule.GetHolidayListRequest\x1a%.gsdk.schedule.GetHolidayListResponse\x12Q\n\nAddHoliday\x12 .gsdk.schedule.AddHolidayRequest\x1a!.gsdk.schedule.AddHolidayResponse\x12`\n\x0f\x41\x64\x64HolidayMulti\x12%.gsdk.schedule.AddHolidayMultiRequest\x1a&.gsdk.schedule.AddHolidayMultiResponse\x12Z\n\rDeleteHoliday\x12#.gsdk.schedule.DeleteHolidayRequest\x1a$.gsdk.schedule.DeleteHolidayResponse\x12i\n\x12\x44\x65leteHolidayMulti\x12(.gsdk.schedule.DeleteHolidayMultiRequest\x1a).gsdk.schedule.DeleteHolidayMultiResponse\x12\x63\n\x10\x44\x65leteAllHoliday\x12&.gsdk.schedule.DeleteAllHolidayRequest\x1a\'.gsdk.schedule.DeleteAllHolidayResponse\x12r\n\x15\x44\x65leteAllHolidayMulti\x12+.gsdk.schedule.DeleteAllHolidayMultiRequest\x1a,.gsdk.schedule.DeleteAllHolidayMultiResponseB9\n\x1b\x63om.supremainc.sdk.scheduleP\x01Z\x18\x62iostar/service/scheduleb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'schedule_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\033com.supremainc.sdk.scheduleP\001Z\030biostar/service/schedule'
  _PREDEFINEDSCHEDULE._serialized_start=2305
  _PREDEFINEDSCHEDULE._serialized_end=2348
  _WEEKDAY._serialized_start=2350
  _WEEKDAY._serialized_end=2465
  _ENUM._serialized_start=2468
  _ENUM._serialized_end=2602
  _HOLIDAYRECURRENCE._serialized_start=2604
  _HOLIDAYRECURRENCE._serialized_end=2696
  _SCHEDULEINFO._serialized_start=45
  _SCHEDULEINFO._serialized_end=243
  _DAYSCHEDULE._serialized_start=245
  _DAYSCHEDULE._serialized_end=302
  _TIMEPERIOD._serialized_start=304
  _TIMEPERIOD._serialized_end=352
  _WEEKLYSCHEDULE._serialized_start=354
  _WEEKLYSCHEDULE._serialized_end=420
  _DAILYSCHEDULE._serialized_start=422
  _DAILYSCHEDULE._serialized_end=506
  _HOLIDAYSCHEDULE._serialized_start=508
  _HOLIDAYSCHEDULE._serialized_end=591
  _HOLIDAYGROUP._serialized_start=593
  _HOLIDAYGROUP._serialized_end=675
  _HOLIDAY._serialized_start=677
  _HOLIDAY._serialized_end=754
  _GETLISTREQUEST._serialized_start=756
  _GETLISTREQUEST._serialized_end=790
  _GETLISTRESPONSE._serialized_start=792
  _GETLISTRESPONSE._serialized_end=857
  _ADDREQUEST._serialized_start=859
  _ADDREQUEST._serialized_end=937
  _ADDRESPONSE._serialized_start=939
  _ADDRESPONSE._serialized_end=952
  _ADDMULTIREQUEST._serialized_start=954
  _ADDMULTIREQUEST._serialized_end=1038
  _ADDMULTIRESPONSE._serialized_start=1040
  _ADDMULTIRESPONSE._serialized_end=1105
  _DELETEREQUEST._serialized_start=1107
  _DELETEREQUEST._serialized_end=1161
  _DELETERESPONSE._serialized_start=1163
  _DELETERESPONSE._serialized_end=1179
  _DELETEMULTIREQUEST._serialized_start=1181
  _DELETEMULTIREQUEST._serialized_end=1241
  _DELETEMULTIRESPONSE._serialized_start=1243
  _DELETEMULTIRESPONSE._serialized_end=1311
  _DELETEALLREQUEST._serialized_start=1313
  _DELETEALLREQUEST._serialized_end=1349
  _DELETEALLRESPONSE._serialized_start=1351
  _DELETEALLRESPONSE._serialized_end=1370
  _DELETEALLMULTIREQUEST._serialized_start=1372
  _DELETEALLMULTIREQUEST._serialized_end=1414
  _DELETEALLMULTIRESPONSE._serialized_start=1416
  _DELETEALLMULTIRESPONSE._serialized_end=1487
  _GETHOLIDAYLISTREQUEST._serialized_start=1489
  _GETHOLIDAYLISTREQUEST._serialized_end=1530
  _GETHOLIDAYLISTRESPONSE._serialized_start=1532
  _GETHOLIDAYLISTRESPONSE._serialized_end=1601
  _ADDHOLIDAYREQUEST._serialized_start=1603
  _ADDHOLIDAYREQUEST._serialized_end=1685
  _ADDHOLIDAYRESPONSE._serialized_start=1687
  _ADDHOLIDAYRESPONSE._serialized_end=1707
  _ADDHOLIDAYMULTIREQUEST._serialized_start=1709
  _ADDHOLIDAYMULTIREQUEST._serialized_end=1797
  _ADDHOLIDAYMULTIRESPONSE._serialized_start=1799
  _ADDHOLIDAYMULTIRESPONSE._serialized_end=1871
  _DELETEHOLIDAYREQUEST._serialized_start=1873
  _DELETEHOLIDAYREQUEST._serialized_end=1931
  _DELETEHOLIDAYRESPONSE._serialized_start=1933
  _DELETEHOLIDAYRESPONSE._serialized_end=1956
  _DELETEHOLIDAYMULTIREQUEST._serialized_start=1958
  _DELETEHOLIDAYMULTIREQUEST._serialized_end=2022
  _DELETEHOLIDAYMULTIRESPONSE._serialized_start=2024
  _DELETEHOLIDAYMULTIRESPONSE._serialized_end=2099
  _DELETEALLHOLIDAYREQUEST._serialized_start=2101
  _DELETEALLHOLIDAYREQUEST._serialized_end=2144
  _DELETEALLHOLIDAYRESPONSE._serialized_start=2146
  _DELETEALLHOLIDAYRESPONSE._serialized_end=2172
  _DELETEALLHOLIDAYMULTIREQUEST._serialized_start=2174
  _DELETEALLHOLIDAYMULTIREQUEST._serialized_end=2223
  _DELETEALLHOLIDAYMULTIRESPONSE._serialized_start=2225
  _DELETEALLHOLIDAYMULTIRESPONSE._serialized_end=2303
  _SCHEDULE._serialized_start=2699
  _SCHEDULE._serialized_end=3946
# @@protoc_insertion_point(module_scope)
