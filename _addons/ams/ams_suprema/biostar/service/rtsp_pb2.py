# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: rtsp.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import err_pb2 as err__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nrtsp.proto\x12\tgsdk.rtsp\x1a\terr.proto\"d\n\nRTSPConfig\x12\x11\n\tserverURL\x18\x01 \x01(\t\x12\x12\n\nserverPort\x18\x02 \x01(\r\x12\x0e\n\x06userID\x18\x03 \x01(\t\x12\x0e\n\x06userPW\x18\x04 \x01(\t\x12\x0f\n\x07\x65nabled\x18\x05 \x01(\x08\"$\n\x10GetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\":\n\x11GetConfigResponse\x12%\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x15.gsdk.rtsp.RTSPConfig\"K\n\x10SetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12%\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x15.gsdk.rtsp.RTSPConfig\"\x13\n\x11SetConfigResponse\"Q\n\x15SetConfigMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12%\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x15.gsdk.rtsp.RTSPConfig\"G\n\x16SetConfigMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse*\x82\x01\n\x04\x45num\x12!\n\x1d\x46IRST_ENUM_VALUE_MUST_BE_ZERO\x10\x00\x12\x1d\n\x18\x44\x45\x46\x41ULT_RTSP_SERVER_PORT\x10\xaa\x04\x12\x16\n\x12MAX_RTSP_ID_LENGTH\x10 \x12\x1c\n\x18MAX_RTSP_PASSWORD_LENGTH\x10 \x1a\x02\x10\x01\x32\xed\x01\n\x04RTSP\x12\x46\n\tGetConfig\x12\x1b.gsdk.rtsp.GetConfigRequest\x1a\x1c.gsdk.rtsp.GetConfigResponse\x12\x46\n\tSetConfig\x12\x1b.gsdk.rtsp.SetConfigRequest\x1a\x1c.gsdk.rtsp.SetConfigResponse\x12U\n\x0eSetConfigMulti\x12 .gsdk.rtsp.SetConfigMultiRequest\x1a!.gsdk.rtsp.SetConfigMultiResponseB1\n\x17\x63om.supremainc.sdk.rtspP\x01Z\x14\x62iostar/service/rtspb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'rtsp_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\027com.supremainc.sdk.rtspP\001Z\024biostar/service/rtsp'
  _ENUM._options = None
  _ENUM._serialized_options = b'\020\001'
  _ENUM._serialized_start=491
  _ENUM._serialized_end=621
  _RTSPCONFIG._serialized_start=36
  _RTSPCONFIG._serialized_end=136
  _GETCONFIGREQUEST._serialized_start=138
  _GETCONFIGREQUEST._serialized_end=174
  _GETCONFIGRESPONSE._serialized_start=176
  _GETCONFIGRESPONSE._serialized_end=234
  _SETCONFIGREQUEST._serialized_start=236
  _SETCONFIGREQUEST._serialized_end=311
  _SETCONFIGRESPONSE._serialized_start=313
  _SETCONFIGRESPONSE._serialized_end=332
  _SETCONFIGMULTIREQUEST._serialized_start=334
  _SETCONFIGMULTIREQUEST._serialized_end=415
  _SETCONFIGMULTIRESPONSE._serialized_start=417
  _SETCONFIGMULTIRESPONSE._serialized_end=488
  _RTSP._serialized_start=624
  _RTSP._serialized_end=861
# @@protoc_insertion_point(module_scope)
