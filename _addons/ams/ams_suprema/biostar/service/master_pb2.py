# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: master.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
import event_pb2 as event__pb2
import err_pb2 as err__pb2
import connect_pb2 as connect__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0cmaster.proto\x12\x0bgsdk.master\x1a\x19google/protobuf/any.proto\x1a\x0b\x65vent.proto\x1a\terr.proto\x1a\rconnect.proto\"\'\n\x10SubscribeRequest\x12\x13\n\x0bgatewayCert\x18\x01 \x01(\t\"&\n\x11SubscribeResponse\x12\x11\n\tsessionID\x18\x01 \x01(\r\"?\n\x17UpdateDeviceListRequest\x12\x11\n\tsessionID\x18\x01 \x01(\r\x12\x11\n\tdeviceIDs\x18\x02 \x03(\r\"\x1a\n\x18UpdateDeviceListResponse\"\xca\x01\n\x0e\x43ommandRequest\x12<\n\x0brequestType\x18\x01 \x01(\x0e\x32\'.gsdk.master.CommandRequest.RequestType\x12\x11\n\trequestID\x18\x02 \x01(\r\x12\x11\n\tdeviceIDs\x18\x03 \x03(\r\x12%\n\x07request\x18\x04 \x01(\x0b\x32\x14.google.protobuf.Any\"-\n\x0bRequestType\x12\x0f\n\x0b\x43MD_WRAPPER\x10\x00\x12\r\n\tINIT_CHAN\x10\x01\"_\n\x0f\x43ommandResponse\x12\x11\n\tsessionID\x18\x01 \x01(\r\x12\x11\n\trequestID\x18\x02 \x01(\r\x12&\n\x08response\x18\x03 \x01(\x0b\x32\x14.google.protobuf.Any\"Y\n\rErrorResponse\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12-\n\x0c\x64\x65viceErrors\x18\x03 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"\x15\n\x13InitLogChanResponse\"J\n\rRealtimeEvent\x12\x14\n\x0cmasterChanID\x18\x01 \x01(\t\x12#\n\x05\x65vent\x18\x02 \x01(\x0b\x32\x14.gsdk.event.EventLog\"\x1e\n\x1cInitDeviceStatusChanResponse\"P\n\x0c\x44\x65viceStatus\x12\x14\n\x0cmasterChanID\x18\x01 \x01(\t\x12*\n\x06status\x18\x02 \x01(\x0b\x32\x1a.gsdk.connect.StatusChange2\xb6\x03\n\x06Master\x12J\n\tSubscribe\x12\x1d.gsdk.master.SubscribeRequest\x1a\x1e.gsdk.master.SubscribeResponse\x12_\n\x10UpdateDeviceList\x12$.gsdk.master.UpdateDeviceListRequest\x1a%.gsdk.master.UpdateDeviceListResponse\x12P\n\x0fInitCommandChan\x12\x1c.gsdk.master.CommandResponse\x1a\x1b.gsdk.master.CommandRequest(\x01\x30\x01\x12M\n\x0bInitLogChan\x12\x1a.gsdk.master.RealtimeEvent\x1a .gsdk.master.InitLogChanResponse(\x01\x12^\n\x14InitDeviceStatusChan\x12\x19.gsdk.master.DeviceStatus\x1a).gsdk.master.InitDeviceStatusChanResponse(\x01\x42\x18Z\x16\x62iostar/service/masterb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'master_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\026biostar/service/master'
  _SUBSCRIBEREQUEST._serialized_start=95
  _SUBSCRIBEREQUEST._serialized_end=134
  _SUBSCRIBERESPONSE._serialized_start=136
  _SUBSCRIBERESPONSE._serialized_end=174
  _UPDATEDEVICELISTREQUEST._serialized_start=176
  _UPDATEDEVICELISTREQUEST._serialized_end=239
  _UPDATEDEVICELISTRESPONSE._serialized_start=241
  _UPDATEDEVICELISTRESPONSE._serialized_end=267
  _COMMANDREQUEST._serialized_start=270
  _COMMANDREQUEST._serialized_end=472
  _COMMANDREQUEST_REQUESTTYPE._serialized_start=427
  _COMMANDREQUEST_REQUESTTYPE._serialized_end=472
  _COMMANDRESPONSE._serialized_start=474
  _COMMANDRESPONSE._serialized_end=569
  _ERRORRESPONSE._serialized_start=571
  _ERRORRESPONSE._serialized_end=660
  _INITLOGCHANRESPONSE._serialized_start=662
  _INITLOGCHANRESPONSE._serialized_end=683
  _REALTIMEEVENT._serialized_start=685
  _REALTIMEEVENT._serialized_end=759
  _INITDEVICESTATUSCHANRESPONSE._serialized_start=761
  _INITDEVICESTATUSCHANRESPONSE._serialized_end=791
  _DEVICESTATUS._serialized_start=793
  _DEVICESTATUS._serialized_end=873
  _MASTER._serialized_start=876
  _MASTER._serialized_end=1314
# @@protoc_insertion_point(module_scope)
