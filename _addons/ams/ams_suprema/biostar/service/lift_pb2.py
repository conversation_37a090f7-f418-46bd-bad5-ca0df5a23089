# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lift.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import device_pb2 as device__pb2
import action_pb2 as action__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nlift.proto\x12\tgsdk.lift\x1a\x0c\x64\x65vice.proto\x1a\x0c\x61\x63tion.proto\"P\n\x0b\x46loorStatus\x12\x11\n\tactivated\x18\x01 \x01(\x08\x12\x15\n\ractivateFlags\x18\x02 \x01(\r\x12\x17\n\x0f\x64\x65\x61\x63tivateFlags\x18\x03 \x01(\r\"O\n\x05\x46loor\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0c\n\x04port\x18\x02 \x01(\r\x12&\n\x06status\x18\x03 \x01(\x0b\x32\x16.gsdk.lift.FloorStatus\"u\n\x06Sensor\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0c\n\x04port\x18\x02 \x01(\r\x12%\n\x04type\x18\x03 \x01(\x0e\x32\x17.gsdk.device.SwitchType\x12\x10\n\x08\x64uration\x18\x04 \x01(\r\x12\x12\n\nscheduleID\x18\x05 \x01(\r\"O\n\x05\x41larm\x12!\n\x06sensor\x18\x01 \x01(\x0b\x32\x11.gsdk.lift.Sensor\x12#\n\x06\x61\x63tion\x18\x02 \x01(\x0b\x32\x13.gsdk.action.Action\"f\n\x06Status\x12\x0e\n\x06liftID\x18\x01 \x01(\r\x12\x12\n\nalarmFlags\x18\x02 \x01(\r\x12\x10\n\x08tamperOn\x18\x03 \x01(\x08\x12&\n\x06\x66loors\x18\x04 \x03(\x0b\x32\x16.gsdk.lift.FloorStatus\"\x95\x03\n\x08LiftInfo\x12\x0e\n\x06liftID\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tdeviceIDs\x18\x03 \x03(\r\x12\x17\n\x0f\x61\x63tivateTimeout\x18\x04 \x01(\r\x12\x17\n\x0f\x64ualAuthTimeout\x18\x05 \x01(\r\x12\x39\n\x10\x64ualAuthApproval\x18\x06 \x01(\x0e\x32\x1f.gsdk.lift.DualAuthApprovalType\x12!\n\x19\x64ualAuthRequiredDeviceIDs\x18\x07 \x03(\r\x12\x1a\n\x12\x64ualAuthScheduleID\x18\x08 \x01(\r\x12 \n\x06\x66loors\x18\t \x03(\x0b\x32\x10.gsdk.lift.Floor\x12 \n\x18\x64ualAuthApprovalGroupIDs\x18\n \x03(\r\x12 \n\x06\x61larms\x18\x0b \x03(\x0b\x32\x10.gsdk.lift.Alarm\x12\x12\n\nalarmFlags\x18\x0c \x01(\r\x12 \n\x06tamper\x18\r \x01(\x0b\x32\x10.gsdk.lift.Alarm\x12\x10\n\x08tamperOn\x18\x0e \x01(\x08\"\"\n\x0eGetListRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"5\n\x0fGetListResponse\x12\"\n\x05lifts\x18\x01 \x03(\x0b\x32\x13.gsdk.lift.LiftInfo\"$\n\x10GetStatusRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"6\n\x11GetStatusResponse\x12!\n\x06status\x18\x01 \x03(\x0b\x32\x11.gsdk.lift.Status\"B\n\nAddRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\"\n\x05lifts\x18\x02 \x03(\x0b\x32\x13.gsdk.lift.LiftInfo\"\r\n\x0b\x41\x64\x64Response\"2\n\rDeleteRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0f\n\x07liftIDs\x18\x02 \x03(\r\"\x10\n\x0e\x44\x65leteResponse\"$\n\x10\x44\x65leteAllRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x13\n\x11\x44\x65leteAllResponse\"_\n\x0f\x41\x63tivateRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0e\n\x06liftID\x18\x02 \x01(\r\x12\x14\n\x0c\x66loorIndexes\x18\x03 \x03(\r\x12\x14\n\x0c\x61\x63tivateFlag\x18\x04 \x01(\r\"\x12\n\x10\x41\x63tivateResponse\"c\n\x11\x44\x65\x61\x63tivateRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0e\n\x06liftID\x18\x02 \x01(\r\x12\x14\n\x0c\x66loorIndexes\x18\x03 \x03(\r\x12\x16\n\x0e\x64\x65\x61\x63tivateFlag\x18\x04 \x01(\r\"\x14\n\x12\x44\x65\x61\x63tivateResponse\"[\n\x0eReleaseRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0e\n\x06liftID\x18\x02 \x01(\r\x12\x14\n\x0c\x66loorIndexes\x18\x03 \x03(\r\x12\x11\n\tfloorFlag\x18\x04 \x01(\r\"\x11\n\x0fReleaseResponse\"G\n\x0fSetAlarmRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0f\n\x07liftIDs\x18\x02 \x03(\r\x12\x11\n\talarmFlag\x18\x03 \x01(\r\"\x12\n\x10SetAlarmResponse*\xd1\x01\n\x04\x45num\x12!\n\x1d\x46IRST_ENUM_VALUE_MUST_BE_ZERO\x10\x00\x12\x1c\n\x18\x44\x45\x46\x41ULT_ACTIVATE_TIMEOUT\x10\n\x12\x1d\n\x19\x44\x45\x46\x41ULT_DUAL_AUTH_TIMEOUT\x10\x0f\x12\x0e\n\nMAX_ALARMS\x10\x02\x12\x0f\n\x0bMAX_DEVICES\x10\x04\x12!\n\x1dMAX_DUAL_AUTH_APPROVAL_GROUPS\x10\x10\x12\x14\n\x0fMAX_NAME_LENGTH\x10\x90\x01\x12\x0f\n\nMAX_FLOORS\x10\xff\x01*L\n\tFloorFlag\x12\x08\n\x04NONE\x10\x00\x12\x0c\n\x08SCHEDULE\x10\x01\x12\x0c\n\x08OPERATOR\x10\x04\x12\n\n\x06\x41\x43TION\x10\x08\x12\r\n\tEMERGENCY\x10\x02*<\n\tAlarmFlag\x12\x0c\n\x08NO_ALARM\x10\x00\x12\t\n\x05\x46IRST\x10\x01\x12\n\n\x06SECOND\x10\x02\x12\n\n\x06TAMPER\x10\x04*:\n\x14\x44ualAuthApprovalType\x12\x10\n\x0cNONE_ON_LIFT\x10\x00\x12\x10\n\x0cLAST_ON_LIFT\x10\x01\x32\xe4\x04\n\x04Lift\x12@\n\x07GetList\x12\x19.gsdk.lift.GetListRequest\x1a\x1a.gsdk.lift.GetListResponse\x12\x46\n\tGetStatus\x12\x1b.gsdk.lift.GetStatusRequest\x1a\x1c.gsdk.lift.GetStatusResponse\x12\x34\n\x03\x41\x64\x64\x12\x15.gsdk.lift.AddRequest\x1a\x16.gsdk.lift.AddResponse\x12=\n\x06\x44\x65lete\x12\x18.gsdk.lift.DeleteRequest\x1a\x19.gsdk.lift.DeleteResponse\x12\x46\n\tDeleteAll\x12\x1b.gsdk.lift.DeleteAllRequest\x1a\x1c.gsdk.lift.DeleteAllResponse\x12\x43\n\x08\x41\x63tivate\x12\x1a.gsdk.lift.ActivateRequest\x1a\x1b.gsdk.lift.ActivateResponse\x12I\n\nDeactivate\x12\x1c.gsdk.lift.DeactivateRequest\x1a\x1d.gsdk.lift.DeactivateResponse\x12@\n\x07Release\x12\x19.gsdk.lift.ReleaseRequest\x1a\x1a.gsdk.lift.ReleaseResponse\x12\x43\n\x08SetAlarm\x12\x1a.gsdk.lift.SetAlarmRequest\x1a\x1b.gsdk.lift.SetAlarmResponseB1\n\x17\x63om.supremainc.sdk.liftP\x01Z\x14\x62iostar/service/liftb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lift_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\027com.supremainc.sdk.liftP\001Z\024biostar/service/lift'
  _ENUM._serialized_start=1771
  _ENUM._serialized_end=1980
  _FLOORFLAG._serialized_start=1982
  _FLOORFLAG._serialized_end=2058
  _ALARMFLAG._serialized_start=2060
  _ALARMFLAG._serialized_end=2120
  _DUALAUTHAPPROVALTYPE._serialized_start=2122
  _DUALAUTHAPPROVALTYPE._serialized_end=2180
  _FLOORSTATUS._serialized_start=53
  _FLOORSTATUS._serialized_end=133
  _FLOOR._serialized_start=135
  _FLOOR._serialized_end=214
  _SENSOR._serialized_start=216
  _SENSOR._serialized_end=333
  _ALARM._serialized_start=335
  _ALARM._serialized_end=414
  _STATUS._serialized_start=416
  _STATUS._serialized_end=518
  _LIFTINFO._serialized_start=521
  _LIFTINFO._serialized_end=926
  _GETLISTREQUEST._serialized_start=928
  _GETLISTREQUEST._serialized_end=962
  _GETLISTRESPONSE._serialized_start=964
  _GETLISTRESPONSE._serialized_end=1017
  _GETSTATUSREQUEST._serialized_start=1019
  _GETSTATUSREQUEST._serialized_end=1055
  _GETSTATUSRESPONSE._serialized_start=1057
  _GETSTATUSRESPONSE._serialized_end=1111
  _ADDREQUEST._serialized_start=1113
  _ADDREQUEST._serialized_end=1179
  _ADDRESPONSE._serialized_start=1181
  _ADDRESPONSE._serialized_end=1194
  _DELETEREQUEST._serialized_start=1196
  _DELETEREQUEST._serialized_end=1246
  _DELETERESPONSE._serialized_start=1248
  _DELETERESPONSE._serialized_end=1264
  _DELETEALLREQUEST._serialized_start=1266
  _DELETEALLREQUEST._serialized_end=1302
  _DELETEALLRESPONSE._serialized_start=1304
  _DELETEALLRESPONSE._serialized_end=1323
  _ACTIVATEREQUEST._serialized_start=1325
  _ACTIVATEREQUEST._serialized_end=1420
  _ACTIVATERESPONSE._serialized_start=1422
  _ACTIVATERESPONSE._serialized_end=1440
  _DEACTIVATEREQUEST._serialized_start=1442
  _DEACTIVATEREQUEST._serialized_end=1541
  _DEACTIVATERESPONSE._serialized_start=1543
  _DEACTIVATERESPONSE._serialized_end=1563
  _RELEASEREQUEST._serialized_start=1565
  _RELEASEREQUEST._serialized_end=1656
  _RELEASERESPONSE._serialized_start=1658
  _RELEASERESPONSE._serialized_end=1675
  _SETALARMREQUEST._serialized_start=1677
  _SETALARMREQUEST._serialized_end=1748
  _SETALARMRESPONSE._serialized_start=1750
  _SETALARMRESPONSE._serialized_end=1768
  _LIFT._serialized_start=2183
  _LIFT._serialized_end=2795
# @@protoc_insertion_point(module_scope)
