# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import login_pb2 as login__pb2


class LoginStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Login = channel.unary_unary(
                '/gsdk.login.Login/Login',
                request_serializer=login__pb2.LoginRequest.SerializeToString,
                response_deserializer=login__pb2.LoginResponse.FromString,
                )
        self.LoginAdmin = channel.unary_unary(
                '/gsdk.login.Login/LoginAdmin',
                request_serializer=login__pb2.LoginAdminRequest.SerializeToString,
                response_deserializer=login__pb2.LoginAdminResponse.FromString,
                )
        self.Logout = channel.unary_unary(
                '/gsdk.login.Login/Logout',
                request_serializer=login__pb2.LogoutRequest.SerializeToString,
                response_deserializer=login__pb2.LogoutResponse.FromString,
                )


class LoginServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Login(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoginAdmin(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Logout(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LoginServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Login': grpc.unary_unary_rpc_method_handler(
                    servicer.Login,
                    request_deserializer=login__pb2.LoginRequest.FromString,
                    response_serializer=login__pb2.LoginResponse.SerializeToString,
            ),
            'LoginAdmin': grpc.unary_unary_rpc_method_handler(
                    servicer.LoginAdmin,
                    request_deserializer=login__pb2.LoginAdminRequest.FromString,
                    response_serializer=login__pb2.LoginAdminResponse.SerializeToString,
            ),
            'Logout': grpc.unary_unary_rpc_method_handler(
                    servicer.Logout,
                    request_deserializer=login__pb2.LogoutRequest.FromString,
                    response_serializer=login__pb2.LogoutResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.login.Login', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Login(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Login(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.login.Login/Login',
            login__pb2.LoginRequest.SerializeToString,
            login__pb2.LoginResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LoginAdmin(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.login.Login/LoginAdmin',
            login__pb2.LoginAdminRequest.SerializeToString,
            login__pb2.LoginAdminResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Logout(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.login.Login/Logout',
            login__pb2.LogoutRequest.SerializeToString,
            login__pb2.LogoutResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
