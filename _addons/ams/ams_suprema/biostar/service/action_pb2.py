# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: action.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import device_pb2 as device__pb2
import err_pb2 as err__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x61\x63tion.proto\x12\x0bgsdk.action\x1a\x0c\x64\x65vice.proto\x1a\terr.proto\"!\n\x0c\x45ventTrigger\x12\x11\n\teventCode\x18\x01 \x01(\r\"o\n\x0cInputTrigger\x12\x0c\n\x04port\x18\x01 \x01(\r\x12+\n\nswitchType\x18\x02 \x01(\x0e\x32\x17.gsdk.device.SwitchType\x12\x10\n\x08\x64uration\x18\x03 \x01(\r\x12\x12\n\nscheduleID\x18\x04 \x01(\r\"U\n\x0fScheduleTrigger\x12.\n\x04type\x18\x01 \x01(\x0e\x32 .gsdk.action.ScheduleTriggerType\x12\x12\n\nscheduleID\x18\x02 \x01(\r\"\xd7\x01\n\x07Trigger\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12&\n\x04type\x18\x02 \x01(\x0e\x32\x18.gsdk.action.TriggerType\x12*\n\x05\x65vent\x18\x03 \x01(\x0b\x32\x19.gsdk.action.EventTriggerH\x00\x12*\n\x05input\x18\x04 \x01(\x0b\x32\x19.gsdk.action.InputTriggerH\x00\x12\x30\n\x08schedule\x18\x05 \x01(\x0b\x32\x1c.gsdk.action.ScheduleTriggerH\x00\x42\x08\n\x06\x65ntity\"a\n\x06Signal\x12\x10\n\x08signalID\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x12\n\nonDuration\x18\x03 \x01(\r\x12\x13\n\x0boffDuration\x18\x04 \x01(\r\x12\r\n\x05\x64\x65lay\x18\x05 \x01(\r\"J\n\x10OutputPortAction\x12\x11\n\tportIndex\x18\x01 \x01(\r\x12#\n\x06signal\x18\x02 \x01(\x0b\x32\x13.gsdk.action.Signal\"F\n\x0bRelayAction\x12\x12\n\nrelayIndex\x18\x01 \x01(\r\x12#\n\x06signal\x18\x02 \x01(\x0b\x32\x13.gsdk.action.Signal\"R\n\tLEDSignal\x12$\n\x05\x63olor\x18\x01 \x01(\x0e\x32\x15.gsdk.device.LEDColor\x12\x10\n\x08\x64uration\x18\x02 \x01(\r\x12\r\n\x05\x64\x65lay\x18\x03 \x01(\r\"C\n\tLEDAction\x12\'\n\x07signals\x18\x01 \x03(\x0b\x32\x16.gsdk.action.LEDSignal\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"g\n\x0c\x42uzzerSignal\x12%\n\x04tone\x18\x01 \x01(\x0e\x32\x17.gsdk.device.BuzzerTone\x12\x0f\n\x07\x66\x61\x64\x65out\x18\x02 \x01(\x08\x12\x10\n\x08\x64uration\x18\x03 \x01(\r\x12\r\n\x05\x64\x65lay\x18\x04 \x01(\r\"I\n\x0c\x42uzzerAction\x12*\n\x07signals\x18\x01 \x03(\x0b\x32\x19.gsdk.action.BuzzerSignal\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"H\n\rDisplayAction\x12\x10\n\x08\x64uration\x18\x01 \x01(\r\x12\x11\n\tdisplayID\x18\x02 \x01(\r\x12\x12\n\nresourceID\x18\x03 \x01(\r\"?\n\x0bSoundAction\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x12\n\nsoundIndex\x18\x02 \x01(\r\x12\r\n\x05\x64\x65lay\x18\x03 \x01(\r\"G\n\nLiftAction\x12\x0e\n\x06liftID\x18\x01 \x01(\r\x12)\n\x04type\x18\x02 \x01(\x0e\x32\x1b.gsdk.action.LiftActionType\"\xba\x03\n\x06\x41\x63tion\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12%\n\x04type\x18\x02 \x01(\x0e\x32\x17.gsdk.action.ActionType\x12\'\n\x08stopFlag\x18\x03 \x01(\x0e\x32\x15.gsdk.action.StopFlag\x12\r\n\x05\x64\x65lay\x18\x04 \x01(\r\x12)\n\x05relay\x18\x05 \x01(\x0b\x32\x18.gsdk.action.RelayActionH\x00\x12\x33\n\noutputPort\x18\x06 \x01(\x0b\x32\x1d.gsdk.action.OutputPortActionH\x00\x12-\n\x07\x64isplay\x18\x07 \x01(\x0b\x32\x1a.gsdk.action.DisplayActionH\x00\x12)\n\x05sound\x18\x08 \x01(\x0b\x32\x18.gsdk.action.SoundActionH\x00\x12%\n\x03LED\x18\t \x01(\x0b\x32\x16.gsdk.action.LEDActionH\x00\x12+\n\x06\x62uzzer\x18\n \x01(\x0b\x32\x19.gsdk.action.BuzzerActionH\x00\x12\'\n\x04lift\x18\x0b \x01(\x0b\x32\x17.gsdk.action.LiftActionH\x00\x42\x08\n\x06\x65ntity\"\xba\x01\n\x13TriggerActionConfig\x12\x46\n\x0etriggerActions\x18\x01 \x03(\x0b\x32..gsdk.action.TriggerActionConfig.TriggerAction\x1a[\n\rTriggerAction\x12%\n\x07trigger\x18\x01 \x01(\x0b\x32\x14.gsdk.action.Trigger\x12#\n\x06\x61\x63tion\x18\x02 \x01(\x0b\x32\x13.gsdk.action.Action\"$\n\x10GetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"E\n\x11GetConfigResponse\x12\x30\n\x06\x63onfig\x18\x01 \x01(\x0b\x32 .gsdk.action.TriggerActionConfig\"V\n\x10SetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x30\n\x06\x63onfig\x18\x02 \x01(\x0b\x32 .gsdk.action.TriggerActionConfig\"\x13\n\x11SetConfigResponse\"\\\n\x15SetConfigMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x30\n\x06\x63onfig\x18\x02 \x01(\x0b\x32 .gsdk.action.TriggerActionConfig\"G\n\x16SetConfigMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse*\x84\x01\n\x04\x45num\x12\x15\n\x11REPEAT_INFINITELY\x10\x00\x12\x1c\n\x17\x44\x45\x46\x41ULT_SIGNAL_DURATION\x10\xd0\x0f\x12\x18\n\x14\x44\x45\x46\x41ULT_SIGNAL_DELAY\x10\x00\x12\x0f\n\x0bMAX_SIGNALS\x10\x03\x12\x18\n\x13MAX_TRIGGER_ACTIONS\x10\x80\x01\x1a\x02\x10\x01*[\n\x0bTriggerType\x12\x10\n\x0cTRIGGER_NONE\x10\x00\x12\x11\n\rTRIGGER_EVENT\x10\x01\x12\x11\n\rTRIGGER_INPUT\x10\x02\x12\x14\n\x10TRIGGER_SCHEDULE\x10\x03*Q\n\x13ScheduleTriggerType\x12\x1d\n\x19SCHEDULE_TRIGGER_ON_START\x10\x00\x12\x1b\n\x17SCHEDULE_TRIGGER_ON_END\x10\x01*\xe5\x02\n\nActionType\x12\x0f\n\x0b\x41\x43TION_NONE\x10\x00\x12\x16\n\x12\x41\x43TION_LOCK_DEVICE\x10\x01\x12\x18\n\x14\x41\x43TION_UNLOCK_DEVICE\x10\x02\x12\x18\n\x14\x41\x43TION_REBOOT_DEVICE\x10\x03\x12\x18\n\x14\x41\x43TION_RELEASE_ALARM\x10\x04\x12\x18\n\x14\x41\x43TION_GENERAL_INPUT\x10\x05\x12\x10\n\x0c\x41\x43TION_RELAY\x10\x06\x12\x0e\n\nACTION_TTL\x10\x07\x12\x10\n\x0c\x41\x43TION_SOUND\x10\x08\x12\x12\n\x0e\x41\x43TION_DISPLAY\x10\t\x12\x11\n\rACTION_BUZZER\x10\n\x12\x0e\n\nACTION_LED\x10\x0b\x12\x1b\n\x17\x41\x43TION_FIRE_ALARM_INPUT\x10\x0c\x12\x17\n\x13\x41\x43TION_AUTH_SUCCESS\x10\r\x12\x14\n\x10\x41\x43TION_AUTH_FAIL\x10\x0e\x12\x0f\n\x0b\x41\x43TION_LIFT\x10\x0f*N\n\x08StopFlag\x12\r\n\tSTOP_NONE\x10\x00\x12\x17\n\x13STOP_ON_DOOR_CLOSED\x10\x01\x12\x1a\n\x16STOP_BY_CMD_RUN_ACTION\x10\x02*t\n\x0eLiftActionType\x12\x1f\n\x1bLIFT_ACTION_ACTIVATE_FLOORS\x10\x00\x12!\n\x1dLIFT_ACTION_DEACTIVATE_FLOORS\x10\x01\x12\x1e\n\x1aLIFT_ACTION_RELEASE_FLOORS\x10\x02\x32\x82\x02\n\rTriggerAction\x12J\n\tGetConfig\x12\x1d.gsdk.action.GetConfigRequest\x1a\x1e.gsdk.action.GetConfigResponse\x12J\n\tSetConfig\x12\x1d.gsdk.action.SetConfigRequest\x1a\x1e.gsdk.action.SetConfigResponse\x12Y\n\x0eSetConfigMulti\x12\".gsdk.action.SetConfigMultiRequest\x1a#.gsdk.action.SetConfigMultiResponseB5\n\x19\x63om.supremainc.sdk.actionP\x01Z\x16\x62iostar/service/actionb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'action_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\031com.supremainc.sdk.actionP\001Z\026biostar/service/action'
  _ENUM._options = None
  _ENUM._serialized_options = b'\020\001'
  _ENUM._serialized_start=2319
  _ENUM._serialized_end=2451
  _TRIGGERTYPE._serialized_start=2453
  _TRIGGERTYPE._serialized_end=2544
  _SCHEDULETRIGGERTYPE._serialized_start=2546
  _SCHEDULETRIGGERTYPE._serialized_end=2627
  _ACTIONTYPE._serialized_start=2630
  _ACTIONTYPE._serialized_end=2987
  _STOPFLAG._serialized_start=2989
  _STOPFLAG._serialized_end=3067
  _LIFTACTIONTYPE._serialized_start=3069
  _LIFTACTIONTYPE._serialized_end=3185
  _EVENTTRIGGER._serialized_start=54
  _EVENTTRIGGER._serialized_end=87
  _INPUTTRIGGER._serialized_start=89
  _INPUTTRIGGER._serialized_end=200
  _SCHEDULETRIGGER._serialized_start=202
  _SCHEDULETRIGGER._serialized_end=287
  _TRIGGER._serialized_start=290
  _TRIGGER._serialized_end=505
  _SIGNAL._serialized_start=507
  _SIGNAL._serialized_end=604
  _OUTPUTPORTACTION._serialized_start=606
  _OUTPUTPORTACTION._serialized_end=680
  _RELAYACTION._serialized_start=682
  _RELAYACTION._serialized_end=752
  _LEDSIGNAL._serialized_start=754
  _LEDSIGNAL._serialized_end=836
  _LEDACTION._serialized_start=838
  _LEDACTION._serialized_end=905
  _BUZZERSIGNAL._serialized_start=907
  _BUZZERSIGNAL._serialized_end=1010
  _BUZZERACTION._serialized_start=1012
  _BUZZERACTION._serialized_end=1085
  _DISPLAYACTION._serialized_start=1087
  _DISPLAYACTION._serialized_end=1159
  _SOUNDACTION._serialized_start=1161
  _SOUNDACTION._serialized_end=1224
  _LIFTACTION._serialized_start=1226
  _LIFTACTION._serialized_end=1297
  _ACTION._serialized_start=1300
  _ACTION._serialized_end=1742
  _TRIGGERACTIONCONFIG._serialized_start=1745
  _TRIGGERACTIONCONFIG._serialized_end=1931
  _TRIGGERACTIONCONFIG_TRIGGERACTION._serialized_start=1840
  _TRIGGERACTIONCONFIG_TRIGGERACTION._serialized_end=1931
  _GETCONFIGREQUEST._serialized_start=1933
  _GETCONFIGREQUEST._serialized_end=1969
  _GETCONFIGRESPONSE._serialized_start=1971
  _GETCONFIGRESPONSE._serialized_end=2040
  _SETCONFIGREQUEST._serialized_start=2042
  _SETCONFIGREQUEST._serialized_end=2128
  _SETCONFIGRESPONSE._serialized_start=2130
  _SETCONFIGRESPONSE._serialized_end=2149
  _SETCONFIGMULTIREQUEST._serialized_start=2151
  _SETCONFIGMULTIREQUEST._serialized_end=2243
  _SETCONFIGMULTIRESPONSE._serialized_start=2245
  _SETCONFIGMULTIRESPONSE._serialized_end=2316
  _TRIGGERACTION._serialized_start=3188
  _TRIGGERACTION._serialized_end=3446
# @@protoc_insertion_point(module_scope)
