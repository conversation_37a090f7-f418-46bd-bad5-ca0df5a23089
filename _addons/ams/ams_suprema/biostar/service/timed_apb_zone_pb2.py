# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: timed_apb_zone.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import zone_pb2 as zone__pb2
import action_pb2 as action__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14timed_apb_zone.proto\x12\x13gsdk.timed_apb_zone\x1a\nzone.proto\x1a\x0c\x61\x63tion.proto\"\x1a\n\x06Member\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\xf7\x01\n\x08ZoneInfo\x12\x0e\n\x06zoneID\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\'\n\x04type\x18\x03 \x01(\x0e\x32\x19.gsdk.timed_apb_zone.Type\x12\x10\n\x08\x64isabled\x18\x04 \x01(\x08\x12\x0f\n\x07\x61larmed\x18\x05 \x01(\x08\x12\x15\n\rresetDuration\x18\x06 \x01(\r\x12,\n\x07members\x18\x07 \x03(\x0b\x32\x1b.gsdk.timed_apb_zone.Member\x12$\n\x07\x61\x63tions\x18\x08 \x03(\x0b\x32\x13.gsdk.action.Action\x12\x16\n\x0e\x62ypassGroupIDs\x18\t \x03(\r\"\x1e\n\nGetRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\";\n\x0bGetResponse\x12,\n\x05zones\x18\x01 \x03(\x0b\x32\x1d.gsdk.timed_apb_zone.ZoneInfo\"5\n\x10GetStatusRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0f\n\x07zoneIDs\x18\x02 \x03(\r\":\n\x11GetStatusResponse\x12%\n\x06status\x18\x01 \x03(\x0b\x32\x15.gsdk.zone.ZoneStatus\"L\n\nAddRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12,\n\x05zones\x18\x02 \x03(\x0b\x32\x1d.gsdk.timed_apb_zone.ZoneInfo\"\r\n\x0b\x41\x64\x64Response\"2\n\rDeleteRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0f\n\x07zoneIDs\x18\x02 \x03(\r\"\x10\n\x0e\x44\x65leteResponse\"$\n\x10\x44\x65leteAllRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x13\n\x11\x44\x65leteAllResponse\"A\n\x0c\x43learRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0e\n\x06zoneID\x18\x02 \x01(\r\x12\x0f\n\x07userIDs\x18\x03 \x03(\t\"\x0f\n\rClearResponse\"3\n\x0f\x43learAllRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0e\n\x06zoneID\x18\x02 \x01(\r\"\x12\n\x10\x43learAllResponse\"E\n\x0fSetAlarmRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0f\n\x07zoneIDs\x18\x02 \x03(\r\x12\x0f\n\x07\x61larmed\x18\x03 \x01(\x08\"\x12\n\x10SetAlarmResponse*\x80\x01\n\x04\x45num\x12\x0c\n\x08NO_RESET\x10\x00\x12\x1c\n\x16\x44\x45\x46\x41ULT_RESET_DURATION\x10\x80\xa3\x05\x12\x0e\n\nMAX_ALARMS\x10\x05\x12\x15\n\x11MAX_BYPASS_GROUPS\x10\x10\x12\x0f\n\x0bMAX_MEMBERS\x10@\x12\x14\n\x0fMAX_NAME_LENGTH\x10\x90\x01*\x1a\n\x04Type\x12\x08\n\x04HARD\x10\x00\x12\x08\n\x04SOFT\x10\x01\x32\xaf\x05\n\x0cTimedAPBZone\x12H\n\x03Get\x12\x1f.gsdk.timed_apb_zone.GetRequest\x1a .gsdk.timed_apb_zone.GetResponse\x12Z\n\tGetStatus\x12%.gsdk.timed_apb_zone.GetStatusRequest\x1a&.gsdk.timed_apb_zone.GetStatusResponse\x12H\n\x03\x41\x64\x64\x12\x1f.gsdk.timed_apb_zone.AddRequest\x1a .gsdk.timed_apb_zone.AddResponse\x12N\n\x05\x43lear\x12!.gsdk.timed_apb_zone.ClearRequest\x1a\".gsdk.timed_apb_zone.ClearResponse\x12W\n\x08\x43learAll\x12$.gsdk.timed_apb_zone.ClearAllRequest\x1a%.gsdk.timed_apb_zone.ClearAllResponse\x12Q\n\x06\x44\x65lete\x12\".gsdk.timed_apb_zone.DeleteRequest\x1a#.gsdk.timed_apb_zone.DeleteResponse\x12Z\n\tDeleteAll\x12%.gsdk.timed_apb_zone.DeleteAllRequest\x1a&.gsdk.timed_apb_zone.DeleteAllResponse\x12W\n\x08SetAlarm\x12$.gsdk.timed_apb_zone.SetAlarmRequest\x1a%.gsdk.timed_apb_zone.SetAlarmResponseBC\n!com.supremainc.sdk.timed_apb_zoneP\x01Z\x1c\x62iostar/service/timedApbZoneb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'timed_apb_zone_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n!com.supremainc.sdk.timed_apb_zoneP\001Z\034biostar/service/timedApbZone'
  _ENUM._serialized_start=1028
  _ENUM._serialized_end=1156
  _TYPE._serialized_start=1158
  _TYPE._serialized_end=1184
  _MEMBER._serialized_start=71
  _MEMBER._serialized_end=97
  _ZONEINFO._serialized_start=100
  _ZONEINFO._serialized_end=347
  _GETREQUEST._serialized_start=349
  _GETREQUEST._serialized_end=379
  _GETRESPONSE._serialized_start=381
  _GETRESPONSE._serialized_end=440
  _GETSTATUSREQUEST._serialized_start=442
  _GETSTATUSREQUEST._serialized_end=495
  _GETSTATUSRESPONSE._serialized_start=497
  _GETSTATUSRESPONSE._serialized_end=555
  _ADDREQUEST._serialized_start=557
  _ADDREQUEST._serialized_end=633
  _ADDRESPONSE._serialized_start=635
  _ADDRESPONSE._serialized_end=648
  _DELETEREQUEST._serialized_start=650
  _DELETEREQUEST._serialized_end=700
  _DELETERESPONSE._serialized_start=702
  _DELETERESPONSE._serialized_end=718
  _DELETEALLREQUEST._serialized_start=720
  _DELETEALLREQUEST._serialized_end=756
  _DELETEALLRESPONSE._serialized_start=758
  _DELETEALLRESPONSE._serialized_end=777
  _CLEARREQUEST._serialized_start=779
  _CLEARREQUEST._serialized_end=844
  _CLEARRESPONSE._serialized_start=846
  _CLEARRESPONSE._serialized_end=861
  _CLEARALLREQUEST._serialized_start=863
  _CLEARALLREQUEST._serialized_end=914
  _CLEARALLRESPONSE._serialized_start=916
  _CLEARALLRESPONSE._serialized_end=934
  _SETALARMREQUEST._serialized_start=936
  _SETALARMREQUEST._serialized_end=1005
  _SETALARMRESPONSE._serialized_start=1007
  _SETALARMRESPONSE._serialized_end=1025
  _TIMEDAPBZONE._serialized_start=1187
  _TIMEDAPBZONE._serialized_end=1874
# @@protoc_insertion_point(module_scope)
