# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: device.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import err_pb2 as err__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='device.proto',
  package='device',
  syntax='proto3',
  serialized_options=_b('\n\031com.supremainc.sdk.deviceP\001Z\026biostar/service/device'),
  serialized_pb=_b('\n\x0c\x64\x65vice.proto\x12\x06\x64\x65vice\x1a\terr.proto\"\"\n\x0eGetInfoRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x8e\x01\n\x0b\x46\x61\x63toryInfo\x12\x0f\n\x07MACAddr\x18\x02 \x01(\t\x12\x11\n\tmodelName\x18\x03 \x01(\t\x12\x17\n\x0f\x66irmwareVersion\x18\x04 \x01(\t\x12\x15\n\rkernelVersion\x18\x05 \x01(\t\x12\x15\n\rBSCoreVersion\x18\x06 \x01(\t\x12\x14\n\x0c\x62oardVersion\x18\x07 \x01(\t\"4\n\x0fGetInfoResponse\x12!\n\x04info\x18\x01 \x01(\x0b\x32\x13.device.FactoryInfo\",\n\x18GetCapabilityInfoRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\xf7\x04\n\x0e\x43\x61pabilityInfo\x12\x1a\n\x04type\x18\x01 \x01(\x0e\x32\x0c.device.Type\x12\x14\n\x0cmaxNumOfUser\x18\x02 \x01(\r\x12\x14\n\x0cPINSupported\x18\x03 \x01(\x08\x12\x15\n\rcardSupported\x18\x04 \x01(\x08\x12\x17\n\x0f\x63\x61rd1xSupported\x18\x05 \x01(\x08\x12\x15\n\rSEOSSupported\x18\x06 \x01(\x08\x12\x17\n\x0f\x66ingerSupported\x18\x07 \x01(\x08\x12\x15\n\rfaceSupported\x18\x08 \x01(\x08\x12\x19\n\x11userNameSupported\x18\n \x01(\x08\x12\x1a\n\x12userPhotoSupported\x18\x0b \x01(\x08\x12\x1b\n\x13userPhraseSupported\x18\x0c \x01(\x08\x12\x1f\n\x17\x61lphanumericIDSupported\x18\r \x01(\x08\x12\x15\n\rWLANSupported\x18\x14 \x01(\x08\x12\x19\n\x11imageLogSupported\x18\x15 \x01(\x08\x12\x15\n\rVOIPSupported\x18\x16 \x01(\x08\x12\x14\n\x0cTNASupported\x18\x1e \x01(\x08\x12\x18\n\x10jobCodeSupported\x18\x1f \x01(\x08\x12\x18\n\x10wiegandSupported\x18( \x01(\x08\x12\x1d\n\x15wiegandMultiSupported\x18) \x01(\x08\x12\x1e\n\x16triggerActionSupported\x18* \x01(\x08\x12\x14\n\x0c\x44STSupported\x18+ \x01(\x08\x12\x14\n\x0c\x44NSSupported\x18, \x01(\x08\x12\x18\n\x10OSDPKeySupported\x18\x32 \x01(\x08\x12\x19\n\x11RS485ExtSupported\x18\x33 \x01(\x08\"D\n\x19GetCapabilityInfoResponse\x12\'\n\x07\x63\x61pInfo\x18\x01 \x01(\x0b\x32\x16.device.CapabilityInfo\"\'\n\x13\x44\x65leteRootCARequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x16\n\x14\x44\x65leteRootCAResponse\"\x1f\n\x0bLockRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x0e\n\x0cLockResponse\"%\n\x10LockMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"=\n\x11LockMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse\"!\n\rUnlockRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x10\n\x0eUnlockResponse\"\'\n\x12UnlockMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"?\n\x13UnlockMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse\"!\n\rRebootRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x10\n\x0eRebootResponse\"\'\n\x12RebootMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"?\n\x13RebootMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse\"\'\n\x13\x46\x61\x63toryResetRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x16\n\x14\x46\x61\x63toryResetResponse\"-\n\x18\x46\x61\x63toryResetMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"E\n\x19\x46\x61\x63toryResetMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse\"\"\n\x0e\x43learDBRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x11\n\x0f\x43learDBResponse\"(\n\x13\x43learDBMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"@\n\x14\x43learDBMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse\"K\n\x12ResetConfigRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x13\n\x0bwithNetwork\x18\x02 \x01(\x08\x12\x0e\n\x06withDB\x18\x03 \x01(\x08\"\x15\n\x13ResetConfigResponse\"Q\n\x17ResetConfigMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x13\n\x0bwithNetwork\x18\x02 \x01(\x08\x12\x0e\n\x06withDB\x18\x03 \x01(\x08\"D\n\x18ResetConfigMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse\"@\n\x16UpgradeFirmwareRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x14\n\x0c\x66irmwareData\x18\x02 \x01(\x0c\"\x19\n\x17UpgradeFirmwareResponse\"F\n\x1bUpgradeFirmwareMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x14\n\x0c\x66irmwareData\x18\x02 \x01(\x0c\"H\n\x1cUpgradeFirmwareMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse*\x82\x04\n\x04Type\x12\r\n\tUNDEFINED\x10\x00\x12\x11\n\rBIOENTRY_PLUS\x10\x01\x12\x0e\n\nBIOENTRY_W\x10\x02\x12\x0f\n\x0b\x42IOLITE_NET\x10\x03\x12\t\n\x05XPASS\x10\x04\x12\x0c\n\x08XPASS_S2\x10\x05\x12\r\n\tENTRY_MAX\x10\x05\x12\x0f\n\x0bSECURE_IO_2\x10\x06\x12\x12\n\x0e\x44OOR_MODULE_20\x10\x07\x12\x10\n\x0c\x42IOSTATION_2\x10\x08\x12\x11\n\rBIOSTATION_A2\x10\t\x12\x11\n\rFACESTATION_2\x10\n\x12\r\n\tIO_DEVICE\x10\x0b\x12\x11\n\rBIOSTATION_L2\x10\x0c\x12\x0f\n\x0b\x42IOENTRY_W2\x10\r\x12\x10\n\x0bRS485_SLAVE\x10\x80\x01\x12\x12\n\x0e\x43ORESTATION_40\x10\x0e\x12\x11\n\rOUTPUT_MODULE\x10\x0f\x12\x10\n\x0cINPUT_MODULE\x10\x10\x12\x0f\n\x0b\x42IOENTRY_P2\x10\x11\x12\x0e\n\nBIOLITE_N2\x10\x12\x12\n\n\x06XPASS2\x10\x13\x12\x0c\n\x08XPASS_S3\x10\x14\x12\x0f\n\x0b\x42IOENTRY_R2\x10\x15\x12\x0c\n\x08XPASS_D2\x10\x16\x12\x12\n\x0e\x44OOR_MODULE_21\x10\x17\x12\x13\n\x0fXPASS_D2_KEYPAD\x10\x18\x12\x0c\n\x08\x46\x41\x43\x45LITE\x10\x19\x12\x11\n\rXPASS2_KEYPAD\x10\x1a\x12\x0c\n\x07UNKNOWN\x10\xff\x01\x1a\x02\x10\x01*4\n\nSwitchType\x12\x11\n\rNORMALLY_OPEN\x10\x00\x12\x13\n\x0fNORMALLY_CLOSED\x10\x01*\xaf\x01\n\x08LEDColor\x12\x11\n\rLED_COLOR_OFF\x10\x00\x12\x11\n\rLED_COLOR_RED\x10\x01\x12\x14\n\x10LED_COLOR_YELLOW\x10\x02\x12\x13\n\x0fLED_COLOR_GREEN\x10\x03\x12\x12\n\x0eLED_COLOR_CYAN\x10\x04\x12\x12\n\x0eLED_COLOR_BLUE\x10\x05\x12\x15\n\x11LED_COLOR_MAGENTA\x10\x06\x12\x13\n\x0fLED_COLOR_WHITE\x10\x07*d\n\nBuzzerTone\x12\x13\n\x0f\x42UZZER_TONE_OFF\x10\x00\x12\x13\n\x0f\x42UZZER_TONE_LOW\x10\x01\x12\x16\n\x12\x42UZZER_TONE_MIDDLE\x10\x02\x12\x14\n\x10\x42UZZER_TONE_HIGH\x10\x03\x32\xe2\t\n\x06\x44\x65vice\x12:\n\x07GetInfo\x12\x16.device.GetInfoRequest\x1a\x17.device.GetInfoResponse\x12X\n\x11GetCapabilityInfo\x12 .device.GetCapabilityInfoRequest\x1a!.device.GetCapabilityInfoResponse\x12I\n\x0c\x44\x65leteRootCA\x12\x1b.device.DeleteRootCARequest\x1a\x1c.device.DeleteRootCAResponse\x12\x31\n\x04Lock\x12\x13.device.LockRequest\x1a\x14.device.LockResponse\x12@\n\tLockMulti\x12\x18.device.LockMultiRequest\x1a\x19.device.LockMultiResponse\x12\x37\n\x06Unlock\x12\x15.device.UnlockRequest\x1a\x16.device.UnlockResponse\x12\x46\n\x0bUnlockMulti\x12\x1a.device.UnlockMultiRequest\x1a\x1b.device.UnlockMultiResponse\x12\x37\n\x06Reboot\x12\x15.device.RebootRequest\x1a\x16.device.RebootResponse\x12\x46\n\x0bRebootMulti\x12\x1a.device.RebootMultiRequest\x1a\x1b.device.RebootMultiResponse\x12I\n\x0c\x46\x61\x63toryReset\x12\x1b.device.FactoryResetRequest\x1a\x1c.device.FactoryResetResponse\x12X\n\x11\x46\x61\x63toryResetMulti\x12 .device.FactoryResetMultiRequest\x1a!.device.FactoryResetMultiResponse\x12:\n\x07\x43learDB\x12\x16.device.ClearDBRequest\x1a\x17.device.ClearDBResponse\x12I\n\x0c\x43learDBMulti\x12\x1b.device.ClearDBMultiRequest\x1a\x1c.device.ClearDBMultiResponse\x12\x46\n\x0bResetConfig\x12\x1a.device.ResetConfigRequest\x1a\x1b.device.ResetConfigResponse\x12U\n\x10ResetConfigMulti\x12\x1f.device.ResetConfigMultiRequest\x1a .device.ResetConfigMultiResponse\x12R\n\x0fUpgradeFirmware\x12\x1e.device.UpgradeFirmwareRequest\x1a\x1f.device.UpgradeFirmwareResponse\x12\x61\n\x14UpgradeFirmwareMulti\x12#.device.UpgradeFirmwareMultiRequest\x1a$.device.UpgradeFirmwareMultiResponseB5\n\x19\x63om.supremainc.sdk.deviceP\x01Z\x16\x62iostar/service/deviceb\x06proto3')
  ,
  dependencies=[err__pb2.DESCRIPTOR,])

_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='device.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNDEFINED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOENTRY_PLUS', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOENTRY_W', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOLITE_NET', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='XPASS', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='XPASS_S2', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENTRY_MAX', index=6, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SECURE_IO_2', index=7, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DOOR_MODULE_20', index=8, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOSTATION_2', index=9, number=8,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOSTATION_A2', index=10, number=9,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FACESTATION_2', index=11, number=10,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IO_DEVICE', index=12, number=11,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOSTATION_L2', index=13, number=12,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOENTRY_W2', index=14, number=13,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RS485_SLAVE', index=15, number=128,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CORESTATION_40', index=16, number=14,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OUTPUT_MODULE', index=17, number=15,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INPUT_MODULE', index=18, number=16,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOENTRY_P2', index=19, number=17,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOLITE_N2', index=20, number=18,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='XPASS2', index=21, number=19,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='XPASS_S3', index=22, number=20,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BIOENTRY_R2', index=23, number=21,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='XPASS_D2', index=24, number=22,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DOOR_MODULE_21', index=25, number=23,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='XPASS_D2_KEYPAD', index=26, number=24,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FACELITE', index=27, number=25,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='XPASS2_KEYPAD', index=28, number=26,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=29, number=255,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=_b('\020\001'),
  serialized_start=2393,
  serialized_end=2907,
)
_sym_db.RegisterEnumDescriptor(_TYPE)

Type = enum_type_wrapper.EnumTypeWrapper(_TYPE)
_SWITCHTYPE = _descriptor.EnumDescriptor(
  name='SwitchType',
  full_name='device.SwitchType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NORMALLY_OPEN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NORMALLY_CLOSED', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2909,
  serialized_end=2961,
)
_sym_db.RegisterEnumDescriptor(_SWITCHTYPE)

SwitchType = enum_type_wrapper.EnumTypeWrapper(_SWITCHTYPE)
_LEDCOLOR = _descriptor.EnumDescriptor(
  name='LEDColor',
  full_name='device.LEDColor',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LED_COLOR_OFF', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LED_COLOR_RED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LED_COLOR_YELLOW', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LED_COLOR_GREEN', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LED_COLOR_CYAN', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LED_COLOR_BLUE', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LED_COLOR_MAGENTA', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LED_COLOR_WHITE', index=7, number=7,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2964,
  serialized_end=3139,
)
_sym_db.RegisterEnumDescriptor(_LEDCOLOR)

LEDColor = enum_type_wrapper.EnumTypeWrapper(_LEDCOLOR)
_BUZZERTONE = _descriptor.EnumDescriptor(
  name='BuzzerTone',
  full_name='device.BuzzerTone',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BUZZER_TONE_OFF', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BUZZER_TONE_LOW', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BUZZER_TONE_MIDDLE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BUZZER_TONE_HIGH', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3141,
  serialized_end=3241,
)
_sym_db.RegisterEnumDescriptor(_BUZZERTONE)

BuzzerTone = enum_type_wrapper.EnumTypeWrapper(_BUZZERTONE)
UNDEFINED = 0
BIOENTRY_PLUS = 1
BIOENTRY_W = 2
BIOLITE_NET = 3
XPASS = 4
XPASS_S2 = 5
ENTRY_MAX = 5
SECURE_IO_2 = 6
DOOR_MODULE_20 = 7
BIOSTATION_2 = 8
BIOSTATION_A2 = 9
FACESTATION_2 = 10
IO_DEVICE = 11
BIOSTATION_L2 = 12
BIOENTRY_W2 = 13
RS485_SLAVE = 128
CORESTATION_40 = 14
OUTPUT_MODULE = 15
INPUT_MODULE = 16
BIOENTRY_P2 = 17
BIOLITE_N2 = 18
XPASS2 = 19
XPASS_S3 = 20
BIOENTRY_R2 = 21
XPASS_D2 = 22
DOOR_MODULE_21 = 23
XPASS_D2_KEYPAD = 24
FACELITE = 25
XPASS2_KEYPAD = 26
UNKNOWN = 255
NORMALLY_OPEN = 0
NORMALLY_CLOSED = 1
LED_COLOR_OFF = 0
LED_COLOR_RED = 1
LED_COLOR_YELLOW = 2
LED_COLOR_GREEN = 3
LED_COLOR_CYAN = 4
LED_COLOR_BLUE = 5
LED_COLOR_MAGENTA = 6
LED_COLOR_WHITE = 7
BUZZER_TONE_OFF = 0
BUZZER_TONE_LOW = 1
BUZZER_TONE_MIDDLE = 2
BUZZER_TONE_HIGH = 3



_GETINFOREQUEST = _descriptor.Descriptor(
  name='GetInfoRequest',
  full_name='device.GetInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.GetInfoRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=35,
  serialized_end=69,
)


_FACTORYINFO = _descriptor.Descriptor(
  name='FactoryInfo',
  full_name='device.FactoryInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='MACAddr', full_name='device.FactoryInfo.MACAddr', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='modelName', full_name='device.FactoryInfo.modelName', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='firmwareVersion', full_name='device.FactoryInfo.firmwareVersion', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kernelVersion', full_name='device.FactoryInfo.kernelVersion', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='BSCoreVersion', full_name='device.FactoryInfo.BSCoreVersion', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='boardVersion', full_name='device.FactoryInfo.boardVersion', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=72,
  serialized_end=214,
)


_GETINFORESPONSE = _descriptor.Descriptor(
  name='GetInfoResponse',
  full_name='device.GetInfoResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='info', full_name='device.GetInfoResponse.info', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=216,
  serialized_end=268,
)


_GETCAPABILITYINFOREQUEST = _descriptor.Descriptor(
  name='GetCapabilityInfoRequest',
  full_name='device.GetCapabilityInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.GetCapabilityInfoRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=270,
  serialized_end=314,
)


_CAPABILITYINFO = _descriptor.Descriptor(
  name='CapabilityInfo',
  full_name='device.CapabilityInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='device.CapabilityInfo.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maxNumOfUser', full_name='device.CapabilityInfo.maxNumOfUser', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='PINSupported', full_name='device.CapabilityInfo.PINSupported', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cardSupported', full_name='device.CapabilityInfo.cardSupported', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='card1xSupported', full_name='device.CapabilityInfo.card1xSupported', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='SEOSSupported', full_name='device.CapabilityInfo.SEOSSupported', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fingerSupported', full_name='device.CapabilityInfo.fingerSupported', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='faceSupported', full_name='device.CapabilityInfo.faceSupported', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='userNameSupported', full_name='device.CapabilityInfo.userNameSupported', index=8,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='userPhotoSupported', full_name='device.CapabilityInfo.userPhotoSupported', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='userPhraseSupported', full_name='device.CapabilityInfo.userPhraseSupported', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alphanumericIDSupported', full_name='device.CapabilityInfo.alphanumericIDSupported', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='WLANSupported', full_name='device.CapabilityInfo.WLANSupported', index=12,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageLogSupported', full_name='device.CapabilityInfo.imageLogSupported', index=13,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='VOIPSupported', full_name='device.CapabilityInfo.VOIPSupported', index=14,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='TNASupported', full_name='device.CapabilityInfo.TNASupported', index=15,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jobCodeSupported', full_name='device.CapabilityInfo.jobCodeSupported', index=16,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wiegandSupported', full_name='device.CapabilityInfo.wiegandSupported', index=17,
      number=40, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wiegandMultiSupported', full_name='device.CapabilityInfo.wiegandMultiSupported', index=18,
      number=41, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='triggerActionSupported', full_name='device.CapabilityInfo.triggerActionSupported', index=19,
      number=42, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='DSTSupported', full_name='device.CapabilityInfo.DSTSupported', index=20,
      number=43, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='DNSSupported', full_name='device.CapabilityInfo.DNSSupported', index=21,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='OSDPKeySupported', full_name='device.CapabilityInfo.OSDPKeySupported', index=22,
      number=50, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='RS485ExtSupported', full_name='device.CapabilityInfo.RS485ExtSupported', index=23,
      number=51, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=317,
  serialized_end=948,
)


_GETCAPABILITYINFORESPONSE = _descriptor.Descriptor(
  name='GetCapabilityInfoResponse',
  full_name='device.GetCapabilityInfoResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='capInfo', full_name='device.GetCapabilityInfoResponse.capInfo', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=950,
  serialized_end=1018,
)


_DELETEROOTCAREQUEST = _descriptor.Descriptor(
  name='DeleteRootCARequest',
  full_name='device.DeleteRootCARequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.DeleteRootCARequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1020,
  serialized_end=1059,
)


_DELETEROOTCARESPONSE = _descriptor.Descriptor(
  name='DeleteRootCAResponse',
  full_name='device.DeleteRootCAResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1061,
  serialized_end=1083,
)


_LOCKREQUEST = _descriptor.Descriptor(
  name='LockRequest',
  full_name='device.LockRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.LockRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1085,
  serialized_end=1116,
)


_LOCKRESPONSE = _descriptor.Descriptor(
  name='LockResponse',
  full_name='device.LockResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1118,
  serialized_end=1132,
)


_LOCKMULTIREQUEST = _descriptor.Descriptor(
  name='LockMultiRequest',
  full_name='device.LockMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='device.LockMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1134,
  serialized_end=1171,
)


_LOCKMULTIRESPONSE = _descriptor.Descriptor(
  name='LockMultiResponse',
  full_name='device.LockMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='device.LockMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1173,
  serialized_end=1234,
)


_UNLOCKREQUEST = _descriptor.Descriptor(
  name='UnlockRequest',
  full_name='device.UnlockRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.UnlockRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1236,
  serialized_end=1269,
)


_UNLOCKRESPONSE = _descriptor.Descriptor(
  name='UnlockResponse',
  full_name='device.UnlockResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1271,
  serialized_end=1287,
)


_UNLOCKMULTIREQUEST = _descriptor.Descriptor(
  name='UnlockMultiRequest',
  full_name='device.UnlockMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='device.UnlockMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1289,
  serialized_end=1328,
)


_UNLOCKMULTIRESPONSE = _descriptor.Descriptor(
  name='UnlockMultiResponse',
  full_name='device.UnlockMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='device.UnlockMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1330,
  serialized_end=1393,
)


_REBOOTREQUEST = _descriptor.Descriptor(
  name='RebootRequest',
  full_name='device.RebootRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.RebootRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1395,
  serialized_end=1428,
)


_REBOOTRESPONSE = _descriptor.Descriptor(
  name='RebootResponse',
  full_name='device.RebootResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1430,
  serialized_end=1446,
)


_REBOOTMULTIREQUEST = _descriptor.Descriptor(
  name='RebootMultiRequest',
  full_name='device.RebootMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='device.RebootMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1448,
  serialized_end=1487,
)


_REBOOTMULTIRESPONSE = _descriptor.Descriptor(
  name='RebootMultiResponse',
  full_name='device.RebootMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='device.RebootMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1489,
  serialized_end=1552,
)


_FACTORYRESETREQUEST = _descriptor.Descriptor(
  name='FactoryResetRequest',
  full_name='device.FactoryResetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.FactoryResetRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1554,
  serialized_end=1593,
)


_FACTORYRESETRESPONSE = _descriptor.Descriptor(
  name='FactoryResetResponse',
  full_name='device.FactoryResetResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1595,
  serialized_end=1617,
)


_FACTORYRESETMULTIREQUEST = _descriptor.Descriptor(
  name='FactoryResetMultiRequest',
  full_name='device.FactoryResetMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='device.FactoryResetMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1619,
  serialized_end=1664,
)


_FACTORYRESETMULTIRESPONSE = _descriptor.Descriptor(
  name='FactoryResetMultiResponse',
  full_name='device.FactoryResetMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='device.FactoryResetMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1666,
  serialized_end=1735,
)


_CLEARDBREQUEST = _descriptor.Descriptor(
  name='ClearDBRequest',
  full_name='device.ClearDBRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.ClearDBRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1737,
  serialized_end=1771,
)


_CLEARDBRESPONSE = _descriptor.Descriptor(
  name='ClearDBResponse',
  full_name='device.ClearDBResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1773,
  serialized_end=1790,
)


_CLEARDBMULTIREQUEST = _descriptor.Descriptor(
  name='ClearDBMultiRequest',
  full_name='device.ClearDBMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='device.ClearDBMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1792,
  serialized_end=1832,
)


_CLEARDBMULTIRESPONSE = _descriptor.Descriptor(
  name='ClearDBMultiResponse',
  full_name='device.ClearDBMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='device.ClearDBMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1834,
  serialized_end=1898,
)


_RESETCONFIGREQUEST = _descriptor.Descriptor(
  name='ResetConfigRequest',
  full_name='device.ResetConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.ResetConfigRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='withNetwork', full_name='device.ResetConfigRequest.withNetwork', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='withDB', full_name='device.ResetConfigRequest.withDB', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1900,
  serialized_end=1975,
)


_RESETCONFIGRESPONSE = _descriptor.Descriptor(
  name='ResetConfigResponse',
  full_name='device.ResetConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1977,
  serialized_end=1998,
)


_RESETCONFIGMULTIREQUEST = _descriptor.Descriptor(
  name='ResetConfigMultiRequest',
  full_name='device.ResetConfigMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='device.ResetConfigMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='withNetwork', full_name='device.ResetConfigMultiRequest.withNetwork', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='withDB', full_name='device.ResetConfigMultiRequest.withDB', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2000,
  serialized_end=2081,
)


_RESETCONFIGMULTIRESPONSE = _descriptor.Descriptor(
  name='ResetConfigMultiResponse',
  full_name='device.ResetConfigMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='device.ResetConfigMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2083,
  serialized_end=2151,
)


_UPGRADEFIRMWAREREQUEST = _descriptor.Descriptor(
  name='UpgradeFirmwareRequest',
  full_name='device.UpgradeFirmwareRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='device.UpgradeFirmwareRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='firmwareData', full_name='device.UpgradeFirmwareRequest.firmwareData', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2153,
  serialized_end=2217,
)


_UPGRADEFIRMWARERESPONSE = _descriptor.Descriptor(
  name='UpgradeFirmwareResponse',
  full_name='device.UpgradeFirmwareResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2219,
  serialized_end=2244,
)


_UPGRADEFIRMWAREMULTIREQUEST = _descriptor.Descriptor(
  name='UpgradeFirmwareMultiRequest',
  full_name='device.UpgradeFirmwareMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='device.UpgradeFirmwareMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='firmwareData', full_name='device.UpgradeFirmwareMultiRequest.firmwareData', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2246,
  serialized_end=2316,
)


_UPGRADEFIRMWAREMULTIRESPONSE = _descriptor.Descriptor(
  name='UpgradeFirmwareMultiResponse',
  full_name='device.UpgradeFirmwareMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='device.UpgradeFirmwareMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2318,
  serialized_end=2390,
)

_GETINFORESPONSE.fields_by_name['info'].message_type = _FACTORYINFO
_CAPABILITYINFO.fields_by_name['type'].enum_type = _TYPE
_GETCAPABILITYINFORESPONSE.fields_by_name['capInfo'].message_type = _CAPABILITYINFO
_LOCKMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
_UNLOCKMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
_REBOOTMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
_FACTORYRESETMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
_CLEARDBMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
_RESETCONFIGMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
_UPGRADEFIRMWAREMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
DESCRIPTOR.message_types_by_name['GetInfoRequest'] = _GETINFOREQUEST
DESCRIPTOR.message_types_by_name['FactoryInfo'] = _FACTORYINFO
DESCRIPTOR.message_types_by_name['GetInfoResponse'] = _GETINFORESPONSE
DESCRIPTOR.message_types_by_name['GetCapabilityInfoRequest'] = _GETCAPABILITYINFOREQUEST
DESCRIPTOR.message_types_by_name['CapabilityInfo'] = _CAPABILITYINFO
DESCRIPTOR.message_types_by_name['GetCapabilityInfoResponse'] = _GETCAPABILITYINFORESPONSE
DESCRIPTOR.message_types_by_name['DeleteRootCARequest'] = _DELETEROOTCAREQUEST
DESCRIPTOR.message_types_by_name['DeleteRootCAResponse'] = _DELETEROOTCARESPONSE
DESCRIPTOR.message_types_by_name['LockRequest'] = _LOCKREQUEST
DESCRIPTOR.message_types_by_name['LockResponse'] = _LOCKRESPONSE
DESCRIPTOR.message_types_by_name['LockMultiRequest'] = _LOCKMULTIREQUEST
DESCRIPTOR.message_types_by_name['LockMultiResponse'] = _LOCKMULTIRESPONSE
DESCRIPTOR.message_types_by_name['UnlockRequest'] = _UNLOCKREQUEST
DESCRIPTOR.message_types_by_name['UnlockResponse'] = _UNLOCKRESPONSE
DESCRIPTOR.message_types_by_name['UnlockMultiRequest'] = _UNLOCKMULTIREQUEST
DESCRIPTOR.message_types_by_name['UnlockMultiResponse'] = _UNLOCKMULTIRESPONSE
DESCRIPTOR.message_types_by_name['RebootRequest'] = _REBOOTREQUEST
DESCRIPTOR.message_types_by_name['RebootResponse'] = _REBOOTRESPONSE
DESCRIPTOR.message_types_by_name['RebootMultiRequest'] = _REBOOTMULTIREQUEST
DESCRIPTOR.message_types_by_name['RebootMultiResponse'] = _REBOOTMULTIRESPONSE
DESCRIPTOR.message_types_by_name['FactoryResetRequest'] = _FACTORYRESETREQUEST
DESCRIPTOR.message_types_by_name['FactoryResetResponse'] = _FACTORYRESETRESPONSE
DESCRIPTOR.message_types_by_name['FactoryResetMultiRequest'] = _FACTORYRESETMULTIREQUEST
DESCRIPTOR.message_types_by_name['FactoryResetMultiResponse'] = _FACTORYRESETMULTIRESPONSE
DESCRIPTOR.message_types_by_name['ClearDBRequest'] = _CLEARDBREQUEST
DESCRIPTOR.message_types_by_name['ClearDBResponse'] = _CLEARDBRESPONSE
DESCRIPTOR.message_types_by_name['ClearDBMultiRequest'] = _CLEARDBMULTIREQUEST
DESCRIPTOR.message_types_by_name['ClearDBMultiResponse'] = _CLEARDBMULTIRESPONSE
DESCRIPTOR.message_types_by_name['ResetConfigRequest'] = _RESETCONFIGREQUEST
DESCRIPTOR.message_types_by_name['ResetConfigResponse'] = _RESETCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['ResetConfigMultiRequest'] = _RESETCONFIGMULTIREQUEST
DESCRIPTOR.message_types_by_name['ResetConfigMultiResponse'] = _RESETCONFIGMULTIRESPONSE
DESCRIPTOR.message_types_by_name['UpgradeFirmwareRequest'] = _UPGRADEFIRMWAREREQUEST
DESCRIPTOR.message_types_by_name['UpgradeFirmwareResponse'] = _UPGRADEFIRMWARERESPONSE
DESCRIPTOR.message_types_by_name['UpgradeFirmwareMultiRequest'] = _UPGRADEFIRMWAREMULTIREQUEST
DESCRIPTOR.message_types_by_name['UpgradeFirmwareMultiResponse'] = _UPGRADEFIRMWAREMULTIRESPONSE
DESCRIPTOR.enum_types_by_name['Type'] = _TYPE
DESCRIPTOR.enum_types_by_name['SwitchType'] = _SWITCHTYPE
DESCRIPTOR.enum_types_by_name['LEDColor'] = _LEDCOLOR
DESCRIPTOR.enum_types_by_name['BuzzerTone'] = _BUZZERTONE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetInfoRequest = _reflection.GeneratedProtocolMessageType('GetInfoRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETINFOREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.GetInfoRequest)
  ))
_sym_db.RegisterMessage(GetInfoRequest)

FactoryInfo = _reflection.GeneratedProtocolMessageType('FactoryInfo', (_message.Message,), dict(
  DESCRIPTOR = _FACTORYINFO,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.FactoryInfo)
  ))
_sym_db.RegisterMessage(FactoryInfo)

GetInfoResponse = _reflection.GeneratedProtocolMessageType('GetInfoResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETINFORESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.GetInfoResponse)
  ))
_sym_db.RegisterMessage(GetInfoResponse)

GetCapabilityInfoRequest = _reflection.GeneratedProtocolMessageType('GetCapabilityInfoRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETCAPABILITYINFOREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.GetCapabilityInfoRequest)
  ))
_sym_db.RegisterMessage(GetCapabilityInfoRequest)

CapabilityInfo = _reflection.GeneratedProtocolMessageType('CapabilityInfo', (_message.Message,), dict(
  DESCRIPTOR = _CAPABILITYINFO,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.CapabilityInfo)
  ))
_sym_db.RegisterMessage(CapabilityInfo)

GetCapabilityInfoResponse = _reflection.GeneratedProtocolMessageType('GetCapabilityInfoResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETCAPABILITYINFORESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.GetCapabilityInfoResponse)
  ))
_sym_db.RegisterMessage(GetCapabilityInfoResponse)

DeleteRootCARequest = _reflection.GeneratedProtocolMessageType('DeleteRootCARequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEROOTCAREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.DeleteRootCARequest)
  ))
_sym_db.RegisterMessage(DeleteRootCARequest)

DeleteRootCAResponse = _reflection.GeneratedProtocolMessageType('DeleteRootCAResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEROOTCARESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.DeleteRootCAResponse)
  ))
_sym_db.RegisterMessage(DeleteRootCAResponse)

LockRequest = _reflection.GeneratedProtocolMessageType('LockRequest', (_message.Message,), dict(
  DESCRIPTOR = _LOCKREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.LockRequest)
  ))
_sym_db.RegisterMessage(LockRequest)

LockResponse = _reflection.GeneratedProtocolMessageType('LockResponse', (_message.Message,), dict(
  DESCRIPTOR = _LOCKRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.LockResponse)
  ))
_sym_db.RegisterMessage(LockResponse)

LockMultiRequest = _reflection.GeneratedProtocolMessageType('LockMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _LOCKMULTIREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.LockMultiRequest)
  ))
_sym_db.RegisterMessage(LockMultiRequest)

LockMultiResponse = _reflection.GeneratedProtocolMessageType('LockMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _LOCKMULTIRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.LockMultiResponse)
  ))
_sym_db.RegisterMessage(LockMultiResponse)

UnlockRequest = _reflection.GeneratedProtocolMessageType('UnlockRequest', (_message.Message,), dict(
  DESCRIPTOR = _UNLOCKREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.UnlockRequest)
  ))
_sym_db.RegisterMessage(UnlockRequest)

UnlockResponse = _reflection.GeneratedProtocolMessageType('UnlockResponse', (_message.Message,), dict(
  DESCRIPTOR = _UNLOCKRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.UnlockResponse)
  ))
_sym_db.RegisterMessage(UnlockResponse)

UnlockMultiRequest = _reflection.GeneratedProtocolMessageType('UnlockMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _UNLOCKMULTIREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.UnlockMultiRequest)
  ))
_sym_db.RegisterMessage(UnlockMultiRequest)

UnlockMultiResponse = _reflection.GeneratedProtocolMessageType('UnlockMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _UNLOCKMULTIRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.UnlockMultiResponse)
  ))
_sym_db.RegisterMessage(UnlockMultiResponse)

RebootRequest = _reflection.GeneratedProtocolMessageType('RebootRequest', (_message.Message,), dict(
  DESCRIPTOR = _REBOOTREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.RebootRequest)
  ))
_sym_db.RegisterMessage(RebootRequest)

RebootResponse = _reflection.GeneratedProtocolMessageType('RebootResponse', (_message.Message,), dict(
  DESCRIPTOR = _REBOOTRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.RebootResponse)
  ))
_sym_db.RegisterMessage(RebootResponse)

RebootMultiRequest = _reflection.GeneratedProtocolMessageType('RebootMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _REBOOTMULTIREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.RebootMultiRequest)
  ))
_sym_db.RegisterMessage(RebootMultiRequest)

RebootMultiResponse = _reflection.GeneratedProtocolMessageType('RebootMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _REBOOTMULTIRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.RebootMultiResponse)
  ))
_sym_db.RegisterMessage(RebootMultiResponse)

FactoryResetRequest = _reflection.GeneratedProtocolMessageType('FactoryResetRequest', (_message.Message,), dict(
  DESCRIPTOR = _FACTORYRESETREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.FactoryResetRequest)
  ))
_sym_db.RegisterMessage(FactoryResetRequest)

FactoryResetResponse = _reflection.GeneratedProtocolMessageType('FactoryResetResponse', (_message.Message,), dict(
  DESCRIPTOR = _FACTORYRESETRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.FactoryResetResponse)
  ))
_sym_db.RegisterMessage(FactoryResetResponse)

FactoryResetMultiRequest = _reflection.GeneratedProtocolMessageType('FactoryResetMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _FACTORYRESETMULTIREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.FactoryResetMultiRequest)
  ))
_sym_db.RegisterMessage(FactoryResetMultiRequest)

FactoryResetMultiResponse = _reflection.GeneratedProtocolMessageType('FactoryResetMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _FACTORYRESETMULTIRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.FactoryResetMultiResponse)
  ))
_sym_db.RegisterMessage(FactoryResetMultiResponse)

ClearDBRequest = _reflection.GeneratedProtocolMessageType('ClearDBRequest', (_message.Message,), dict(
  DESCRIPTOR = _CLEARDBREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.ClearDBRequest)
  ))
_sym_db.RegisterMessage(ClearDBRequest)

ClearDBResponse = _reflection.GeneratedProtocolMessageType('ClearDBResponse', (_message.Message,), dict(
  DESCRIPTOR = _CLEARDBRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.ClearDBResponse)
  ))
_sym_db.RegisterMessage(ClearDBResponse)

ClearDBMultiRequest = _reflection.GeneratedProtocolMessageType('ClearDBMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _CLEARDBMULTIREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.ClearDBMultiRequest)
  ))
_sym_db.RegisterMessage(ClearDBMultiRequest)

ClearDBMultiResponse = _reflection.GeneratedProtocolMessageType('ClearDBMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _CLEARDBMULTIRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.ClearDBMultiResponse)
  ))
_sym_db.RegisterMessage(ClearDBMultiResponse)

ResetConfigRequest = _reflection.GeneratedProtocolMessageType('ResetConfigRequest', (_message.Message,), dict(
  DESCRIPTOR = _RESETCONFIGREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.ResetConfigRequest)
  ))
_sym_db.RegisterMessage(ResetConfigRequest)

ResetConfigResponse = _reflection.GeneratedProtocolMessageType('ResetConfigResponse', (_message.Message,), dict(
  DESCRIPTOR = _RESETCONFIGRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.ResetConfigResponse)
  ))
_sym_db.RegisterMessage(ResetConfigResponse)

ResetConfigMultiRequest = _reflection.GeneratedProtocolMessageType('ResetConfigMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _RESETCONFIGMULTIREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.ResetConfigMultiRequest)
  ))
_sym_db.RegisterMessage(ResetConfigMultiRequest)

ResetConfigMultiResponse = _reflection.GeneratedProtocolMessageType('ResetConfigMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _RESETCONFIGMULTIRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.ResetConfigMultiResponse)
  ))
_sym_db.RegisterMessage(ResetConfigMultiResponse)

UpgradeFirmwareRequest = _reflection.GeneratedProtocolMessageType('UpgradeFirmwareRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPGRADEFIRMWAREREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.UpgradeFirmwareRequest)
  ))
_sym_db.RegisterMessage(UpgradeFirmwareRequest)

UpgradeFirmwareResponse = _reflection.GeneratedProtocolMessageType('UpgradeFirmwareResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPGRADEFIRMWARERESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.UpgradeFirmwareResponse)
  ))
_sym_db.RegisterMessage(UpgradeFirmwareResponse)

UpgradeFirmwareMultiRequest = _reflection.GeneratedProtocolMessageType('UpgradeFirmwareMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPGRADEFIRMWAREMULTIREQUEST,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.UpgradeFirmwareMultiRequest)
  ))
_sym_db.RegisterMessage(UpgradeFirmwareMultiRequest)

UpgradeFirmwareMultiResponse = _reflection.GeneratedProtocolMessageType('UpgradeFirmwareMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPGRADEFIRMWAREMULTIRESPONSE,
  __module__ = 'device_pb2'
  # @@protoc_insertion_point(class_scope:device.UpgradeFirmwareMultiResponse)
  ))
_sym_db.RegisterMessage(UpgradeFirmwareMultiResponse)


DESCRIPTOR._options = None
_TYPE._options = None

_DEVICE = _descriptor.ServiceDescriptor(
  name='Device',
  full_name='device.Device',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3244,
  serialized_end=4494,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetInfo',
    full_name='device.Device.GetInfo',
    index=0,
    containing_service=None,
    input_type=_GETINFOREQUEST,
    output_type=_GETINFORESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetCapabilityInfo',
    full_name='device.Device.GetCapabilityInfo',
    index=1,
    containing_service=None,
    input_type=_GETCAPABILITYINFOREQUEST,
    output_type=_GETCAPABILITYINFORESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteRootCA',
    full_name='device.Device.DeleteRootCA',
    index=2,
    containing_service=None,
    input_type=_DELETEROOTCAREQUEST,
    output_type=_DELETEROOTCARESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Lock',
    full_name='device.Device.Lock',
    index=3,
    containing_service=None,
    input_type=_LOCKREQUEST,
    output_type=_LOCKRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='LockMulti',
    full_name='device.Device.LockMulti',
    index=4,
    containing_service=None,
    input_type=_LOCKMULTIREQUEST,
    output_type=_LOCKMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Unlock',
    full_name='device.Device.Unlock',
    index=5,
    containing_service=None,
    input_type=_UNLOCKREQUEST,
    output_type=_UNLOCKRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UnlockMulti',
    full_name='device.Device.UnlockMulti',
    index=6,
    containing_service=None,
    input_type=_UNLOCKMULTIREQUEST,
    output_type=_UNLOCKMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Reboot',
    full_name='device.Device.Reboot',
    index=7,
    containing_service=None,
    input_type=_REBOOTREQUEST,
    output_type=_REBOOTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RebootMulti',
    full_name='device.Device.RebootMulti',
    index=8,
    containing_service=None,
    input_type=_REBOOTMULTIREQUEST,
    output_type=_REBOOTMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='FactoryReset',
    full_name='device.Device.FactoryReset',
    index=9,
    containing_service=None,
    input_type=_FACTORYRESETREQUEST,
    output_type=_FACTORYRESETRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='FactoryResetMulti',
    full_name='device.Device.FactoryResetMulti',
    index=10,
    containing_service=None,
    input_type=_FACTORYRESETMULTIREQUEST,
    output_type=_FACTORYRESETMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ClearDB',
    full_name='device.Device.ClearDB',
    index=11,
    containing_service=None,
    input_type=_CLEARDBREQUEST,
    output_type=_CLEARDBRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ClearDBMulti',
    full_name='device.Device.ClearDBMulti',
    index=12,
    containing_service=None,
    input_type=_CLEARDBMULTIREQUEST,
    output_type=_CLEARDBMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ResetConfig',
    full_name='device.Device.ResetConfig',
    index=13,
    containing_service=None,
    input_type=_RESETCONFIGREQUEST,
    output_type=_RESETCONFIGRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ResetConfigMulti',
    full_name='device.Device.ResetConfigMulti',
    index=14,
    containing_service=None,
    input_type=_RESETCONFIGMULTIREQUEST,
    output_type=_RESETCONFIGMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UpgradeFirmware',
    full_name='device.Device.UpgradeFirmware',
    index=15,
    containing_service=None,
    input_type=_UPGRADEFIRMWAREREQUEST,
    output_type=_UPGRADEFIRMWARERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UpgradeFirmwareMulti',
    full_name='device.Device.UpgradeFirmwareMulti',
    index=16,
    containing_service=None,
    input_type=_UPGRADEFIRMWAREMULTIREQUEST,
    output_type=_UPGRADEFIRMWAREMULTIRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_DEVICE)

DESCRIPTOR.services_by_name['Device'] = _DEVICE

# @@protoc_insertion_point(module_scope)
