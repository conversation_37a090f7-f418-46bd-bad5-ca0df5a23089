# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import connect_pb2 as connect__pb2


class ConnectStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Connect = channel.unary_unary(
        '/connect.Connect/Connect',
        request_serializer=connect__pb2.ConnectRequest.SerializeToString,
        response_deserializer=connect__pb2.ConnectResponse.FromString,
        )
    self.AddAsyncConnection = channel.unary_unary(
        '/connect.Connect/AddAsyncConnection',
        request_serializer=connect__pb2.AddAsyncConnectionRequest.SerializeToString,
        response_deserializer=connect__pb2.AddAsyncConnectionResponse.FromString,
        )
    self.DeleteAsyncConnection = channel.unary_unary(
        '/connect.Connect/DeleteAsyncConnection',
        request_serializer=connect__pb2.DeleteAsyncConnectionRequest.SerializeToString,
        response_deserializer=connect__pb2.DeleteAsyncConnectionResponse.FromString,
        )
    self.SetAcceptFilter = channel.unary_unary(
        '/connect.Connect/SetAcceptFilter',
        request_serializer=connect__pb2.SetAcceptFilterRequest.SerializeToString,
        response_deserializer=connect__pb2.SetAcceptFilterResponse.FromString,
        )
    self.GetAcceptFilter = channel.unary_unary(
        '/connect.Connect/GetAcceptFilter',
        request_serializer=connect__pb2.GetAcceptFilterRequest.SerializeToString,
        response_deserializer=connect__pb2.GetAcceptFilterResponse.FromString,
        )
    self.GetPendingList = channel.unary_unary(
        '/connect.Connect/GetPendingList',
        request_serializer=connect__pb2.GetPendingListRequest.SerializeToString,
        response_deserializer=connect__pb2.GetPendingListResponse.FromString,
        )
    self.GetDeviceList = channel.unary_unary(
        '/connect.Connect/GetDeviceList',
        request_serializer=connect__pb2.GetDeviceListRequest.SerializeToString,
        response_deserializer=connect__pb2.GetDeviceListResponse.FromString,
        )
    self.Disconnect = channel.unary_unary(
        '/connect.Connect/Disconnect',
        request_serializer=connect__pb2.DisconnectRequest.SerializeToString,
        response_deserializer=connect__pb2.DisconnectResponse.FromString,
        )
    self.DisconnectAll = channel.unary_unary(
        '/connect.Connect/DisconnectAll',
        request_serializer=connect__pb2.DisconnectAllRequest.SerializeToString,
        response_deserializer=connect__pb2.DisconnectAllResponse.FromString,
        )
    self.SearchDevice = channel.unary_unary(
        '/connect.Connect/SearchDevice',
        request_serializer=connect__pb2.SearchDeviceRequest.SerializeToString,
        response_deserializer=connect__pb2.SearchDeviceResponse.FromString,
        )
    self.SetConnectionMode = channel.unary_unary(
        '/connect.Connect/SetConnectionMode',
        request_serializer=connect__pb2.SetConnectionModeRequest.SerializeToString,
        response_deserializer=connect__pb2.SetConnectionModeResponse.FromString,
        )
    self.SetConnectionModeMulti = channel.unary_unary(
        '/connect.Connect/SetConnectionModeMulti',
        request_serializer=connect__pb2.SetConnectionModeMultiRequest.SerializeToString,
        response_deserializer=connect__pb2.SetConnectionModeMultiResponse.FromString,
        )
    self.EnableSSL = channel.unary_unary(
        '/connect.Connect/EnableSSL',
        request_serializer=connect__pb2.EnableSSLRequest.SerializeToString,
        response_deserializer=connect__pb2.EnableSSLResponse.FromString,
        )
    self.EnableSSLMulti = channel.unary_unary(
        '/connect.Connect/EnableSSLMulti',
        request_serializer=connect__pb2.EnableSSLMultiRequest.SerializeToString,
        response_deserializer=connect__pb2.EnableSSLMultiResponse.FromString,
        )
    self.DisableSSL = channel.unary_unary(
        '/connect.Connect/DisableSSL',
        request_serializer=connect__pb2.DisableSSLRequest.SerializeToString,
        response_deserializer=connect__pb2.DisableSSLResponse.FromString,
        )
    self.DisableSSLMulti = channel.unary_unary(
        '/connect.Connect/DisableSSLMulti',
        request_serializer=connect__pb2.DisableSSLMultiRequest.SerializeToString,
        response_deserializer=connect__pb2.DisableSSLMultiResponse.FromString,
        )
    self.SubscribeStatus = channel.unary_stream(
        '/connect.Connect/SubscribeStatus',
        request_serializer=connect__pb2.SubscribeStatusRequest.SerializeToString,
        response_deserializer=connect__pb2.StatusChange.FromString,
        )


class ConnectServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Connect(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddAsyncConnection(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteAsyncConnection(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SetAcceptFilter(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAcceptFilter(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPendingList(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDeviceList(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Disconnect(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DisconnectAll(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SearchDevice(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SetConnectionMode(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SetConnectionModeMulti(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def EnableSSL(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def EnableSSLMulti(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DisableSSL(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DisableSSLMulti(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubscribeStatus(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ConnectServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Connect': grpc.unary_unary_rpc_method_handler(
          servicer.Connect,
          request_deserializer=connect__pb2.ConnectRequest.FromString,
          response_serializer=connect__pb2.ConnectResponse.SerializeToString,
      ),
      'AddAsyncConnection': grpc.unary_unary_rpc_method_handler(
          servicer.AddAsyncConnection,
          request_deserializer=connect__pb2.AddAsyncConnectionRequest.FromString,
          response_serializer=connect__pb2.AddAsyncConnectionResponse.SerializeToString,
      ),
      'DeleteAsyncConnection': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteAsyncConnection,
          request_deserializer=connect__pb2.DeleteAsyncConnectionRequest.FromString,
          response_serializer=connect__pb2.DeleteAsyncConnectionResponse.SerializeToString,
      ),
      'SetAcceptFilter': grpc.unary_unary_rpc_method_handler(
          servicer.SetAcceptFilter,
          request_deserializer=connect__pb2.SetAcceptFilterRequest.FromString,
          response_serializer=connect__pb2.SetAcceptFilterResponse.SerializeToString,
      ),
      'GetAcceptFilter': grpc.unary_unary_rpc_method_handler(
          servicer.GetAcceptFilter,
          request_deserializer=connect__pb2.GetAcceptFilterRequest.FromString,
          response_serializer=connect__pb2.GetAcceptFilterResponse.SerializeToString,
      ),
      'GetPendingList': grpc.unary_unary_rpc_method_handler(
          servicer.GetPendingList,
          request_deserializer=connect__pb2.GetPendingListRequest.FromString,
          response_serializer=connect__pb2.GetPendingListResponse.SerializeToString,
      ),
      'GetDeviceList': grpc.unary_unary_rpc_method_handler(
          servicer.GetDeviceList,
          request_deserializer=connect__pb2.GetDeviceListRequest.FromString,
          response_serializer=connect__pb2.GetDeviceListResponse.SerializeToString,
      ),
      'Disconnect': grpc.unary_unary_rpc_method_handler(
          servicer.Disconnect,
          request_deserializer=connect__pb2.DisconnectRequest.FromString,
          response_serializer=connect__pb2.DisconnectResponse.SerializeToString,
      ),
      'DisconnectAll': grpc.unary_unary_rpc_method_handler(
          servicer.DisconnectAll,
          request_deserializer=connect__pb2.DisconnectAllRequest.FromString,
          response_serializer=connect__pb2.DisconnectAllResponse.SerializeToString,
      ),
      'SearchDevice': grpc.unary_unary_rpc_method_handler(
          servicer.SearchDevice,
          request_deserializer=connect__pb2.SearchDeviceRequest.FromString,
          response_serializer=connect__pb2.SearchDeviceResponse.SerializeToString,
      ),
      'SetConnectionMode': grpc.unary_unary_rpc_method_handler(
          servicer.SetConnectionMode,
          request_deserializer=connect__pb2.SetConnectionModeRequest.FromString,
          response_serializer=connect__pb2.SetConnectionModeResponse.SerializeToString,
      ),
      'SetConnectionModeMulti': grpc.unary_unary_rpc_method_handler(
          servicer.SetConnectionModeMulti,
          request_deserializer=connect__pb2.SetConnectionModeMultiRequest.FromString,
          response_serializer=connect__pb2.SetConnectionModeMultiResponse.SerializeToString,
      ),
      'EnableSSL': grpc.unary_unary_rpc_method_handler(
          servicer.EnableSSL,
          request_deserializer=connect__pb2.EnableSSLRequest.FromString,
          response_serializer=connect__pb2.EnableSSLResponse.SerializeToString,
      ),
      'EnableSSLMulti': grpc.unary_unary_rpc_method_handler(
          servicer.EnableSSLMulti,
          request_deserializer=connect__pb2.EnableSSLMultiRequest.FromString,
          response_serializer=connect__pb2.EnableSSLMultiResponse.SerializeToString,
      ),
      'DisableSSL': grpc.unary_unary_rpc_method_handler(
          servicer.DisableSSL,
          request_deserializer=connect__pb2.DisableSSLRequest.FromString,
          response_serializer=connect__pb2.DisableSSLResponse.SerializeToString,
      ),
      'DisableSSLMulti': grpc.unary_unary_rpc_method_handler(
          servicer.DisableSSLMulti,
          request_deserializer=connect__pb2.DisableSSLMultiRequest.FromString,
          response_serializer=connect__pb2.DisableSSLMultiResponse.SerializeToString,
      ),
      'SubscribeStatus': grpc.unary_stream_rpc_method_handler(
          servicer.SubscribeStatus,
          request_deserializer=connect__pb2.SubscribeStatusRequest.FromString,
          response_serializer=connect__pb2.StatusChange.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'connect.Connect', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
