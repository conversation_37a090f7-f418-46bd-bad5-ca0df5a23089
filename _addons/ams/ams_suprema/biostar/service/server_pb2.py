# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: server.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import card_pb2 as card__pb2
import finger_pb2 as finger__pb2
import user_pb2 as user__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0cserver.proto\x12\x0bgsdk.server\x1a\ncard.proto\x1a\x0c\x66inger.proto\x1a\nuser.proto\"\xa9\x02\n\rServerRequest\x12)\n\x07reqType\x18\x01 \x01(\x0e\x32\x18.gsdk.server.RequestType\x12\x10\n\x08\x64\x65viceID\x18\x02 \x01(\r\x12\r\n\x05seqNO\x18\x03 \x01(\r\x12-\n\tverifyReq\x18\x04 \x01(\x0b\x32\x1a.gsdk.server.VerifyRequest\x12\x31\n\x0bidentifyReq\x18\x05 \x01(\x0b\x32\x1c.gsdk.server.IdentifyRequest\x12\x33\n\x0cglobalAPBReq\x18\x06 \x01(\x0b\x32\x1d.gsdk.server.GlobalAPBRequest\x12\x35\n\ruserPhraseReq\x18\x07 \x01(\x0b\x32\x1e.gsdk.server.UserPhraseRequest\"d\n\rVerifyRequest\x12\x0e\n\x06isCard\x18\x01 \x01(\x08\x12!\n\x08\x63\x61rdType\x18\x02 \x01(\x0e\x32\x0f.gsdk.card.Type\x12\x10\n\x08\x63\x61rdData\x18\x03 \x01(\x0c\x12\x0e\n\x06userID\x18\x04 \x01(\t\"\\\n\x0fIdentifyRequest\x12\x33\n\x0etemplateFormat\x18\x01 \x01(\x0e\x32\x1b.gsdk.finger.TemplateFormat\x12\x14\n\x0ctemplateData\x18\x02 \x01(\x0c\"#\n\x10GlobalAPBRequest\x12\x0f\n\x07userIDs\x18\x01 \x03(\t\"#\n\x11UserPhraseRequest\x12\x0e\n\x06userID\x18\x01 \x01(\t\"%\n\x10SubscribeRequest\x12\x11\n\tqueueSize\x18\x01 \x01(\x05\"\x14\n\x12UnsubscribeRequest\"\x15\n\x13UnsubscribeResponse\"\x88\x01\n\x13HandleVerifyRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\r\n\x05seqNO\x18\x02 \x01(\r\x12-\n\x07\x65rrCode\x18\x03 \x01(\x0e\x32\x1c.gsdk.server.ServerErrorCode\x12!\n\x04user\x18\x04 \x01(\x0b\x32\x13.gsdk.user.UserInfo\"\x16\n\x14HandleVerifyResponse\"\x8a\x01\n\x15HandleIdentifyRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\r\n\x05seqNO\x18\x02 \x01(\r\x12-\n\x07\x65rrCode\x18\x03 \x01(\x0e\x32\x1c.gsdk.server.ServerErrorCode\x12!\n\x04user\x18\x04 \x01(\x0b\x32\x13.gsdk.user.UserInfo\"\x18\n\x16HandleIdentifyResponse\"x\n\x16HandleGlobalAPBRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\r\n\x05seqNO\x18\x02 \x01(\r\x12-\n\x07\x65rrCode\x18\x03 \x01(\x0e\x32\x1c.gsdk.server.ServerErrorCode\x12\x0e\n\x06zoneID\x18\x04 \x01(\r\"\x19\n\x17HandleGlobalAPBResponse\"}\n\x17HandleUserPhraseRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\r\n\x05seqNO\x18\x02 \x01(\r\x12-\n\x07\x65rrCode\x18\x03 \x01(\x0e\x32\x1c.gsdk.server.ServerErrorCode\x12\x12\n\nuserPhrase\x18\x04 \x01(\t\"\x1a\n\x18HandleUserPhraseResponse*x\n\x0bRequestType\x12\x0e\n\nNO_REQUEST\x10\x00\x12\x12\n\x0eVERIFY_REQUEST\x10\x01\x12\x14\n\x10IDENTIFY_REQUEST\x10\x02\x12\x16\n\x12GLOBAL_APB_REQUEST\x10\x03\x12\x17\n\x13USER_PHRASE_REQUEST\x10\x04*\xb5\x01\n\x0fServerErrorCode\x12\x0b\n\x07SUCCESS\x10\x00\x12\x18\n\x0bVERIFY_FAIL\x10\xd3\xfd\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1a\n\rIDENTIFY_FAIL\x10\xd2\xfd\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1f\n\x12HARD_APB_VIOLATION\x10\xce\xf6\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1f\n\x12SOFT_APB_VIOLATION\x10\xcd\xf6\xff\xff\xff\xff\xff\xff\xff\x01\x12\x1d\n\x10\x43\x41NNOT_FIND_USER\x10\xb6\xfa\xff\xff\xff\xff\xff\xff\xff\x01\x32\x93\x04\n\x06Server\x12H\n\tSubscribe\x12\x1d.gsdk.server.SubscribeRequest\x1a\x1a.gsdk.server.ServerRequest0\x01\x12P\n\x0bUnsubscribe\x12\x1f.gsdk.server.UnsubscribeRequest\x1a .gsdk.server.UnsubscribeResponse\x12S\n\x0cHandleVerify\x12 .gsdk.server.HandleVerifyRequest\x1a!.gsdk.server.HandleVerifyResponse\x12Y\n\x0eHandleIdentify\x12\".gsdk.server.HandleIdentifyRequest\x1a#.gsdk.server.HandleIdentifyResponse\x12\\\n\x0fHandleGlobalAPB\x12#.gsdk.server.HandleGlobalAPBRequest\x1a$.gsdk.server.HandleGlobalAPBResponse\x12_\n\x10HandleUserPhrase\x12$.gsdk.server.HandleUserPhraseRequest\x1a%.gsdk.server.HandleUserPhraseResponseB5\n\x19\x63om.supremainc.sdk.serverP\x01Z\x16\x62iostar/service/serverb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'server_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\031com.supremainc.sdk.serverP\001Z\026biostar/service/server'
  _REQUESTTYPE._serialized_start=1355
  _REQUESTTYPE._serialized_end=1475
  _SERVERERRORCODE._serialized_start=1478
  _SERVERERRORCODE._serialized_end=1659
  _SERVERREQUEST._serialized_start=68
  _SERVERREQUEST._serialized_end=365
  _VERIFYREQUEST._serialized_start=367
  _VERIFYREQUEST._serialized_end=467
  _IDENTIFYREQUEST._serialized_start=469
  _IDENTIFYREQUEST._serialized_end=561
  _GLOBALAPBREQUEST._serialized_start=563
  _GLOBALAPBREQUEST._serialized_end=598
  _USERPHRASEREQUEST._serialized_start=600
  _USERPHRASEREQUEST._serialized_end=635
  _SUBSCRIBEREQUEST._serialized_start=637
  _SUBSCRIBEREQUEST._serialized_end=674
  _UNSUBSCRIBEREQUEST._serialized_start=676
  _UNSUBSCRIBEREQUEST._serialized_end=696
  _UNSUBSCRIBERESPONSE._serialized_start=698
  _UNSUBSCRIBERESPONSE._serialized_end=719
  _HANDLEVERIFYREQUEST._serialized_start=722
  _HANDLEVERIFYREQUEST._serialized_end=858
  _HANDLEVERIFYRESPONSE._serialized_start=860
  _HANDLEVERIFYRESPONSE._serialized_end=882
  _HANDLEIDENTIFYREQUEST._serialized_start=885
  _HANDLEIDENTIFYREQUEST._serialized_end=1023
  _HANDLEIDENTIFYRESPONSE._serialized_start=1025
  _HANDLEIDENTIFYRESPONSE._serialized_end=1049
  _HANDLEGLOBALAPBREQUEST._serialized_start=1051
  _HANDLEGLOBALAPBREQUEST._serialized_end=1171
  _HANDLEGLOBALAPBRESPONSE._serialized_start=1173
  _HANDLEGLOBALAPBRESPONSE._serialized_end=1198
  _HANDLEUSERPHRASEREQUEST._serialized_start=1200
  _HANDLEUSERPHRASEREQUEST._serialized_end=1325
  _HANDLEUSERPHRASERESPONSE._serialized_start=1327
  _HANDLEUSERPHRASERESPONSE._serialized_end=1353
  _SERVER._serialized_start=1662
  _SERVER._serialized_end=2193
# @@protoc_insertion_point(module_scope)
