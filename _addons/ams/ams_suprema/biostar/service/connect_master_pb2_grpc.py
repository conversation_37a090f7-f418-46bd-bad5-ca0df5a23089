# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import connect_master_pb2 as connect__master__pb2
import connect_pb2 as connect__pb2


class ConnectMasterStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Connect = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/Connect',
                request_serializer=connect__master__pb2.ConnectRequest.SerializeToString,
                response_deserializer=connect__master__pb2.ConnectResponse.FromString,
                )
        self.AddAsyncConnection = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/AddAsyncConnection',
                request_serializer=connect__master__pb2.AddAsyncConnectionRequest.SerializeToString,
                response_deserializer=connect__master__pb2.AddAsyncConnectionResponse.FromString,
                )
        self.DeleteAsyncConnection = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/DeleteAsyncConnection',
                request_serializer=connect__master__pb2.DeleteAsyncConnectionRequest.SerializeToString,
                response_deserializer=connect__master__pb2.DeleteAsyncConnectionResponse.FromString,
                )
        self.AddAsyncConnectionDB = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/AddAsyncConnectionDB',
                request_serializer=connect__master__pb2.AddAsyncConnectionDBRequest.SerializeToString,
                response_deserializer=connect__master__pb2.AddAsyncConnectionDBResponse.FromString,
                )
        self.DeleteAsyncConnectionDB = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/DeleteAsyncConnectionDB',
                request_serializer=connect__master__pb2.DeleteAsyncConnectionDBRequest.SerializeToString,
                response_deserializer=connect__master__pb2.DeleteAsyncConnectionDBResponse.FromString,
                )
        self.GetAsyncConnectionDB = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/GetAsyncConnectionDB',
                request_serializer=connect__master__pb2.GetAsyncConnectionDBRequest.SerializeToString,
                response_deserializer=connect__master__pb2.GetAsyncConnectionDBResponse.FromString,
                )
        self.SetAcceptFilter = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/SetAcceptFilter',
                request_serializer=connect__master__pb2.SetAcceptFilterRequest.SerializeToString,
                response_deserializer=connect__master__pb2.SetAcceptFilterResponse.FromString,
                )
        self.GetAcceptFilter = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/GetAcceptFilter',
                request_serializer=connect__master__pb2.GetAcceptFilterRequest.SerializeToString,
                response_deserializer=connect__master__pb2.GetAcceptFilterResponse.FromString,
                )
        self.SetAcceptFilterDB = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/SetAcceptFilterDB',
                request_serializer=connect__master__pb2.SetAcceptFilterDBRequest.SerializeToString,
                response_deserializer=connect__master__pb2.SetAcceptFilterDBResponse.FromString,
                )
        self.GetAcceptFilterDB = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/GetAcceptFilterDB',
                request_serializer=connect__master__pb2.GetAcceptFilterDBRequest.SerializeToString,
                response_deserializer=connect__master__pb2.GetAcceptFilterDBResponse.FromString,
                )
        self.GetPendingList = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/GetPendingList',
                request_serializer=connect__master__pb2.GetPendingListRequest.SerializeToString,
                response_deserializer=connect__master__pb2.GetPendingListResponse.FromString,
                )
        self.GetDeviceList = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/GetDeviceList',
                request_serializer=connect__master__pb2.GetDeviceListRequest.SerializeToString,
                response_deserializer=connect__master__pb2.GetDeviceListResponse.FromString,
                )
        self.Disconnect = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/Disconnect',
                request_serializer=connect__master__pb2.DisconnectRequest.SerializeToString,
                response_deserializer=connect__master__pb2.DisconnectResponse.FromString,
                )
        self.DisconnectAll = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/DisconnectAll',
                request_serializer=connect__master__pb2.DisconnectAllRequest.SerializeToString,
                response_deserializer=connect__master__pb2.DisconnectAllResponse.FromString,
                )
        self.SearchDevice = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/SearchDevice',
                request_serializer=connect__master__pb2.SearchDeviceRequest.SerializeToString,
                response_deserializer=connect__master__pb2.SearchDeviceResponse.FromString,
                )
        self.GetSlaveDevice = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/GetSlaveDevice',
                request_serializer=connect__master__pb2.GetSlaveDeviceRequest.SerializeToString,
                response_deserializer=connect__master__pb2.GetSlaveDeviceResponse.FromString,
                )
        self.SetSlaveDevice = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/SetSlaveDevice',
                request_serializer=connect__master__pb2.SetSlaveDeviceRequest.SerializeToString,
                response_deserializer=connect__master__pb2.SetSlaveDeviceResponse.FromString,
                )
        self.AddSlaveDeviceDB = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/AddSlaveDeviceDB',
                request_serializer=connect__master__pb2.AddSlaveDeviceDBRequest.SerializeToString,
                response_deserializer=connect__master__pb2.AddSlaveDeviceDBResponse.FromString,
                )
        self.DeleteSlaveDeviceDB = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/DeleteSlaveDeviceDB',
                request_serializer=connect__master__pb2.DeleteSlaveDeviceDBRequest.SerializeToString,
                response_deserializer=connect__master__pb2.DeleteSlaveDeviceDBResponse.FromString,
                )
        self.GetSlaveDeviceDB = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/GetSlaveDeviceDB',
                request_serializer=connect__master__pb2.GetSlaveDeviceDBRequest.SerializeToString,
                response_deserializer=connect__master__pb2.GetSlaveDeviceDBResponse.FromString,
                )
        self.SetConnectionMode = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/SetConnectionMode',
                request_serializer=connect__master__pb2.SetConnectionModeRequest.SerializeToString,
                response_deserializer=connect__master__pb2.SetConnectionModeResponse.FromString,
                )
        self.SetConnectionModeMulti = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/SetConnectionModeMulti',
                request_serializer=connect__master__pb2.SetConnectionModeMultiRequest.SerializeToString,
                response_deserializer=connect__master__pb2.SetConnectionModeMultiResponse.FromString,
                )
        self.EnableSSL = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/EnableSSL',
                request_serializer=connect__master__pb2.EnableSSLRequest.SerializeToString,
                response_deserializer=connect__master__pb2.EnableSSLResponse.FromString,
                )
        self.EnableSSLMulti = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/EnableSSLMulti',
                request_serializer=connect__master__pb2.EnableSSLMultiRequest.SerializeToString,
                response_deserializer=connect__master__pb2.EnableSSLMultiResponse.FromString,
                )
        self.DisableSSL = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/DisableSSL',
                request_serializer=connect__master__pb2.DisableSSLRequest.SerializeToString,
                response_deserializer=connect__master__pb2.DisableSSLResponse.FromString,
                )
        self.DisableSSLMulti = channel.unary_unary(
                '/gsdk.connect_master.ConnectMaster/DisableSSLMulti',
                request_serializer=connect__master__pb2.DisableSSLMultiRequest.SerializeToString,
                response_deserializer=connect__master__pb2.DisableSSLMultiResponse.FromString,
                )
        self.SubscribeStatus = channel.unary_stream(
                '/gsdk.connect_master.ConnectMaster/SubscribeStatus',
                request_serializer=connect__master__pb2.SubscribeStatusRequest.SerializeToString,
                response_deserializer=connect__pb2.StatusChange.FromString,
                )


class ConnectMasterServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Connect(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddAsyncConnection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAsyncConnection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddAsyncConnectionDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAsyncConnectionDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAsyncConnectionDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetAcceptFilter(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAcceptFilter(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetAcceptFilterDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAcceptFilterDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPendingList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeviceList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Disconnect(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DisconnectAll(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchDevice(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSlaveDevice(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetSlaveDevice(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddSlaveDeviceDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSlaveDeviceDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSlaveDeviceDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConnectionMode(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConnectionModeMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnableSSL(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnableSSLMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DisableSSL(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DisableSSLMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ConnectMasterServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Connect': grpc.unary_unary_rpc_method_handler(
                    servicer.Connect,
                    request_deserializer=connect__master__pb2.ConnectRequest.FromString,
                    response_serializer=connect__master__pb2.ConnectResponse.SerializeToString,
            ),
            'AddAsyncConnection': grpc.unary_unary_rpc_method_handler(
                    servicer.AddAsyncConnection,
                    request_deserializer=connect__master__pb2.AddAsyncConnectionRequest.FromString,
                    response_serializer=connect__master__pb2.AddAsyncConnectionResponse.SerializeToString,
            ),
            'DeleteAsyncConnection': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAsyncConnection,
                    request_deserializer=connect__master__pb2.DeleteAsyncConnectionRequest.FromString,
                    response_serializer=connect__master__pb2.DeleteAsyncConnectionResponse.SerializeToString,
            ),
            'AddAsyncConnectionDB': grpc.unary_unary_rpc_method_handler(
                    servicer.AddAsyncConnectionDB,
                    request_deserializer=connect__master__pb2.AddAsyncConnectionDBRequest.FromString,
                    response_serializer=connect__master__pb2.AddAsyncConnectionDBResponse.SerializeToString,
            ),
            'DeleteAsyncConnectionDB': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAsyncConnectionDB,
                    request_deserializer=connect__master__pb2.DeleteAsyncConnectionDBRequest.FromString,
                    response_serializer=connect__master__pb2.DeleteAsyncConnectionDBResponse.SerializeToString,
            ),
            'GetAsyncConnectionDB': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAsyncConnectionDB,
                    request_deserializer=connect__master__pb2.GetAsyncConnectionDBRequest.FromString,
                    response_serializer=connect__master__pb2.GetAsyncConnectionDBResponse.SerializeToString,
            ),
            'SetAcceptFilter': grpc.unary_unary_rpc_method_handler(
                    servicer.SetAcceptFilter,
                    request_deserializer=connect__master__pb2.SetAcceptFilterRequest.FromString,
                    response_serializer=connect__master__pb2.SetAcceptFilterResponse.SerializeToString,
            ),
            'GetAcceptFilter': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAcceptFilter,
                    request_deserializer=connect__master__pb2.GetAcceptFilterRequest.FromString,
                    response_serializer=connect__master__pb2.GetAcceptFilterResponse.SerializeToString,
            ),
            'SetAcceptFilterDB': grpc.unary_unary_rpc_method_handler(
                    servicer.SetAcceptFilterDB,
                    request_deserializer=connect__master__pb2.SetAcceptFilterDBRequest.FromString,
                    response_serializer=connect__master__pb2.SetAcceptFilterDBResponse.SerializeToString,
            ),
            'GetAcceptFilterDB': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAcceptFilterDB,
                    request_deserializer=connect__master__pb2.GetAcceptFilterDBRequest.FromString,
                    response_serializer=connect__master__pb2.GetAcceptFilterDBResponse.SerializeToString,
            ),
            'GetPendingList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPendingList,
                    request_deserializer=connect__master__pb2.GetPendingListRequest.FromString,
                    response_serializer=connect__master__pb2.GetPendingListResponse.SerializeToString,
            ),
            'GetDeviceList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeviceList,
                    request_deserializer=connect__master__pb2.GetDeviceListRequest.FromString,
                    response_serializer=connect__master__pb2.GetDeviceListResponse.SerializeToString,
            ),
            'Disconnect': grpc.unary_unary_rpc_method_handler(
                    servicer.Disconnect,
                    request_deserializer=connect__master__pb2.DisconnectRequest.FromString,
                    response_serializer=connect__master__pb2.DisconnectResponse.SerializeToString,
            ),
            'DisconnectAll': grpc.unary_unary_rpc_method_handler(
                    servicer.DisconnectAll,
                    request_deserializer=connect__master__pb2.DisconnectAllRequest.FromString,
                    response_serializer=connect__master__pb2.DisconnectAllResponse.SerializeToString,
            ),
            'SearchDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchDevice,
                    request_deserializer=connect__master__pb2.SearchDeviceRequest.FromString,
                    response_serializer=connect__master__pb2.SearchDeviceResponse.SerializeToString,
            ),
            'GetSlaveDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSlaveDevice,
                    request_deserializer=connect__master__pb2.GetSlaveDeviceRequest.FromString,
                    response_serializer=connect__master__pb2.GetSlaveDeviceResponse.SerializeToString,
            ),
            'SetSlaveDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.SetSlaveDevice,
                    request_deserializer=connect__master__pb2.SetSlaveDeviceRequest.FromString,
                    response_serializer=connect__master__pb2.SetSlaveDeviceResponse.SerializeToString,
            ),
            'AddSlaveDeviceDB': grpc.unary_unary_rpc_method_handler(
                    servicer.AddSlaveDeviceDB,
                    request_deserializer=connect__master__pb2.AddSlaveDeviceDBRequest.FromString,
                    response_serializer=connect__master__pb2.AddSlaveDeviceDBResponse.SerializeToString,
            ),
            'DeleteSlaveDeviceDB': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSlaveDeviceDB,
                    request_deserializer=connect__master__pb2.DeleteSlaveDeviceDBRequest.FromString,
                    response_serializer=connect__master__pb2.DeleteSlaveDeviceDBResponse.SerializeToString,
            ),
            'GetSlaveDeviceDB': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSlaveDeviceDB,
                    request_deserializer=connect__master__pb2.GetSlaveDeviceDBRequest.FromString,
                    response_serializer=connect__master__pb2.GetSlaveDeviceDBResponse.SerializeToString,
            ),
            'SetConnectionMode': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConnectionMode,
                    request_deserializer=connect__master__pb2.SetConnectionModeRequest.FromString,
                    response_serializer=connect__master__pb2.SetConnectionModeResponse.SerializeToString,
            ),
            'SetConnectionModeMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConnectionModeMulti,
                    request_deserializer=connect__master__pb2.SetConnectionModeMultiRequest.FromString,
                    response_serializer=connect__master__pb2.SetConnectionModeMultiResponse.SerializeToString,
            ),
            'EnableSSL': grpc.unary_unary_rpc_method_handler(
                    servicer.EnableSSL,
                    request_deserializer=connect__master__pb2.EnableSSLRequest.FromString,
                    response_serializer=connect__master__pb2.EnableSSLResponse.SerializeToString,
            ),
            'EnableSSLMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.EnableSSLMulti,
                    request_deserializer=connect__master__pb2.EnableSSLMultiRequest.FromString,
                    response_serializer=connect__master__pb2.EnableSSLMultiResponse.SerializeToString,
            ),
            'DisableSSL': grpc.unary_unary_rpc_method_handler(
                    servicer.DisableSSL,
                    request_deserializer=connect__master__pb2.DisableSSLRequest.FromString,
                    response_serializer=connect__master__pb2.DisableSSLResponse.SerializeToString,
            ),
            'DisableSSLMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DisableSSLMulti,
                    request_deserializer=connect__master__pb2.DisableSSLMultiRequest.FromString,
                    response_serializer=connect__master__pb2.DisableSSLMultiResponse.SerializeToString,
            ),
            'SubscribeStatus': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribeStatus,
                    request_deserializer=connect__master__pb2.SubscribeStatusRequest.FromString,
                    response_serializer=connect__pb2.StatusChange.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.connect_master.ConnectMaster', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ConnectMaster(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Connect(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/Connect',
            connect__master__pb2.ConnectRequest.SerializeToString,
            connect__master__pb2.ConnectResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddAsyncConnection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/AddAsyncConnection',
            connect__master__pb2.AddAsyncConnectionRequest.SerializeToString,
            connect__master__pb2.AddAsyncConnectionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAsyncConnection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/DeleteAsyncConnection',
            connect__master__pb2.DeleteAsyncConnectionRequest.SerializeToString,
            connect__master__pb2.DeleteAsyncConnectionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddAsyncConnectionDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/AddAsyncConnectionDB',
            connect__master__pb2.AddAsyncConnectionDBRequest.SerializeToString,
            connect__master__pb2.AddAsyncConnectionDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAsyncConnectionDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/DeleteAsyncConnectionDB',
            connect__master__pb2.DeleteAsyncConnectionDBRequest.SerializeToString,
            connect__master__pb2.DeleteAsyncConnectionDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetAsyncConnectionDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/GetAsyncConnectionDB',
            connect__master__pb2.GetAsyncConnectionDBRequest.SerializeToString,
            connect__master__pb2.GetAsyncConnectionDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetAcceptFilter(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/SetAcceptFilter',
            connect__master__pb2.SetAcceptFilterRequest.SerializeToString,
            connect__master__pb2.SetAcceptFilterResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetAcceptFilter(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/GetAcceptFilter',
            connect__master__pb2.GetAcceptFilterRequest.SerializeToString,
            connect__master__pb2.GetAcceptFilterResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetAcceptFilterDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/SetAcceptFilterDB',
            connect__master__pb2.SetAcceptFilterDBRequest.SerializeToString,
            connect__master__pb2.SetAcceptFilterDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetAcceptFilterDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/GetAcceptFilterDB',
            connect__master__pb2.GetAcceptFilterDBRequest.SerializeToString,
            connect__master__pb2.GetAcceptFilterDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPendingList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/GetPendingList',
            connect__master__pb2.GetPendingListRequest.SerializeToString,
            connect__master__pb2.GetPendingListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDeviceList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/GetDeviceList',
            connect__master__pb2.GetDeviceListRequest.SerializeToString,
            connect__master__pb2.GetDeviceListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Disconnect(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/Disconnect',
            connect__master__pb2.DisconnectRequest.SerializeToString,
            connect__master__pb2.DisconnectResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DisconnectAll(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/DisconnectAll',
            connect__master__pb2.DisconnectAllRequest.SerializeToString,
            connect__master__pb2.DisconnectAllResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SearchDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/SearchDevice',
            connect__master__pb2.SearchDeviceRequest.SerializeToString,
            connect__master__pb2.SearchDeviceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSlaveDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/GetSlaveDevice',
            connect__master__pb2.GetSlaveDeviceRequest.SerializeToString,
            connect__master__pb2.GetSlaveDeviceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetSlaveDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/SetSlaveDevice',
            connect__master__pb2.SetSlaveDeviceRequest.SerializeToString,
            connect__master__pb2.SetSlaveDeviceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddSlaveDeviceDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/AddSlaveDeviceDB',
            connect__master__pb2.AddSlaveDeviceDBRequest.SerializeToString,
            connect__master__pb2.AddSlaveDeviceDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteSlaveDeviceDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/DeleteSlaveDeviceDB',
            connect__master__pb2.DeleteSlaveDeviceDBRequest.SerializeToString,
            connect__master__pb2.DeleteSlaveDeviceDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSlaveDeviceDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/GetSlaveDeviceDB',
            connect__master__pb2.GetSlaveDeviceDBRequest.SerializeToString,
            connect__master__pb2.GetSlaveDeviceDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetConnectionMode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/SetConnectionMode',
            connect__master__pb2.SetConnectionModeRequest.SerializeToString,
            connect__master__pb2.SetConnectionModeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetConnectionModeMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/SetConnectionModeMulti',
            connect__master__pb2.SetConnectionModeMultiRequest.SerializeToString,
            connect__master__pb2.SetConnectionModeMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnableSSL(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/EnableSSL',
            connect__master__pb2.EnableSSLRequest.SerializeToString,
            connect__master__pb2.EnableSSLResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnableSSLMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/EnableSSLMulti',
            connect__master__pb2.EnableSSLMultiRequest.SerializeToString,
            connect__master__pb2.EnableSSLMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DisableSSL(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/DisableSSL',
            connect__master__pb2.DisableSSLRequest.SerializeToString,
            connect__master__pb2.DisableSSLResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DisableSSLMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.connect_master.ConnectMaster/DisableSSLMulti',
            connect__master__pb2.DisableSSLMultiRequest.SerializeToString,
            connect__master__pb2.DisableSSLMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/gsdk.connect_master.ConnectMaster/SubscribeStatus',
            connect__master__pb2.SubscribeStatusRequest.SerializeToString,
            connect__pb2.StatusChange.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
