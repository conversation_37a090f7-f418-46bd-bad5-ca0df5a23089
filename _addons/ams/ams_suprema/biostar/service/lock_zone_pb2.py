# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lock_zone.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import zone_pb2 as zone__pb2
import action_pb2 as action__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0flock_zone.proto\x12\x0egsdk.lock_zone\x1a\nzone.proto\x1a\x0c\x61\x63tion.proto\"\xff\x01\n\x08ZoneInfo\x12\x0e\n\x06zoneID\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x16\n\x0elockScheduleID\x18\x03 \x01(\r\x12\x18\n\x10unlockScheduleID\x18\x04 \x01(\r\x12\x19\n\x11\x62idirectionalLock\x18\x05 \x01(\x08\x12\x10\n\x08\x64isabled\x18\x06 \x01(\x08\x12\x0f\n\x07\x61larmed\x18\x07 \x01(\x08\x12\x0f\n\x07\x64oorIDs\x18\x08 \x03(\r\x12$\n\x07\x61\x63tions\x18\t \x03(\x0b\x32\x13.gsdk.action.Action\x12\x16\n\x0e\x62ypassGroupIDs\x18\n \x03(\r\x12\x16\n\x0eunlockGroupIDs\x18\x0b \x03(\r\"\x1e\n\nGetRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"6\n\x0bGetResponse\x12\'\n\x05zones\x18\x01 \x03(\x0b\x32\x18.gsdk.lock_zone.ZoneInfo\"5\n\x10GetStatusRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0f\n\x07zoneIDs\x18\x02 \x03(\r\":\n\x11GetStatusResponse\x12%\n\x06status\x18\x01 \x03(\x0b\x32\x15.gsdk.zone.ZoneStatus\"G\n\nAddRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\'\n\x05zones\x18\x02 \x03(\x0b\x32\x18.gsdk.lock_zone.ZoneInfo\"\r\n\x0b\x41\x64\x64Response\"2\n\rDeleteRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0f\n\x07zoneIDs\x18\x02 \x03(\r\"\x10\n\x0e\x44\x65leteResponse\"$\n\x10\x44\x65leteAllRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x13\n\x11\x44\x65leteAllResponse\"E\n\x0fSetAlarmRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0f\n\x07zoneIDs\x18\x02 \x03(\r\x12\x0f\n\x07\x61larmed\x18\x03 \x01(\x08\"\x12\n\x10SetAlarmResponse*\x90\x01\n\x04\x45num\x12!\n\x1d\x46IRST_ENUM_VALUE_MUST_BE_ZERO\x10\x00\x12\x0e\n\nMAX_ALARMS\x10\x05\x12\x15\n\x11MAX_BYPASS_GROUPS\x10\x10\x12\x15\n\x11MAX_UNLOCK_GROUPS\x10\x10\x12\r\n\tMAX_DOORS\x10 \x12\x14\n\x0fMAX_NAME_LENGTH\x10\x90\x01\x1a\x02\x10\x01\x32\xc6\x03\n\x08LockZone\x12>\n\x03Get\x12\x1a.gsdk.lock_zone.GetRequest\x1a\x1b.gsdk.lock_zone.GetResponse\x12P\n\tGetStatus\x12 .gsdk.lock_zone.GetStatusRequest\x1a!.gsdk.lock_zone.GetStatusResponse\x12>\n\x03\x41\x64\x64\x12\x1a.gsdk.lock_zone.AddRequest\x1a\x1b.gsdk.lock_zone.AddResponse\x12G\n\x06\x44\x65lete\x12\x1d.gsdk.lock_zone.DeleteRequest\x1a\x1e.gsdk.lock_zone.DeleteResponse\x12P\n\tDeleteAll\x12 .gsdk.lock_zone.DeleteAllRequest\x1a!.gsdk.lock_zone.DeleteAllResponse\x12M\n\x08SetAlarm\x12\x1f.gsdk.lock_zone.SetAlarmRequest\x1a .gsdk.lock_zone.SetAlarmResponseB:\n\x1c\x63om.supremainc.sdk.lock_zoneP\x01Z\x18\x62iostar/service/lockZoneb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lock_zone_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.supremainc.sdk.lock_zoneP\001Z\030biostar/service/lockZone'
  _ENUM._options = None
  _ENUM._serialized_options = b'\020\001'
  _ENUM._serialized_start=831
  _ENUM._serialized_end=975
  _ZONEINFO._serialized_start=62
  _ZONEINFO._serialized_end=317
  _GETREQUEST._serialized_start=319
  _GETREQUEST._serialized_end=349
  _GETRESPONSE._serialized_start=351
  _GETRESPONSE._serialized_end=405
  _GETSTATUSREQUEST._serialized_start=407
  _GETSTATUSREQUEST._serialized_end=460
  _GETSTATUSRESPONSE._serialized_start=462
  _GETSTATUSRESPONSE._serialized_end=520
  _ADDREQUEST._serialized_start=522
  _ADDREQUEST._serialized_end=593
  _ADDRESPONSE._serialized_start=595
  _ADDRESPONSE._serialized_end=608
  _DELETEREQUEST._serialized_start=610
  _DELETEREQUEST._serialized_end=660
  _DELETERESPONSE._serialized_start=662
  _DELETERESPONSE._serialized_end=678
  _DELETEALLREQUEST._serialized_start=680
  _DELETEALLREQUEST._serialized_end=716
  _DELETEALLRESPONSE._serialized_start=718
  _DELETEALLRESPONSE._serialized_end=737
  _SETALARMREQUEST._serialized_start=739
  _SETALARMREQUEST._serialized_end=808
  _SETALARMRESPONSE._serialized_start=810
  _SETALARMRESPONSE._serialized_end=828
  _LOCKZONE._serialized_start=978
  _LOCKZONE._serialized_end=1432
# @@protoc_insertion_point(module_scope)
