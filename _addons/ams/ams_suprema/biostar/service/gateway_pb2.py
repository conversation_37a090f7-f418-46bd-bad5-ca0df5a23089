# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: gateway.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import cert_pb2 as cert__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rgateway.proto\x12\x0cgsdk.gateway\x1a\ncert.proto\"H\n\x0bGatewayInfo\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x11\n\tdeviceIDs\x18\x02 \x03(\r\x12\x13\n\x0bisConnected\x18\x03 \x01(\x08\"\x10\n\x0eGetListRequest\"%\n\x0fGetListResponse\x12\x12\n\ngatewayIDs\x18\x01 \x03(\t\" \n\nGetRequest\x12\x12\n\ngatewayIDs\x18\x01 \x03(\t\">\n\x0bGetResponse\x12/\n\x0cgatewayInfos\x18\x01 \x03(\x0b\x32\x19.gsdk.gateway.GatewayInfo\" \n\nAddRequest\x12\x12\n\ngatewayIDs\x18\x01 \x03(\t\"\r\n\x0b\x41\x64\x64Response\"#\n\rDeleteRequest\x12\x12\n\ngatewayIDs\x18\x01 \x03(\t\"\x10\n\x0e\x44\x65leteResponse\"l\n\x18\x43reateCertificateRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12#\n\x07subject\x18\x02 \x01(\x0b\x32\x12.gsdk.cert.PKIName\x12\x18\n\x10\x65xpireAfterYears\x18\x03 \x01(\x05\"D\n\x19\x43reateCertificateResponse\x12\x13\n\x0bgatewayCert\x18\x01 \x01(\t\x12\x12\n\ngatewayKey\x18\x02 \x01(\t\"/\n\x19GetIssuanceHistoryRequest\x12\x12\n\ngatewayIDs\x18\x01 \x03(\t\"\x97\x01\n\x0f\x43\x65rtificateInfo\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12#\n\x07subject\x18\x02 \x01(\x0b\x32\x12.gsdk.cert.PKIName\x12\x10\n\x08serialNO\x18\x03 \x01(\x03\x12\x11\n\tissueDate\x18\x04 \x01(\x03\x12\x12\n\nexpiryDate\x18\x05 \x01(\x03\x12\x13\n\x0b\x62lacklisted\x18\x06 \x01(\x08\"N\n\x1aGetIssuanceHistoryResponse\x12\x30\n\tcertInfos\x18\x01 \x03(\x0b\x32\x1d.gsdk.gateway.CertificateInfo\"4\n\x1eGetCertificateBlacklistRequest\x12\x12\n\ngatewayIDs\x18\x01 \x03(\t\"S\n\x1fGetCertificateBlacklistResponse\x12\x30\n\tcertInfos\x18\x01 \x03(\x0b\x32\x1d.gsdk.gateway.CertificateInfo\"F\n\x1e\x41\x64\x64\x43\x65rtificateBlacklistRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x11\n\tserialNOs\x18\x02 \x03(\x03\"!\n\x1f\x41\x64\x64\x43\x65rtificateBlacklistResponse\"I\n!DeleteCertificateBlacklistRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x11\n\tserialNOs\x18\x02 \x03(\x03\"$\n\"DeleteCertificateBlacklistResponse\"+\n\x16SubscribeStatusRequest\x12\x11\n\tqueueSize\x18\x01 \x01(\x05\"Z\n\x0cStatusChange\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12$\n\x06status\x18\x02 \x01(\x0e\x32\x14.gsdk.gateway.Status\x12\x11\n\ttimestamp\x18\x03 \x01(\r*)\n\x06Status\x12\x10\n\x0c\x44ISCONNECTED\x10\x00\x12\r\n\tCONNECTED\x10\x01\x32\xa5\x07\n\x07Gateway\x12\x46\n\x07GetList\x12\x1c.gsdk.gateway.GetListRequest\x1a\x1d.gsdk.gateway.GetListResponse\x12:\n\x03Get\x12\x18.gsdk.gateway.GetRequest\x1a\x19.gsdk.gateway.GetResponse\x12:\n\x03\x41\x64\x64\x12\x18.gsdk.gateway.AddRequest\x1a\x19.gsdk.gateway.AddResponse\x12\x43\n\x06\x44\x65lete\x12\x1b.gsdk.gateway.DeleteRequest\x1a\x1c.gsdk.gateway.DeleteResponse\x12\x64\n\x11\x43reateCertificate\x12&.gsdk.gateway.CreateCertificateRequest\x1a\'.gsdk.gateway.CreateCertificateResponse\x12g\n\x12GetIssuanceHistory\x12\'.gsdk.gateway.GetIssuanceHistoryRequest\x1a(.gsdk.gateway.GetIssuanceHistoryResponse\x12v\n\x17GetCertificateBlacklist\x12,.gsdk.gateway.GetCertificateBlacklistRequest\x1a-.gsdk.gateway.GetCertificateBlacklistResponse\x12v\n\x17\x41\x64\x64\x43\x65rtificateBlacklist\x12,.gsdk.gateway.AddCertificateBlacklistRequest\x1a-.gsdk.gateway.AddCertificateBlacklistResponse\x12\x7f\n\x1a\x44\x65leteCertificateBlacklist\x12/.gsdk.gateway.DeleteCertificateBlacklistRequest\x1a\x30.gsdk.gateway.DeleteCertificateBlacklistResponse\x12U\n\x0fSubscribeStatus\x12$.gsdk.gateway.SubscribeStatusRequest\x1a\x1a.gsdk.gateway.StatusChange0\x01\x42\x37\n\x1a\x63om.supremainc.sdk.gatewayP\x01Z\x17\x62iostar/service/gatewayb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gateway_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.supremainc.sdk.gatewayP\001Z\027biostar/service/gateway'
  _STATUS._serialized_start=1335
  _STATUS._serialized_end=1376
  _GATEWAYINFO._serialized_start=43
  _GATEWAYINFO._serialized_end=115
  _GETLISTREQUEST._serialized_start=117
  _GETLISTREQUEST._serialized_end=133
  _GETLISTRESPONSE._serialized_start=135
  _GETLISTRESPONSE._serialized_end=172
  _GETREQUEST._serialized_start=174
  _GETREQUEST._serialized_end=206
  _GETRESPONSE._serialized_start=208
  _GETRESPONSE._serialized_end=270
  _ADDREQUEST._serialized_start=272
  _ADDREQUEST._serialized_end=304
  _ADDRESPONSE._serialized_start=306
  _ADDRESPONSE._serialized_end=319
  _DELETEREQUEST._serialized_start=321
  _DELETEREQUEST._serialized_end=356
  _DELETERESPONSE._serialized_start=358
  _DELETERESPONSE._serialized_end=374
  _CREATECERTIFICATEREQUEST._serialized_start=376
  _CREATECERTIFICATEREQUEST._serialized_end=484
  _CREATECERTIFICATERESPONSE._serialized_start=486
  _CREATECERTIFICATERESPONSE._serialized_end=554
  _GETISSUANCEHISTORYREQUEST._serialized_start=556
  _GETISSUANCEHISTORYREQUEST._serialized_end=603
  _CERTIFICATEINFO._serialized_start=606
  _CERTIFICATEINFO._serialized_end=757
  _GETISSUANCEHISTORYRESPONSE._serialized_start=759
  _GETISSUANCEHISTORYRESPONSE._serialized_end=837
  _GETCERTIFICATEBLACKLISTREQUEST._serialized_start=839
  _GETCERTIFICATEBLACKLISTREQUEST._serialized_end=891
  _GETCERTIFICATEBLACKLISTRESPONSE._serialized_start=893
  _GETCERTIFICATEBLACKLISTRESPONSE._serialized_end=976
  _ADDCERTIFICATEBLACKLISTREQUEST._serialized_start=978
  _ADDCERTIFICATEBLACKLISTREQUEST._serialized_end=1048
  _ADDCERTIFICATEBLACKLISTRESPONSE._serialized_start=1050
  _ADDCERTIFICATEBLACKLISTRESPONSE._serialized_end=1083
  _DELETECERTIFICATEBLACKLISTREQUEST._serialized_start=1085
  _DELETECERTIFICATEBLACKLISTREQUEST._serialized_end=1158
  _DELETECERTIFICATEBLACKLISTRESPONSE._serialized_start=1160
  _DELETECERTIFICATEBLACKLISTRESPONSE._serialized_end=1196
  _SUBSCRIBESTATUSREQUEST._serialized_start=1198
  _SUBSCRIBESTATUSREQUEST._serialized_end=1241
  _STATUSCHANGE._serialized_start=1243
  _STATUSCHANGE._serialized_end=1333
  _GATEWAY._serialized_start=1379
  _GATEWAY._serialized_end=2312
# @@protoc_insertion_point(module_scope)
