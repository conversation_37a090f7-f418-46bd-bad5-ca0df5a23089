# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: connect_master.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import connect_pb2 as connect__pb2
import err_pb2 as err__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x63onnect_master.proto\x12\x13gsdk.connect_master\x1a\rconnect.proto\x1a\terr.proto\"S\n\x0e\x43onnectRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12.\n\x0b\x63onnectInfo\x18\x02 \x01(\x0b\x32\x19.gsdk.connect.ConnectInfo\"#\n\x0f\x43onnectResponse\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"d\n\x19\x41\x64\x64\x41syncConnectionRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x34\n\x0c\x63onnectInfos\x18\x02 \x03(\x0b\x32\x1e.gsdk.connect.AsyncConnectInfo\"\x1c\n\x1a\x41\x64\x64\x41syncConnectionResponse\"D\n\x1c\x44\x65leteAsyncConnectionRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x11\n\tdeviceIDs\x18\x02 \x03(\r\"\x1f\n\x1d\x44\x65leteAsyncConnectionResponse\"f\n\x1b\x41\x64\x64\x41syncConnectionDBRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x34\n\x0c\x63onnectInfos\x18\x02 \x03(\x0b\x32\x1e.gsdk.connect.AsyncConnectInfo\"\x1e\n\x1c\x41\x64\x64\x41syncConnectionDBResponse\"F\n\x1e\x44\x65leteAsyncConnectionDBRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x11\n\tdeviceIDs\x18\x02 \x03(\r\"!\n\x1f\x44\x65leteAsyncConnectionDBResponse\"0\n\x1bGetAsyncConnectionDBRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\"T\n\x1cGetAsyncConnectionDBResponse\x12\x34\n\x0c\x63onnectInfos\x18\x01 \x03(\x0b\x32\x1e.gsdk.connect.AsyncConnectInfo\"W\n\x16SetAcceptFilterRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12*\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x1a.gsdk.connect.AcceptFilter\"\x19\n\x17SetAcceptFilterResponse\"+\n\x16GetAcceptFilterRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\"E\n\x17GetAcceptFilterResponse\x12*\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x1a.gsdk.connect.AcceptFilter\"Y\n\x18SetAcceptFilterDBRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12*\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x1a.gsdk.connect.AcceptFilter\"\x1b\n\x19SetAcceptFilterDBResponse\"-\n\x18GetAcceptFilterDBRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\"G\n\x19GetAcceptFilterDBResponse\x12*\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x1a.gsdk.connect.AcceptFilter\"*\n\x15GetPendingListRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\"N\n\x16GetPendingListResponse\x12\x34\n\x0b\x64\x65viceInfos\x18\x01 \x03(\x0b\x32\x1f.gsdk.connect.PendingDeviceInfo\")\n\x14GetDeviceListRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\"F\n\x15GetDeviceListResponse\x12-\n\x0b\x64\x65viceInfos\x18\x01 \x03(\x0b\x32\x18.gsdk.connect.DeviceInfo\"&\n\x11\x44isconnectRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"\x14\n\x12\x44isconnectResponse\")\n\x14\x44isconnectAllRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\"\x17\n\x15\x44isconnectAllResponse\"9\n\x13SearchDeviceRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\r\"K\n\x14SearchDeviceResponse\x12\x33\n\x0b\x64\x65viceInfos\x18\x01 \x03(\x0b\x32\x1e.gsdk.connect.SearchDeviceInfo\"*\n\x15GetSlaveDeviceRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\"Q\n\x16GetSlaveDeviceResponse\x12\x37\n\x10slaveDeviceInfos\x18\x01 \x03(\x0b\x32\x1d.gsdk.connect.SlaveDeviceInfo\"c\n\x15SetSlaveDeviceRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x37\n\x10slaveDeviceInfos\x18\x02 \x03(\x0b\x32\x1d.gsdk.connect.SlaveDeviceInfo\"\x18\n\x16SetSlaveDeviceResponse\"e\n\x17\x41\x64\x64SlaveDeviceDBRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x37\n\x10slaveDeviceInfos\x18\x02 \x03(\x0b\x32\x1d.gsdk.connect.SlaveDeviceInfo\"\x1a\n\x18\x41\x64\x64SlaveDeviceDBResponse\"B\n\x1a\x44\x65leteSlaveDeviceDBRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x11\n\tdeviceIDs\x18\x02 \x03(\r\"\x1d\n\x1b\x44\x65leteSlaveDeviceDBResponse\",\n\x17GetSlaveDeviceDBRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\"S\n\x18GetSlaveDeviceDBResponse\x12\x37\n\x10slaveDeviceInfos\x18\x01 \x03(\x0b\x32\x1d.gsdk.connect.SlaveDeviceInfo\"+\n\x16SubscribeStatusRequest\x12\x11\n\tqueueSize\x18\x01 \x01(\x05\"b\n\x18SetConnectionModeRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x34\n\x0e\x63onnectionMode\x18\x02 \x01(\x0e\x32\x1c.gsdk.connect.ConnectionMode\"\x1b\n\x19SetConnectionModeResponse\"h\n\x1dSetConnectionModeMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12\x34\n\x0e\x63onnectionMode\x18\x02 \x01(\x0e\x32\x1c.gsdk.connect.ConnectionMode\"O\n\x1eSetConnectionModeMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"$\n\x10\x45nableSSLRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x13\n\x11\x45nableSSLResponse\"*\n\x15\x45nableSSLMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"G\n\x16\x45nableSSLMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"%\n\x11\x44isableSSLRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x14\n\x12\x44isableSSLResponse\"+\n\x16\x44isableSSLMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"H\n\x17\x44isableSSLMultiResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse2\xbc\x17\n\rConnectMaster\x12T\n\x07\x43onnect\x12#.gsdk.connect_master.ConnectRequest\x1a$.gsdk.connect_master.ConnectResponse\x12u\n\x12\x41\x64\x64\x41syncConnection\x12..gsdk.connect_master.AddAsyncConnectionRequest\x1a/.gsdk.connect_master.AddAsyncConnectionResponse\x12~\n\x15\x44\x65leteAsyncConnection\x12\x31.gsdk.connect_master.DeleteAsyncConnectionRequest\x1a\x32.gsdk.connect_master.DeleteAsyncConnectionResponse\x12{\n\x14\x41\x64\x64\x41syncConnectionDB\x12\x30.gsdk.connect_master.AddAsyncConnectionDBRequest\x1a\x31.gsdk.connect_master.AddAsyncConnectionDBResponse\x12\x84\x01\n\x17\x44\x65leteAsyncConnectionDB\x12\x33.gsdk.connect_master.DeleteAsyncConnectionDBRequest\x1a\x34.gsdk.connect_master.DeleteAsyncConnectionDBResponse\x12{\n\x14GetAsyncConnectionDB\x12\x30.gsdk.connect_master.GetAsyncConnectionDBRequest\x1a\x31.gsdk.connect_master.GetAsyncConnectionDBResponse\x12l\n\x0fSetAcceptFilter\x12+.gsdk.connect_master.SetAcceptFilterRequest\x1a,.gsdk.connect_master.SetAcceptFilterResponse\x12l\n\x0fGetAcceptFilter\x12+.gsdk.connect_master.GetAcceptFilterRequest\x1a,.gsdk.connect_master.GetAcceptFilterResponse\x12r\n\x11SetAcceptFilterDB\x12-.gsdk.connect_master.SetAcceptFilterDBRequest\x1a..gsdk.connect_master.SetAcceptFilterDBResponse\x12r\n\x11GetAcceptFilterDB\x12-.gsdk.connect_master.GetAcceptFilterDBRequest\x1a..gsdk.connect_master.GetAcceptFilterDBResponse\x12i\n\x0eGetPendingList\x12*.gsdk.connect_master.GetPendingListRequest\x1a+.gsdk.connect_master.GetPendingListResponse\x12\x66\n\rGetDeviceList\x12).gsdk.connect_master.GetDeviceListRequest\x1a*.gsdk.connect_master.GetDeviceListResponse\x12]\n\nDisconnect\x12&.gsdk.connect_master.DisconnectRequest\x1a\'.gsdk.connect_master.DisconnectResponse\x12\x66\n\rDisconnectAll\x12).gsdk.connect_master.DisconnectAllRequest\x1a*.gsdk.connect_master.DisconnectAllResponse\x12\x63\n\x0cSearchDevice\x12(.gsdk.connect_master.SearchDeviceRequest\x1a).gsdk.connect_master.SearchDeviceResponse\x12i\n\x0eGetSlaveDevice\x12*.gsdk.connect_master.GetSlaveDeviceRequest\x1a+.gsdk.connect_master.GetSlaveDeviceResponse\x12i\n\x0eSetSlaveDevice\x12*.gsdk.connect_master.SetSlaveDeviceRequest\x1a+.gsdk.connect_master.SetSlaveDeviceResponse\x12o\n\x10\x41\x64\x64SlaveDeviceDB\x12,.gsdk.connect_master.AddSlaveDeviceDBRequest\x1a-.gsdk.connect_master.AddSlaveDeviceDBResponse\x12x\n\x13\x44\x65leteSlaveDeviceDB\x12/.gsdk.connect_master.DeleteSlaveDeviceDBRequest\x1a\x30.gsdk.connect_master.DeleteSlaveDeviceDBResponse\x12o\n\x10GetSlaveDeviceDB\x12,.gsdk.connect_master.GetSlaveDeviceDBRequest\x1a-.gsdk.connect_master.GetSlaveDeviceDBResponse\x12r\n\x11SetConnectionMode\x12-.gsdk.connect_master.SetConnectionModeRequest\x1a..gsdk.connect_master.SetConnectionModeResponse\x12\x81\x01\n\x16SetConnectionModeMulti\x12\x32.gsdk.connect_master.SetConnectionModeMultiRequest\x1a\x33.gsdk.connect_master.SetConnectionModeMultiResponse\x12Z\n\tEnableSSL\x12%.gsdk.connect_master.EnableSSLRequest\x1a&.gsdk.connect_master.EnableSSLResponse\x12i\n\x0e\x45nableSSLMulti\x12*.gsdk.connect_master.EnableSSLMultiRequest\x1a+.gsdk.connect_master.EnableSSLMultiResponse\x12]\n\nDisableSSL\x12&.gsdk.connect_master.DisableSSLRequest\x1a\'.gsdk.connect_master.DisableSSLResponse\x12l\n\x0f\x44isableSSLMulti\x12+.gsdk.connect_master.DisableSSLMultiRequest\x1a,.gsdk.connect_master.DisableSSLMultiResponse\x12\\\n\x0fSubscribeStatus\x12+.gsdk.connect_master.SubscribeStatusRequest\x1a\x1a.gsdk.connect.StatusChange0\x01\x42\x44\n!com.supremainc.sdk.connect_masterP\x01Z\x1d\x62iostar/service/connectMasterb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'connect_master_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n!com.supremainc.sdk.connect_masterP\001Z\035biostar/service/connectMaster'
  _CONNECTREQUEST._serialized_start=71
  _CONNECTREQUEST._serialized_end=154
  _CONNECTRESPONSE._serialized_start=156
  _CONNECTRESPONSE._serialized_end=191
  _ADDASYNCCONNECTIONREQUEST._serialized_start=193
  _ADDASYNCCONNECTIONREQUEST._serialized_end=293
  _ADDASYNCCONNECTIONRESPONSE._serialized_start=295
  _ADDASYNCCONNECTIONRESPONSE._serialized_end=323
  _DELETEASYNCCONNECTIONREQUEST._serialized_start=325
  _DELETEASYNCCONNECTIONREQUEST._serialized_end=393
  _DELETEASYNCCONNECTIONRESPONSE._serialized_start=395
  _DELETEASYNCCONNECTIONRESPONSE._serialized_end=426
  _ADDASYNCCONNECTIONDBREQUEST._serialized_start=428
  _ADDASYNCCONNECTIONDBREQUEST._serialized_end=530
  _ADDASYNCCONNECTIONDBRESPONSE._serialized_start=532
  _ADDASYNCCONNECTIONDBRESPONSE._serialized_end=562
  _DELETEASYNCCONNECTIONDBREQUEST._serialized_start=564
  _DELETEASYNCCONNECTIONDBREQUEST._serialized_end=634
  _DELETEASYNCCONNECTIONDBRESPONSE._serialized_start=636
  _DELETEASYNCCONNECTIONDBRESPONSE._serialized_end=669
  _GETASYNCCONNECTIONDBREQUEST._serialized_start=671
  _GETASYNCCONNECTIONDBREQUEST._serialized_end=719
  _GETASYNCCONNECTIONDBRESPONSE._serialized_start=721
  _GETASYNCCONNECTIONDBRESPONSE._serialized_end=805
  _SETACCEPTFILTERREQUEST._serialized_start=807
  _SETACCEPTFILTERREQUEST._serialized_end=894
  _SETACCEPTFILTERRESPONSE._serialized_start=896
  _SETACCEPTFILTERRESPONSE._serialized_end=921
  _GETACCEPTFILTERREQUEST._serialized_start=923
  _GETACCEPTFILTERREQUEST._serialized_end=966
  _GETACCEPTFILTERRESPONSE._serialized_start=968
  _GETACCEPTFILTERRESPONSE._serialized_end=1037
  _SETACCEPTFILTERDBREQUEST._serialized_start=1039
  _SETACCEPTFILTERDBREQUEST._serialized_end=1128
  _SETACCEPTFILTERDBRESPONSE._serialized_start=1130
  _SETACCEPTFILTERDBRESPONSE._serialized_end=1157
  _GETACCEPTFILTERDBREQUEST._serialized_start=1159
  _GETACCEPTFILTERDBREQUEST._serialized_end=1204
  _GETACCEPTFILTERDBRESPONSE._serialized_start=1206
  _GETACCEPTFILTERDBRESPONSE._serialized_end=1277
  _GETPENDINGLISTREQUEST._serialized_start=1279
  _GETPENDINGLISTREQUEST._serialized_end=1321
  _GETPENDINGLISTRESPONSE._serialized_start=1323
  _GETPENDINGLISTRESPONSE._serialized_end=1401
  _GETDEVICELISTREQUEST._serialized_start=1403
  _GETDEVICELISTREQUEST._serialized_end=1444
  _GETDEVICELISTRESPONSE._serialized_start=1446
  _GETDEVICELISTRESPONSE._serialized_end=1516
  _DISCONNECTREQUEST._serialized_start=1518
  _DISCONNECTREQUEST._serialized_end=1556
  _DISCONNECTRESPONSE._serialized_start=1558
  _DISCONNECTRESPONSE._serialized_end=1578
  _DISCONNECTALLREQUEST._serialized_start=1580
  _DISCONNECTALLREQUEST._serialized_end=1621
  _DISCONNECTALLRESPONSE._serialized_start=1623
  _DISCONNECTALLRESPONSE._serialized_end=1646
  _SEARCHDEVICEREQUEST._serialized_start=1648
  _SEARCHDEVICEREQUEST._serialized_end=1705
  _SEARCHDEVICERESPONSE._serialized_start=1707
  _SEARCHDEVICERESPONSE._serialized_end=1782
  _GETSLAVEDEVICEREQUEST._serialized_start=1784
  _GETSLAVEDEVICEREQUEST._serialized_end=1826
  _GETSLAVEDEVICERESPONSE._serialized_start=1828
  _GETSLAVEDEVICERESPONSE._serialized_end=1909
  _SETSLAVEDEVICEREQUEST._serialized_start=1911
  _SETSLAVEDEVICEREQUEST._serialized_end=2010
  _SETSLAVEDEVICERESPONSE._serialized_start=2012
  _SETSLAVEDEVICERESPONSE._serialized_end=2036
  _ADDSLAVEDEVICEDBREQUEST._serialized_start=2038
  _ADDSLAVEDEVICEDBREQUEST._serialized_end=2139
  _ADDSLAVEDEVICEDBRESPONSE._serialized_start=2141
  _ADDSLAVEDEVICEDBRESPONSE._serialized_end=2167
  _DELETESLAVEDEVICEDBREQUEST._serialized_start=2169
  _DELETESLAVEDEVICEDBREQUEST._serialized_end=2235
  _DELETESLAVEDEVICEDBRESPONSE._serialized_start=2237
  _DELETESLAVEDEVICEDBRESPONSE._serialized_end=2266
  _GETSLAVEDEVICEDBREQUEST._serialized_start=2268
  _GETSLAVEDEVICEDBREQUEST._serialized_end=2312
  _GETSLAVEDEVICEDBRESPONSE._serialized_start=2314
  _GETSLAVEDEVICEDBRESPONSE._serialized_end=2397
  _SUBSCRIBESTATUSREQUEST._serialized_start=2399
  _SUBSCRIBESTATUSREQUEST._serialized_end=2442
  _SETCONNECTIONMODEREQUEST._serialized_start=2444
  _SETCONNECTIONMODEREQUEST._serialized_end=2542
  _SETCONNECTIONMODERESPONSE._serialized_start=2544
  _SETCONNECTIONMODERESPONSE._serialized_end=2571
  _SETCONNECTIONMODEMULTIREQUEST._serialized_start=2573
  _SETCONNECTIONMODEMULTIREQUEST._serialized_end=2677
  _SETCONNECTIONMODEMULTIRESPONSE._serialized_start=2679
  _SETCONNECTIONMODEMULTIRESPONSE._serialized_end=2758
  _ENABLESSLREQUEST._serialized_start=2760
  _ENABLESSLREQUEST._serialized_end=2796
  _ENABLESSLRESPONSE._serialized_start=2798
  _ENABLESSLRESPONSE._serialized_end=2817
  _ENABLESSLMULTIREQUEST._serialized_start=2819
  _ENABLESSLMULTIREQUEST._serialized_end=2861
  _ENABLESSLMULTIRESPONSE._serialized_start=2863
  _ENABLESSLMULTIRESPONSE._serialized_end=2934
  _DISABLESSLREQUEST._serialized_start=2936
  _DISABLESSLREQUEST._serialized_end=2973
  _DISABLESSLRESPONSE._serialized_start=2975
  _DISABLESSLRESPONSE._serialized_end=2995
  _DISABLESSLMULTIREQUEST._serialized_start=2997
  _DISABLESSLMULTIREQUEST._serialized_end=3040
  _DISABLESSLMULTIRESPONSE._serialized_start=3042
  _DISABLESSLMULTIRESPONSE._serialized_end=3114
  _CONNECTMASTER._serialized_start=3117
  _CONNECTMASTER._serialized_end=6121
# @@protoc_insertion_point(module_scope)
