# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: err.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\terr.proto\x12\x08gsdk.err\"<\n\rErrorResponse\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0c\n\x04\x63ode\x18\x02 \x01(\x05\x12\x0b\n\x03msg\x18\x03 \x01(\t\"C\n\x12MultiErrorResponse\x12-\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x17.gsdk.err.ErrorResponse\"D\n\x14GatewayErrorResponse\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\x05\x12\x0b\n\x03msg\x18\x03 \x01(\t2\x07\n\x05\x45rrorB/\n\x16\x63om.supremainc.sdk.errP\x01Z\x13\x62iostar/service/errb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'err_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\026com.supremainc.sdk.errP\001Z\023biostar/service/err'
  _ERRORRESPONSE._serialized_start=23
  _ERRORRESPONSE._serialized_end=83
  _MULTIERRORRESPONSE._serialized_start=85
  _MULTIERRORRESPONSE._serialized_end=152
  _GATEWAYERRORRESPONSE._serialized_start=154
  _GATEWAYERRORRESPONSE._serialized_end=222
  _ERROR._serialized_start=224
  _ERROR._serialized_end=231
# @@protoc_insertion_point(module_scope)
