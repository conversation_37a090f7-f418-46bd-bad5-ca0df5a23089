# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import network_pb2 as network__pb2


class NetworkStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetIPConfig = channel.unary_unary(
                '/gsdk.network.Network/GetIPConfig',
                request_serializer=network__pb2.GetIPConfigRequest.SerializeToString,
                response_deserializer=network__pb2.GetIPConfigResponse.FromString,
                )
        self.SetIPConfig = channel.unary_unary(
                '/gsdk.network.Network/SetIPConfig',
                request_serializer=network__pb2.SetIPConfigRequest.SerializeToString,
                response_deserializer=network__pb2.SetIPConfigResponse.FromString,
                )
        self.SetIPConfigMulti = channel.unary_unary(
                '/gsdk.network.Network/SetIPConfigMulti',
                request_serializer=network__pb2.SetIPConfigMultiRequest.SerializeToString,
                response_deserializer=network__pb2.SetIPConfigMultiResponse.FromString,
                )
        self.GetWLANConfig = channel.unary_unary(
                '/gsdk.network.Network/GetWLANConfig',
                request_serializer=network__pb2.GetWLANConfigRequest.SerializeToString,
                response_deserializer=network__pb2.GetWLANConfigResponse.FromString,
                )
        self.SetWLANConfig = channel.unary_unary(
                '/gsdk.network.Network/SetWLANConfig',
                request_serializer=network__pb2.SetWLANConfigRequest.SerializeToString,
                response_deserializer=network__pb2.SetWLANConfigResponse.FromString,
                )
        self.SetWLANConfigMulti = channel.unary_unary(
                '/gsdk.network.Network/SetWLANConfigMulti',
                request_serializer=network__pb2.SetWLANConfigMultiRequest.SerializeToString,
                response_deserializer=network__pb2.SetWLANConfigMultiResponse.FromString,
                )


class NetworkServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetIPConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetIPConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetIPConfigMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetWLANConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetWLANConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetWLANConfigMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_NetworkServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetIPConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIPConfig,
                    request_deserializer=network__pb2.GetIPConfigRequest.FromString,
                    response_serializer=network__pb2.GetIPConfigResponse.SerializeToString,
            ),
            'SetIPConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetIPConfig,
                    request_deserializer=network__pb2.SetIPConfigRequest.FromString,
                    response_serializer=network__pb2.SetIPConfigResponse.SerializeToString,
            ),
            'SetIPConfigMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.SetIPConfigMulti,
                    request_deserializer=network__pb2.SetIPConfigMultiRequest.FromString,
                    response_serializer=network__pb2.SetIPConfigMultiResponse.SerializeToString,
            ),
            'GetWLANConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetWLANConfig,
                    request_deserializer=network__pb2.GetWLANConfigRequest.FromString,
                    response_serializer=network__pb2.GetWLANConfigResponse.SerializeToString,
            ),
            'SetWLANConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetWLANConfig,
                    request_deserializer=network__pb2.SetWLANConfigRequest.FromString,
                    response_serializer=network__pb2.SetWLANConfigResponse.SerializeToString,
            ),
            'SetWLANConfigMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.SetWLANConfigMulti,
                    request_deserializer=network__pb2.SetWLANConfigMultiRequest.FromString,
                    response_serializer=network__pb2.SetWLANConfigMultiResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.network.Network', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Network(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetIPConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.network.Network/GetIPConfig',
            network__pb2.GetIPConfigRequest.SerializeToString,
            network__pb2.GetIPConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetIPConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.network.Network/SetIPConfig',
            network__pb2.SetIPConfigRequest.SerializeToString,
            network__pb2.SetIPConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetIPConfigMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.network.Network/SetIPConfigMulti',
            network__pb2.SetIPConfigMultiRequest.SerializeToString,
            network__pb2.SetIPConfigMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetWLANConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.network.Network/GetWLANConfig',
            network__pb2.GetWLANConfigRequest.SerializeToString,
            network__pb2.GetWLANConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetWLANConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.network.Network/SetWLANConfig',
            network__pb2.SetWLANConfigRequest.SerializeToString,
            network__pb2.SetWLANConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetWLANConfigMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.network.Network/SetWLANConfigMulti',
            network__pb2.SetWLANConfigMultiRequest.SerializeToString,
            network__pb2.SetWLANConfigMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
