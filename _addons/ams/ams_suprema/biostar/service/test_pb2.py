# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: test.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import card_pb2 as card__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\ntest.proto\x12\tgsdk.test\x1a\ncard.proto\"O\n\x11\x44\x65tectCardRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12(\n\x08\x63\x61rdData\x18\x02 \x01(\x0b\x32\x16.gsdk.card.CSNCardData\"\x14\n\x12\x44\x65tectCardResponse\";\n\x11\x44\x65tectFaceRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x14\n\x0c\x66\x61\x63\x65Template\x18\x02 \x01(\x0c\"\x14\n\x12\x44\x65tectFaceResponse\"I\n\x18\x44\x65tectFingerprintRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x1b\n\x13\x66ingerprintTemplate\x18\x02 \x01(\x0c\"\x1b\n\x19\x44\x65tectFingerprintResponse\"2\n\x0f\x45nterKeyRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\r\n\x05input\x18\x02 \x01(\t\"\x12\n\x10\x45nterKeyResponse2\xc1\x02\n\x04Test\x12I\n\nDetectCard\x12\x1c.gsdk.test.DetectCardRequest\x1a\x1d.gsdk.test.DetectCardResponse\x12I\n\nDetectFace\x12\x1c.gsdk.test.DetectFaceRequest\x1a\x1d.gsdk.test.DetectFaceResponse\x12^\n\x11\x44\x65tectFingerprint\x12#.gsdk.test.DetectFingerprintRequest\x1a$.gsdk.test.DetectFingerprintResponse\x12\x43\n\x08\x45nterKey\x12\x1a.gsdk.test.EnterKeyRequest\x1a\x1b.gsdk.test.EnterKeyResponseB1\n\x17\x63om.supremainc.sdk.testP\x01Z\x14\x62iostar/service/testb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'test_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\027com.supremainc.sdk.testP\001Z\024biostar/service/test'
  _DETECTCARDREQUEST._serialized_start=37
  _DETECTCARDREQUEST._serialized_end=116
  _DETECTCARDRESPONSE._serialized_start=118
  _DETECTCARDRESPONSE._serialized_end=138
  _DETECTFACEREQUEST._serialized_start=140
  _DETECTFACEREQUEST._serialized_end=199
  _DETECTFACERESPONSE._serialized_start=201
  _DETECTFACERESPONSE._serialized_end=221
  _DETECTFINGERPRINTREQUEST._serialized_start=223
  _DETECTFINGERPRINTREQUEST._serialized_end=296
  _DETECTFINGERPRINTRESPONSE._serialized_start=298
  _DETECTFINGERPRINTRESPONSE._serialized_end=325
  _ENTERKEYREQUEST._serialized_start=327
  _ENTERKEYREQUEST._serialized_end=377
  _ENTERKEYRESPONSE._serialized_start=379
  _ENTERKEYRESPONSE._serialized_end=397
  _TEST._serialized_start=400
  _TEST._serialized_end=721
# @@protoc_insertion_point(module_scope)
