# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import schedule_pb2 as schedule__pb2


class ScheduleStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetList = channel.unary_unary(
                '/gsdk.schedule.Schedule/GetList',
                request_serializer=schedule__pb2.GetListRequest.SerializeToString,
                response_deserializer=schedule__pb2.GetListResponse.FromString,
                )
        self.Add = channel.unary_unary(
                '/gsdk.schedule.Schedule/Add',
                request_serializer=schedule__pb2.AddRequest.SerializeToString,
                response_deserializer=schedule__pb2.AddResponse.FromString,
                )
        self.AddMulti = channel.unary_unary(
                '/gsdk.schedule.Schedule/AddMulti',
                request_serializer=schedule__pb2.AddMultiRequest.SerializeToString,
                response_deserializer=schedule__pb2.AddMultiResponse.FromString,
                )
        self.Delete = channel.unary_unary(
                '/gsdk.schedule.Schedule/Delete',
                request_serializer=schedule__pb2.DeleteRequest.SerializeToString,
                response_deserializer=schedule__pb2.DeleteResponse.FromString,
                )
        self.DeleteMulti = channel.unary_unary(
                '/gsdk.schedule.Schedule/DeleteMulti',
                request_serializer=schedule__pb2.DeleteMultiRequest.SerializeToString,
                response_deserializer=schedule__pb2.DeleteMultiResponse.FromString,
                )
        self.DeleteAll = channel.unary_unary(
                '/gsdk.schedule.Schedule/DeleteAll',
                request_serializer=schedule__pb2.DeleteAllRequest.SerializeToString,
                response_deserializer=schedule__pb2.DeleteAllResponse.FromString,
                )
        self.DeleteAllMulti = channel.unary_unary(
                '/gsdk.schedule.Schedule/DeleteAllMulti',
                request_serializer=schedule__pb2.DeleteAllMultiRequest.SerializeToString,
                response_deserializer=schedule__pb2.DeleteAllMultiResponse.FromString,
                )
        self.GetHolidayList = channel.unary_unary(
                '/gsdk.schedule.Schedule/GetHolidayList',
                request_serializer=schedule__pb2.GetHolidayListRequest.SerializeToString,
                response_deserializer=schedule__pb2.GetHolidayListResponse.FromString,
                )
        self.AddHoliday = channel.unary_unary(
                '/gsdk.schedule.Schedule/AddHoliday',
                request_serializer=schedule__pb2.AddHolidayRequest.SerializeToString,
                response_deserializer=schedule__pb2.AddHolidayResponse.FromString,
                )
        self.AddHolidayMulti = channel.unary_unary(
                '/gsdk.schedule.Schedule/AddHolidayMulti',
                request_serializer=schedule__pb2.AddHolidayMultiRequest.SerializeToString,
                response_deserializer=schedule__pb2.AddHolidayMultiResponse.FromString,
                )
        self.DeleteHoliday = channel.unary_unary(
                '/gsdk.schedule.Schedule/DeleteHoliday',
                request_serializer=schedule__pb2.DeleteHolidayRequest.SerializeToString,
                response_deserializer=schedule__pb2.DeleteHolidayResponse.FromString,
                )
        self.DeleteHolidayMulti = channel.unary_unary(
                '/gsdk.schedule.Schedule/DeleteHolidayMulti',
                request_serializer=schedule__pb2.DeleteHolidayMultiRequest.SerializeToString,
                response_deserializer=schedule__pb2.DeleteHolidayMultiResponse.FromString,
                )
        self.DeleteAllHoliday = channel.unary_unary(
                '/gsdk.schedule.Schedule/DeleteAllHoliday',
                request_serializer=schedule__pb2.DeleteAllHolidayRequest.SerializeToString,
                response_deserializer=schedule__pb2.DeleteAllHolidayResponse.FromString,
                )
        self.DeleteAllHolidayMulti = channel.unary_unary(
                '/gsdk.schedule.Schedule/DeleteAllHolidayMulti',
                request_serializer=schedule__pb2.DeleteAllHolidayMultiRequest.SerializeToString,
                response_deserializer=schedule__pb2.DeleteAllHolidayMultiResponse.FromString,
                )


class ScheduleServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Add(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Delete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAll(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAllMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetHolidayList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddHoliday(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddHolidayMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteHoliday(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteHolidayMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAllHoliday(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAllHolidayMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ScheduleServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetList,
                    request_deserializer=schedule__pb2.GetListRequest.FromString,
                    response_serializer=schedule__pb2.GetListResponse.SerializeToString,
            ),
            'Add': grpc.unary_unary_rpc_method_handler(
                    servicer.Add,
                    request_deserializer=schedule__pb2.AddRequest.FromString,
                    response_serializer=schedule__pb2.AddResponse.SerializeToString,
            ),
            'AddMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.AddMulti,
                    request_deserializer=schedule__pb2.AddMultiRequest.FromString,
                    response_serializer=schedule__pb2.AddMultiResponse.SerializeToString,
            ),
            'Delete': grpc.unary_unary_rpc_method_handler(
                    servicer.Delete,
                    request_deserializer=schedule__pb2.DeleteRequest.FromString,
                    response_serializer=schedule__pb2.DeleteResponse.SerializeToString,
            ),
            'DeleteMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteMulti,
                    request_deserializer=schedule__pb2.DeleteMultiRequest.FromString,
                    response_serializer=schedule__pb2.DeleteMultiResponse.SerializeToString,
            ),
            'DeleteAll': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAll,
                    request_deserializer=schedule__pb2.DeleteAllRequest.FromString,
                    response_serializer=schedule__pb2.DeleteAllResponse.SerializeToString,
            ),
            'DeleteAllMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAllMulti,
                    request_deserializer=schedule__pb2.DeleteAllMultiRequest.FromString,
                    response_serializer=schedule__pb2.DeleteAllMultiResponse.SerializeToString,
            ),
            'GetHolidayList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetHolidayList,
                    request_deserializer=schedule__pb2.GetHolidayListRequest.FromString,
                    response_serializer=schedule__pb2.GetHolidayListResponse.SerializeToString,
            ),
            'AddHoliday': grpc.unary_unary_rpc_method_handler(
                    servicer.AddHoliday,
                    request_deserializer=schedule__pb2.AddHolidayRequest.FromString,
                    response_serializer=schedule__pb2.AddHolidayResponse.SerializeToString,
            ),
            'AddHolidayMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.AddHolidayMulti,
                    request_deserializer=schedule__pb2.AddHolidayMultiRequest.FromString,
                    response_serializer=schedule__pb2.AddHolidayMultiResponse.SerializeToString,
            ),
            'DeleteHoliday': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteHoliday,
                    request_deserializer=schedule__pb2.DeleteHolidayRequest.FromString,
                    response_serializer=schedule__pb2.DeleteHolidayResponse.SerializeToString,
            ),
            'DeleteHolidayMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteHolidayMulti,
                    request_deserializer=schedule__pb2.DeleteHolidayMultiRequest.FromString,
                    response_serializer=schedule__pb2.DeleteHolidayMultiResponse.SerializeToString,
            ),
            'DeleteAllHoliday': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAllHoliday,
                    request_deserializer=schedule__pb2.DeleteAllHolidayRequest.FromString,
                    response_serializer=schedule__pb2.DeleteAllHolidayResponse.SerializeToString,
            ),
            'DeleteAllHolidayMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAllHolidayMulti,
                    request_deserializer=schedule__pb2.DeleteAllHolidayMultiRequest.FromString,
                    response_serializer=schedule__pb2.DeleteAllHolidayMultiResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.schedule.Schedule', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Schedule(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/GetList',
            schedule__pb2.GetListRequest.SerializeToString,
            schedule__pb2.GetListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Add(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/Add',
            schedule__pb2.AddRequest.SerializeToString,
            schedule__pb2.AddResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/AddMulti',
            schedule__pb2.AddMultiRequest.SerializeToString,
            schedule__pb2.AddMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Delete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/Delete',
            schedule__pb2.DeleteRequest.SerializeToString,
            schedule__pb2.DeleteResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/DeleteMulti',
            schedule__pb2.DeleteMultiRequest.SerializeToString,
            schedule__pb2.DeleteMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAll(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/DeleteAll',
            schedule__pb2.DeleteAllRequest.SerializeToString,
            schedule__pb2.DeleteAllResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAllMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/DeleteAllMulti',
            schedule__pb2.DeleteAllMultiRequest.SerializeToString,
            schedule__pb2.DeleteAllMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetHolidayList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/GetHolidayList',
            schedule__pb2.GetHolidayListRequest.SerializeToString,
            schedule__pb2.GetHolidayListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddHoliday(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/AddHoliday',
            schedule__pb2.AddHolidayRequest.SerializeToString,
            schedule__pb2.AddHolidayResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddHolidayMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/AddHolidayMulti',
            schedule__pb2.AddHolidayMultiRequest.SerializeToString,
            schedule__pb2.AddHolidayMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteHoliday(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/DeleteHoliday',
            schedule__pb2.DeleteHolidayRequest.SerializeToString,
            schedule__pb2.DeleteHolidayResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteHolidayMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/DeleteHolidayMulti',
            schedule__pb2.DeleteHolidayMultiRequest.SerializeToString,
            schedule__pb2.DeleteHolidayMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAllHoliday(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/DeleteAllHoliday',
            schedule__pb2.DeleteAllHolidayRequest.SerializeToString,
            schedule__pb2.DeleteAllHolidayResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAllHolidayMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.schedule.Schedule/DeleteAllHolidayMulti',
            schedule__pb2.DeleteAllHolidayMultiRequest.SerializeToString,
            schedule__pb2.DeleteAllHolidayMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
