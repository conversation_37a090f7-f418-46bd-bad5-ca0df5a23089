# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: connect.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import device_pb2 as device__pb2
import err_pb2 as err__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='connect.proto',
  package='connect',
  syntax='proto3',
  serialized_options=_b('\n\032com.supremainc.sdk.connectP\001Z\027biostar/service/connect'),
  serialized_pb=_b('\n\rconnect.proto\x12\x07\x63onnect\x1a\x0c\x64\x65vice.proto\x1a\terr.proto\";\n\x0b\x43onnectInfo\x12\x0e\n\x06IPAddr\x18\x01 \x01(\t\x12\x0c\n\x04port\x18\x02 \x01(\x05\x12\x0e\n\x06useSSL\x18\x03 \x01(\x08\";\n\x0e\x43onnectRequest\x12)\n\x0b\x63onnectInfo\x18\x01 \x01(\x0b\x32\x14.connect.ConnectInfo\"#\n\x0f\x43onnectResponse\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"R\n\x10\x41syncConnectInfo\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0e\n\x06IPAddr\x18\x02 \x01(\t\x12\x0c\n\x04port\x18\x03 \x01(\x05\x12\x0e\n\x06useSSL\x18\x04 \x01(\x08\"L\n\x19\x41\x64\x64\x41syncConnectionRequest\x12/\n\x0c\x63onnectInfos\x18\x01 \x03(\x0b\x32\x19.connect.AsyncConnectInfo\"\x1c\n\x1a\x41\x64\x64\x41syncConnectionResponse\"1\n\x1c\x44\x65leteAsyncConnectionRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"\x1f\n\x1d\x44\x65leteAsyncConnectionResponse\"Y\n\x0c\x41\x63\x63\x65ptFilter\x12\x10\n\x08\x61llowAll\x18\x01 \x01(\x08\x12\x11\n\tdeviceIDs\x18\x02 \x03(\r\x12\x0f\n\x07IPAddrs\x18\x03 \x03(\t\x12\x13\n\x0bsubnetMasks\x18\x04 \x03(\t\"?\n\x16SetAcceptFilterRequest\x12%\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x15.connect.AcceptFilter\"\x19\n\x17SetAcceptFilterResponse\"\x18\n\x16GetAcceptFilterRequest\"@\n\x17GetAcceptFilterResponse\x12%\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x15.connect.AcceptFilter\"F\n\x11PendingDeviceInfo\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x0e\n\x06IPAddr\x18\x02 \x01(\t\x12\x0f\n\x07lastTry\x18\x03 \x01(\r\"\x17\n\x15GetPendingListRequest\"I\n\x16GetPendingListResponse\x12/\n\x0b\x64\x65viceInfos\x18\x01 \x03(\x0b\x32\x1a.connect.PendingDeviceInfo\"\xb5\x01\n\nDeviceInfo\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12/\n\x0e\x63onnectionMode\x18\x02 \x01(\x0e\x32\x17.connect.ConnectionMode\x12\x0e\n\x06IPAddr\x18\x03 \x01(\t\x12\x0c\n\x04port\x18\x04 \x01(\x05\x12\x1f\n\x06status\x18\x05 \x01(\x0e\x32\x0f.connect.Status\x12\x15\n\rautoReconnect\x18\x06 \x01(\x08\x12\x0e\n\x06useSSL\x18\x07 \x01(\x08\"\x16\n\x14GetDeviceListRequest\"A\n\x15GetDeviceListResponse\x12(\n\x0b\x64\x65viceInfos\x18\x01 \x03(\x0b\x32\x13.connect.DeviceInfo\"&\n\x11\x44isconnectRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"\x14\n\x12\x44isconnectResponse\"\x16\n\x14\x44isconnectAllRequest\"\x17\n\x15\x44isconnectAllResponse\"&\n\x13SearchDeviceRequest\x12\x0f\n\x07timeout\x18\x01 \x01(\r\"\xb0\x01\n\x10SearchDeviceInfo\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x1a\n\x04type\x18\x02 \x01(\x0e\x32\x0c.device.Type\x12\x0f\n\x07useDHCP\x18\x03 \x01(\x08\x12/\n\x0e\x63onnectionMode\x18\x04 \x01(\x0e\x32\x17.connect.ConnectionMode\x12\x0e\n\x06IPAddr\x18\x05 \x01(\t\x12\x0c\n\x04port\x18\x06 \x01(\x05\x12\x0e\n\x06useSSL\x18\x07 \x01(\x08\"F\n\x14SearchDeviceResponse\x12.\n\x0b\x64\x65viceInfos\x18\x01 \x03(\x0b\x32\x19.connect.SearchDeviceInfo\"+\n\x16SubscribeStatusRequest\x12\x11\n\tqueueSize\x18\x01 \x01(\x05\"T\n\x0cStatusChange\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12\x1f\n\x06status\x18\x02 \x01(\x0e\x32\x0f.connect.Status\x12\x11\n\ttimestamp\x18\x03 \x01(\r\"]\n\x18SetConnectionModeRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\x12/\n\x0e\x63onnectionMode\x18\x02 \x01(\x0e\x32\x17.connect.ConnectionMode\"\x1b\n\x19SetConnectionModeResponse\"c\n\x1dSetConnectionModeMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\x12/\n\x0e\x63onnectionMode\x18\x02 \x01(\x0e\x32\x17.connect.ConnectionMode\"J\n\x1eSetConnectionModeMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse\"$\n\x10\x45nableSSLRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x13\n\x11\x45nableSSLResponse\"*\n\x15\x45nableSSLMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"B\n\x16\x45nableSSLMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse\"%\n\x11\x44isableSSLRequest\x12\x10\n\x08\x64\x65viceID\x18\x01 \x01(\r\"\x14\n\x12\x44isableSSLResponse\"+\n\x16\x44isableSSLMultiRequest\x12\x11\n\tdeviceIDs\x18\x01 \x03(\r\"C\n\x17\x44isableSSLMultiResponse\x12(\n\x0c\x64\x65viceErrors\x18\x01 \x03(\x0b\x32\x12.err.ErrorResponse*M\n\x0e\x43onnectionMode\x12\x14\n\x10SERVER_TO_DEVICE\x10\x00\x12\x14\n\x10\x44\x45VICE_TO_SERVER\x10\x01\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x1a\x02\x10\x01*\x9e\x01\n\x06Status\x12\x10\n\x0c\x44ISCONNECTED\x10\x00\x12\x11\n\rTCP_CONNECTED\x10\x01\x12\x11\n\rTLS_CONNECTED\x10\x02\x12\x17\n\x12TCP_CANNOT_CONNECT\x10\x80\x02\x12\x14\n\x0fTCP_NOT_ALLOWED\x10\x81\x02\x12\x17\n\x12TLS_CANNOT_CONNECT\x10\x80\x04\x12\x14\n\x0fTLS_NOT_ALLOWED\x10\x81\x04\x32\x89\x0b\n\x07\x43onnect\x12<\n\x07\x43onnect\x12\x17.connect.ConnectRequest\x1a\x18.connect.ConnectResponse\x12]\n\x12\x41\x64\x64\x41syncConnection\x12\".connect.AddAsyncConnectionRequest\x1a#.connect.AddAsyncConnectionResponse\x12\x66\n\x15\x44\x65leteAsyncConnection\x12%.connect.DeleteAsyncConnectionRequest\x1a&.connect.DeleteAsyncConnectionResponse\x12T\n\x0fSetAcceptFilter\x12\x1f.connect.SetAcceptFilterRequest\x1a .connect.SetAcceptFilterResponse\x12T\n\x0fGetAcceptFilter\x12\x1f.connect.GetAcceptFilterRequest\x1a .connect.GetAcceptFilterResponse\x12Q\n\x0eGetPendingList\x12\x1e.connect.GetPendingListRequest\x1a\x1f.connect.GetPendingListResponse\x12N\n\rGetDeviceList\x12\x1d.connect.GetDeviceListRequest\x1a\x1e.connect.GetDeviceListResponse\x12\x45\n\nDisconnect\x12\x1a.connect.DisconnectRequest\x1a\x1b.connect.DisconnectResponse\x12N\n\rDisconnectAll\x12\x1d.connect.DisconnectAllRequest\x1a\x1e.connect.DisconnectAllResponse\x12K\n\x0cSearchDevice\x12\x1c.connect.SearchDeviceRequest\x1a\x1d.connect.SearchDeviceResponse\x12Z\n\x11SetConnectionMode\x12!.connect.SetConnectionModeRequest\x1a\".connect.SetConnectionModeResponse\x12i\n\x16SetConnectionModeMulti\x12&.connect.SetConnectionModeMultiRequest\x1a\'.connect.SetConnectionModeMultiResponse\x12\x42\n\tEnableSSL\x12\x19.connect.EnableSSLRequest\x1a\x1a.connect.EnableSSLResponse\x12Q\n\x0e\x45nableSSLMulti\x12\x1e.connect.EnableSSLMultiRequest\x1a\x1f.connect.EnableSSLMultiResponse\x12\x45\n\nDisableSSL\x12\x1a.connect.DisableSSLRequest\x1a\x1b.connect.DisableSSLResponse\x12T\n\x0f\x44isableSSLMulti\x12\x1f.connect.DisableSSLMultiRequest\x1a .connect.DisableSSLMultiResponse\x12K\n\x0fSubscribeStatus\x12\x1f.connect.SubscribeStatusRequest\x1a\x15.connect.StatusChange0\x01\x42\x37\n\x1a\x63om.supremainc.sdk.connectP\x01Z\x17\x62iostar/service/connectb\x06proto3')
  ,
  dependencies=[device__pb2.DESCRIPTOR,err__pb2.DESCRIPTOR,])

_CONNECTIONMODE = _descriptor.EnumDescriptor(
  name='ConnectionMode',
  full_name='connect.ConnectionMode',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SERVER_TO_DEVICE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEVICE_TO_SERVER', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEFAULT', index=2, number=0,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=_b('\020\001'),
  serialized_start=2388,
  serialized_end=2465,
)
_sym_db.RegisterEnumDescriptor(_CONNECTIONMODE)

ConnectionMode = enum_type_wrapper.EnumTypeWrapper(_CONNECTIONMODE)
_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='connect.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DISCONNECTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TCP_CONNECTED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TLS_CONNECTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TCP_CANNOT_CONNECT', index=3, number=256,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TCP_NOT_ALLOWED', index=4, number=257,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TLS_CANNOT_CONNECT', index=5, number=512,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TLS_NOT_ALLOWED', index=6, number=513,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2468,
  serialized_end=2626,
)
_sym_db.RegisterEnumDescriptor(_STATUS)

Status = enum_type_wrapper.EnumTypeWrapper(_STATUS)
SERVER_TO_DEVICE = 0
DEVICE_TO_SERVER = 1
DEFAULT = 0
DISCONNECTED = 0
TCP_CONNECTED = 1
TLS_CONNECTED = 2
TCP_CANNOT_CONNECT = 256
TCP_NOT_ALLOWED = 257
TLS_CANNOT_CONNECT = 512
TLS_NOT_ALLOWED = 513



_CONNECTINFO = _descriptor.Descriptor(
  name='ConnectInfo',
  full_name='connect.ConnectInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='IPAddr', full_name='connect.ConnectInfo.IPAddr', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='port', full_name='connect.ConnectInfo.port', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='useSSL', full_name='connect.ConnectInfo.useSSL', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=51,
  serialized_end=110,
)


_CONNECTREQUEST = _descriptor.Descriptor(
  name='ConnectRequest',
  full_name='connect.ConnectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='connectInfo', full_name='connect.ConnectRequest.connectInfo', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=112,
  serialized_end=171,
)


_CONNECTRESPONSE = _descriptor.Descriptor(
  name='ConnectResponse',
  full_name='connect.ConnectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.ConnectResponse.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=173,
  serialized_end=208,
)


_ASYNCCONNECTINFO = _descriptor.Descriptor(
  name='AsyncConnectInfo',
  full_name='connect.AsyncConnectInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.AsyncConnectInfo.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='IPAddr', full_name='connect.AsyncConnectInfo.IPAddr', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='port', full_name='connect.AsyncConnectInfo.port', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='useSSL', full_name='connect.AsyncConnectInfo.useSSL', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=210,
  serialized_end=292,
)


_ADDASYNCCONNECTIONREQUEST = _descriptor.Descriptor(
  name='AddAsyncConnectionRequest',
  full_name='connect.AddAsyncConnectionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='connectInfos', full_name='connect.AddAsyncConnectionRequest.connectInfos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=294,
  serialized_end=370,
)


_ADDASYNCCONNECTIONRESPONSE = _descriptor.Descriptor(
  name='AddAsyncConnectionResponse',
  full_name='connect.AddAsyncConnectionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=372,
  serialized_end=400,
)


_DELETEASYNCCONNECTIONREQUEST = _descriptor.Descriptor(
  name='DeleteAsyncConnectionRequest',
  full_name='connect.DeleteAsyncConnectionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='connect.DeleteAsyncConnectionRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=402,
  serialized_end=451,
)


_DELETEASYNCCONNECTIONRESPONSE = _descriptor.Descriptor(
  name='DeleteAsyncConnectionResponse',
  full_name='connect.DeleteAsyncConnectionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=453,
  serialized_end=484,
)


_ACCEPTFILTER = _descriptor.Descriptor(
  name='AcceptFilter',
  full_name='connect.AcceptFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='allowAll', full_name='connect.AcceptFilter.allowAll', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='connect.AcceptFilter.deviceIDs', index=1,
      number=2, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='IPAddrs', full_name='connect.AcceptFilter.IPAddrs', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='subnetMasks', full_name='connect.AcceptFilter.subnetMasks', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=486,
  serialized_end=575,
)


_SETACCEPTFILTERREQUEST = _descriptor.Descriptor(
  name='SetAcceptFilterRequest',
  full_name='connect.SetAcceptFilterRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='filter', full_name='connect.SetAcceptFilterRequest.filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=577,
  serialized_end=640,
)


_SETACCEPTFILTERRESPONSE = _descriptor.Descriptor(
  name='SetAcceptFilterResponse',
  full_name='connect.SetAcceptFilterResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=642,
  serialized_end=667,
)


_GETACCEPTFILTERREQUEST = _descriptor.Descriptor(
  name='GetAcceptFilterRequest',
  full_name='connect.GetAcceptFilterRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=669,
  serialized_end=693,
)


_GETACCEPTFILTERRESPONSE = _descriptor.Descriptor(
  name='GetAcceptFilterResponse',
  full_name='connect.GetAcceptFilterResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='filter', full_name='connect.GetAcceptFilterResponse.filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=695,
  serialized_end=759,
)


_PENDINGDEVICEINFO = _descriptor.Descriptor(
  name='PendingDeviceInfo',
  full_name='connect.PendingDeviceInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.PendingDeviceInfo.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='IPAddr', full_name='connect.PendingDeviceInfo.IPAddr', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastTry', full_name='connect.PendingDeviceInfo.lastTry', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=761,
  serialized_end=831,
)


_GETPENDINGLISTREQUEST = _descriptor.Descriptor(
  name='GetPendingListRequest',
  full_name='connect.GetPendingListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=833,
  serialized_end=856,
)


_GETPENDINGLISTRESPONSE = _descriptor.Descriptor(
  name='GetPendingListResponse',
  full_name='connect.GetPendingListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceInfos', full_name='connect.GetPendingListResponse.deviceInfos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=858,
  serialized_end=931,
)


_DEVICEINFO = _descriptor.Descriptor(
  name='DeviceInfo',
  full_name='connect.DeviceInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.DeviceInfo.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='connectionMode', full_name='connect.DeviceInfo.connectionMode', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='IPAddr', full_name='connect.DeviceInfo.IPAddr', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='port', full_name='connect.DeviceInfo.port', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='connect.DeviceInfo.status', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='autoReconnect', full_name='connect.DeviceInfo.autoReconnect', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='useSSL', full_name='connect.DeviceInfo.useSSL', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=934,
  serialized_end=1115,
)


_GETDEVICELISTREQUEST = _descriptor.Descriptor(
  name='GetDeviceListRequest',
  full_name='connect.GetDeviceListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1117,
  serialized_end=1139,
)


_GETDEVICELISTRESPONSE = _descriptor.Descriptor(
  name='GetDeviceListResponse',
  full_name='connect.GetDeviceListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceInfos', full_name='connect.GetDeviceListResponse.deviceInfos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1141,
  serialized_end=1206,
)


_DISCONNECTREQUEST = _descriptor.Descriptor(
  name='DisconnectRequest',
  full_name='connect.DisconnectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='connect.DisconnectRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1208,
  serialized_end=1246,
)


_DISCONNECTRESPONSE = _descriptor.Descriptor(
  name='DisconnectResponse',
  full_name='connect.DisconnectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1248,
  serialized_end=1268,
)


_DISCONNECTALLREQUEST = _descriptor.Descriptor(
  name='DisconnectAllRequest',
  full_name='connect.DisconnectAllRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1270,
  serialized_end=1292,
)


_DISCONNECTALLRESPONSE = _descriptor.Descriptor(
  name='DisconnectAllResponse',
  full_name='connect.DisconnectAllResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1294,
  serialized_end=1317,
)


_SEARCHDEVICEREQUEST = _descriptor.Descriptor(
  name='SearchDeviceRequest',
  full_name='connect.SearchDeviceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='timeout', full_name='connect.SearchDeviceRequest.timeout', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1319,
  serialized_end=1357,
)


_SEARCHDEVICEINFO = _descriptor.Descriptor(
  name='SearchDeviceInfo',
  full_name='connect.SearchDeviceInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.SearchDeviceInfo.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='connect.SearchDeviceInfo.type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='useDHCP', full_name='connect.SearchDeviceInfo.useDHCP', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='connectionMode', full_name='connect.SearchDeviceInfo.connectionMode', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='IPAddr', full_name='connect.SearchDeviceInfo.IPAddr', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='port', full_name='connect.SearchDeviceInfo.port', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='useSSL', full_name='connect.SearchDeviceInfo.useSSL', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1360,
  serialized_end=1536,
)


_SEARCHDEVICERESPONSE = _descriptor.Descriptor(
  name='SearchDeviceResponse',
  full_name='connect.SearchDeviceResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceInfos', full_name='connect.SearchDeviceResponse.deviceInfos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1538,
  serialized_end=1608,
)


_SUBSCRIBESTATUSREQUEST = _descriptor.Descriptor(
  name='SubscribeStatusRequest',
  full_name='connect.SubscribeStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='queueSize', full_name='connect.SubscribeStatusRequest.queueSize', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1610,
  serialized_end=1653,
)


_STATUSCHANGE = _descriptor.Descriptor(
  name='StatusChange',
  full_name='connect.StatusChange',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.StatusChange.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='connect.StatusChange.status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='connect.StatusChange.timestamp', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1655,
  serialized_end=1739,
)


_SETCONNECTIONMODEREQUEST = _descriptor.Descriptor(
  name='SetConnectionModeRequest',
  full_name='connect.SetConnectionModeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.SetConnectionModeRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='connectionMode', full_name='connect.SetConnectionModeRequest.connectionMode', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1741,
  serialized_end=1834,
)


_SETCONNECTIONMODERESPONSE = _descriptor.Descriptor(
  name='SetConnectionModeResponse',
  full_name='connect.SetConnectionModeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1836,
  serialized_end=1863,
)


_SETCONNECTIONMODEMULTIREQUEST = _descriptor.Descriptor(
  name='SetConnectionModeMultiRequest',
  full_name='connect.SetConnectionModeMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='connect.SetConnectionModeMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='connectionMode', full_name='connect.SetConnectionModeMultiRequest.connectionMode', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1865,
  serialized_end=1964,
)


_SETCONNECTIONMODEMULTIRESPONSE = _descriptor.Descriptor(
  name='SetConnectionModeMultiResponse',
  full_name='connect.SetConnectionModeMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='connect.SetConnectionModeMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1966,
  serialized_end=2040,
)


_ENABLESSLREQUEST = _descriptor.Descriptor(
  name='EnableSSLRequest',
  full_name='connect.EnableSSLRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.EnableSSLRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2042,
  serialized_end=2078,
)


_ENABLESSLRESPONSE = _descriptor.Descriptor(
  name='EnableSSLResponse',
  full_name='connect.EnableSSLResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2080,
  serialized_end=2099,
)


_ENABLESSLMULTIREQUEST = _descriptor.Descriptor(
  name='EnableSSLMultiRequest',
  full_name='connect.EnableSSLMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='connect.EnableSSLMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2101,
  serialized_end=2143,
)


_ENABLESSLMULTIRESPONSE = _descriptor.Descriptor(
  name='EnableSSLMultiResponse',
  full_name='connect.EnableSSLMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='connect.EnableSSLMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2145,
  serialized_end=2211,
)


_DISABLESSLREQUEST = _descriptor.Descriptor(
  name='DisableSSLRequest',
  full_name='connect.DisableSSLRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceID', full_name='connect.DisableSSLRequest.deviceID', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2213,
  serialized_end=2250,
)


_DISABLESSLRESPONSE = _descriptor.Descriptor(
  name='DisableSSLResponse',
  full_name='connect.DisableSSLResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2252,
  serialized_end=2272,
)


_DISABLESSLMULTIREQUEST = _descriptor.Descriptor(
  name='DisableSSLMultiRequest',
  full_name='connect.DisableSSLMultiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceIDs', full_name='connect.DisableSSLMultiRequest.deviceIDs', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2274,
  serialized_end=2317,
)


_DISABLESSLMULTIRESPONSE = _descriptor.Descriptor(
  name='DisableSSLMultiResponse',
  full_name='connect.DisableSSLMultiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='deviceErrors', full_name='connect.DisableSSLMultiResponse.deviceErrors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2319,
  serialized_end=2386,
)

_CONNECTREQUEST.fields_by_name['connectInfo'].message_type = _CONNECTINFO
_ADDASYNCCONNECTIONREQUEST.fields_by_name['connectInfos'].message_type = _ASYNCCONNECTINFO
_SETACCEPTFILTERREQUEST.fields_by_name['filter'].message_type = _ACCEPTFILTER
_GETACCEPTFILTERRESPONSE.fields_by_name['filter'].message_type = _ACCEPTFILTER
_GETPENDINGLISTRESPONSE.fields_by_name['deviceInfos'].message_type = _PENDINGDEVICEINFO
_DEVICEINFO.fields_by_name['connectionMode'].enum_type = _CONNECTIONMODE
_DEVICEINFO.fields_by_name['status'].enum_type = _STATUS
_GETDEVICELISTRESPONSE.fields_by_name['deviceInfos'].message_type = _DEVICEINFO
_SEARCHDEVICEINFO.fields_by_name['type'].enum_type = device__pb2._TYPE
_SEARCHDEVICEINFO.fields_by_name['connectionMode'].enum_type = _CONNECTIONMODE
_SEARCHDEVICERESPONSE.fields_by_name['deviceInfos'].message_type = _SEARCHDEVICEINFO
_STATUSCHANGE.fields_by_name['status'].enum_type = _STATUS
_SETCONNECTIONMODEREQUEST.fields_by_name['connectionMode'].enum_type = _CONNECTIONMODE
_SETCONNECTIONMODEMULTIREQUEST.fields_by_name['connectionMode'].enum_type = _CONNECTIONMODE
_SETCONNECTIONMODEMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
_ENABLESSLMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
_DISABLESSLMULTIRESPONSE.fields_by_name['deviceErrors'].message_type = err__pb2._ERRORRESPONSE
DESCRIPTOR.message_types_by_name['ConnectInfo'] = _CONNECTINFO
DESCRIPTOR.message_types_by_name['ConnectRequest'] = _CONNECTREQUEST
DESCRIPTOR.message_types_by_name['ConnectResponse'] = _CONNECTRESPONSE
DESCRIPTOR.message_types_by_name['AsyncConnectInfo'] = _ASYNCCONNECTINFO
DESCRIPTOR.message_types_by_name['AddAsyncConnectionRequest'] = _ADDASYNCCONNECTIONREQUEST
DESCRIPTOR.message_types_by_name['AddAsyncConnectionResponse'] = _ADDASYNCCONNECTIONRESPONSE
DESCRIPTOR.message_types_by_name['DeleteAsyncConnectionRequest'] = _DELETEASYNCCONNECTIONREQUEST
DESCRIPTOR.message_types_by_name['DeleteAsyncConnectionResponse'] = _DELETEASYNCCONNECTIONRESPONSE
DESCRIPTOR.message_types_by_name['AcceptFilter'] = _ACCEPTFILTER
DESCRIPTOR.message_types_by_name['SetAcceptFilterRequest'] = _SETACCEPTFILTERREQUEST
DESCRIPTOR.message_types_by_name['SetAcceptFilterResponse'] = _SETACCEPTFILTERRESPONSE
DESCRIPTOR.message_types_by_name['GetAcceptFilterRequest'] = _GETACCEPTFILTERREQUEST
DESCRIPTOR.message_types_by_name['GetAcceptFilterResponse'] = _GETACCEPTFILTERRESPONSE
DESCRIPTOR.message_types_by_name['PendingDeviceInfo'] = _PENDINGDEVICEINFO
DESCRIPTOR.message_types_by_name['GetPendingListRequest'] = _GETPENDINGLISTREQUEST
DESCRIPTOR.message_types_by_name['GetPendingListResponse'] = _GETPENDINGLISTRESPONSE
DESCRIPTOR.message_types_by_name['DeviceInfo'] = _DEVICEINFO
DESCRIPTOR.message_types_by_name['GetDeviceListRequest'] = _GETDEVICELISTREQUEST
DESCRIPTOR.message_types_by_name['GetDeviceListResponse'] = _GETDEVICELISTRESPONSE
DESCRIPTOR.message_types_by_name['DisconnectRequest'] = _DISCONNECTREQUEST
DESCRIPTOR.message_types_by_name['DisconnectResponse'] = _DISCONNECTRESPONSE
DESCRIPTOR.message_types_by_name['DisconnectAllRequest'] = _DISCONNECTALLREQUEST
DESCRIPTOR.message_types_by_name['DisconnectAllResponse'] = _DISCONNECTALLRESPONSE
DESCRIPTOR.message_types_by_name['SearchDeviceRequest'] = _SEARCHDEVICEREQUEST
DESCRIPTOR.message_types_by_name['SearchDeviceInfo'] = _SEARCHDEVICEINFO
DESCRIPTOR.message_types_by_name['SearchDeviceResponse'] = _SEARCHDEVICERESPONSE
DESCRIPTOR.message_types_by_name['SubscribeStatusRequest'] = _SUBSCRIBESTATUSREQUEST
DESCRIPTOR.message_types_by_name['StatusChange'] = _STATUSCHANGE
DESCRIPTOR.message_types_by_name['SetConnectionModeRequest'] = _SETCONNECTIONMODEREQUEST
DESCRIPTOR.message_types_by_name['SetConnectionModeResponse'] = _SETCONNECTIONMODERESPONSE
DESCRIPTOR.message_types_by_name['SetConnectionModeMultiRequest'] = _SETCONNECTIONMODEMULTIREQUEST
DESCRIPTOR.message_types_by_name['SetConnectionModeMultiResponse'] = _SETCONNECTIONMODEMULTIRESPONSE
DESCRIPTOR.message_types_by_name['EnableSSLRequest'] = _ENABLESSLREQUEST
DESCRIPTOR.message_types_by_name['EnableSSLResponse'] = _ENABLESSLRESPONSE
DESCRIPTOR.message_types_by_name['EnableSSLMultiRequest'] = _ENABLESSLMULTIREQUEST
DESCRIPTOR.message_types_by_name['EnableSSLMultiResponse'] = _ENABLESSLMULTIRESPONSE
DESCRIPTOR.message_types_by_name['DisableSSLRequest'] = _DISABLESSLREQUEST
DESCRIPTOR.message_types_by_name['DisableSSLResponse'] = _DISABLESSLRESPONSE
DESCRIPTOR.message_types_by_name['DisableSSLMultiRequest'] = _DISABLESSLMULTIREQUEST
DESCRIPTOR.message_types_by_name['DisableSSLMultiResponse'] = _DISABLESSLMULTIRESPONSE
DESCRIPTOR.enum_types_by_name['ConnectionMode'] = _CONNECTIONMODE
DESCRIPTOR.enum_types_by_name['Status'] = _STATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ConnectInfo = _reflection.GeneratedProtocolMessageType('ConnectInfo', (_message.Message,), dict(
  DESCRIPTOR = _CONNECTINFO,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.ConnectInfo)
  ))
_sym_db.RegisterMessage(ConnectInfo)

ConnectRequest = _reflection.GeneratedProtocolMessageType('ConnectRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONNECTREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.ConnectRequest)
  ))
_sym_db.RegisterMessage(ConnectRequest)

ConnectResponse = _reflection.GeneratedProtocolMessageType('ConnectResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONNECTRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.ConnectResponse)
  ))
_sym_db.RegisterMessage(ConnectResponse)

AsyncConnectInfo = _reflection.GeneratedProtocolMessageType('AsyncConnectInfo', (_message.Message,), dict(
  DESCRIPTOR = _ASYNCCONNECTINFO,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.AsyncConnectInfo)
  ))
_sym_db.RegisterMessage(AsyncConnectInfo)

AddAsyncConnectionRequest = _reflection.GeneratedProtocolMessageType('AddAsyncConnectionRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDASYNCCONNECTIONREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.AddAsyncConnectionRequest)
  ))
_sym_db.RegisterMessage(AddAsyncConnectionRequest)

AddAsyncConnectionResponse = _reflection.GeneratedProtocolMessageType('AddAsyncConnectionResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADDASYNCCONNECTIONRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.AddAsyncConnectionResponse)
  ))
_sym_db.RegisterMessage(AddAsyncConnectionResponse)

DeleteAsyncConnectionRequest = _reflection.GeneratedProtocolMessageType('DeleteAsyncConnectionRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEASYNCCONNECTIONREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DeleteAsyncConnectionRequest)
  ))
_sym_db.RegisterMessage(DeleteAsyncConnectionRequest)

DeleteAsyncConnectionResponse = _reflection.GeneratedProtocolMessageType('DeleteAsyncConnectionResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEASYNCCONNECTIONRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DeleteAsyncConnectionResponse)
  ))
_sym_db.RegisterMessage(DeleteAsyncConnectionResponse)

AcceptFilter = _reflection.GeneratedProtocolMessageType('AcceptFilter', (_message.Message,), dict(
  DESCRIPTOR = _ACCEPTFILTER,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.AcceptFilter)
  ))
_sym_db.RegisterMessage(AcceptFilter)

SetAcceptFilterRequest = _reflection.GeneratedProtocolMessageType('SetAcceptFilterRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETACCEPTFILTERREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SetAcceptFilterRequest)
  ))
_sym_db.RegisterMessage(SetAcceptFilterRequest)

SetAcceptFilterResponse = _reflection.GeneratedProtocolMessageType('SetAcceptFilterResponse', (_message.Message,), dict(
  DESCRIPTOR = _SETACCEPTFILTERRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SetAcceptFilterResponse)
  ))
_sym_db.RegisterMessage(SetAcceptFilterResponse)

GetAcceptFilterRequest = _reflection.GeneratedProtocolMessageType('GetAcceptFilterRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETACCEPTFILTERREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.GetAcceptFilterRequest)
  ))
_sym_db.RegisterMessage(GetAcceptFilterRequest)

GetAcceptFilterResponse = _reflection.GeneratedProtocolMessageType('GetAcceptFilterResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETACCEPTFILTERRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.GetAcceptFilterResponse)
  ))
_sym_db.RegisterMessage(GetAcceptFilterResponse)

PendingDeviceInfo = _reflection.GeneratedProtocolMessageType('PendingDeviceInfo', (_message.Message,), dict(
  DESCRIPTOR = _PENDINGDEVICEINFO,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.PendingDeviceInfo)
  ))
_sym_db.RegisterMessage(PendingDeviceInfo)

GetPendingListRequest = _reflection.GeneratedProtocolMessageType('GetPendingListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPENDINGLISTREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.GetPendingListRequest)
  ))
_sym_db.RegisterMessage(GetPendingListRequest)

GetPendingListResponse = _reflection.GeneratedProtocolMessageType('GetPendingListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPENDINGLISTRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.GetPendingListResponse)
  ))
_sym_db.RegisterMessage(GetPendingListResponse)

DeviceInfo = _reflection.GeneratedProtocolMessageType('DeviceInfo', (_message.Message,), dict(
  DESCRIPTOR = _DEVICEINFO,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DeviceInfo)
  ))
_sym_db.RegisterMessage(DeviceInfo)

GetDeviceListRequest = _reflection.GeneratedProtocolMessageType('GetDeviceListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEVICELISTREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.GetDeviceListRequest)
  ))
_sym_db.RegisterMessage(GetDeviceListRequest)

GetDeviceListResponse = _reflection.GeneratedProtocolMessageType('GetDeviceListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDEVICELISTRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.GetDeviceListResponse)
  ))
_sym_db.RegisterMessage(GetDeviceListResponse)

DisconnectRequest = _reflection.GeneratedProtocolMessageType('DisconnectRequest', (_message.Message,), dict(
  DESCRIPTOR = _DISCONNECTREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DisconnectRequest)
  ))
_sym_db.RegisterMessage(DisconnectRequest)

DisconnectResponse = _reflection.GeneratedProtocolMessageType('DisconnectResponse', (_message.Message,), dict(
  DESCRIPTOR = _DISCONNECTRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DisconnectResponse)
  ))
_sym_db.RegisterMessage(DisconnectResponse)

DisconnectAllRequest = _reflection.GeneratedProtocolMessageType('DisconnectAllRequest', (_message.Message,), dict(
  DESCRIPTOR = _DISCONNECTALLREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DisconnectAllRequest)
  ))
_sym_db.RegisterMessage(DisconnectAllRequest)

DisconnectAllResponse = _reflection.GeneratedProtocolMessageType('DisconnectAllResponse', (_message.Message,), dict(
  DESCRIPTOR = _DISCONNECTALLRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DisconnectAllResponse)
  ))
_sym_db.RegisterMessage(DisconnectAllResponse)

SearchDeviceRequest = _reflection.GeneratedProtocolMessageType('SearchDeviceRequest', (_message.Message,), dict(
  DESCRIPTOR = _SEARCHDEVICEREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SearchDeviceRequest)
  ))
_sym_db.RegisterMessage(SearchDeviceRequest)

SearchDeviceInfo = _reflection.GeneratedProtocolMessageType('SearchDeviceInfo', (_message.Message,), dict(
  DESCRIPTOR = _SEARCHDEVICEINFO,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SearchDeviceInfo)
  ))
_sym_db.RegisterMessage(SearchDeviceInfo)

SearchDeviceResponse = _reflection.GeneratedProtocolMessageType('SearchDeviceResponse', (_message.Message,), dict(
  DESCRIPTOR = _SEARCHDEVICERESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SearchDeviceResponse)
  ))
_sym_db.RegisterMessage(SearchDeviceResponse)

SubscribeStatusRequest = _reflection.GeneratedProtocolMessageType('SubscribeStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBSCRIBESTATUSREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SubscribeStatusRequest)
  ))
_sym_db.RegisterMessage(SubscribeStatusRequest)

StatusChange = _reflection.GeneratedProtocolMessageType('StatusChange', (_message.Message,), dict(
  DESCRIPTOR = _STATUSCHANGE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.StatusChange)
  ))
_sym_db.RegisterMessage(StatusChange)

SetConnectionModeRequest = _reflection.GeneratedProtocolMessageType('SetConnectionModeRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETCONNECTIONMODEREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SetConnectionModeRequest)
  ))
_sym_db.RegisterMessage(SetConnectionModeRequest)

SetConnectionModeResponse = _reflection.GeneratedProtocolMessageType('SetConnectionModeResponse', (_message.Message,), dict(
  DESCRIPTOR = _SETCONNECTIONMODERESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SetConnectionModeResponse)
  ))
_sym_db.RegisterMessage(SetConnectionModeResponse)

SetConnectionModeMultiRequest = _reflection.GeneratedProtocolMessageType('SetConnectionModeMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETCONNECTIONMODEMULTIREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SetConnectionModeMultiRequest)
  ))
_sym_db.RegisterMessage(SetConnectionModeMultiRequest)

SetConnectionModeMultiResponse = _reflection.GeneratedProtocolMessageType('SetConnectionModeMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _SETCONNECTIONMODEMULTIRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.SetConnectionModeMultiResponse)
  ))
_sym_db.RegisterMessage(SetConnectionModeMultiResponse)

EnableSSLRequest = _reflection.GeneratedProtocolMessageType('EnableSSLRequest', (_message.Message,), dict(
  DESCRIPTOR = _ENABLESSLREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.EnableSSLRequest)
  ))
_sym_db.RegisterMessage(EnableSSLRequest)

EnableSSLResponse = _reflection.GeneratedProtocolMessageType('EnableSSLResponse', (_message.Message,), dict(
  DESCRIPTOR = _ENABLESSLRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.EnableSSLResponse)
  ))
_sym_db.RegisterMessage(EnableSSLResponse)

EnableSSLMultiRequest = _reflection.GeneratedProtocolMessageType('EnableSSLMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _ENABLESSLMULTIREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.EnableSSLMultiRequest)
  ))
_sym_db.RegisterMessage(EnableSSLMultiRequest)

EnableSSLMultiResponse = _reflection.GeneratedProtocolMessageType('EnableSSLMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _ENABLESSLMULTIRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.EnableSSLMultiResponse)
  ))
_sym_db.RegisterMessage(EnableSSLMultiResponse)

DisableSSLRequest = _reflection.GeneratedProtocolMessageType('DisableSSLRequest', (_message.Message,), dict(
  DESCRIPTOR = _DISABLESSLREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DisableSSLRequest)
  ))
_sym_db.RegisterMessage(DisableSSLRequest)

DisableSSLResponse = _reflection.GeneratedProtocolMessageType('DisableSSLResponse', (_message.Message,), dict(
  DESCRIPTOR = _DISABLESSLRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DisableSSLResponse)
  ))
_sym_db.RegisterMessage(DisableSSLResponse)

DisableSSLMultiRequest = _reflection.GeneratedProtocolMessageType('DisableSSLMultiRequest', (_message.Message,), dict(
  DESCRIPTOR = _DISABLESSLMULTIREQUEST,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DisableSSLMultiRequest)
  ))
_sym_db.RegisterMessage(DisableSSLMultiRequest)

DisableSSLMultiResponse = _reflection.GeneratedProtocolMessageType('DisableSSLMultiResponse', (_message.Message,), dict(
  DESCRIPTOR = _DISABLESSLMULTIRESPONSE,
  __module__ = 'connect_pb2'
  # @@protoc_insertion_point(class_scope:connect.DisableSSLMultiResponse)
  ))
_sym_db.RegisterMessage(DisableSSLMultiResponse)


DESCRIPTOR._options = None
_CONNECTIONMODE._options = None

_CONNECT = _descriptor.ServiceDescriptor(
  name='Connect',
  full_name='connect.Connect',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2629,
  serialized_end=4046,
  methods=[
  _descriptor.MethodDescriptor(
    name='Connect',
    full_name='connect.Connect.Connect',
    index=0,
    containing_service=None,
    input_type=_CONNECTREQUEST,
    output_type=_CONNECTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='AddAsyncConnection',
    full_name='connect.Connect.AddAsyncConnection',
    index=1,
    containing_service=None,
    input_type=_ADDASYNCCONNECTIONREQUEST,
    output_type=_ADDASYNCCONNECTIONRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAsyncConnection',
    full_name='connect.Connect.DeleteAsyncConnection',
    index=2,
    containing_service=None,
    input_type=_DELETEASYNCCONNECTIONREQUEST,
    output_type=_DELETEASYNCCONNECTIONRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='SetAcceptFilter',
    full_name='connect.Connect.SetAcceptFilter',
    index=3,
    containing_service=None,
    input_type=_SETACCEPTFILTERREQUEST,
    output_type=_SETACCEPTFILTERRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetAcceptFilter',
    full_name='connect.Connect.GetAcceptFilter',
    index=4,
    containing_service=None,
    input_type=_GETACCEPTFILTERREQUEST,
    output_type=_GETACCEPTFILTERRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetPendingList',
    full_name='connect.Connect.GetPendingList',
    index=5,
    containing_service=None,
    input_type=_GETPENDINGLISTREQUEST,
    output_type=_GETPENDINGLISTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetDeviceList',
    full_name='connect.Connect.GetDeviceList',
    index=6,
    containing_service=None,
    input_type=_GETDEVICELISTREQUEST,
    output_type=_GETDEVICELISTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Disconnect',
    full_name='connect.Connect.Disconnect',
    index=7,
    containing_service=None,
    input_type=_DISCONNECTREQUEST,
    output_type=_DISCONNECTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='DisconnectAll',
    full_name='connect.Connect.DisconnectAll',
    index=8,
    containing_service=None,
    input_type=_DISCONNECTALLREQUEST,
    output_type=_DISCONNECTALLRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='SearchDevice',
    full_name='connect.Connect.SearchDevice',
    index=9,
    containing_service=None,
    input_type=_SEARCHDEVICEREQUEST,
    output_type=_SEARCHDEVICERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='SetConnectionMode',
    full_name='connect.Connect.SetConnectionMode',
    index=10,
    containing_service=None,
    input_type=_SETCONNECTIONMODEREQUEST,
    output_type=_SETCONNECTIONMODERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='SetConnectionModeMulti',
    full_name='connect.Connect.SetConnectionModeMulti',
    index=11,
    containing_service=None,
    input_type=_SETCONNECTIONMODEMULTIREQUEST,
    output_type=_SETCONNECTIONMODEMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='EnableSSL',
    full_name='connect.Connect.EnableSSL',
    index=12,
    containing_service=None,
    input_type=_ENABLESSLREQUEST,
    output_type=_ENABLESSLRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='EnableSSLMulti',
    full_name='connect.Connect.EnableSSLMulti',
    index=13,
    containing_service=None,
    input_type=_ENABLESSLMULTIREQUEST,
    output_type=_ENABLESSLMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='DisableSSL',
    full_name='connect.Connect.DisableSSL',
    index=14,
    containing_service=None,
    input_type=_DISABLESSLREQUEST,
    output_type=_DISABLESSLRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='DisableSSLMulti',
    full_name='connect.Connect.DisableSSLMulti',
    index=15,
    containing_service=None,
    input_type=_DISABLESSLMULTIREQUEST,
    output_type=_DISABLESSLMULTIRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='SubscribeStatus',
    full_name='connect.Connect.SubscribeStatus',
    index=16,
    containing_service=None,
    input_type=_SUBSCRIBESTATUSREQUEST,
    output_type=_STATUSCHANGE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_CONNECT)

DESCRIPTOR.services_by_name['Connect'] = _CONNECT

# @@protoc_insertion_point(module_scope)
