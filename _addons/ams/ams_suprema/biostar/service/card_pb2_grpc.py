# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import card_pb2 as card__pb2


class CardStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Scan = channel.unary_unary(
                '/gsdk.card.Card/Scan',
                request_serializer=card__pb2.ScanRequest.SerializeToString,
                response_deserializer=card__pb2.ScanResponse.FromString,
                )
        self.Erase = channel.unary_unary(
                '/gsdk.card.Card/Erase',
                request_serializer=card__pb2.EraseRequest.SerializeToString,
                response_deserializer=card__pb2.EraseResponse.FromString,
                )
        self.Write = channel.unary_unary(
                '/gsdk.card.Card/Write',
                request_serializer=card__pb2.WriteRequest.SerializeToString,
                response_deserializer=card__pb2.WriteResponse.FromString,
                )
        self.GetConfig = channel.unary_unary(
                '/gsdk.card.Card/GetConfig',
                request_serializer=card__pb2.GetConfigRequest.SerializeToString,
                response_deserializer=card__pb2.GetConfigResponse.FromString,
                )
        self.SetConfig = channel.unary_unary(
                '/gsdk.card.Card/SetConfig',
                request_serializer=card__pb2.SetConfigRequest.SerializeToString,
                response_deserializer=card__pb2.SetConfigResponse.FromString,
                )
        self.SetConfigMulti = channel.unary_unary(
                '/gsdk.card.Card/SetConfigMulti',
                request_serializer=card__pb2.SetConfigMultiRequest.SerializeToString,
                response_deserializer=card__pb2.SetConfigMultiResponse.FromString,
                )
        self.GetBlacklist = channel.unary_unary(
                '/gsdk.card.Card/GetBlacklist',
                request_serializer=card__pb2.GetBlacklistRequest.SerializeToString,
                response_deserializer=card__pb2.GetBlacklistResponse.FromString,
                )
        self.AddBlacklist = channel.unary_unary(
                '/gsdk.card.Card/AddBlacklist',
                request_serializer=card__pb2.AddBlacklistRequest.SerializeToString,
                response_deserializer=card__pb2.AddBlacklistResponse.FromString,
                )
        self.AddBlacklistMulti = channel.unary_unary(
                '/gsdk.card.Card/AddBlacklistMulti',
                request_serializer=card__pb2.AddBlacklistMultiRequest.SerializeToString,
                response_deserializer=card__pb2.AddBlacklistMultiResponse.FromString,
                )
        self.DeleteBlacklist = channel.unary_unary(
                '/gsdk.card.Card/DeleteBlacklist',
                request_serializer=card__pb2.DeleteBlacklistRequest.SerializeToString,
                response_deserializer=card__pb2.DeleteBlacklistResponse.FromString,
                )
        self.DeleteBlacklistMulti = channel.unary_unary(
                '/gsdk.card.Card/DeleteBlacklistMulti',
                request_serializer=card__pb2.DeleteBlacklistMultiRequest.SerializeToString,
                response_deserializer=card__pb2.DeleteBlacklistMultiResponse.FromString,
                )
        self.DeleteAllBlacklist = channel.unary_unary(
                '/gsdk.card.Card/DeleteAllBlacklist',
                request_serializer=card__pb2.DeleteAllBlacklistRequest.SerializeToString,
                response_deserializer=card__pb2.DeleteAllBlacklistResponse.FromString,
                )
        self.DeleteAllBlacklistMulti = channel.unary_unary(
                '/gsdk.card.Card/DeleteAllBlacklistMulti',
                request_serializer=card__pb2.DeleteAllBlacklistMultiRequest.SerializeToString,
                response_deserializer=card__pb2.DeleteAllBlacklistMultiResponse.FromString,
                )
        self.Get1XConfig = channel.unary_unary(
                '/gsdk.card.Card/Get1XConfig',
                request_serializer=card__pb2.Get1XConfigRequest.SerializeToString,
                response_deserializer=card__pb2.Get1XConfigResponse.FromString,
                )
        self.Set1XConfig = channel.unary_unary(
                '/gsdk.card.Card/Set1XConfig',
                request_serializer=card__pb2.Set1XConfigRequest.SerializeToString,
                response_deserializer=card__pb2.Set1XConfigResponse.FromString,
                )
        self.Set1XConfigMulti = channel.unary_unary(
                '/gsdk.card.Card/Set1XConfigMulti',
                request_serializer=card__pb2.Set1XConfigMultiRequest.SerializeToString,
                response_deserializer=card__pb2.Set1XConfigMultiResponse.FromString,
                )
        self.WriteQRCode = channel.unary_unary(
                '/gsdk.card.Card/WriteQRCode',
                request_serializer=card__pb2.WriteQRCodeRequest.SerializeToString,
                response_deserializer=card__pb2.WriteQRCodeResponse.FromString,
                )
        self.GetQRConfig = channel.unary_unary(
                '/gsdk.card.Card/GetQRConfig',
                request_serializer=card__pb2.GetQRConfigRequest.SerializeToString,
                response_deserializer=card__pb2.GetQRConfigResponse.FromString,
                )
        self.SetQRConfig = channel.unary_unary(
                '/gsdk.card.Card/SetQRConfig',
                request_serializer=card__pb2.SetQRConfigRequest.SerializeToString,
                response_deserializer=card__pb2.SetQRConfigResponse.FromString,
                )
        self.SetQRConfigMulti = channel.unary_unary(
                '/gsdk.card.Card/SetQRConfigMulti',
                request_serializer=card__pb2.SetQRConfigMultiRequest.SerializeToString,
                response_deserializer=card__pb2.SetQRConfigMultiResponse.FromString,
                )


class CardServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Scan(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Erase(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Write(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConfigMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBlacklist(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddBlacklist(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddBlacklistMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteBlacklist(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteBlacklistMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAllBlacklist(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAllBlacklistMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Get1XConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Set1XConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Set1XConfigMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def WriteQRCode(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetQRConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetQRConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetQRConfigMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CardServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Scan': grpc.unary_unary_rpc_method_handler(
                    servicer.Scan,
                    request_deserializer=card__pb2.ScanRequest.FromString,
                    response_serializer=card__pb2.ScanResponse.SerializeToString,
            ),
            'Erase': grpc.unary_unary_rpc_method_handler(
                    servicer.Erase,
                    request_deserializer=card__pb2.EraseRequest.FromString,
                    response_serializer=card__pb2.EraseResponse.SerializeToString,
            ),
            'Write': grpc.unary_unary_rpc_method_handler(
                    servicer.Write,
                    request_deserializer=card__pb2.WriteRequest.FromString,
                    response_serializer=card__pb2.WriteResponse.SerializeToString,
            ),
            'GetConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConfig,
                    request_deserializer=card__pb2.GetConfigRequest.FromString,
                    response_serializer=card__pb2.GetConfigResponse.SerializeToString,
            ),
            'SetConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConfig,
                    request_deserializer=card__pb2.SetConfigRequest.FromString,
                    response_serializer=card__pb2.SetConfigResponse.SerializeToString,
            ),
            'SetConfigMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConfigMulti,
                    request_deserializer=card__pb2.SetConfigMultiRequest.FromString,
                    response_serializer=card__pb2.SetConfigMultiResponse.SerializeToString,
            ),
            'GetBlacklist': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBlacklist,
                    request_deserializer=card__pb2.GetBlacklistRequest.FromString,
                    response_serializer=card__pb2.GetBlacklistResponse.SerializeToString,
            ),
            'AddBlacklist': grpc.unary_unary_rpc_method_handler(
                    servicer.AddBlacklist,
                    request_deserializer=card__pb2.AddBlacklistRequest.FromString,
                    response_serializer=card__pb2.AddBlacklistResponse.SerializeToString,
            ),
            'AddBlacklistMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.AddBlacklistMulti,
                    request_deserializer=card__pb2.AddBlacklistMultiRequest.FromString,
                    response_serializer=card__pb2.AddBlacklistMultiResponse.SerializeToString,
            ),
            'DeleteBlacklist': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteBlacklist,
                    request_deserializer=card__pb2.DeleteBlacklistRequest.FromString,
                    response_serializer=card__pb2.DeleteBlacklistResponse.SerializeToString,
            ),
            'DeleteBlacklistMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteBlacklistMulti,
                    request_deserializer=card__pb2.DeleteBlacklistMultiRequest.FromString,
                    response_serializer=card__pb2.DeleteBlacklistMultiResponse.SerializeToString,
            ),
            'DeleteAllBlacklist': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAllBlacklist,
                    request_deserializer=card__pb2.DeleteAllBlacklistRequest.FromString,
                    response_serializer=card__pb2.DeleteAllBlacklistResponse.SerializeToString,
            ),
            'DeleteAllBlacklistMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAllBlacklistMulti,
                    request_deserializer=card__pb2.DeleteAllBlacklistMultiRequest.FromString,
                    response_serializer=card__pb2.DeleteAllBlacklistMultiResponse.SerializeToString,
            ),
            'Get1XConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.Get1XConfig,
                    request_deserializer=card__pb2.Get1XConfigRequest.FromString,
                    response_serializer=card__pb2.Get1XConfigResponse.SerializeToString,
            ),
            'Set1XConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.Set1XConfig,
                    request_deserializer=card__pb2.Set1XConfigRequest.FromString,
                    response_serializer=card__pb2.Set1XConfigResponse.SerializeToString,
            ),
            'Set1XConfigMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.Set1XConfigMulti,
                    request_deserializer=card__pb2.Set1XConfigMultiRequest.FromString,
                    response_serializer=card__pb2.Set1XConfigMultiResponse.SerializeToString,
            ),
            'WriteQRCode': grpc.unary_unary_rpc_method_handler(
                    servicer.WriteQRCode,
                    request_deserializer=card__pb2.WriteQRCodeRequest.FromString,
                    response_serializer=card__pb2.WriteQRCodeResponse.SerializeToString,
            ),
            'GetQRConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetQRConfig,
                    request_deserializer=card__pb2.GetQRConfigRequest.FromString,
                    response_serializer=card__pb2.GetQRConfigResponse.SerializeToString,
            ),
            'SetQRConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetQRConfig,
                    request_deserializer=card__pb2.SetQRConfigRequest.FromString,
                    response_serializer=card__pb2.SetQRConfigResponse.SerializeToString,
            ),
            'SetQRConfigMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.SetQRConfigMulti,
                    request_deserializer=card__pb2.SetQRConfigMultiRequest.FromString,
                    response_serializer=card__pb2.SetQRConfigMultiResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.card.Card', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Card(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Scan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/Scan',
            card__pb2.ScanRequest.SerializeToString,
            card__pb2.ScanResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Erase(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/Erase',
            card__pb2.EraseRequest.SerializeToString,
            card__pb2.EraseResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Write(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/Write',
            card__pb2.WriteRequest.SerializeToString,
            card__pb2.WriteResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/GetConfig',
            card__pb2.GetConfigRequest.SerializeToString,
            card__pb2.GetConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/SetConfig',
            card__pb2.SetConfigRequest.SerializeToString,
            card__pb2.SetConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetConfigMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/SetConfigMulti',
            card__pb2.SetConfigMultiRequest.SerializeToString,
            card__pb2.SetConfigMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetBlacklist(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/GetBlacklist',
            card__pb2.GetBlacklistRequest.SerializeToString,
            card__pb2.GetBlacklistResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddBlacklist(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/AddBlacklist',
            card__pb2.AddBlacklistRequest.SerializeToString,
            card__pb2.AddBlacklistResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddBlacklistMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/AddBlacklistMulti',
            card__pb2.AddBlacklistMultiRequest.SerializeToString,
            card__pb2.AddBlacklistMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteBlacklist(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/DeleteBlacklist',
            card__pb2.DeleteBlacklistRequest.SerializeToString,
            card__pb2.DeleteBlacklistResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteBlacklistMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/DeleteBlacklistMulti',
            card__pb2.DeleteBlacklistMultiRequest.SerializeToString,
            card__pb2.DeleteBlacklistMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAllBlacklist(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/DeleteAllBlacklist',
            card__pb2.DeleteAllBlacklistRequest.SerializeToString,
            card__pb2.DeleteAllBlacklistResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAllBlacklistMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/DeleteAllBlacklistMulti',
            card__pb2.DeleteAllBlacklistMultiRequest.SerializeToString,
            card__pb2.DeleteAllBlacklistMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Get1XConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/Get1XConfig',
            card__pb2.Get1XConfigRequest.SerializeToString,
            card__pb2.Get1XConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Set1XConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/Set1XConfig',
            card__pb2.Set1XConfigRequest.SerializeToString,
            card__pb2.Set1XConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Set1XConfigMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/Set1XConfigMulti',
            card__pb2.Set1XConfigMultiRequest.SerializeToString,
            card__pb2.Set1XConfigMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def WriteQRCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/WriteQRCode',
            card__pb2.WriteQRCodeRequest.SerializeToString,
            card__pb2.WriteQRCodeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetQRConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/GetQRConfig',
            card__pb2.GetQRConfigRequest.SerializeToString,
            card__pb2.GetQRConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetQRConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/SetQRConfig',
            card__pb2.SetQRConfigRequest.SerializeToString,
            card__pb2.SetQRConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetQRConfigMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.card.Card/SetQRConfigMulti',
            card__pb2.SetQRConfigMultiRequest.SerializeToString,
            card__pb2.SetQRConfigMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
