# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: udp_master.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import network_pb2 as network__pb2
import udp_pb2 as udp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10udp_master.proto\x12\x0fgsdk.udp_master\x1a\rnetwork.proto\x1a\tudp.proto\"Q\n\x12GetIPConfigRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12(\n\ndeviceInfo\x18\x02 \x01(\x0b\x32\x14.gsdk.udp.DeviceInfo\"=\n\x13GetIPConfigResponse\x12&\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x16.gsdk.network.IPConfig\"y\n\x12SetIPConfigRequest\x12\x11\n\tgatewayID\x18\x01 \x01(\t\x12(\n\ndeviceInfo\x18\x02 \x01(\x0b\x32\x14.gsdk.udp.DeviceInfo\x12&\n\x06\x63onfig\x18\x03 \x01(\x0b\x32\x16.gsdk.network.IPConfig\"\x15\n\x13SetIPConfigResponse2\xbf\x01\n\tUDPMaster\x12X\n\x0bGetIPConfig\x12#.gsdk.udp_master.GetIPConfigRequest\x1a$.gsdk.udp_master.GetIPConfigResponse\x12X\n\x0bSetIPConfig\x12#.gsdk.udp_master.SetIPConfigRequest\x1a$.gsdk.udp_master.SetIPConfigResponseB<\n\x1d\x63om.supremainc.sdk.udp_masterP\x01Z\x19\x62iostar/service/udpMasterb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'udp_master_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\035com.supremainc.sdk.udp_masterP\001Z\031biostar/service/udpMaster'
  _GETIPCONFIGREQUEST._serialized_start=63
  _GETIPCONFIGREQUEST._serialized_end=144
  _GETIPCONFIGRESPONSE._serialized_start=146
  _GETIPCONFIGRESPONSE._serialized_end=207
  _SETIPCONFIGREQUEST._serialized_start=209
  _SETIPCONFIGREQUEST._serialized_end=330
  _SETIPCONFIGRESPONSE._serialized_start=332
  _SETIPCONFIGRESPONSE._serialized_end=353
  _UDPMASTER._serialized_start=356
  _UDPMASTER._serialized_end=547
# @@protoc_insertion_point(module_scope)
