# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import thermal_pb2 as thermal__pb2


class ThermalStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetConfig = channel.unary_unary(
                '/gsdk.thermal.Thermal/GetConfig',
                request_serializer=thermal__pb2.GetConfigRequest.SerializeToString,
                response_deserializer=thermal__pb2.GetConfigResponse.FromString,
                )
        self.SetConfig = channel.unary_unary(
                '/gsdk.thermal.Thermal/SetConfig',
                request_serializer=thermal__pb2.SetConfigRequest.SerializeToString,
                response_deserializer=thermal__pb2.SetConfigResponse.FromString,
                )
        self.SetConfigMulti = channel.unary_unary(
                '/gsdk.thermal.Thermal/SetConfigMulti',
                request_serializer=thermal__pb2.SetConfigMultiRequest.SerializeToString,
                response_deserializer=thermal__pb2.SetConfigMultiResponse.FromString,
                )
        self.GetTemperatureLog = channel.unary_unary(
                '/gsdk.thermal.Thermal/GetTemperatureLog',
                request_serializer=thermal__pb2.GetTemperatureLogRequest.SerializeToString,
                response_deserializer=thermal__pb2.GetTemperatureLogResponse.FromString,
                )


class ThermalServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConfigMulti(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTemperatureLog(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ThermalServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConfig,
                    request_deserializer=thermal__pb2.GetConfigRequest.FromString,
                    response_serializer=thermal__pb2.GetConfigResponse.SerializeToString,
            ),
            'SetConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConfig,
                    request_deserializer=thermal__pb2.SetConfigRequest.FromString,
                    response_serializer=thermal__pb2.SetConfigResponse.SerializeToString,
            ),
            'SetConfigMulti': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConfigMulti,
                    request_deserializer=thermal__pb2.SetConfigMultiRequest.FromString,
                    response_serializer=thermal__pb2.SetConfigMultiResponse.SerializeToString,
            ),
            'GetTemperatureLog': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTemperatureLog,
                    request_deserializer=thermal__pb2.GetTemperatureLogRequest.FromString,
                    response_serializer=thermal__pb2.GetTemperatureLogResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.thermal.Thermal', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Thermal(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.thermal.Thermal/GetConfig',
            thermal__pb2.GetConfigRequest.SerializeToString,
            thermal__pb2.GetConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.thermal.Thermal/SetConfig',
            thermal__pb2.SetConfigRequest.SerializeToString,
            thermal__pb2.SetConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetConfigMulti(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.thermal.Thermal/SetConfigMulti',
            thermal__pb2.SetConfigMultiRequest.SerializeToString,
            thermal__pb2.SetConfigMultiResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTemperatureLog(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.thermal.Thermal/GetTemperatureLog',
            thermal__pb2.GetTemperatureLogRequest.SerializeToString,
            thermal__pb2.GetTemperatureLogResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
