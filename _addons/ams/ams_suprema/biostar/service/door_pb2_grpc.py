# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import door_pb2 as door__pb2


class DoorStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetList = channel.unary_unary(
                '/gsdk.door.Door/GetList',
                request_serializer=door__pb2.GetListRequest.SerializeToString,
                response_deserializer=door__pb2.GetListResponse.FromString,
                )
        self.GetStatus = channel.unary_unary(
                '/gsdk.door.Door/GetStatus',
                request_serializer=door__pb2.GetStatusRequest.SerializeToString,
                response_deserializer=door__pb2.GetStatusResponse.FromString,
                )
        self.Add = channel.unary_unary(
                '/gsdk.door.Door/Add',
                request_serializer=door__pb2.AddRequest.SerializeToString,
                response_deserializer=door__pb2.AddResponse.FromString,
                )
        self.Delete = channel.unary_unary(
                '/gsdk.door.Door/Delete',
                request_serializer=door__pb2.DeleteRequest.SerializeToString,
                response_deserializer=door__pb2.DeleteResponse.FromString,
                )
        self.DeleteAll = channel.unary_unary(
                '/gsdk.door.Door/DeleteAll',
                request_serializer=door__pb2.DeleteAllRequest.SerializeToString,
                response_deserializer=door__pb2.DeleteAllResponse.FromString,
                )
        self.Lock = channel.unary_unary(
                '/gsdk.door.Door/Lock',
                request_serializer=door__pb2.LockRequest.SerializeToString,
                response_deserializer=door__pb2.LockResponse.FromString,
                )
        self.Unlock = channel.unary_unary(
                '/gsdk.door.Door/Unlock',
                request_serializer=door__pb2.UnlockRequest.SerializeToString,
                response_deserializer=door__pb2.UnlockResponse.FromString,
                )
        self.Release = channel.unary_unary(
                '/gsdk.door.Door/Release',
                request_serializer=door__pb2.ReleaseRequest.SerializeToString,
                response_deserializer=door__pb2.ReleaseResponse.FromString,
                )
        self.SetAlarm = channel.unary_unary(
                '/gsdk.door.Door/SetAlarm',
                request_serializer=door__pb2.SetAlarmRequest.SerializeToString,
                response_deserializer=door__pb2.SetAlarmResponse.FromString,
                )


class DoorServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Add(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Delete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAll(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Lock(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Unlock(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Release(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetAlarm(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DoorServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetList,
                    request_deserializer=door__pb2.GetListRequest.FromString,
                    response_serializer=door__pb2.GetListResponse.SerializeToString,
            ),
            'GetStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStatus,
                    request_deserializer=door__pb2.GetStatusRequest.FromString,
                    response_serializer=door__pb2.GetStatusResponse.SerializeToString,
            ),
            'Add': grpc.unary_unary_rpc_method_handler(
                    servicer.Add,
                    request_deserializer=door__pb2.AddRequest.FromString,
                    response_serializer=door__pb2.AddResponse.SerializeToString,
            ),
            'Delete': grpc.unary_unary_rpc_method_handler(
                    servicer.Delete,
                    request_deserializer=door__pb2.DeleteRequest.FromString,
                    response_serializer=door__pb2.DeleteResponse.SerializeToString,
            ),
            'DeleteAll': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAll,
                    request_deserializer=door__pb2.DeleteAllRequest.FromString,
                    response_serializer=door__pb2.DeleteAllResponse.SerializeToString,
            ),
            'Lock': grpc.unary_unary_rpc_method_handler(
                    servicer.Lock,
                    request_deserializer=door__pb2.LockRequest.FromString,
                    response_serializer=door__pb2.LockResponse.SerializeToString,
            ),
            'Unlock': grpc.unary_unary_rpc_method_handler(
                    servicer.Unlock,
                    request_deserializer=door__pb2.UnlockRequest.FromString,
                    response_serializer=door__pb2.UnlockResponse.SerializeToString,
            ),
            'Release': grpc.unary_unary_rpc_method_handler(
                    servicer.Release,
                    request_deserializer=door__pb2.ReleaseRequest.FromString,
                    response_serializer=door__pb2.ReleaseResponse.SerializeToString,
            ),
            'SetAlarm': grpc.unary_unary_rpc_method_handler(
                    servicer.SetAlarm,
                    request_deserializer=door__pb2.SetAlarmRequest.FromString,
                    response_serializer=door__pb2.SetAlarmResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gsdk.door.Door', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Door(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/GetList',
            door__pb2.GetListRequest.SerializeToString,
            door__pb2.GetListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/GetStatus',
            door__pb2.GetStatusRequest.SerializeToString,
            door__pb2.GetStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Add(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/Add',
            door__pb2.AddRequest.SerializeToString,
            door__pb2.AddResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Delete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/Delete',
            door__pb2.DeleteRequest.SerializeToString,
            door__pb2.DeleteResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAll(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/DeleteAll',
            door__pb2.DeleteAllRequest.SerializeToString,
            door__pb2.DeleteAllResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Lock(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/Lock',
            door__pb2.LockRequest.SerializeToString,
            door__pb2.LockResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Unlock(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/Unlock',
            door__pb2.UnlockRequest.SerializeToString,
            door__pb2.UnlockResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Release(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/Release',
            door__pb2.ReleaseRequest.SerializeToString,
            door__pb2.ReleaseResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetAlarm(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/gsdk.door.Door/SetAlarm',
            door__pb2.SetAlarmRequest.SerializeToString,
            door__pb2.SetAlarmResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
