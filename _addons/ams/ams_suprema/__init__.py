#!/usr/bin/python


# from . import biostar
# from biostar import service
# from biostar import service

import os
import sys
from pprint import pprint

# region --- add biostar service library using system path
# TODO add in  PYTHONPATH environment
gsdk_path = os.path.join(os.path.dirname(__file__), 'gsdk')
if os.path.exists(gsdk_path):
    sys.path.append(gsdk_path)

gsdk_custom_path = os.path.join(os.path.dirname(__file__), 'gsdk_custom')
if os.path.exists(gsdk_custom_path):
    sys.path.append(gsdk_custom_path)

biostar_path = os.path.join(os.path.dirname(__file__), 'biostar')
if os.path.exists(biostar_path):
    sys.path.append(biostar_path)

# Add 'biostar/service' sub folder
service_path = os.path.join(os.path.dirname(__file__), 'biostar', 'service')
if os.path.exists(service_path):
    sys.path.append(service_path)

# Add 'biostar/proto' sub folder
proto_path = os.path.join(os.path.dirname(__file__), 'biostar', 'proto')
if os.path.exists(proto_path):
    sys.path.append(proto_path)

# pprint(sys.path)
# endregion
from . import connector
from . import models
from . import api
from . import wizard
from . import connector
connector.Connector.register_connection() # register connection on module load
