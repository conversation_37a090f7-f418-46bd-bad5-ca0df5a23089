<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="deviceLog_view_form" model="ir.ui.view">
        <field name="name">ams_suprema.device_log.form</field>
        <field name="model">ams_suprema.device_log</field>
        <field name="arch" type="xml">
            <form edit="0">
                <header>
                    <field name="state" widget="statusbar"/>
                    <button class="btn-primary" name="action_execute_log" string="Execute" type="object"
                            title="transfer user device log to punch log in time attendance "/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="device_id"/>
                            <field name="log_time"/>
                            <field name="executed_datetime"/>
                            <field name="code_name"/>
                            <field name="event_id"/>
                            <field name="event_code"/>
                            <field name="sub_code"/>
                            <field name="enroll_name"/>
                            <field name="name" invisible="1"/>

                        </group>
                        <group>
                            <field name="description"/>
                            <field name="timestamp" readonly="True"/>
                            <field name="enroll_number"/>
                            <field name="temperature"/>
                            <field name="has_image"/>
                            <field name="card_data"/>

                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_list-->
    <record id="device_log_view_list" model="ir.ui.view">
        <field name="name">ams_suprema.device_log.list</field>
        <field name="model">ams_suprema.device_log</field>
        <field name="arch" type="xml">
            <list string="Desc" create="0" edit="0" duplicate="0" delete="1">
                <header>
                    <button class="btn-primary" name="action_execute_log" string="Execute" type="object"
                            title="transfer user device log to punch log in time attendance "/>
                </header>
                <field name="event_device_id"/>
                <field name="log_time"/>
                <field name="description" optional="show"/>
                <field name="code_name" optional="hide"/>
                <field name="name" optional="hide"/>
                <field name="device_id"/>
                <field name="event_id" optional="hide"/>
                <field name="event_code" optional="hide"/>
                <field name="state" optional="show"/>
                <field name="enroll_number"/>
                <field name="enroll_name"/>
                <field name="log_date" optional="hide"/>
                <field name="card_data" optional="hide"/>

            </list>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_search-->
    <record id="device_log_view_search" model="ir.ui.view">
        <field name="name">ams_suprema.device_log.search</field>
        <field name="model">ams_suprema.device_log</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="enroll_number"/>
                <field name="event_device_id"/>
                <field name="description"/>

                <field name="event_code"/>
                <field name="device_id"/>
                <field name="event_id"/>
                <filter name="is_ta" string="Punches" domain="[('is_ta','=',True)]"/>
                <separator/>
                <filter string="Pending" name="pending" domain="[('state', '=','pending')]"/>
                <filter string="Executed" name="executed" domain="[('state', '=','executed')]"/>

                <filter string="Today" name="today"
                        domain="[('date', '=', context_today().strftime('%Y-%m-%d'))]"/>

                <filter string="Yesterday" name="yesterday"
                        domain="[('date', '=', (context_today()- relativedelta(days=1)).strftime('%Y-%m-%d'))]"/>


                <group expand="1" string="Group By">
                    <filter string="Device" name='event_device_id' context="{'group_by':'event_device_id'}"/>
                    <filter string="Device ID" name='device_id' context="{'group_by':'device_id'}"/>
                    <filter string="Event Code" name='event_code' context="{'group_by':'event_code'}"/>
                    <filter string="Code Name" name='code_name' context="{'group_by':'code_name'}"/>
                    <filter string="Event Description" name='description' context="{'group_by':'description'}"/>
                    <filter string="Enroll Number" name='enroll_number' context="{'group_by':'enroll_number'}"/>
                    <filter string="Date" name="group_by_date" context="{'group_by': 'date'}"/>

                </group>
            </search>
        </field>
    </record>


    <!--TODO[IMP]: <model_name> _action-->
    <record id="device_log_action" model="ir.actions.act_window">
        <field name="name">Device Logs</field>
        <field name="res_model">ams_suprema.device_log</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
