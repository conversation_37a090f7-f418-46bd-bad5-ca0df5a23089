<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="user_view_form" model="ir.ui.view">
        <field name="name">ams_suprema.user.form</field>
        <field name="model">ams_suprema.user</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <!--                    <button class="btn-primary" name="action_open_transfer_wizard" string="Transfer" type="object"/>-->
                    <button class="btn-primary" name="action_enroll" string="Enroll ⇋ Sync" type="object" icon="fa-rocket"
                            title="create device actions to enroll or update user to selected devices by transfer wizard"/>
                    <button class="btn btn-danger" name="action_delete_user" string="Delete From Devices"
                            title="create device actions to delete user from selected devices by transfer wizard"
                            type="object"/>
                </header>
                <sheet>
                    <div name="button_box" class="oe_button_box">
                        <button type="object" name="action_open_transfer_wizard" class="oe_stat_button"
                                icon="fa-exchange" style="width: 100px !important;" string="Transfer">

                        </button>
                        <button type="object" name="action_open_device_action_views" class="oe_stat_button"
                                icon="fa-list" style="width: 100px !important;" string="Action Queue">

                        </button>

                    </div>

                    <group>
                        <group>
                            <field name="name"/>
                            <field name="enroll_device_id" readonly="enrollment_done == True"/>
                            <label for="card"/>
                            <div class="o_row">
                                <field id="card" name="card_no" readonly="enrollment_done == True"/>
                                <button class="btn btn-sm btn-outline-success fa fa-plus-circle p-1" context="{'type': 'card'}"
                                        name="action_scan" title="scan card" type="object"/>
                            </div>
                            <field name="finger_quality" readonly="enrollment_done == True"/>
                        </group>
                        <group>
                            <field name="employee_id" invisible="user_type == 'visitor' or user_type == False"
                                   readonly="enrollment_done == True"/>
                            <field name="user_type" readonly="enrollment_done == True"/>
                            <field name="enroll_number" readonly="enrollment_done == True"/>
                            <field name="device_group_id"/>
                        </group>

                    </group>
                    <div class="container-fluid">
                        <div class="row">
                            <!--                            Fingers enrollment-->
                            <div class="col-6">
                                <div class="row">
                                    <!--                                        finger1 enrollment -->
                                    <div class="col-6">
                                        <strong>
                                            <span class="text-muted mt-2 mx-4">
                                                Finger 1
                                            </span>
                                        </strong>
                                        <div class=" finger1">
                                            <field invisible="1" name="finger1_saved"/>
                                            <img alt="nofp1" invisible="finger1_saved == True"
                                                 src="/ams_suprema/static/img/nofp.png"/>
                                            <img alt="fp1" invisible="finger1_saved == False"
                                                 src="/ams_suprema/static/img/fp.png"/>
                                        </div>
                                        <button class="btn btn-sm btn-outline-success fa fa-plus-circle" context="{'type': 'finger1'}"
                                                name="action_scan" title="scan" type="object"/>
                                        <button class="btn btn-sm btn-danger fa fa-minus-circle m-2"
                                                context="{'type': 'finger1'}" name="action_remove" title="del"
                                                type="object"/>
                                    </div>
                                    <!--                                        finger2 enrollment-->
                                    <div class="col-6">
                                        <strong>
                                            <span class="text-muted mt-2 mx-4">
                                                Finger 2
                                            </span>
                                        </strong>
                                        <div class=" finger2">
                                            <field invisible="1" name="finger2_saved"/>
                                            <img alt="nofp1" invisible="finger2_saved == True"
                                                 src="/ams_suprema/static/img/nofp.png"/>
                                            <img alt="fp1" invisible="finger2_saved == False"
                                                 src="/ams_suprema/static/img/fp.png"/>
                                        </div>
                                        <button class="btn btn-sm btn-outline-success fa fa-plus-circle m-2" context="{'type': 'finger2'}"
                                                name="action_scan" title="scan" type="object"/>
                                        <button class="btn btn-sm btn-danger fa fa-minus-circle m-2"
                                                context="{'type': 'finger2'}" name="action_remove" title="del"
                                                type="object"/>
                                    </div>
                                </div>
                            </div>
                            <!--                            Face enrollment-->
                            <div class="col-6">
                                <strong>
                                    <span class="text-muted mt-2 mx-4">
                                        Face
                                    </span>
                                </strong>
                                <div class="face">
                                    <field invisible="1" name="face_saved"/>
                                    <img alt="noface" invisible="face_saved == True"
                                         src="/ams_suprema/static/img/noface.png"/>
                                    <img alt="face" invisible="face_saved == False"
                                         src="/ams_suprema/static/img/face.png"/>
                                </div>
                                <button class="btn btn-sm btn-outline-success fa fa-plus-circle m-2" context="{'type': 'face'}"
                                        name="action_scan" title="scan" type="object"/>
                                <button class="btn btn-sm btn-danger fa fa-minus-circle m-2" context="{'type': 'face'}"
                                        name="action_remove" title="del" type="object"/>
                            </div>
                        </div>
                    </div>
                    <group>
                        <field class="text-danger" name="error_msg" readonly="1"/>
                    </group>
                    <notebook>
                        <page string="Setting">
                            <group>
                                <group>
                                    <field name="start_datetime" readonly="enrollment_done == True" required="1"/>
                                    <field name="end_datetime" readonly="enrollment_done == True" required="1"/>
                                    <field name="response_code" readonly="1"/>
                                    <field name="enrollment_done" readonly="0"/>
                                    <field name="sync_user" readonly="1"/>
                                </group>
                                 <group>
                                    <field name="card_auth_mode_domain" invisible="1"/>
                                    <field name="fingerprint_auth_mode_domain" invisible="1"/>
                                    <field name="face_auth_mode_domain" invisible="1"/>
                                    <field name="id_auth_mode_domain" invisible="1"/>

                                    <field name="ext_auth_supported"/>
                                    <field name="card_auth_mode_id" domain="card_auth_mode_domain"/>
                                    <field name="fingerprint_auth_mode_id" domain="fingerprint_auth_mode_domain"/>
                                    <field name="face_auth_mode_id" domain="face_auth_mode_domain"/>
                                    <field name="id_auth_mode_id" domain="id_auth_mode_domain"/>


                                </group>
                            </group>
                        </page>
                        <page name="device" string="Devices">
                            <div>
                                <span>
                                    <p class="text-muted">
                                        <i class="fa fa-info-circle"></i>
                                        click on transfer action to add or delete devices
                                    </p>

                                </span>
                            </div>
                            <group>
                                <group>
                                    <field force_save="1" name="device_ids" readonly="1" widget="many2many_tags"/>
                                </group>
                            </group>
                        </page>
                        <page groups="base.group_no_one" name="binary_info" string="Scan Info">
                            <group>
                                <group string="Finger Data">
                                    <field groups="base.group_no_one" name="finger_template_format"/>
                                    <field name="finger1_template1"/>
                                    <field name="finger1_template2"/>
                                    <field name="finger2_template1"/>
                                    <field name="finger2_template2"/>
                                </group>
                                <group string="Card Data | Face Data">
                                    <field name="card_data"/>
                                    <field name="card_type"/>
                                    <field name="face_data"/>
                                    <field groups="base.group_no_one" name="face_enroll_threshold"/>
                                </group>
                            </group>
                        </page>
                        <!--                        <page string="Page2">-->
                        <!--                        </page>-->
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field groups="base.group_user" name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form></field>
    </record>
    <!--TODO[IMP]: <model_name> _view_list-->
    <record id="user_view_list" model="ir.ui.view">
        <field name="name">ams_suprema.user.list</field>
        <field name="model">ams_suprema.user</field>
        <field name="arch" type="xml">
            <list string="Desc">
                <header>
                     <button class="btn-primary" name="action_enroll" string="Enroll" type="object"
                            title="create device actions to enroll or update user to selected devices by transfer wizard"/>
                    <button class="btn btn-danger" name="action_delete_user" string="Delete From Devices"
                            title="create device actions to delete user from selected devices by transfer wizard"
                            type="object"/>
                </header>
                <field name="name"/>
                <field name="user_type"/>
                <field name="enroll_number"/>
                <field name="device_group_id" optional="show"/>
                <field name="start_datetime" optional="hide"/>
                <field name="end_datetime" optional="hide"/>
                <field name="card_no"/>
                <field name="finger_quality" optional="hide"/>
                <field name="card_type" optional="hide"/>
                <field name="sync_user" optional="hide"/>
                <field name="last_sync_time" optional="hide"/>
                <field name="last_batch_no" optional="hide"/>
            </list></field>
    </record>
    <!--TODO[IMP]: <model_name> _view_search-->
    <record id="user_view_search" model="ir.ui.view">
        <field name="name">ams_suprema.user.search</field>
        <field name="model">ams_suprema.user</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="enroll_number"/>
                <field name="card_no"/>
                <field name="card_type"/>
                <!--<field name="living_area" filter_domain="[('living_area', '>=', self)]"/>-->
                <filter domain="[('user_type', '=', 'visitor')]" name="visitor" string="Visitors"/>
                <filter domain="[('user_type', '=', 'employee')]" name="emplyee" string="Employees"/>
                <filter domain="[('enrollment_done', '=', True)]" name="enrolled" string="Enrolled"/>
                <group expand="1" string="Group By">
                    <filter string="Device Group" name='device_group_id' context="{'group_by':'device_group_id'}"/>
                    <filter string="User Type" name='user_type' context="{'group_by':'user_type'}"/>
                </group>
            </search></field>
    </record>
    <!--TODO[IMP]: <model_name> _view_kanban-->
    <!--    <record id="model_view_kanban" model="ir.ui.view">-->
    <!--        <field name="name">model.kanban</field>-->
    <!--        <field name="model">module.model</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <kanban default_group_by="field_name" records_draggable="0">-->
    <!--                &lt;!&ndash;<field name="state"/>&ndash;&gt;-->
    <!--                <templates>-->
    <!--                    <t t-name="kanban-box">-->
    <!--                        <div class="oe_kanban_global_click">-->
    <!--                            <div>-->
    <!--                                <strong class="o_kanban_record_title">-->
    <!--                                    &lt;!&ndash;<field name="name"/>&ndash;&gt;-->
    <!--                                </strong>-->
    <!--                            </div>-->
    <!--                            &lt;!&ndash;<div>-->
    <!--                                Expected Price: <field name="expected_price"/>-->
    <!--                            </div>-->
    <!--                            <div t-if="record.state.raw_value == 'offer_received'">-->
    <!--                                Best Offer: <field name="best_price"/>-->
    <!--                            </div>-->
    <!--                            <div t-if="record.selling_price.raw_value">-->
    <!--                                Selling Price: <field name="selling_price"/>-->
    <!--                            </div>-->
    <!--                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>&ndash;&gt;-->
    <!--                        </div>-->
    <!--                    </t>-->
    <!--                </templates>-->
    <!--            </kanban>-->
    <!--</field>-->
    <!--    </record>-->
    <!--TODO[IMP]: <model_name> _view_calendar-->
    <!--    <record id="model_view_calendar" model="ir.ui.view">-->
    <!--        <field name="name">module.model.calendar</field>-->
    <!--        <field name="model">module.model</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <calendar string="Appointment" date_start="start_date" date_stop="end_date" event_limit="5"-->
    <!--                      quick_add="False" color="state" event_open_popup="True">-->
    <!--                &lt;!&ndash;<field name="name" />-->
    <!--                <field name="company_id" filters="1" />-->
    <!--                <field name="partner_id" filters="1" />-->
    <!--                <field name="patient_id" avatar_field="image_128" />-->
    <!--                <field name="start_date" options='{"datepicker": {"daysOfWeekDisabled": [5, 6]}}'/>&ndash;&gt;-->
    <!--            </calendar>-->
    <!--</field>-->
    <!--    </record>-->
    <!--TODO[IMP]: <model_name> _view_graph-->
    <!--    <record id="model_view_graph" model="ir.ui.view">-->
    <!--        <field name="name">module.model.graph</field>-->
    <!--        <field name="model">module.model</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <graph string="Appointment" type="bar">-->
    <!--                &lt;!&ndash;<field name="slot_id"/>&ndash;&gt;-->
    <!--            </graph>-->
    <!--</field>-->
    <!--    </record>-->
    <!--TODO[IMP]: <model_name> _action-->
    <record id="user_action" model="ir.actions.act_window">
        <field name="name">User Enrollment</field>
        <field name="res_model">ams_suprema.user</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
