<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="device_group_view_form" model="ir.ui.view">
        <field name="name">ams_base.devic_group.form</field>
        <field name="model">ams_base.device_group</field>
        <field name="arch" type="xml">
            <form>
                <header>

                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name" />
                            <field name="description" />
                            <field name="is_predefined"  readonly="1"/>

                        </group>
                        <group>

                        </group>
                    </group>
                    <notebook>
                        <page string="Devices">
                            <field name="suprema_device_ids"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" />
                    </div>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_list-->
    <record id="device_group_view_list" model="ir.ui.view">
        <field name="name">ams_base.device_group.list</field>
        <field name="model">ams_base.device_group</field>
        <field name="arch" type="xml">
            <list string="Desc">
                <field name="name"/>
                <field name="description" />
                <field name="group_number" optional="hide" />
            </list>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_search-->
    <record id="device_group_view_search" model="ir.ui.view">
        <field name="name">ams_base.device_group.search</field>
        <field name="model">ams_base.device_group</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="description" />
                <field name="group_number" />

            </search>
        </field>
    </record>


    <!--TODO[IMP]: <model_name> _action-->
    <record id="device_group_action" model="ir.actions.act_window">
        <field name="name">Device Groups</field>
        <field name="res_model">ams_base.device_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
