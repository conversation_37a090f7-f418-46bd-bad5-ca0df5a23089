<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- List View -->
        <record model="ir.ui.view" id="device_user_tree_view">
            <field name="name">ams_suprema.device_user.form</field>
            <field name="model">ams_suprema.device_user</field>
            <field name="arch" type="xml">
                <list delete="0" edit="0">
                    <header>
                        <button name="action_delete_device_users" type="object" string="Delete" icon="fa-trash"
                                class="btn-outline-danger"></button>
                    </header>
                    <field name="user_enroll_id" decoration-danger="error"/>
                    <field name="device_id"/>
                    <field name="enroll_number" optional="hide"/>
                    <field name="device_no" optional="hide"/>

                    <field name="user_enrolled" optional="show"/>
                    <field name="card_enrolled" optional="show"/>
                    <field name="finger_enrolled" optional="show"/>
                    <field name="face_enrolled" optional="show"/>
                    <field name="ac_group_enrolled" optional="hide"/>

                    <field name="create_date" optional="hide"/>
                    <field name="write_date" optional="hide"/>
                    <field name="error" optional="hide"/>
                </list>
            </field>
        </record>

        <!-- Form View -->
        <record model="ir.ui.view" id="device_user_form_view">
            <field name="name">ams_suprema.device_user.form</field>
            <field name="model">ams_suprema.device_user</field>
            <field name="arch" type="xml">
                <form edit="0">
                    <!--                    <header>-->
                    <!--                       -->
                    <!--                        <field name="state" widget="statusbar"/>-->
                    <!--                    </header>-->
                    <sheet>
                        <group>
                            <group>
                                <field name="user_enroll_id" decoration-danger="error"/>
                                <field name="device_id"/>
                                <field name="enroll_number"  />
                                <field name="device_no"  />
                                <field name="create_date" />
                                <field name="write_date" />

                            </group>
                            <group>
                                <field name="user_enrolled"/>
                                <field name="card_enrolled"/>
                                <field name="finger_enrolled"/>
                                <field name="face_enrolled"/>
                                <field name="ac_group_enrolled"/>
                                <field name="error" />
                                <!--  <field name="state"/>-->
                            </group>

                        </group>
                        <group>
                            <field name="error_msg" class="text-danger"/>
                        </group>

                    </sheet>
                </form>
            </field>
        </record>

        <!-- Search View -->
        <record model="ir.ui.view" id="device_user_search_view">
            <field name="name">Device Action Search View</field>
            <field name="model">ams_suprema.device_user</field>
            <field name="arch" type="xml">
                <search>

                    <field name="enroll_number"/>
                    <field name="device_no"/>
                    <field name="device_id"/>
                    <field name="user_enroll_id"/>


                    <!--                    <filter string="Pending" name="state_pending" domain="[('state', '=', 'pending')]"/>-->


                    <group expand="0" string="Device">
                        <!--                        <filter string="Device" name="groupby_device_id" context="{'group_by': 'device_id'}"/>-->

                    </group>
                </search>
            </field>
        </record>

        <!-- Window action for the model -->
        <record id="action_device_user" model="ir.actions.act_window">
            <field name="name">Device Users</field>
            <field name="res_model">ams_suprema.device_user</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="device_user_search_view"/>
            <field name="context">{}</field>
            <field name="help">Manage Users in devices</field>
        </record>

        <!-- Existing menus for the application -->

        <!-- Menu item for the window action -->
        <!--        <menuitem id="menu_device_user" name="Device Actions" parent="parent_menu_id" sequence="10"-->
        <!--                  action="action_device_user"/>-->
    </data>
</odoo>
