<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.suprema</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app data-string="ta-suprema" string="Suprema" name="ta-suprema"
                logo="/ams_suprema/static/description/icon.png">
                    <block title="Suprema">
                        <setting help="Define Suprema G-SDK Server setting" class="row">
                            <div class="content-group">
                                <div class="mt16 row">
                                    <span class="o_form_label">Gateway CA File Path</span>
                                    <field name="getway_ca_file_path"/>
                                </div>

                                <div class="mt16 row">
                                    <span class="o_form_label pr-3">Gateway IP</span>
                                    <field name="gateway_ip"/>
                                </div>

                                <div class="mt16 row">
                                    <span class="o_form_label pr-3">Gateway Port</span>
                                    <field name="gateway_port"/>
                                </div>

                                <div class="mt16 row">
                                    <span class="o_form_label pr-3">Use SSL</span>
                                    <field name="use_ssl"/>
                                </div>

                                <div class="mt16 row">
                                    <span class="o_form_label pr-3">Finger Template Formate </span>
                                    <field name="finger_template_formate"/>
                                </div>

                                <div class="mt16 row">
                                    <span class="o_form_label pr-3">Finger Min Templates </span>
                                    <field name="min_templates"/>
                                </div>

                                <div class="mt16 row">
                                    <span class="o_form_label pr-3">Auth Mode </span>
                                    <field name="auth_mode"/>
                                </div>
                                 <div class="mt16">
<!--                                <button name="action_test_connection" type="action"-->
<!--                                    string="Test Connection"-->
<!--                                    class="btn-primary"/>-->
                            </div>
                            </div>
                        </setting>
                    </block>
                </app>
            </xpath>
        </field>
    </record>

    <record id="action_device_config" model="ir.actions.act_window">
         <field name="name">Settings</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_id" ref="res_config_settings_view_form"/>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'ams_suprema', 'bin_size': False}</field>
    </record>
</odoo>