<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- List View -->
        <record model="ir.ui.view" id="device_action_tree_view">
            <field name="name">ams_suprema.device_action.list</field>
            <field name="model">ams_suprema.device_action</field>
            <field name="arch" type="xml">
                <list edit="0" default_order="create_date desc, id desc">
                    <header>
                        <button name="action_execute" type="object" string="Execute" class="btn-primary mx-1"></button>
                    </header>
                    <field name="name"/>
                    <field name="user_name"/>
                    <field name="device_id"/>
                    <field name="employee_id" optional="hide"/>
                    <field name="enroll_number" optional="show"/>
                    <field name="employee_number" optional="hide"/>
                    <field name="action_type" class="text-bold"
                           decoration-success="action_type == 'add' " decoration-danger="action_type == 'delete'"/>
                    <field name="device_no" optional="hide"/>
                    <field name="execute_date"/>
                    <field name="error" optional="hide"/>
                    <field name="comment" optional="hide"/>
                    <field name="batch_no" optional="hide"/>
                    <field name="state"/>
                    <field name="user_enrolled" optional="show"/>
                    <field name="card_enrolled" optional="show"/>
                    <field name="finger_enrolled" optional="show"/>
                    <field name="face_enrolled" optional="show"/>
                    <field name="ac_group_enrolled" optional="hide"/>
                    <field name="command_type" optional="hide"/>

                </list>
            </field>
        </record>

        <!-- Form View -->
        <record model="ir.ui.view" id="device_action_form_view">
            <field name="name">ams_suprema.device_action.form</field>
            <field name="model">ams_suprema.device_action</field>
            <field name="arch" type="xml">
                <form edit="0">
                    <header>
                        <button name="action_execute" type="object" string="Execute" class="btn-primary"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="user_name"/>
                                <field name="enroll_number"/>
                                <field name="employee_number"/>
                            </group>
                            <group>
                                <field name="comment"/>
                                <field name="batch_no"/>
                                <field name="device_no"/>
                                <field name="action_type"/>
                                <!--  <field name="state"/>-->
                            </group>

                        </group>
                        <group>
                            <field name="error_msg" class="text-danger"/>
                        </group>
                        <notebook>
                            <page string="Enroll Status">
                                <group>
                                    <group>
                                        <field name="device_id"/>
                                        <field name="user_enroll_id"/>
                                        <field name="execute_date"/>
                                        <field name="error"/>
                                    </group>
                                    <group>
                                        <field name="user_enrolled"/>
                                        <field name="card_enrolled"/>
                                        <field name="finger_enrolled"/>
                                        <field name="face_enrolled"/>
                                    </group>
                                </group>


                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Search View -->
        <record model="ir.ui.view" id="device_action_search_view">
            <field name="name">ams_suprema.device_action.search</field>
            <field name="model">ams_suprema.device_action</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name"/>
                    <field name="enroll_number"/>
                    <field name="employee_number"/>
                    <field name="device_no"/>
                    <field name="execute_date"/>
                    <field name="error"/>
                    <field name="comment"/>
                    <field name="batch_no"/>
                    <field name="state"/>
                    <field name="action_type"/>

                    <filter string="Pending" name="state_pending" domain="[('state', '=', 'pending')]"/>
                    <filter string="Executed" name="state_executed" domain="[('state', '=', 'executed')]"/>
                    <filter string="Error" name="filter_error" domain="[('error', '=', True)]"/>


                    <group expand="0" string="Device">
                        <filter string="Device" name="groupby_device_id" context="{'group_by': 'device_id'}"/>
                        <filter string="Employee" name="groupby_employee_id" context="{'group_by': 'employee_id'}"/>
                        <filter string="Batch" name="groupby_batch_no" context="{'group_by': 'batch_no'}"/>
                    </group>


                </search>
            </field>
        </record>

        <!-- Window action for the model -->
        <record id="action_device_action" model="ir.actions.act_window">
            <field name="name">Device Actions</field>
            <field name="res_model">ams_suprema.device_action</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="device_action_search_view"/>
            <field name="context">{}</field>
            <field name="help">Manage device actions queue and track errors</field>
        </record>

        <!-- Existing menus for the application -->

        <!-- Menu item for the window action -->
        <!--        <menuitem id="menu_device_action" name="Device Actions" parent="parent_menu_id" sequence="10"-->
        <!--                  action="action_device_action"/>-->
    </data>
</odoo>
