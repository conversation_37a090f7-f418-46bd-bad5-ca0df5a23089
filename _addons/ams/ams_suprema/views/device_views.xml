<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="device_view_form" model="ir.ui.view">
        <field name="name">ams_suprema.devic.form</field>
        <field name="model">ams_suprema.device</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <!--                    options="{'clickable': '1', 'fold_field': 'fold'}"-->
                    <field name="state" nolabel="1" widget="statusbar"/>

                    <button name="action_check_status" type="object" icon="fa-plug"
                            class="btn-primary" title="trying to connect device to check status and get device info"/>

                    <button name="action_disconnect" type="object" icon="fa-power-off"
                            class="btn-outline-danger" title="Disconnect device and change state to offline"/>

                    <button name="action_get_logs" string="Sync Logs" type="object" icon="fa-history"
                            class="btn-outline-primary"/>

                    <button name="action_clear_logs" string="Delete Logs" type="object" icon="fa-trash"
                            class="btn-outline-danger" title="trying to download log from device"/>

                    <button class="btn-outline-primary" name="action_close_gateway_channel" invisible="1" string="Close Channel"
                            type="object"/>
                    <button class="btn-outline-primary" name="action_start_gateway_channel" invisible="1" string="Start Channel"
                            type="object"/>

                    <button name="action_sync_users" string="Sync User" type="object" icon="fa-cloud-download"
                            class="btn-outline-primary" title="download user from device to save in the system"/>

                    <button name="action_upload_users" string="Upload User" type="object" icon="fa-cloud-upload"
                            class="btn-outline-primary" title="upload users to device based on default user group"/>
                </header>
                <sheet>
                    <div name="button_box" class="oe_button_box">
                        <button type="object" name="action_open_device_logs_views" class="oe_stat_button"
                                icon="fa-history" style="width: 100px !important;" string="Logs">
                            <!--                                <div class="o_field_widget o_stat_info">-->
                            <!--                                    <field name="count_field" class="p-0 o_stat_value" readonly="1"/>-->
                            <!--                                    <span class="o_stat_text p-0 text-wrap" style="font-size:12px;">Units</span>-->
                            <!--                                </div>-->
                        </button>
                        <button type="object" name="action_open_device_action_views" class="oe_stat_button"
                                icon="fa-list" style="width: 100px !important;" string="Action Queue">

                        </button>
                         <button type="object" name="action_open_device_users_views" class="oe_stat_button"
                                icon="fa-user" style="width: 100px !important;" string="Users">

                        </button>

                        <!--                        <div class="o_stat_info border-0" invisible="response_code != '1'">-->
                        <!--                            <span class="border-0 fa fa-fw fa-circle text-success o_button_icon"/>-->
                        <!--                            <span class="badge badge-success">-->
                        <!--                                Connected-->
                        <!--                            </span>-->
                        <!--                        </div>-->
                        <!--                        <div class="o_stat_info border-0" invisible="response_code == '1'">-->
                        <!--                            <span class="fa fa-fw fa-circle text-muted o_button_icon"/>-->
                        <!--                            <span class="badge badge-muted">-->
                        <!--                                Disconnected-->
                        <!--                            </span>-->
                        <!--                        </div>-->

                    </div>


                    <group>
                        <group>
                            <field name="name" required="True"/>
                            <field name="ip"/>
                            <field name="port"/>
                            <field name="device_group_id"/>
                        </group>
                        <group>
                            <field name="device_id"/>
                            <field name="is_public"/>
                            <field name="is_enroll"/>
                            <field name="dedicated"/>
                            <field name="activate"/>
                        </group>
                    </group>
                    <group>
                        <field class="text-danger" name="error_msg"/>
                    </group>
                    <notebook>
                        <page name="info" string="Info">
                            <group>
                                <group>
                                    <field name="mac_addr" readonly="1"/>
                                    <field name="model_name" readonly="1"/>
                                    <field name="firmware_version" readonly="1"/>
                                    <field name="kernel_version" readonly="1"/>
                                    <field name="bs_core_version" readonly="1"/>
                                    <field name="board_version" readonly="1"/>
                                    <field name="type" readonly="1"/>
                                </group>
                                <group>
                                    <field name="status"/>
                                    <field name="last_log_id"/>
                                    <field name="log_active"/>
                                    <field name="response_code"/>
                                    <field name="error"/>
                                </group>
                            </group>
                        </page>
                        <page name="device_cap_data" string="Capability Info">
                            <field name="device_cap_json"/>
                        </page>
                        <page string="Auth Setting">
                            <group>
                                <group>
                                    <field name="card_auth_mode_domain" invisible="1"/>
                                    <field name="fingerprint_auth_mode_domain" invisible="1"/>
                                    <field name="face_auth_mode_domain" invisible="1"/>
                                    <field name="id_auth_mode_domain" invisible="1"/>

                                    <field name="ext_auth_supported"/>
                                    <field name="card_auth_mode_id" domain="card_auth_mode_domain"/>
                                    <field name="fingerprint_auth_mode_id" domain="fingerprint_auth_mode_domain"/>
                                    <field name="face_auth_mode_id" domain="face_auth_mode_domain"/>
                                    <field name="id_auth_mode_id" domain="id_auth_mode_domain"/>


                                </group>
                                <group>

                                    <field name="use_private_auth"/>
                                    <field name="card_supported"/>
                                    <field name="fingerprint_supported"/>
                                    <field name="face_supported"/>
                                    <field name="id_supported"/>
                                </group>
                            </group>
                            <button type="object" name="action_set_config" string="Update Config"></button>
                        </page>
                        <!--                        <page invisible="response_code == 1" name="error_msg" string="Error Message">-->
                        <!--                            <group style="height:100% !important;">-->
                        <!--                                <field class="text-danger" name="error_msg" nolabel="1"/>-->
                        <!--                            </group>-->
                        <!--                        </page>-->
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field groups="base.group_user" name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form></field>
    </record>
    <!--TODO[IMP]: <model_name> _view_list-->
    <record id="device_view_list" model="ir.ui.view">
        <field name="name">ams_suprema.device.list</field>
        <field name="model">ams_suprema.device</field>
        <field name="arch" type="xml">
            <list string="Desc">
                <field name="name"/>
                 <field name="state" decoration-success="state=='online'"
                decoration-danger="state=='offline'"
                decoration-warning="state=='pending'"/>
                <field name="device_group_id"/>
                <field name="device_id"/>
                <field name="ip"/>
                <field name="status" optional="hide"/>
                <field name="type" optional="hide"/>
                <field name="mac_addr" optional="hide"/>
                <field name="model_name" optional="hide"/>
                <field name="use_private_auth" optional="hide"/>
            </list>
        </field>
    </record>
    <!--TODO[IMP]: <model_name> _view_search-->
    <record id="device_view_search" model="ir.ui.view">
        <field name="name">ams_suprema.device.search</field>
        <field name="model">ams_suprema.device</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="device_id"/>
                <field name="type"/>
                <field name="ip"/>
                <field name="mac_addr"/>
                <field name="model_name"/>
                 <field name="board_version"/>
                <field name="bs_core_version"/>

                <filter domain="[('state', '=', 'pending')]" name="pending" string="Pending"/>
                <filter domain="[('state', '=', 'offline')]" name="pending" string="Offline"/>
                <filter domain="[('state', '=', 'online')]" name="pending" string="Online"/>
                <separator/>

                <filter domain="[('is_public', '=', True)]" name="public_devices" string="Public Devices"/>
                <filter domain="[('is_enroll', '=', True)]" name="is_enroll" string="Enroll Devices"/>
                <filter domain="[('dedicated', '=', True)]" name="dedicated" string="Dedicated"/>
                <filter domain="[('dedicated', '=', False)]" name="dedicated" string="Undedicated"/>
                <separator/>

                <filter domain="[('card_supported', '=', True)]" name="card_supported" string="Support Card"/>
                <filter domain="[('fingerprint_supported', '=', True)]" name="fingerprint_supported" string="Support Fingerprint"/>
                <filter domain="[('face_supported', '=', True)]" name="face_supported" string="Support Face"/>
                <separator/>

                <filter domain="[('activate', '=', True)]" name="is_enroll" string="Active"/>
                <filter domain="[('activate', '=', False)]" name="is_enroll" string="Not Active"/>
                <separator/>



                <group expand="1" string="Group By">
                    <filter context="{'group_by':'device_group_id'}" name="groupby_device_group_id" string="DeviceGroup"/>
                    <filter context="{'group_by':'state'}" name="groupby_state" string="States"/>
                    <filter context="{'group_by':'model_name'}" name="groupby_model" string="Model"/>
                </group>
            </search></field>
    </record>
    <!--TODO[IMP]: <model_name> _view_kanban-->
    <record id="device_view_kanban" model="ir.ui.view">
        <field name="name">device.kanban</field>
        <field name="model">ams_suprema.device</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" records_draggable="0">
                <!--                <field name="state"/>-->
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click mt-3">
                            <div>
                                <strong class="o_kanban_record_title">
                                    Name:
                                </strong>
                                <field name="name"/>
                            </div>
                            <div class="o_kanban_record_body">
                                <strong class="o_kanban_record_title">
                                    Device ID:
                                </strong>
                                <field name="device_id"/>
                                <!--                            <div class="o_kanban_record_body">-->
                                <!--                               <strong class="o_kanban_record_title">Type: </strong> <field name="type" />-->
                                <!--                            </div>-->
                                <div class="o_kanban_record_body">
                                    <strong class="o_kanban_record_title">
                                        IP:
                                    </strong>
                                    <field name="ip"/>
                                </div>
                                <div class="o_kanban_record_body">
                                    <strong class="o_kanban_record_title">
                                        Last Log Date:
                                    </strong>
                                    <field name="last_log_date"/>
                                </div>
                                <div class="o_kanban_record_body">
                                    <strong class="o_kanban_record_title">
                                        Status:
                                    </strong>
                                    <field name="status"/>
                                </div>
                                <div class="o_kanban_record_body">
                                    <strong class="o_kanban_record_title">
                                        Group
                                    </strong>
                                    <field name="device_group_id"/>
                                </div>
                            </div>
                            <!--<div>
                                Expected Price: <field name="expected_price"/>
                            </div>
                            <div t-if="record.state.raw_value == 'offer_received'">
                                Best Offer: <field name="best_price"/>
                            </div>
                            <div t-if="record.selling_price.raw_value">
                                Selling Price: <field name="selling_price"/>
                            </div>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>-->
                        </div>
                    </t>
                </templates>
            </kanban></field>
    </record>
    <!--TODO[IMP]: <model_name> _view_calendar-->
    <!--    <record id="model_view_calendar" model="ir.ui.view">-->
    <!--        <field name="name">module.model.calendar</field>-->
    <!--        <field name="model">module.model</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <calendar string="Appointment" date_start="start_date" date_stop="end_date" event_limit="5"-->
    <!--                      quick_add="False" color="state" event_open_popup="True">-->
    <!--                &lt;!&ndash;<field name="name" />-->
    <!--                <field name="company_id" filters="1" />-->
    <!--                <field name="partner_id" filters="1" />-->
    <!--                <field name="patient_id" avatar_field="image_128" />-->
    <!--                <field name="start_date" options='{"datepicker": {"daysOfWeekDisabled": [5, 6]}}'/>&ndash;&gt;-->
    <!--            </calendar>-->
    <!--</field>-->
    <!--    </record>-->
    <!--TODO[IMP]: <model_name> _view_graph-->
    <!--    <record id="model_view_graph" model="ir.ui.view">-->
    <!--        <field name="name">module.model.graph</field>-->
    <!--        <field name="model">module.model</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <graph string="Appointment" type="bar">-->
    <!--                &lt;!&ndash;<field name="slot_id"/>&ndash;&gt;-->
    <!--            </graph>-->
    <!--</field>-->
    <!--    </record>-->
    <!--TODO[IMP]: <model_name> _action-->
    <record id="device_action" model="ir.actions.act_window">
        <field name="name">Devices</field>
        <field name="res_model">ams_suprema.device</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
