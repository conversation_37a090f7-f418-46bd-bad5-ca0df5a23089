<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="auth_mode_view_form" model="ir.ui.view">
        <field name="name">ams_suprema.auth_mode.form</field>
        <field name="model">ams_suprema.auth_mode</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="name"/>
                            <field name="extension_supported"/>
                             <field name="category_type"/>
                             <field name="activate"/>
                            <field name="mapping_auth_mode_id"/>
                        </group>
                        <group>

                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="auth_mode_view_list" model="ir.ui.view">
        <field name="name">ams_suprema.auth_mode.list</field>
        <field name="model">ams_suprema.auth_mode</field>
        <field name="arch" type="xml">
            <list string="Desc" editable="bottom">
                <field name="code"  optional="show"/>
                <field name="name" decoration-warning="not extension_supported"/>
                <field name="mapping_auth_mode_id" optional="hide"
                 decoration-warning="not extension_supported"/>
                <field name="extension_supported"/>
                <field name="category_type"/>
                <field name="activate"/>

            </list>
        </field>
    </record>

    <record id="auth_mode_view_search" model="ir.ui.view">
        <field name="name">ams_suprema.auth_mode.search</field>
        <field name="model">ams_suprema.auth_mode</field>
        <field name="arch" type="xml">
            <search>
                <field name="code"/>
                <field name="name"/>
                <field name="extension_supported"/>

                <!--<field name="name"/>-->
                <filter name="extension_supported" string="Ext Support" domain="[('extension_supported', '=', True)]"/>
                <filter name="extension_not_supported" string="Ext Not Support"
                       domain="[('extension_supported', '=', False)]"/>
                <separator/>
                <filter name="not_active" string="Not Active" domain="[('activate', '=', False)]"/>

                <filter name="active" string="Active" domain="[('activate', '=', True)]"/>

                <group expand="1" string="Group By">
                    <filter string="Category Type" name='category_type' context="{'group_by':'category_type'}"/>
                    <filter string="Name" name='name' context="{'group_by':'name'}"/>
                </group>


            </search>
        </field>
    </record>

    <record id="auth_mode_action" model="ir.actions.act_window">
        <field name="name">Auth Mode</field>
        <field name="res_model">ams_suprema.auth_mode</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create  Auth Mode
            </p>
            <p>
                Create Auth Mode
            </p>
        </field>
    </record>
</odoo>
