# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.addons.ta.helper.helper import *


class ReportGenerator(models.TransientModel):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.report_generator_wizard"
    _description = "Report Generator"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # report_type = fields.Selection([('attend_details', 'Attendance Details Report'),
    #                                 ('attend_summary', 'Employee Attendance Summary Report')])
    name = fields.Char(default='Report Generator', readonly=True)
    from_date = fields.Date()
    to_date = fields.Date()
    # endregion

    # region  Special
    # endregion

    # region  Relational
    employee_id = fields.Many2one('hr.employee', default=lambda self: self.env.user._get_associated_employee())
    department_ids = fields.Many2many('hr.department','department_ids')
    allowed_department_ids = fields.Many2many('hr.department', relation='allowed_department_ids', store=False,
                                            compute='_compute_allowed_departments', )

    employee_ids = fields.Many2many('hr.employee', 'employee_ids')
    allowed_employee_ids = fields.Many2many('hr.employee', relation='allowed_employee_ids',store=False,
                                            compute='_compute_allowed_employees',)
    timesheet_ids = fields.Many2many('ta.timesheet')
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('employee_id')
    def _compute_allowed_departments(self):
        for record in self:
            if record.employee_id:
                record.allowed_department_ids = self.env.user._get_allowed_department_ids(record.employee_id)
            else:
                record.allowed_department_ids = False

    @api.depends('department_ids', 'allowed_department_ids')
    def _compute_allowed_employees(self):
        for record in self:
            employees_ids = []
            if record.employee_id:
                employees_ids = self.env.user._get_allowed_employee_ids(record.employee_id, record.allowed_department_ids)
                if employees_ids and record.department_ids:
                    employees_ids = employees_ids.filtered(lambda employee: employee.department_id.id in record.department_ids.ids)

            record.allowed_employee_ids = employees_ids
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------

    @api.onchange('employee_ids','allowed_employee_ids', 'department_ids', 'from_date', 'to_date')
    def _compute_timesheet_ids(self):
        domain = [('parent_id', '=', False)]

        employee_ids = self.employee_ids.ids if self.employee_ids else self.allowed_employee_ids.ids
        domain.append(('employee_id', 'in', employee_ids))

        if self.from_date:
            domain.append(('date', '>=', self.from_date))
        if self.to_date:
            domain.append(('date', '<=',  self.to_date))

        self.timesheet_ids = False
        self.timesheet_ids = self.env['ta.timesheet'].search(domain)

    # @api.constrains('employee_ids', 'department_ids')
    # def _constraint_employees_departments(self):
    #     if not self.department_ids and not self.employee_ids:
    #         raise ValidationError("Please choose department or employee.")

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_generate(self):
         return {

         }
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def convert_to_time_format(self, float_time):
        return convert_to_str_time_format(float_time)
    # endregion
