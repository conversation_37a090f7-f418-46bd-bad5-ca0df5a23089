<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="manual_attendance_wizard_view_form" model="ir.ui.view">
        <field name="name">ta.manual_attendance_wizard.form</field>
        <field name="model">ta.manual_attendance_wizard</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field invisible="1" name="shift_unit_id"/>
                </header>
                <sheet>
                    <group invisible="shift_unit_id != False">
                        <group>
                            <field name="employee_id"/>
                        </group>
                        <group>
                            <field name="from_date"/>
                            <field name="to_date"/>
                        </group>
                        <group>
                            <field name="with_time"/>
                            <field name="is_random" groups="base.group_no_one"/>
                        </group>
                        <group invisible="with_time == False">
                            <field name="checkin_time" widget="float_time"/>
                            <field name="checkout_time" widget="float_time"/>
                        </group>
                    </group>
                    <group invisible="shift_unit_id == False">
                        <field name="checkin_datetime"/>
                        <field name="checkout_datetime"/>
                    </group>
                    <footer>
                        <button invisible="shift_unit_id != False" name="action_attend" string="Add Attendance" type="object"/>
                        <button invisible="shift_unit_id == False" name="action_recalculate_timesheet" string="Recalculate" type="object"/>
                        <button invisible="shift_unit_id == False" name="action_absent" string="Absent" type="object"/>
                    </footer>
                </sheet>
            </form></field>
    </record>
    <record id="action_manual_attendance_wizard" model="ir.actions.act_window">
        <field name="name">Manual Attendance</field>
        <field name="res_model">ta.manual_attendance_wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p></field>
    </record>
</odoo>
