# -*- coding: utf-8 -*-
from datetime import datetime, timedelta

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.addons.ta.helper.helper import *
import random

class ManualAttendance(models.TransientModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.manual_attendance_wizard"
    _description = "Manual Attendance"
    _inherit = ["ta.base_transaction", "ams_base.abstract_model"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    from_date = fields.Date(default=fields.Date.today())
    to_date = fields.Date(default=fields.Date.today())

    with_time = fields.Boolean(default=False)
    checkin_time = fields.Float()
    checkout_time = fields.Float()

    checkin_datetime = fields.Datetime()
    checkout_datetime = fields.Datetime()

    is_random = fields.Boolean(string="Random", default=False)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    employee_ids = fields.Many2many('hr.employee')
    timesheet_ids = fields.Many2many('ta.timesheet')

    employee_id = fields.Many2one('hr.employee')

    shift_unit_id = fields.Many2one('ta.timesheet_shift_unit')

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.onchange('checkout_datetime', 'checkin_datetime')
    def _onchange_checkin_out_dates(self):
        if self.shift_unit_id and (self.shift_unit_id.date or self.shift_unit_id.timesheet_id.date):
            shift_date = self.shift_unit_id.date or self.shift_unit_id.timesheet_id.date
            default_checkin_date = compine_date_time(shift_date, self.shift_unit_id.start_time)

            if self.checkin_datetime and self.checkin_datetime.date() != shift_date:
                self.checkin_datetime = convert_to_utc(default_checkin_date, self._get_tz())

            default_checkout_date = compine_date_time(shift_date, self.shift_unit_id.end_time)

            if self.checkout_datetime and (
                    (not self.shift_unit_id.is_overnight and self.checkout_datetime.date() != shift_date) or
                    (self.shift_unit_id.is_overnight and self.checkout_datetime.date() > shift_date + timedelta(
                        days=1)) or
                    (self.shift_unit_id.is_overnight and self.checkout_datetime.date() < shift_date) or
                    (self.checkin_datetime and self.checkout_datetime <= self.checkin_datetime)
            ):
                if self.shift_unit_id.is_overnight:
                    default_checkout_date = default_checkout_date + timedelta(days=1)

                self.checkout_datetime = convert_to_utc(default_checkout_date, self._get_tz())

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_recalculate_timesheet(self):
        if self.checkin_datetime and self.shift_unit_id.first_checkin_datetime != self.checkin_datetime:
            if self.shift_unit_id.first_checkin_datetime and self.shift_unit_id.timesheet_id.parent_id.punch_logs:
                new_str = self.shift_unit_id.timesheet_id.parent_id.punch_logs.replace(
                    str(self.shift_unit_id.first_checkin_datetime),
                    str(self.checkin_datetime))
                self.shift_unit_id.timesheet_id.parent_id.punch_logs = new_str

            if self.shift_unit_id.timesheet_id.shift_unit_history_ids.ids[0] == self.shift_unit_id.id:
                self._checkin_timesheet(self.shift_unit_id.timesheet_id, self.checkin_datetime)
            else:
                self._checkin_shift_unit(self.shift_unit_id, self.checkin_datetime)

        if self.checkout_datetime and self.shift_unit_id.last_checkout_datetime != self.checkout_datetime:
            if self.shift_unit_id.last_checkout_datetime and self.shift_unit_id.timesheet_id.parent_id.punch_logs:
                new_str = self.shift_unit_id.timesheet_id.parent_id.punch_logs.replace(
                    str(self.shift_unit_id.last_checkout_datetime),
                    str(self.checkout_datetime))
                self.shift_unit_id.timesheet_id.parent_id.punch_logs = new_str

            if self.shift_unit_id.timesheet_id.shift_unit_history_ids.ids[
                -1] == self.shift_unit_id.id or not self.shift_unit_id.timesheet_id.last_checkout_datetime:
                self._checkout_timesheet(self.shift_unit_id.timesheet_id, self.checkout_datetime)
            self._checkout_shift_unit(self.shift_unit_id, self.checkout_datetime)

        self.shift_unit_id.timesheet_id.parent_id.action_calc_timesheet()
        self.shift_unit_id.timesheet_id.manual_edit = True
        self.shift_unit_id.timesheet_id.parent_id.manual_edit = True

    def action_absent(self):
        """set selcted date as absent day for employee"""
        if self.shift_unit_id:
            self.shift_unit_id.set_day_absent()

    def action_attend(self):
        if self.from_date and self.to_date:
            if self.employee_id:
                self.employee_ids = self.employee_id

            employees_timesheets = self.env['ta.timesheet'].search([('employee_id', 'in', self.employee_ids.ids),
                                                                    ('date', '>=', self.from_date),
                                                                    ('date', '<=', self.to_date),
                                                                    ('parent_id', '!=', False)])
            if self.employee_ids:
                ts_ids = []

                date = self.from_date
                while date <= self.to_date:
                    for employee in self.employee_ids:
                        ts = employees_timesheets.filtered(
                            lambda t: t.employee_id.id == employee.id and t.date == date)
                        # ts = employees_timesheets.search([('employee_id', '=', employee.id), ('date', '=', date), ('parent_id', '!=', False)])

                        if ts and not ts.first_checkin_datetime and not ts.is_dayoff:
                            ts_ids.append(ts.id)
                        elif not ts:
                            datetime_utc = fields.datetime(date.year, date.month, date.day, 0, 0, 0)
                            datetime_utz = datetime_utc.astimezone(pytz.timezone(ts._get_tz()))
                            datetime_tz = datetime.datetime(datetime_utz.year, datetime_utz.month, datetime_utz.day,
                                                            datetime_utz.hour, datetime_utz.minute, 0)
                            current_timesheet = self._create_timesheet(employee, datetime_utc, datetime_tz, absent=True)
                            if current_timesheet:
                                if not current_timesheet.is_dayoff:
                                    ts_ids.append(current_timesheet.id)
                    date = date + timedelta(days=1)

                self.timesheet_ids = ts_ids

            if self.timesheet_ids:
                if self.with_time:
                    for ts in self.timesheet_ids:
                        checkin_datetime = fields.datetime.combine(ts.date, convert_to_time_object(self.checkin_time)
                                                                   )
                        checkin_datetime = convert_to_utc(checkin_datetime, self._get_tz())
                        ts._assign_shift_unit_checkin_out(ts.parent_id, self.randomize_time(checkin_datetime))

                        log_date = ts.date
                        if self.checkin_time >= self.checkout_time:
                            log_date = ts.date + timedelta(days=1)

                        checkout_datetime = fields.datetime.combine(log_date,
                                                                    convert_to_time_object(self.checkout_time))
                        checkout_datetime = convert_to_utc(checkout_datetime, self._get_tz())
                        ts._assign_shift_unit_checkin_out(ts.parent_id, self.randomize_time(checkout_datetime))

                        ts.parent_id.action_calc_timesheet()
                        ts.manual_edit = True
                else:
                    for ts in self.timesheet_ids:
                        start_time = ts.shift_unit_history_ids[0].start_time
                        end_time = ts.shift_unit_history_ids[-1].end_time if ts.shift_unit_history_ids[
                                                                                 -1].shift_type == 'normal' \
                            else get_sum(start_time, ts.shift_unit_history_ids[-1].required_time * 60)

                        start_datetime = self._prepare_datetime(ts.date, start_time)
                        self._checkin_timesheet(ts, self.randomize_time(start_datetime))

                        end_datetime = self._prepare_datetime(
                            ts.date + timedelta(days=1) if ts.shift_unit_history_ids[-1].is_overnight else ts.date,
                            end_time)
                        self._checkout_timesheet(ts, self.randomize_time(end_datetime))
                        all_logs = []
                        for unit in ts.shift_unit_history_ids:
                            unit.first_checkin_time = unit.start_time
                            unit.first_checkin_datetime = self._prepare_datetime(ts.date, unit.start_time)

                            unit.last_checkout_time = unit.end_time if unit.shift_type == 'normal' \
                                else get_sum(start_time, unit.required_time * 60)

                            unit.last_checkout_datetime = self._prepare_datetime(
                                ts.date + timedelta(days=1) if unit.is_overnight else ts.date, unit.last_checkout_time)
                            all_logs.extend([unit.first_checkin_datetime, unit.last_checkout_datetime])

                        ts.parent_id.punch_logs = ','.join(list(map(str, all_logs)))
                        ts.parent_id.action_calc_timesheet()
                        ts.manual_edit = True
                        ts.parent_id.manual_edit = True

    def randomize_time(self, log_time):
        """randomize time for log time by add minutes to log_time or subtract minutes from log_time"""
        if self.is_random:
            random_mintes = self.get_random_minutes()
            log_time = log_time + timedelta(minutes=random_mintes)

        return log_time

    def get_random_minutes(self, min_minutes=-60, max_minutes=60):
        # Ensure min_minutes and max_minutes are multiples of 5
        min_minutes = ((min_minutes + 4) // 5) * 5
        max_minutes = ((max_minutes // 5) * 5)

        random_number = random.randint(min_minutes // 5, max_minutes // 5)
        result = random_number * 5
        result = max(min(result, max_minutes), min_minutes)

        return result

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _prepare_datetime(self, date, float_time):
        datetime = fields.datetime(year=date.year, month=date.month, day=date.day, hour=0,
                                   minute=0)
        log_datetime = datetime.replace(hour=int(float_time), minute=get_minutes(float_time))
        log_datetime = convert_to_utc(log_datetime, self._get_tz())
        return log_datetime

    # endregion
