# -*- coding: utf-8 -*-
import datetime

from odoo import api, fields, models,_
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta, date

STATES = [('pending', 'Pending'), ('running', 'Running'), ('finished', 'Finished')]


class Shift(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.shift"
    _description = "ta.shift"
    _inherit = ["ams_base.name_model", "mail.thread", "mail.activity.mixin", "ta.base_transaction"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    start_date = fields.Date(string="Start date", states={"pending": [("readonly", False)]}, required=True, tracking=True, help="")
    end_date = fields.Date(string="End date", states={"pending": [("readonly", False)]}, tracking=True, help="")
    state = fields.Selection(selection=STATES, compute='_compute_state', store=False, tracking=True, default='pending')
    state_fixed = fields.Selection(selection=STATES, default='pending')
    is_default = fields.Boolean(string="Is default", states={"pending": [("readonly", False)]}, tracking=True, help="")
    is_exception = fields.Boolean()
    status = fields.Selection([('pending', 'Pending'), ('executed', 'Executed')], default='pending')
    # endregion

    # region  Special
    # endregion

    # region  Relational
    employee_ids = fields.One2many(comodel_name="hr.employee", inverse_name="shift_id", string="Employees",
                                   states={"pending": [("readonly", False)]}, help="")
    exception_employee_ids = fields.Many2many('hr.employee')
    # employee_id = fields.Many2one('hr.employee') Implement in base transaction

    schedule_id = fields.Many2one(comodel_name="ta.shift_schedule", string="Schedule",
                                  states={"pending": [("readonly", False)]}, required=True, help="", tracking=True)

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------

    @api.depends('start_date', 'end_date', 'status')
    def _compute_state(self):
        for record in self:
            if record.end_date and record.start_date and record.start_date <= fields.Date.context_today(record):
                if record.is_running(fields.Date.context_today(record)):
                    if record.state != 'running':
                        record.state = 'running'
                        record.state_fixed = 'running'
                else:
                    if record.state != 'finished':
                        record.state = 'finished'
                        record.state_fixed = 'finished'

            elif record.state != 'pending':
                record.state = 'pending'
                record.state_fixed = 'pending'

                # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------

    @api.constrains('employee_id', 'start_date', 'end_date')
    def _check_shift_overlap(self):
        if len(self) == 1 and self.employee_id and self.start_date and self.end_date:
            overlap_shifts = self.search([
                ('employee_id', '=', self.employee_id.id),
                ('id', '!=', self.id),
                ('start_date', '<=', self.end_date),
                ('end_date', '>=', self.start_date)
            ])
            if overlap_shifts:
                overlapped_ids = overlap_shifts.mapped(lambda a: str(a.id) + ' - ' + a.name)
                raise ValidationError(
                    _('Their is an overlapped shift  at this dates , previous shifts: \n %s'
                      , repr(overlapped_ids)))

    # @api.constrains('employee_id', 'start_date', 'end_date')
    # def _check_shift_overlap(self):
    #     if len(self) == 1 and self.employee_id:
    #         overlap_shifts = self.search([
    #             ('employee_id', '=', self.employee_id.id),
    #             ('id', '!=', self.id), ('is_exception', '=', True),
    #             ('start_date', '<=', self.end_date),
    #             ('end_date', '>=', self.start_date)
    #         ])
    #         if overlap_shifts:
    #             raise ValidationError('Shift overlaps with another shift.')


    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def unlink(self):
        for shift in self:
            shift.action_reset()
        return super(Shift, self).unlink()
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_execute_exception_emp_shift(self):
        if self.is_exception and self.status == 'pending' and self.employee_id:
            self.status = 'executed'
            all_timesheets = self.env['ta.timesheet'].search([('employee_id', '=', self.employee_id.id),
                                                              ('parent_id', '=', False),
                                                              ('date', '>=', self.start_date),
                                                              ('date', '<=', self.end_date)
                                                              ])
            if all_timesheets:
                for ts in all_timesheets:
                    ts.action_update_shift_info()


            # todo: call action_update_shift_info when shift delete to reset calculation

    def action_open_employee_timesheets(self):
        return {
            'name': 'Employee Time-sheets for shift' + self.name,
            'res_model': 'ta.timesheet',
            'view_mode': 'list,form',
            'target': 'new',
            'type': 'ir.actions.act_window',
            'domain': [('date', '<=', self.end_date), ('date', '>=', self.start_date), ('shift_id', '=', self.id),
                       ('parent_id', '=', False), ('employee_id', 'in', self.env.user._get_allowed_employee_ids().ids)],
        }

    def action_reset(self):
        if self.is_exception and self.status == 'executed':
            self.status = 'pending'
            timesheets = self.env['ta.timesheet'].search([('shift_id', '=', self.id), ('parent_id', '=', False)])
            if timesheets:
                for ts in timesheets:
                    ts.action_update_shift_info()
    # def action_confirm(self):
    #     self.status = 'executed'
    #
    # def action_done(self):
    #     self.status = 'pending'
    #
    # def action_draft(self):
    #     self.status = 'pending'

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def is_running(self, date):
        """
        Check if the shift is running on the specified date.

        :param date: a datetime object representing the date to check.
        :type date: datetime.datetime

        :return: True if the shift is running on the specified date, False otherwise.
        :rtype: bool
        """
        return self.start_date <= date <= self.end_date
    # endregion
