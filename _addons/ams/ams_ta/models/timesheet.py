# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.addons.ta.api.dto_mobile_att import *
from odoo.addons.ta.api.dto_emp_att_log import *
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta
from odoo.addons.ta.helper.helper import *
import logging

_logger = logging.getLogger(__name__)


class Timesheet(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.timesheet"
    _description = "Time Sheet"
    _inherit = ["ta.base_timesheet", "ta.base_transaction"]
    _order = 'date'
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    total_deduction = fields.Float(help="total_delay_shortage or required time if absent day")
    is_dayoff = fields.Boolean(help="All types like vacation, public vacation and weekend")
    is_weekend = fields.Boolean()
    is_vacation = fields.Boolean()
    is_public_vacation = fields.Boolean()
    is_absent = fields.Boolean()

    is_attend_day = fields.Boolean(help="if found checkin or checkout")
    is_working_day = fields.Boolean(default=True, string="Is working day", help="if not day off & not vacation")

    manual_edit = fields.Boolean(tracking=True)
    delay_alert_sent = fields.Boolean()
    shortage_alert_sent = fields.Boolean()
    absent_alert_sent = fields.Boolean()
    cycle_days = fields.Integer()
    schedule_type = fields.Selection(selection=[('daily', 'Daily'), ('weekly', 'Weekly')], string="Schedule type",
                                     help="")
    day_index = fields.Integer()
    time_off_hours = fields.Float()
    # region grouping summary pivot view ------------------------------
    delay_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0)
    shortage_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0)
    absent_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0)
    dayoff_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0)
    weekend_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0)
    attend_day_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0)
    working_day_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0)
    overtime_day_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0)
    vacation_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0,
                                    help="Employee official vacations")
    permission_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0,
                                      help="Employee permission")
    total_vacation_count = fields.Integer(compute="_compute_flag_fields", store=True, default=0,
                                          help="Include public vacations")
    color = fields.Char(string="Color", help="", compute="_compute_color", store=True)

    # endregion

    # region  Special
    # endregion

    # region  Relational
    current_timesheet_id = fields.Many2one(comodel_name="ta.timesheet", string="Current timesheet",
                                           help="Current active  timesheet", tracking=True)
    shift_unit_current_ids = fields.One2many(comodel_name="ta.timesheet_shift_unit", inverse_name="parent_timesheet_id",
                                             string="Current Shift Units", help="Related field from current timesheet",
                                             tracking=True)

    shift_id = fields.Many2one(comodel_name="ta.shift", string="Shift", help="")
    parent_id = fields.Many2one(comodel_name="ta.timesheet", string="Parent", help="")
    timesheet_history_ids = fields.One2many(comodel_name="ta.timesheet", inverse_name="parent_id",
                                            string="Timesheet Historys", help="", tracking=True)
    shift_unit_history_ids = fields.One2many(comodel_name="ta.timesheet_shift_unit", inverse_name="timesheet_id",
                                             string="Shift Unit historys",  # domain=[('parent_timesheet_id', '=', id)],
                                             help="Related field from parent timesheet",
                                             tracking=True)
    punch_logs = fields.Char()

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('delay_time', 'shortage_time', 'overtime', 'is_absent', 'is_dayoff', 'is_working_day',
                 'is_vacation', 'first_checkin_datetime', 'last_checkout_datetime', 'is_public_vacation',
                 'is_weekend'
                 )
    def _compute_flag_fields(self):
        for rec in self:
            rec._update_employee_info()  # TODO :Remove # replace color with js custom widget
            if rec.first_checkin_datetime or rec.last_checkout_datetime:
                # rec.color = rec.company_id.attend_color
                rec.is_attend_day = True
                rec.attend_day_count = 1

            if rec.delay_time > 0:
                rec.delay_count = 1
                rec.is_delayed = True

            if rec.shortage_time > 0:
                rec.shortage_count = 1
                rec.is_shortage = True

            if rec.is_absent:
                rec.absent_count = 1
                rec.is_attend_day = False
                rec.attend_day_count = 0
                # rec.color = rec.company_id.absent_color

            if rec.is_weekend:
                rec.weekend_count = 1

            if rec.is_dayoff:
                rec.dayoff_count = 1
                # rec.color = rec.company_id.dayoff_color

            if rec.is_working_day:
                rec.working_day_count = 1

            if rec.overtime > 0:
                rec.overtime_day_count = 1

            if rec.is_vacation:
                rec.total_vacation_count = 1
                # rec.color = rec.company_id.vacation_color

                # don't include public vacation in vacation count
                rec.vacation_count = 0 if rec.is_public_vacation else 1

            if rec.time_off_hours > 0:
                rec.permission_count = 1

            if rec.first_checkin_datetime and not rec.is_absent:
                rec.checked_in = True
                if rec.last_checkout_datetime:
                    in_out_diff = rec.last_checkout_datetime - rec.first_checkin_datetime
                    if in_out_diff.total_seconds() > 60:
                        rec.checked_out = True
                # endregion

    @api.depends('is_absent', 'is_dayoff', 'is_vacation', 'is_attend_day',
                 'company_id.absent_color', 'company_id.dayoff_color', 'company_id.vacation_color',
                 'company_id.attend_color', 'is_weekend')
    def _compute_color(self):
        for rec in self:
            rec.color = False
            if rec.first_checkin_datetime or rec.last_checkout_datetime:
                rec.color = rec.company_id.attend_color

            if rec.is_absent:
                rec.color = rec.company_id.absent_color

            if rec.is_weekend:
                rec.color = rec.company_id.dayoff_color

            if rec.is_vacation or rec.is_public_vacation:
                rec.color = rec.company_id.vacation_color
                # endregion

    @property
    def mobile_status(self):
        # 0 = WorkingDay, 1 = DayOff, 2 = Absent, 3 = Vacation
        self.ensure_one()
        if self.is_working_day:
            return 0
        if self.is_vacation:
            return 3
        if self.is_absent:
            return 2
        if self.is_dayoff:
            return 1

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model
    def create(self, values):
        timesheet = super(Timesheet, self).create(values)
        if timesheet.parent_id and not timesheet.shift_unit_history_ids:
            schedule_day = False
            if timesheet.shift_id.schedule_id.schedule_type == 'daily':
                day_index = timesheet.employee_id._get_schedule_index(timesheet.shift_id.start_date,
                                                                      timesheet.date,
                                                                      timesheet.cycle_days)
                timesheet.day_index = day_index
                timesheet.parent_id.day_index = day_index
                schedule_day = timesheet.shift_id.schedule_id.day_ids.filtered(lambda day: day.index == day_index)
            else:

                schedule_day = timesheet.employee_id._get_schedule_day(timesheet.shift_id.schedule_id, timesheet.date)
            shift_units = schedule_day.shift_units_ids
            if shift_units:
                timesheet.update({'shift_unit_history_ids': [(0, 0, timesheet._prepare_shift_units(shift_unit)) for
                                                             shift_unit in shift_units]})

                if timesheet.first_checkin_datetime:
                    self._assign_shift_unit_checkin_out(timesheet.parent_id, timesheet.first_checkin_datetime)
                    timesheet.parent_id.action_calc_timesheet()
            else:
                timesheet.is_dayoff = True
                timesheet.parent_id.is_dayoff = True
                timesheet.is_absent = False
                timesheet.parent_id.is_absent = False
                timesheet.is_working_day = False
                timesheet.parent_id.is_working_day = False
                timesheet.is_weekend = True
                timesheet.parent_id.is_weekend = True
        return timesheet

    #
    # def write(self):
    #     pass
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_calc_timesheet(self):
        """calculate parent timesheet fields based on date & employee to read from appropriate shift unit &
        apply rules on calculation"""
        for timesheet in self:
            absent_time_criteria = 0.0
            timesheet.required_time = 0
            timesheet.is_absent = False
            timesheet.reset_time_calculations()
            for shift_unit in timesheet.shift_unit_current_ids:
                if shift_unit.first_checkin_datetime:
                    shift_unit.reset_time_calculations()
                    timesheet._calc_shift_unit(shift_unit)
                    timesheet.required_time += shift_unit.required_time
                    timesheet.working_time += shift_unit.working_time
                    if not timesheet.is_dayoff:
                        timesheet.total_delay_shortage += shift_unit.total_delay_shortage
                        timesheet.delay_time += shift_unit.delay_time
                        timesheet.shortage_time += shift_unit.shortage_time
                        absent_time_criteria += shift_unit.absent_time_criteria
                    timesheet.overtime += shift_unit.overtime
                    timesheet.overtime_factored += shift_unit.overtime_factored

            timesheet._check_absent(absent_time_criteria)

            timesheet.total_deduction = timesheet.required_time if timesheet.is_absent else timesheet.total_delay_shortage
            timesheet.diff_working_time = timesheet.working_time - timesheet.required_time

    def action_update_shift_info(self):
        """re-calculate based on new setting from shift,units,rules"""
        for timesheet in self:
            if not timesheet.parent_id:
                timesheet.shift_unit_current_ids.write(
                    {'parent_timesheet_id': timesheet.current_timesheet_id.id, 'timesheet_id': timesheet.id})
                timesheet.current_timesheet_id.required_time = timesheet.required_time
                timesheet.current_timesheet_id.working_time = timesheet.working_time
                timesheet.current_timesheet_id.total_delay_shortage = timesheet.total_delay_shortage
                timesheet.current_timesheet_id.delay_time = timesheet.delay_time
                timesheet.current_timesheet_id.shortage_time = timesheet.shortage_time
                timesheet.current_timesheet_id.overtime = timesheet.overtime
                timesheet.current_timesheet_id.is_absent = timesheet.is_absent
                timesheet.current_timesheet_id.diff_working_time = timesheet.diff_working_time
                timesheet.current_timesheet_id.last_checkout_time = timesheet.last_checkout_time
                timesheet.current_timesheet_id.last_checkout_datetime = timesheet.last_checkout_datetime
                timesheet.current_timesheet_id.shift_id = timesheet.shift_id

                new_shift_id = timesheet.employee_id.get_available_employee_shift(timesheet.date)
                new_timesheet = timesheet.env['ta.timesheet'].create({
                    'employee_id': timesheet.employee_id.id,
                    'date': timesheet.current_timesheet_id.date,
                    'first_checkin_time': timesheet.current_timesheet_id.first_checkin_time,
                    'first_checkin_datetime': timesheet.current_timesheet_id.first_checkin_datetime,
                    'last_checkout_time': timesheet.current_timesheet_id.last_checkout_time,
                    'last_checkout_datetime': timesheet.current_timesheet_id.last_checkout_datetime,
                    'schedule_type': timesheet.schedule_type,
                    'cycle_days': timesheet.cycle_days,
                    'department_id': timesheet.employee_id.department_id.id,
                    'policy_group_id': timesheet.employee_id.policy_group_id.id,
                    'shift_id': new_shift_id.id if new_shift_id else False,
                    'name': timesheet.current_timesheet_id.name,
                    'parent_id': timesheet.id
                })
                timesheet.current_timesheet_id = new_timesheet
                timesheet.shift_id = new_shift_id
                punch_logs = timesheet.env['ta.punch_log'].search([('employee_id', '=', timesheet.employee_id.id),
                                                              ('log_time', '>', timesheet.first_checkin_datetime),
                                                              ('log_time', '<=', timesheet.last_checkout_datetime)])
                # self.shift_unit_current_ids[0].first_checkin_datetime = self.first_checkin_datetime
                # self.shift_unit_current_ids[0].first_checkin_time = self.first_checkin_time
                # self._recompute_shift_start_end(self.shift_unit_current_ids[0], self.first_checkin_datetime,
                #                                 self.first_checkin_time)

                for punch_log in punch_logs:
                    # self._assign_shift_unit_checkin_out(new_timesheet.parent_id, punch_log.log_time)
                    punch_log.action_execute_log()

    def action_create_timesheet_automated(self):
        last_timesheet_date = self.env['ir.config_parameter'].sudo().get_param('ta.last_timesheet_date', default=False)
        if last_timesheet_date:
            last_timesheet_date = datetime.datetime.strptime(last_timesheet_date, '%Y-%m-%d').date() + timedelta(days=1)
        else:
            last_timesheet_date = fields.Date.today()

        employee_ids = self.env['hr.employee'].search([('active', '=', True), '|', ('shift_id', '!=', False),
                                                       ('policy_group_id', '!=', False)])

        while last_timesheet_date <= fields.Date.today():
            log_date = datetime.datetime.combine(last_timesheet_date, datetime.time(hour=0, minute=0))
            for employee_id in employee_ids:
                self._create_timesheet(employee_id, log_date, log_date, absent=True)
            _logger.log(25, "Create Timesheets for " + str(log_date))
            last_timesheet_date = last_timesheet_date + timedelta(days=1)

        self.env['ir.config_parameter'].set_param('ta.last_timesheet_date', fields.Date.today())

    def action_open_timesheet_history(self):
        return {
            'name': 'Timesheet History',
            'res_model': 'ta.timesheet',
            'view_mode': 'list',
            'target': 'new',
            'domain': [('id', 'in', self.timesheet_history_ids.ids),
                       ('id', 'not in', [self.id, self.current_timesheet_id.id])],
            'type': 'ir.actions.act_window',
        }

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _check_absent(timesheet, absent_time_criteria):
        timesheet.is_absent = False
        if timesheet.current_timesheet_id:
            timesheet.current_timesheet_id.is_absent = False

        if timesheet.working_time < absent_time_criteria and not timesheet.is_dayoff:
            timesheet.is_absent = True
            timesheet.current_timesheet_id.is_absent = True

    def _prepare_shift_units(self, shift_unit):
        return {
            'name': shift_unit.name,
            'color': shift_unit.color,
            'employee_id': self.employee_id.id,
            'start_time': shift_unit.start_time,
            'end_time': shift_unit.end_time,
            'start_limit': shift_unit.start_limit,
            'required_time': shift_unit.duration,
            'is_overnight': shift_unit.is_overnight,
            'shift_type': shift_unit.shift_type,
            'apply_min_max': shift_unit.apply_min_max,
            'min_checkin_time': shift_unit.min_checkin_time,
            'max_checkout_time': shift_unit.max_checkout_time,
            'grace_in_time': shift_unit.rule_id.grace_in_time,
            'grace_out_time': shift_unit.rule_id.grace_out_time,
            'absent_time_criteria': shift_unit.absent_time_criteria,
            'half_work_checkin_criteria': shift_unit.rule_id.half_work_checkin_criteria,
            'half_work_checkout_criteria': shift_unit.rule_id.half_work_checkout_criteria,
            'calc_half_no_checkout_time': shift_unit.rule_id.calc_half_no_checkout_time,
            'parent_timesheet_id': self.parent_id.id,
            'date': self.date
        }

    def _assign_shift_unit_checkin_out(self, timesheet, log_datetime):

        all_logs = []
        if timesheet.punch_logs:
            all_logs = list(map(lambda datetime_str: datetime.datetime.strptime(datetime_str,
                                                                                '%Y-%m-%d %H:%M:%S'),
                                timesheet.punch_logs.split(',')))
        if log_datetime not in all_logs:
            all_logs.append(log_datetime)
        all_logs.sort()
        timesheet.punch_logs = ','.join(list(map(str, all_logs)))

        if timesheet.shift_unit_current_ids:
            sorted_shift_units = timesheet.shift_unit_current_ids.sorted('start_time')
            # TODO review why reset all sorted_shift_units , but you will update last record only
            sorted_shift_units.write({
                'first_checkin_datetime': False, 'first_checkin_time': 0.0, 'last_checkout_datetime': False,
                'last_checkout_time': 0.0
            })

            for log in all_logs:
                user_tz_log = log.astimezone(pytz.timezone(self._get_tz()))
                if user_tz_log.date() == timesheet.date + timedelta(days=1):
                    if not sorted_shift_units[-1].first_checkin_datetime:
                        sorted_shift_units[-1].first_checkin_datetime = log
                        sorted_shift_units[-1].first_checkin_time = convert_to_float_time(user_tz_log.time())
                    else:
                        sorted_shift_units[-1].last_checkout_datetime = log
                        sorted_shift_units[-1].last_checkout_time = convert_to_float_time(user_tz_log.time())

                    timesheet.last_checkout_datetime = log
                    timesheet.last_checkout_time = convert_to_float_time(user_tz_log.time())
                else:
                    self._re_assign_shift_unit_checkin_out(timesheet, sorted_shift_units, log)

            timesheet.first_checkin_datetime = sorted_shift_units[0].first_checkin_datetime
            timesheet.first_checkin_time = sorted_shift_units[0].first_checkin_time
        else:
            timesheet.first_checkin_time = convert_to_float_time(
                all_logs[0].astimezone(pytz.timezone(self._get_tz())).time())
            timesheet.first_checkin_datetime = all_logs[0]
            if len(all_logs) > 1:
                timesheet.last_checkout_time = convert_to_float_time(
                    all_logs[-1].astimezone(pytz.timezone(self._get_tz())).time())
                timesheet.last_checkout_datetime = all_logs[-1]

    def _re_assign_shift_unit_checkin_out(self, timesheet, sorted_shift_units,
                                          punch_datetime):  # old was _get_shift_unit
        """detect shift unit based on punch time"""
        punch_datetime = punch_datetime.replace(second=0)
        log_time = convert_to_float_time(punch_datetime.astimezone(pytz.timezone(self._get_tz())).time())
        timesheet.last_checkout_time = log_time
        timesheet.last_checkout_datetime = punch_datetime

        diff_in = abs(log_time - sorted_shift_units[0].start_time)
        diff_out = abs(log_time - sorted_shift_units[0].end_time) if not sorted_shift_units[0].is_overnight else (
                                                                                                                         24 - log_time) + \
                                                                                                                 sorted_shift_units[
                                                                                                                     0].end_time
        min_diff = min([diff_out, diff_in])
        nearest_units = [sorted_shift_units[0]]
        close_times = ['in' if diff_in <= diff_out else 'out']

        if len(sorted_shift_units) > 1:
            index = 1
            while index < len(sorted_shift_units):
                shift_unit = sorted_shift_units[index]
                diff_in = abs(log_time - shift_unit.start_time)
                diff_out = abs(log_time - shift_unit.end_time) if not shift_unit.is_overnight else (
                                                                                                           24 - log_time) + shift_unit.end_time
                if diff_out < min_diff or diff_in < min_diff:
                    nearest_units = [shift_unit]
                    min_diff = min([diff_out, diff_in])
                    close_times = ['in' if diff_in <= diff_out else 'out']

                elif diff_out == min_diff or diff_in == min_diff:
                    nearest_units.append(shift_unit)
                    close_times.append('in' if diff_in <= diff_out else 'out')
                index += 1

            if len(nearest_units) == 1:
                unit = nearest_units[0]
                if close_times[0] == 'in' and (not unit.first_checkin_datetime):
                    unit.first_checkin_datetime = punch_datetime
                    unit.first_checkin_time = log_time
                elif close_times[0] == 'in' and unit.first_checkin_datetime:
                    unit_index = sorted_shift_units.ids.index(unit.id)
                    previous_unit = sorted_shift_units[unit_index - 1] if unit_index > 0 else False
                    if previous_unit:
                        previous_diff = abs(unit.first_checkin_time - previous_unit.end_time)
                        current_diff = abs(unit.first_checkin_time - unit.start_time)
                        if previous_diff == current_diff:
                            if not previous_unit.apply_min_max or \
                                    (
                                            previous_unit.apply_min_max and previous_unit.max_checkout_time >= unit.first_checkin_time):
                                previous_unit.last_checkout_datetime = unit.first_checkin_datetime
                                previous_unit.last_checkout_time = unit.first_checkin_time
                        unit.first_checkin_datetime = punch_datetime
                        unit.first_checkin_time = log_time
                    else:
                        unit.last_checkout_datetime = punch_datetime
                        unit.last_checkout_time = log_time

                else:
                    unit.last_checkout_datetime = punch_datetime
                    unit.last_checkout_time = log_time

            elif len(nearest_units) == 2:
                first_unit = nearest_units[0]
                if not first_unit.last_checkout_datetime:
                    first_unit.last_checkout_datetime = punch_datetime
                    first_unit.last_checkout_time = log_time
                else:
                    nearest_units[1].first_checkin_time = log_time
                    nearest_units[1].first_checkin_datetime = punch_datetime

        elif len(sorted_shift_units) == 1:
            if not sorted_shift_units[0].first_checkin_datetime:
                sorted_shift_units[0].first_checkin_datetime = punch_datetime
                sorted_shift_units[0].first_checkin_time = log_time
                self._recompute_shift_start_end(sorted_shift_units[0], punch_datetime, log_time)
            else:
                sorted_shift_units[0].last_checkout_time = log_time
                sorted_shift_units[0].last_checkout_datetime = punch_datetime

    def _recompute_shift_start_end(self, shift_unit, punch_datetime, log_time):
        if shift_unit.shift_type != 'normal':
            shift_start_dt = datetime.datetime.combine(shift_unit.date,
                                                       convert_to_time_object(shift_unit.start_time))
            shift_start_dt = convert_to_utc(shift_start_dt, self._get_tz())
            shift_start_limit_dt = datetime.datetime.combine(
                shift_unit.date if shift_unit.start_limit > shift_unit.start_time \
                    else shift_unit.date + timedelta(days=1),
                convert_to_time_object(shift_unit.start_limit))
            shift_start_limit_dt = convert_to_utc(shift_start_limit_dt, self._get_tz())

            if shift_start_dt <= punch_datetime <= shift_start_limit_dt or shift_unit.shift_type == 'open':
                shift_unit.start_time = log_time
            if punch_datetime > shift_start_limit_dt and shift_unit.shift_type == 'flexible':
                shift_unit.start_time = shift_unit.start_limit

            shift_unit.end_time = get_sum(shift_unit.start_time, shift_unit.required_time * 60)
            if shift_unit.start_time > shift_unit.end_time:
                shift_unit.is_overnight = True
            else:
                shift_unit.is_overnight = False

    def _calc_shift_unit(self, shift_unit):
        """calculate  time sheet fields  for specified shift unit history to apply rule on time calculation"""
        if shift_unit.apply_min_max:
            self._apply_min_max_calculation(shift_unit)
        # --- Delay ---------------------------------------------------
        self._calc_delay(shift_unit)
        # --- Shortage ---------------------------------------------------
        self._calc_shortage(shift_unit)
        # --- Working Time & OverTime-------------------------------------------------
        self._calc_working_time(shift_unit)

        if shift_unit.working_time > shift_unit.required_time:
            shift_unit.overtime = shift_unit.working_time - shift_unit.required_time
            shift_unit.overtime_factored = shift_unit.overtime * shift_unit.overtime_factor
        else:
            shift_unit.overtime = 0

        # --- Final Results -------------------------------------------------
        if not shift_unit.is_delayed:
            shift_unit.delay_time = 0

        if not shift_unit.is_shortage:
            shift_unit.shortage_time = 0

        shift_unit.total_delay_shortage = shift_unit.delay_time + shift_unit.shortage_time

        # Half Working Day Conditions
        self._check_half_work_criteria(shift_unit)

    def _check_half_work_criteria(self, shift_unit):

        if (shift_unit.half_work_checkout_criteria and shift_unit.shortage_time > get_float_min(
                shift_unit.half_work_checkout_criteria)) \
                or (shift_unit.half_work_checkin_criteria and shift_unit.delay_time > get_float_min(
            shift_unit.half_work_checkin_criteria)) \
                or (shift_unit.calc_half_no_checkout_time and not shift_unit.last_checkout_datetime):
            shift_unit.working_time = shift_unit.required_time / 2 if shift_unit.working_time > shift_unit.required_time / 2 \
                else shift_unit.working_time

    def _apply_min_max_calculation(self, shift_unit):
        if shift_unit.first_checkin_datetime:
            shift_start_datetime = compine_date_time(shift_unit.date, shift_unit.min_checkin_time)
            utc_shift_start = convert_to_utc(shift_start_datetime, self._get_tz())
            if utc_shift_start > shift_unit.first_checkin_datetime:
                shift_unit.first_checkin_datetime = utc_shift_start
                shift_unit.first_checkin_time = shift_unit.min_checkin_time

        if shift_unit.last_checkout_datetime:
            shift_end_datetime = compine_date_time(shift_unit.date if not shift_unit.is_overnight
                                                   else shift_unit.date + timedelta(days=1),
                                                   shift_unit.max_checkout_time)
            utc_shift_end = convert_to_utc(shift_end_datetime, self._get_tz())
            if shift_unit.last_checkout_datetime > utc_shift_end:
                shift_unit.last_checkout_datetime = utc_shift_end
                shift_unit.last_checkout_time = shift_unit.max_checkout_time

    def _calc_delay(self, shift_unit):
        shift_unit.is_delayed = False
        if shift_unit.first_checkin_datetime:
            shift_start_datetime = compine_date_time(shift_unit.date, shift_unit.start_time)
            utc_shift_start = convert_to_utc(shift_start_datetime, self._get_tz())
            if shift_unit.first_checkin_datetime > utc_shift_start:
                shift_unit.delay_time = round(
                    (shift_unit.first_checkin_datetime - utc_shift_start).total_seconds() / 60 / 60, 2)
            if shift_unit.delay_time > get_float_min(shift_unit.grace_in_time):
                shift_unit.is_delayed = True

    def _calc_shortage(self, shift_unit):
        shift_unit.is_shortage = False
        if shift_unit.last_checkout_datetime:
            shift_end_datetime = compine_date_time(shift_unit.date if not shift_unit.is_overnight
                                                   else shift_unit.date + timedelta(days=1), shift_unit.end_time)
            utc_shift_end = convert_to_utc(shift_end_datetime, self._get_tz())
            if shift_unit.last_checkout_datetime < utc_shift_end:
                shift_unit.shortage_time = round(
                    (utc_shift_end - shift_unit.last_checkout_datetime).total_seconds() / 60 / 60, 2)

            if shift_unit.shortage_time > get_float_min(shift_unit.grace_out_time):
                shift_unit.is_shortage = True

    def _calc_working_time(self, shift_unit):
        if shift_unit.last_checkout_datetime:
            shift_unit.working_time = round(
                (shift_unit.last_checkout_datetime - shift_unit.first_checkin_datetime).total_seconds() / 60 / 60, 2)
        else:
            if shift_unit.calc_half_no_checkout_time:
                shift_unit.working_time = shift_unit.required_time / 2
            else:
                shift_unit.working_time = 0

    # def _re_assign_shift_unit_checkin_out_old(self, timesheet, sorted_shift_units, punch_datetime):  # old was _get_shift_unit
    #     """detect shift unit based on punch time"""
    #     log_time = convert_to_float_time(punch_datetime.astimezone(pytz.timezone(self.env.user.tz)).time())
    #     timesheet.last_checkout_time = log_time
    #     timesheet.last_checkout_datetime = punch_datetime
    #
    #     if len(sorted_shift_units) == 1 and not timesheet.parent_id:
    #         sorted_shift_units.last_checkout_datetime = punch_datetime
    #         sorted_shift_units.last_checkout_time = log_time
    #
    #     elif len(sorted_shift_units) > 1 and not timesheet.parent_id:
    #         index = 0
    #         while index < len(sorted_shift_units):
    #             shift_unit = sorted_shift_units[index]
    #             min_start = shift_unit.start_time if not shift_unit.apply_min_max else shift_unit.min_checkin_time
    #             max_end = shift_unit.end_time if not shift_unit.apply_min_max else shift_unit.max_checkout_time
    #
    #             if not shift_unit.first_checkin_datetime and not shift_unit.last_checkout_datetime \
    #                     and ((min_start <= log_time <= max_end and not shift_unit.is_overnight) \
    #                          or (min_start <= log_time and shift_unit.is_overnight)) \
    #                     or min_start >= log_time:
    #                 previous_unit = sorted_shift_units[index - 1]
    #
    #                 if not previous_unit.last_checkout_datetime and previous_unit.first_checkin_datetime:  # and previous_unit.max_checkout_time >= log_time:
    #                     previous_unit.last_checkout_datetime = punch_datetime
    #                     previous_unit.last_checkout_time = log_time
    #                     break
    #
    #                 else:
    #                     shift_unit.first_checkin_datetime = punch_datetime
    #                     shift_unit.first_checkin_time = log_time
    #                     self._recompute_shift_start_end(shift_unit, punch_datetime, log_time)
    #                     break
    #
    #             if index + 1 == len(sorted_shift_units) and shift_unit.first_checkin_datetime:
    #                 shift_unit.last_checkout_datetime = punch_datetime
    #                 shift_unit.last_checkout_time = log_time
    #
    #             index += 1

    # def _create_timesheet_history(self, schedule_day, shift):
    #     pass
    # def _clac_timesheet_summary(self, ):
    #     """calculate time fields summation from current shift units to store in parent time sheet"""
    #     pass
    # def _set_employee_info(self, ):
    #     """assign department,policy group , shift"""
    #     pass
    # def _create_timesheet_history(self, schedule_day, shift):
    #     pass
    # endregion

    # region ---------------------- TODO[IMP] API  Methods  -------------------------------------
    def action_test_get_att(self):
        # create object AttendanceParameterDTO with static parameters

        att_param = AttendanceParameterDTO('4343', 'Success', '122', '2013', 'dev/2012', 'abigail/13031', '1001',
                                           '45749857fdjfjfldjf545j4jl54j5', '', '-1',
                                           '20230522', '20230527')
        self.get_employee_attendance(att_param)
        att_param = AttendanceParameterDTO('4343', 'Success', '122', '2013', 'dev/2012', 'abigail/13031', '1001',
                                           '45749857fdjfjfldjf545j4jl54j5', '', '0',
                                           '20230522', '20230527')
        self.get_employee_attendance(att_param)

        att_param = AttendanceParameterDTO('4343', 'Success', '122', '2013', 'dev/2012', 'abigail/13031', '1001',
                                           '45749857fdjfjfldjf545j4jl54j5', '', '1',
                                           '20230522', '20230527')
        self.get_employee_attendance(att_param)
        att_param = AttendanceParameterDTO('4343', 'Success', '122', '2013', 'dev/2012', 'abigail/13031', '1001',
                                           '45749857fdjfjfldjf545j4jl54j5', '', '2',
                                           '20230522', '20230527')
        self.get_employee_attendance(att_param)

    def get_employee_attendance(self, att_param: AttendanceParameterDTO) -> MobileAttendanceDTO:
        """
         Retrieve the attendance record of an employee based on the given attendance parameters.
         Args:
             att_param (AttendanceParameterDTO): An object containing the attendance parameters.
         Returns:
             MobileAttendanceDTO: An object containing the attendance record of the employee.
         """
        attendance, vacations, permissions = [], [], []
        summary = False
        employee_domain = self.env['hr.employee']._get_employee_domain(att_param)
        # ('employee_number', '=', att_param.emp_no) if att_param.emp_no \
        # else ('user_name', '=', att_param.username)

        if att_param.action in ['-1', '0']:
            domain = [employee_domain, ('parent_id', '=', False),
                      ('date', '>=', datetime.datetime.strptime(att_param.date_from, '%Y%m%d').date()),
                      ('date', '<=', datetime.datetime.strptime(att_param.date_to, '%Y%m%d').date())]

            if att_param.action == '0':
                domain.extend([('is_vacation', '=', False), ('is_public_vacation', '=', False)])

            timesheets = self.env['ta.timesheet'].search(domain)

            summary = self._prepare_attendance(timesheets, attendance)

        else:
            self._prepare_time_off(employee_domain, att_param, permissions, vacations)
            summary = self._prepare_attendance([], attendance)
        mobile_attendance_dto = MobileAttendanceDTO(attendance=attendance, device_info=att_param.device_info,
                                                    permissions=permissions, response_code=att_param.response_code,
                                                    response_message=att_param.response_message, summary=summary,
                                                    vacations=vacations)
        mobile_attendance_dto.response_code = '1'
        mobile_attendance_dto.response_message = 'OK'
        return mobile_attendance_dto

    def _prepare_attendance(self, timesheets, attendance: []):
        total_absent = 0  # : int
        total_delay_and_shortage_minutes = 0  # : int
        total_delay_minutes = 0  # : int
        total_overtime_minutes = 0  # : int
        total_permission_minutes = 0  # : int
        total_shortage_minutes = 0  # : int
        total_vacation = 0  # : int
        total_working_minutes = 0  # : int

        for ts in timesheets:
            delay_in_min = get_total_minutes(ts.delay_time) if ts.delay_time else 0
            shortage_in_min = get_total_minutes(ts.shortage_time) if ts.shortage_time else 0
            over_in_min = get_total_minutes(ts.overtime) if ts.overtime else 0
            permission_time_min = get_total_minutes(ts.time_off_hours) if ts.time_off_hours else 0
            working_time_min = get_total_minutes(ts.working_time) if ts.working_time else 0
            required_time_min = get_total_minutes(ts.required_time) if ts.required_time else 0

            total_absent = total_absent + 1 if ts.is_absent else total_absent
            total_delay_and_shortage_minutes += shortage_in_min + delay_in_min  # : int
            total_delay_minutes += delay_in_min  # : int
            total_overtime_minutes += over_in_min  # : int
            total_permission_minutes += permission_time_min  # : int
            total_shortage_minutes += shortage_in_min  # : int
            total_vacation = total_vacation + 1 if ts.is_vacation else total_vacation  # : int
            total_working_minutes += working_time_min  # : int

            department_ar_name = ts.employee_id.with_context(
                lang='ar_001').department_id.name if ts.employee_id.department_id else ''
            department_en_name = ts.employee_id.with_context(
                lang='en_us').department_id.name if ts.employee_id.department_id else ''
            shift_ar_name = ts.employee_id.with_context(lang='ar_001').shift_id.name if ts.employee_id.shift_id else ''
            shift_en_name = ts.employee_id.with_context(lang='en_us').shift_id.name if ts.employee_id.shift_id else ''
            ar_notes = ts.with_context(lang='ar_001').notes if ts.notes else ''
            en_notes = ts.with_context(lang='en_us').notes if ts.notes else ''
            date_string = ts.date.strftime('%Y%m%d')
            checkin_time_string = fields.Datetime.context_timestamp(ts, ts.first_checkin_datetime) \
                .strftime('%Y%m%d%H%M%S') if ts.first_checkin_datetime else ""  # ts.first_checkin_time_char
            checkout_time_string = fields.Datetime.context_timestamp(ts, ts.last_checkout_datetime) \
                .strftime('%Y%m%d%H%M%S') if ts.last_checkout_datetime else ""  # ts.last_checkout_time_char

            attend = Attendance(checkin_time_string, checkout_time_string, date_string,
                                round(required_time_min), round(delay_in_min),
                                department_ar_name, department_en_name, round(ts.diff_working_time),
                                str(ts.employee_id.id), ar_notes, en_notes, over_in_min, permission_time_min,
                                shift_ar_name, shift_en_name, shortage_in_min, ts.mobile_status, working_time_min)

            attendance.append(attend)

        summary = Summary(total_absent, total_delay_and_shortage_minutes, str(total_delay_and_shortage_minutes),
                          total_delay_minutes, str(total_delay_minutes), total_overtime_minutes,
                          str(total_overtime_minutes), total_permission_minutes, str(total_permission_minutes),
                          total_shortage_minutes, str(total_shortage_minutes), total_vacation, total_working_minutes,
                          str(total_working_minutes))
        return summary

    def _prepare_time_off(self, employee_domain, att_param, permissions, vacations):
        # override method in time off & return dto
        pass

    # -------------------- API Attendance log creation ----------------------------------------------------
    def action_test_attend_log(self):  # '%Y/%m/%d H:M:S'
        data = EmpAttendanceLogDTO('', '', '', '', '23231', 'abigail/13031', '', 'u0z3rt0k3n', '',
                                   '2023/06/07 01:00:00',
                                   23.0, 34.7, '01:00', 1)
        result = self.add_attendance_log(data)
        print("done")
        # data2 = EmpAttendanceLogDTO('', '', '', '', '23231', 'abigail/13031', '', 'u0z3rt0k3n', '',
        #                            '2023/06/07 01:00:00',
        #                            23.0, 34.7, '01:00', 1)
        # self.add_attendance_log(data2)

    def add_attendance_log(self, data: EmpAttendanceLogDTO) -> EmpAttendanceLogDTO:
        """
        create a new punch log for employee based on specific time or time now and assign map location

        Args:
            data (EmpAttendanceLogDTO): A data transfer object containing the employee's attendance information.

        Returns:
            EmpAttendanceLogDTO: A data transfer object containing the updated attendance information for the employee.
        """
        if not data.emp_no and not data.hr_code and not data.username:
            data.response_code = "100"
            data.response_message = "Input data not found"
            return data

        employee_domain = ('employee_number', '=', data.emp_no) if data.emp_no \
            else ('user_name', '=', data.username) if data.username \
            else ('enroll_number', '=', data.hr_code)

        employee = self.env['hr.employee'].search([employee_domain])

        if len(employee.ids) == 1:
            if not employee.mobile_app_allow or not employee.mobile_attendance_allow:
                data.response_code = "114"
                data.response_message = "Attendance permission is denied"
                return data

            if employee.mobile_register_id in [False, '', ' ']:
                data.response_code = "113"
                data.response_message = "AppId not valid"  # "Error:You don't have registration for mobile app"
                return data

            log_datetime = fields.Datetime.now()
            if data.date_time_string:
                log_datetime = datetime.datetime.strptime(data.date_time_string,
                                                          '%Y/%m/%d %H:%M:%S')  # yyyy/MM/dd HH:mm:ss
                log_datetime = convert_to_utc(log_datetime, self._get_tz())

            punch_log = self.env['ta.punch_log'].create({'employee_id': employee.id, 'log_time': log_datetime,
                                                         'from_mobile': True, 'longitude': data.longitude,
                                                         'latitude': data.latitude, 'device_number': data.device_info,
                                                         })

            if punch_log:
                if not data.date_time_string:
                    data.date_time_string = log_datetime.strftime("%Y/%m/%d H:M:S")
                    data.time = log_datetime.strftime("H:M")
                # employee.mobile_last_login_date = log_datetime
                employee.mobile_token = employee.mobile_token
                data.response_code = "1"  # success
                data.response_message = "OK"
        else:
            data.response_code = "111"
            data.response_message = "This user not registered in time attendance system"
        return data

    # endregion
