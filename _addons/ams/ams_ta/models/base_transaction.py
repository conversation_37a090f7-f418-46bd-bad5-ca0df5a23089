# -*- coding: utf-8 -*-
import datetime

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.addons.ta.helper.helper import *


class BaseTransaction(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.base_transaction"
    _description = "Base Timesheet Transactions"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    @property
    def ta_tz(self):
        return self.env['ams_base.abstract_model']._get_tz()

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    employee_number = fields.Char(related="employee_id.employee_number", string="Employee No", index=True)
    enroll_number = fields.Char(related="employee_id.enroll_number", string="Enroll No", readonly=True,
                                help="Device Enroll Number", index=True)
    user_name = fields.Char(related="employee_id.user_id.login", string="Username")
    emp_user_id = fields.Many2one(related="employee_id.user_id", string="User ID", index=True)
    dept_path_code = fields.Char(string="Dept path code",
                                 help="Technical field used for employee time folow-up based on department hierarchy include parent ids .current department id like 1.5.6")

    color = fields.Char(string="Color", help="")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    employee_id = fields.Many2one(comodel_name="hr.employee", string="Employee", index=True)
    policy_group_id = fields.Many2one(comodel_name="ta.policy_group", string="Policy Group")
    department_id = fields.Many2one(comodel_name="hr.department", string="Department")
    company_id = fields.Many2one(comodel_name="res.company", string="Company")
    parent_department_id = fields.Many2one(related="department_id.parent_id", string="Parent Department", store=True)
    current_department_id = fields.Many2one(related="employee_id.department_id", string="Current Department",
                                            store=True)

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model
    def create(self, values):
        res = super(BaseTransaction, self).create(values)
        res._update_employee_info()
        return res

    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    def _prepare_timesheet_values(self, employee_id, log_date, u_tz_log_date, absent=False, parent_timesheet=None):
        shift_id = employee_id.get_available_employee_shift(u_tz_log_date.date())
        schedule_id = shift_id.schedule_id if shift_id else False
        values = {
            'name': employee_id.name + "/" + u_tz_log_date.strftime('%Y-%m-%d'),
            'employee_id': employee_id.id,
            'date': u_tz_log_date.date(),
            'department_id': employee_id.department_id.id,
            'parent_id': parent_timesheet.id if parent_timesheet else False,
        }
        if schedule_id:
            values.update({'schedule_type': schedule_id.schedule_type,
                           'cycle_days': schedule_id.cycle_days,
                           'policy_group_id': employee_id.policy_group_id.id,
                           'shift_id': shift_id.id if shift_id else False})
            if absent:
                values['is_absent'] = True

        if not absent:
            values['first_checkin_time'] = convert_to_float_time(log_date.time())
            values['first_checkin_datetime'] = log_date
            values['name'] = employee_id.name + "/" + u_tz_log_date.strftime('%Y-%m-%d %H:%M')
        else:
            if not schedule_id:
                values = False

        return values

    def _create_timesheet(self, employee_id, date, u_tz_date, absent=False):
        created_timesheet = self.env['ta.timesheet'].search([('date', '=', u_tz_date.date()),
                                                             ('employee_id', '=', employee_id.id),
                                                             ('parent_id', '!=', False)])
        if created_timesheet:
            return created_timesheet

        values = self._prepare_timesheet_values(employee_id, date, u_tz_date, absent)
        if values:
            parent_timesheet = self.env['ta.timesheet'].create(values) # origin timesheet displayed for user
            values['parent_id'] = parent_timesheet.id
            child_timesheet = self.env['ta.timesheet'].create(values) # timesheet act as logging in background
            parent_timesheet.current_timesheet_id = child_timesheet

            if not absent:
                parent_timesheet.action_calc_timesheet()

            return child_timesheet
        return False

    def _update_employee_info(self):
        for rec in self:
            if rec.employee_id:
                rec.department_id = rec.employee_id.department_id
                rec.company_id = rec.employee_id.company_id
                rec.policy_group_id = rec.employee_id.policy_group_id
                rec.dept_path_code = rec.employee_id.path_code
                rec.employee_number = rec.employee_id.employee_number
                rec.enroll_number = rec.employee_id.enroll_number

    def _checkin_timesheet(self, timesheet, log_time):
        timesheet.write({'date': log_time.astimezone(pytz.timezone(self.ta_tz)).date(),
                         'first_checkin_time': convert_to_float_time(
                             log_time.astimezone(pytz.timezone(self.ta_tz)).time()),
                         'first_checkin_datetime': log_time})
        timesheet.parent_id.write({'date': log_time.astimezone(pytz.timezone(self.ta_tz)).date(),
                                   'first_checkin_time': convert_to_float_time(
                                       log_time.astimezone(pytz.timezone(self.ta_tz)).time()),
                                   'first_checkin_datetime': log_time})
        self._checkin_shift_unit(timesheet.shift_unit_history_ids[0], log_time)
        timesheet._convert_time_char()

    def _checkin_shift_unit(self, shift_unit, log_time):
        shift_unit.write(
            {'date': log_time.astimezone(pytz.timezone(self.ta_tz)).date(),
             'first_checkin_time': convert_to_float_time(log_time.astimezone(pytz.timezone(self.ta_tz)).time()),
             'first_checkin_datetime': log_time})
        timesheet = shift_unit.timesheet_id
        timesheet._recompute_shift_start_end(timesheet.shift_unit_history_ids[0],
                                             timesheet.first_checkin_datetime,
                                             timesheet.first_checkin_time)

    def _checkout_timesheet(self, timesheet, log_time):
        timesheet.write({'last_checkout_time': convert_to_float_time(
            log_time.astimezone(pytz.timezone(self.ta_tz)).time()),
            'last_checkout_datetime': log_time})
        timesheet.parent_id.write({'last_checkout_time': convert_to_float_time(
            log_time.astimezone(pytz.timezone(self.ta_tz)).time()),
            'last_checkout_datetime': log_time})
        timesheet._convert_time_char()

    def _checkout_shift_unit(self, shift_unit, log_time):
        shift_unit.write(
            {'last_checkout_time': convert_to_float_time(log_time.astimezone(pytz.timezone(self.ta_tz)).time()),
             'last_checkout_datetime': log_time})

    # endregion
