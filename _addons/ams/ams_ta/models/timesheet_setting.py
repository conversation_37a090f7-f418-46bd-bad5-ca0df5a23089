# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class ResConfigSettings(models.TransientModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = 'res.config.settings'
    _description = "System Info"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    last_timesheet_date = fields.Date()
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def set_values(self):
        res = super(ResConfigSettings, self).set_values()
        self.env['ir.config_parameter'].set_param('ta.last_timesheet_date', self.last_timesheet_date)
        return res

    def get_values(self):
        res = super(ResConfigSettings, self).get_values()
        last_timesheet_date_value = self.env['ir.config_parameter'].sudo().get_param('ta.last_timesheet_date',  default=False)

        if last_timesheet_date_value:
            res.update(
                last_timesheet_date=last_timesheet_date_value
            )
        return res
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    # endregion
