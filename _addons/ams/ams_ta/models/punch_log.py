# -*- coding: utf-8 -*-
import datetime

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.addons.ta.helper.helper import *

STATES = [('pending', 'Pending'), ('executed', 'Executed')]


class PunchLog(models.Model):
    _name = "ta.punch_log"
    _description = "Punch Log"
    _inherit = ["ams_base.abstract_model", "ta.base_transaction", "mail.thread", "mail.activity.mixin"]
    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(required=True, default="New", readonly=True, string="Name", help="")
    log_time = fields.Datetime(string="Time", readonly=True, states={"pending": [("readonly", False)]},
                               help="Punch time manually or from device", required=True)
    date = fields.Date(string="Date", readonly=True, compute="_compute_date", store=True,
                       states={"pending": [("readonly", False)]})

    execute_log_time = fields.Datetime(string="Execute log time", readonly=True, tracking=True,
                                       states={"pending": [("readonly", False)]},
                                       help="when time proceed in time sheet")
    employee_number = fields.Char(string="Employee No", readonly=True, states={"pending": [("readonly", False)]},
                                  help="Related from employee.registeration_number")
    enroll_number = fields.Char(string="Enroll No", readonly=True, states={"pending": [("readonly", False)]},
                                help="Device Enroll Number")
    device_number = fields.Char(string="Device No", readonly=True, states={"pending": [("readonly", False)]},
                                help="")
    state = fields.Selection(selection=STATES, readonly=True, default=STATES[0][0], string="State",
                             help="if log time  transferred to time sheet and time processing done, the state would be executed")
    dept_path_code = fields.Char(string="Dept path code", readonly=True, states={"pending": [("readonly", False)]},
                                 help="Technical field used for employee  time folow-up based on department hierarchy")
    notes = fields.Char(string="Notes", readonly=True, states={"pending": [("readonly", False)]}, tracking=True,
                        help="")
    longitude = fields.Float(string="Longitude", readonly=True, states={"pending": [("readonly", False)]},
                             help="Technical field when creating  multiple record at same time , like  import from excel sheet ")
    latitude = fields.Float(string="Latitude", readonly=True, states={"pending": [("readonly", False)]}, help="")
    from_mobile = fields.Boolean(string="From mobile", readonly=True, states={"pending": [("readonly", False)]},
                                 help="")

    # endregion

    # region  Special
    # endregion

    # region  Computed
    @api.depends('log_time')
    def _compute_date(self):
        for rec in self:
            rec.date = fields.Datetime.context_timestamp(rec, rec.log_time).date()
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model
    def create(self, vals):
        punch_log = super(PunchLog, self).create(vals)
        punch_log._generate_timesheet(punch_log.employee_id, punch_log.log_time)
        return punch_log

    # def write(self, vals):
    #     pass

    # def unlink(self):
    #     for me_id in self:
    #         if me_id.state != STATES[0][0]:
    #             raise UserError("Cannot delete non pending record!")
    #     return super(PunchLog, self).unlink()
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods ---------------------------------------
    def action_execute_log(self):
        for rec in self:
            rec._update_employee_info()
            rec._generate_timesheet(rec.employee_id, rec.log_time)

    def action_pending(self):
        self.state = 'pending'

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    def _generate_timesheet(self, employee_id, log_time_utc):
        # TODO Method & code documentation
        log_time_user_tz = log_time_utc.astimezone(pytz.timezone(self._get_tz()))
        log_time = datetime.datetime(log_time_user_tz.year, log_time_user_tz.month, log_time_user_tz.day,
                                     log_time_user_tz.hour, log_time_user_tz.minute, 0)
        last_timesheets = self.env['ta.timesheet'].search([('employee_id', '=', employee_id.id),
                                                           ('parent_id', '!=', False),
                                                           '|',
                                                           ('date', '=', log_time.date()),
                                                           ('date', '=', log_time.date() - datetime.timedelta(days=1))
                                                           ], limit=2, order='date DESC')
        yesterday_timesheet = False
        today_timesheet = False

        for ts in last_timesheets:
            if ts.date == log_time.date():
                today_timesheet = ts
            elif ts.date == (log_time.date() - datetime.timedelta(days=1)):
                yesterday_timesheet = ts

        if not today_timesheet:
            today_timesheet = self._create_timesheet(employee_id, log_time_utc, log_time, absent=True)

        # Check if the log time belongs to yesterday timesheet checkout if it's overnight
        if not yesterday_timesheet:
            date_time = log_time.replace(hour=0, minute=0) - datetime.timedelta(days=1)
            date_time_utc = convert_to_utc(date_time, self._get_tz())
            yesterday_timesheet = self._create_timesheet(employee_id, date_time_utc, date_time, absent=True)

        check_yesterday = False
        is_today = False
        if today_timesheet and today_timesheet.shift_unit_history_ids:
            first_unit = today_timesheet.shift_unit_history_ids[0]
            start_time = first_unit.min_checkin_time if first_unit.min_checkin_time else first_unit.start_time
            start_datetime = datetime.datetime.combine(today_timesheet.date, convert_to_time_object(start_time))
            if log_time >= start_datetime:
                is_today = True
            else:
                check_yesterday = True
        else:
            check_yesterday = True

        if check_yesterday:
            is_today = True

            if yesterday_timesheet and yesterday_timesheet.shift_unit_history_ids:
                last_shift_unit = yesterday_timesheet.shift_unit_history_ids[-1]
                first_today_unit = today_timesheet.shift_unit_history_ids[
                    0] if today_timesheet.shift_unit_history_ids else False

                if last_shift_unit.is_overnight:
                    start_datetime = yesterday_timesheet.first_checkin_datetime if yesterday_timesheet.first_checkin_datetime \
                        else datetime.datetime.combine(last_shift_unit.date,
                                                       convert_to_time_object(last_shift_unit.start_time))
                    end_diff = 0
                    start_diff = 0
                    if first_today_unit:
                        end_diff = abs(convert_to_float_time(log_time.time()) - last_shift_unit.end_time)
                        start_diff = abs(convert_to_float_time(log_time.time()) - first_today_unit.start_time)

                    if ((log_time - start_datetime).total_seconds() / 60) / 60 <= 10 or end_diff < start_diff:
                        yesterday_timesheet._assign_shift_unit_checkin_out(yesterday_timesheet.parent_id, log_time_utc)
                        yesterday_timesheet.parent_id.action_calc_timesheet()
                        is_today = False

                    # if start_diff > 5:
                    #     is_today = False

        if is_today and today_timesheet:
            today_timesheet._assign_shift_unit_checkin_out(today_timesheet.parent_id, log_time_utc)
            today_timesheet.parent_id.action_calc_timesheet()

        self.state = 'executed'
        self.execute_log_time = fields.Datetime.now()

    # def _create_timesheet(self):
    #     # TODO Method & code documentation
    #     punch_log = self
    #     last_timesheets = self.env['ta.timesheet'].search([('employee_id', '=', punch_log.employee_id.id),
    #                                                        ('parent_id', '!=', False), '|',
    #                                                        ('date', '=', punch_log.log_time.date()),
    #                                                        ('date', '=',
    #                                                         punch_log.log_time.date() - datetime.timedelta(days=1))
    #                                                        ], limit=2, order='date DESC')
    #     timesheet = False
    #     if len(last_timesheets) == 2:  # TODO explain
    #         timesheet = last_timesheets[0]
    #     else:
    #         if last_timesheets:
    #             if last_timesheets.date == punch_log.log_time.date():
    #                 timesheet = last_timesheets
    #             else:
    #                 if last_timesheets.shift_unit_history_ids:
    #                     # Fixme shift_unit_history_ids[-1] may raise error when shift_unit_history_ids
    #                     last_shift_unit = last_timesheets.shift_unit_history_ids[-1]
    #                     if last_shift_unit.is_overnight and not last_shift_unit.last_checkout_datetime:
    #                         if punch_log.log_time.time() >= datetime.time(hour=6):  # TODO explain
    #                             timesheet = last_timesheets
    #
    #     if not timesheet:
    #         # TODO explain what's parent , child
    #         parent_timesheet = self.env['ta.timesheet'].create(punch_log._prepare_timesheet_values())
    #         # parent_timesheet.action_calc_timesheet()  # abadr: #TODO review by abeer
    #         child_timesheet = self.env['ta.timesheet'].create(punch_log._prepare_timesheet_values(parent_timesheet))
    #         parent_timesheet.current_timesheet_id = child_timesheet
    #     else:
    #         if timesheet.first_checkin_datetime:
    #             timesheet._assign_shift_unit_checkin_out(timesheet.parent_id, punch_log.log_time)
    #         else:
    #             if timesheet.shift_unit_history_ids:
    #                 timesheet.write({'date': punch_log.log_time.date(),
    #                 'first_checkin_time': convert_to_float_time(punch_log.log_time.astimezone(pytz.timezone(self.env.user.tz)).time()),
    #                 'first_checkin_datetime': punch_log.log_time})
    #                 timesheet.shift_unit_history_ids[0].first_checkin_datetime = timesheet.first_checkin_datetime
    #                 timesheet.shift_unit_history_ids[0].first_checkin_time = timesheet.first_checkin_time
    #                 timesheet._recompute_shift_start_end(timesheet.shift_unit_history_ids[0],
    #                                                      timesheet.first_checkin_datetime,
    #                                                      timesheet.first_checkin_time)
    #         timesheet.parent_id.action_calc_timesheet()  # abadr: #TODO review by abeer
    #
    # def _prepare_timesheet_values(self, parent_timesheet=None):
    #     employee_id = self.employee_id
    #     log_date = self.log_time
    #     shift_id = employee_id.get_available_employee_shift(log_date.date())
    #     schedule_id = shift_id.schedule_id if shift_id else False
    #     if schedule_id:
    #         values = {
    #             'employee_id': employee_id.id,
    #             'date': log_date.date(),
    #             'first_checkin_time': convert_to_float_time(log_date.astimezone(pytz.timezone(self.env.user.tz)).time()),
    #             'first_checkin_datetime': log_date,
    #             'schedule_type': schedule_id.schedule_type,
    #             'cycle_days': schedule_id.cycle_days,
    #             'department_id': employee_id.department_id.id,
    #             'policy_group_id': employee_id.policy_group_id.id,
    #             'shift_id': shift_id.id if shift_id else False,
    #             'name': employee_id.name + "/" + log_date.astimezone(pytz.timezone(self.env.user.tz)).strftime(
    #                 '%Y-%m-%d %H:%M'),
    #             'parent_id': parent_timesheet.id if parent_timesheet else False,
    #         }
    #     else:
    #         values = {'name': employee_id.name + "/" + log_date.astimezone(pytz.timezone(self.env.user.tz)).strftime(
    #                 '%Y-%m-%d %H:%M')}
    #     return values

    # durations.append(abs(shift_unit.start_time - log_time))
    # durations.append(abs(shift_unit.end_date - log_time) if not shift_unit.is_overnight
    #                  else (24 - log_time) + shift_unit.end_time)

# min_duration = min(durations)
# duration_index = durations.index(min_duration)
# if duration_index % 2 != 0:
#     index = (duration_index - 1) - (duration_index - 1) / 2 if duration_index > 1 else 0
#     shift_unit = sorted_shift_units[index]
#     if shift_unit.last_checkout_datetime:
#         if index < len(sorted_shift_units):
#             if not sorted_shift_units[index + 1].first_checkin_datetime
#     shift_unit.last_checkout_datetime = punch_log.log_time
#     shift_unit.last_checkout_time = log_time
# else:
#     index = duration_index - duration_index / 2 if duration_index > 0 else 0
#     shift_unit = sorted_shift_units[index]
#     shift_unit.first_checkin_datetime = punch_log.log_time
#     shift_unit.first_checkin_time = log_time
# endregion
