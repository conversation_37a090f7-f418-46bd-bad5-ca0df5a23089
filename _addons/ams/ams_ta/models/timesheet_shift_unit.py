import datetime

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.tools import pytz
from odoo.addons.ta.helper.helper import *


class TimeSheetShiftUnit(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.timesheet_shift_unit"
    _description = "Time Sheet Shift Unit"
    _inherit = ["ta.base_timesheet", "ta.base_transaction"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    apply_min_max = fields.Boolean()
    min_checkin_time = fields.Float(string="Min checkin time", help="minimum time allowed to checkin ")
    max_checkout_time = fields.Float(string="Max checkout time", help="maximum time allowed to checkout")
    grace_in_time = fields.Float(string="Grace in time", help="Allowed minutes to delay in start of day")
    grace_out_time = fields.Float(string="Grace out time", help="Allowed minutes to leave early in end of day")
    absent_time_criteria = fields.Float(string="Absent time criteria",
                                        help="make employee absent if working time less than (hours)")
    half_work_checkin_criteria = fields.Float(string="Half day If Checkin After",
                                              help="Calculate half of working day if employee checkin after time")
    half_work_checkout_criteria = fields.Float(string="Half day If Checkout Before",
                                               help="Calculate half of working  if employee leave before time ...")
    calc_half_no_checkout_time = fields.Boolean(string="If No Checkout",
                                                help="Half day Calculate half of working day if no checkout ")

    start_time = fields.Float(string="Start Time", help="", required=True)
    end_time = fields.Float(string="End Time", help="", required=True )
    start_limit = fields.Float(string="Max Start")
    shift_type = fields.Selection(selection=[('normal', 'Normal'), ('flexible', 'Flexible'), ('open', 'Open')],
                                  string="Shift Type", help="")
    color = fields.Char(string="Color", help="")

    overtime_factor = fields.Float(string="Overtime Factor", help="Factor")  # 1.5
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # TODO: add rule fields
    # TODO: add shift unit fields
    timesheet_id = fields.Many2one(comodel_name="ta.timesheet", string="Timesheet", help="", ondelete='cascade')
    parent_timesheet_id = fields.Many2one(comodel_name="ta.timesheet", string="Parent Timesheet", help="", ondelete='cascade')
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
     # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def _message_create(self, values_list):
        message_id = super(TimeSheetShiftUnit, self)._message_create(values_list)
        for rec in self:
            dates_vals = message_id.tracking_value_ids.filtered(lambda line: line.field_id.name in ['first_checkin_datetime', 'last_checkout_datetime'] )
            if rec.parent_timesheet_id and dates_vals:
                for date_val in dates_vals:
                    old_value = date_val.old_value_datetime.astimezone(pytz.timezone(self._get_tz())).strftime('%Y-%m-%d %H:%M') if date_val.old_value_datetime else "None"
                    new_value = date_val.new_value_datetime.astimezone(pytz.timezone(self._get_tz())).strftime('%Y-%m-%d %H:%M') if date_val.new_value_datetime else "None"
                    str_body = str('Shift "'+rec.name + '" ' + date_val.field_id.field_description +
                                   ' changed from ' + old_value
                                   + ' to ' + new_value)
                    rec.parent_timesheet_id.message_post(body=str_body)
        return message_id
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_manual_checkin_checkout(self):
        default_date = compine_date_time(self.date, 0.0)
        default_date = convert_to_utc(default_date, self._get_tz())
        return {
            'name': 'Shit ' + self.name,
            'res_model': 'ta.manual_attendance_wizard',
            'view_mode': 'form',
            # 'view_id': self.env.ref('ta.view_shift_unit_manual_attend_form').id,
            'target': 'new',
            'type': 'ir.actions.act_window',
            'context': {'default_shift_unit_id': self.id,
                        'default_checkin_datetime': self.first_checkin_datetime if self.first_checkin_datetime
                        else default_date,
                        'default_checkout_datetime': self.last_checkout_datetime if self.last_checkout_datetime
                        else default_date if not self.is_overnight else default_date + datetime.timedelta(days=1)
                        }
        }
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion

    # @api.onchange('first_checkin_time')
    # def _onchange_checkin_time(self):
    #     if self.first_checkin_datetime:
    #         new_datetime = self._update_log(self.first_checkin_datetime, self.first_checkin_time)
    #         self.first_checkin_datetime = self.first_checkin_datetime.replace(year=new_datetime.year, month=new_datetime.month,
    #                                                                           day=new_datetime.day, hour=new_datetime.hour,
    #                                                                           minute=new_datetime.minute)
    #     else:
    #         self.first_checkin_datetime = datetime.datetime.combine(self.date, convert_to_time_object(self.first_checkin_time))
    #
    #     self.timesheet_id.parent_id.action_calc_timesheet()
    #
    # @api.onchange('last_checkout_time')
    # def _onchange_checkout_time(self):
    #     if self.last_checkout_datetime:
    #         new_datetime = self._update_log(self.last_checkout_datetime, self.last_checkout_time)
    #         self.last_checkout_datetime = self.last_checkout_datetime.replace(year=new_datetime.year,
    #                                                                           month=new_datetime.month,
    #                                                                           day=new_datetime.day,
    #                                                                           hour=new_datetime.hour,
    #                                                                           minute=new_datetime.minute)
    #     else:
    #         self.last_checkout_datetime = datetime.datetime.combine(self.date if not self.is_overnight else self.date + datetime.timedelta(days=1)
    #                                                                 , convert_to_time_object(self.last_checkout_time))
    #
    #     self.timesheet_id.parent_id.action_calc_timesheet()
    #
    # def _update_log(self, datetime, new_time):
    #     new_datetime = datetime.astimezone(pytz.timezone(self.env.user.tz))
    #     new_datetime = new_datetime.replace(hour=int(new_time), minute=get_minutes(new_time))
    #     new_datetime = new_datetime.astimezone(pytz.UTC)
    #     self.timesheet_id.punch_logs.replace(str(datetime), new_datetime.strftime('%Y-%m-%d %H:%M:%S'))
    #     return new_datetime

