# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class PolicyGroup(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.policy_group"
    _description = "ta.policy_group"
    _inherit = "ams_base.code_model"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    employee_ids = fields.One2many(comodel_name="hr.employee", inverse_name="policy_group_id", string="Employees",
                                   help="")
    primary_shift_id = fields.Many2one(comodel_name="ta.shift", string="Primary shift", help="", tracking=True)
    secondary_shift_id = fields.Many2one(comodel_name="ta.shift", string="Secondary shift", help="", tracking=True)
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
