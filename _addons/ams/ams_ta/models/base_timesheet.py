# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
# from odoo.addons.ta.helper.helper import *
# from addons_ta.ta.helper.helper import *
from ..helper.helper import *


class BaseTimesheet(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.base_timesheet"
    _description = "Base Time Sheet"
    _inherit = ["ams_base.abstract_model", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(required=True, help="")
    date = fields.Date(string="Date", help="Checkin Date")
    first_checkin_time = fields.Float(string="First Checkin Time", help="")
    last_checkout_time = fields.Float(string="Last Checkout Time", help="")

    first_checkin_time_char = fields.Char(compute='_convert_time_char', store=True)
    last_checkout_time_char = fields.Char(compute='_convert_time_char', store=True)

    first_checkin_datetime = fields.Datetime(string="First Checkin Datetime", tracking=True,
                                             help="Technical fields for time calculation")
    last_checkout_datetime = fields.Datetime(string="Last Checkout Datetime", tracking=True,
                                             help="Technical fields for time calculation")
    checked_in = fields.Boolean()
    checked_out = fields.Boolean()

    required_time = fields.Float(string="Required Time", help="Required Time by hours")
    working_time = fields.Float(string="Working Time", help="Working Time by hours")
    diff_working_time = fields.Float(string="Diff Working Time", help="working_time - required_time")
    delay_time = fields.Float(string="Delay Time", help="Delay Time by hours")
    shortage_time = fields.Float(string="Shortage Time", help="Shortage Time by hours")
    total_delay_shortage = fields.Float(string="Total Delay Shortage")
    overtime = fields.Float(string="Overtime", help="Overtime by hours")
    overtime_factored = fields.Float(string="Overtime Factor", help="Sum of units (Overtime * Factor)")

    required_time_min = fields.Float(string="Required Time (Min)", help="Required Time by minutes",
                                     compute='_compute_time_min')
    working_time_min = fields.Float(string="Working Time (Min)", help="Working Time by minutes")
    diff_working_time_min = fields.Float(string="Diff Working Time (Min)", help="working_time - required_time")
    delay_time_min = fields.Float(string="Delay Time (Min)", help="Delay Time by minutes")
    shortage_time_min = fields.Float(string="Shortage Time (Min)", help="Shortage Time by minutes")
    total_delay_shortage_min = fields.Float(string="Total Delay|Shortage (Min)")
    overtime_min = fields.Float(string="Overtime (Min)", help="Overtime by minutes")
    overtime_factored_min = fields.Float(string="Overtime Factor (Min)", help="Sum of units (Overtime * Factor)")

    notes = fields.Char(string="Notes", help="", tracking=True, translate=True)
    is_delayed = fields.Boolean(string="Is Delayed", help="Compute field if employee delayed")
    is_shortage = fields.Boolean(string="Is Shortage", help="Compute field if employee leaved early ")
    is_overnight = fields.Boolean(string="Is Overnight", help="")

    from_mobile = fields.Boolean(string="From Mobile", help="")
    in_longitude = fields.Float(string="In Longitude", help="Technical field, Checkin longitude  from mobile")
    in_latitude = fields.Float(string="In Latitude", help="Technical field,  Checkin latitude from mobile")
    out_longitude = fields.Float(string="Out Longitude", help="Technical field,  Checkout longitude  from mobile")
    out_latitude = fields.Float(string="Out Latitude", help="Technical field,  Checkout latitude from mobile")

    # endregion

    # region  Special
    # endregion

    # region  Relational
    employee_id = fields.Many2one(comodel_name="hr.employee", string="Employee", help="")

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('first_checkin_datetime', 'last_checkout_datetime')
    def _convert_time_char(self):
        for record in self:
            if record.first_checkin_datetime:
                record.first_checkin_time_char = convert_to_str_time_format(record.first_checkin_time)
            else:
                record.first_checkin_time_char = ' - '

            if record.last_checkout_datetime:
                record.last_checkout_time_char = convert_to_str_time_format(record.last_checkout_time)
            else:
                record.last_checkout_time_char = ' - '

    @api.depends('required_time', 'working_time', 'delay_time', 'shortage_time', 'overtime')
    def _compute_time_min(self):
        # compute all float time field to minutes by dividing by 60 and rounding
        for record in self:
            record.required_time_min = round(record.required_time * 60)
            record.working_time_min = round(record.working_time * 60)
            record.diff_working_time_min = round(record.diff_working_time * 60)
            record.delay_time_min = round(record.delay_time * 60)
            record.shortage_time_min = round(record.shortage_time * 60)
            record.total_delay_shortage_min = round(record.total_delay_shortage * 60)
            record.overtime_min = round(record.overtime * 60)
            record.overtime_factored_min = round(record.overtime_factored * 60)

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def reset_time_calculations(self):
        self.working_time = 0
        self.total_delay_shortage = 0
        self.delay_time = 0
        self.shortage_time = 0
        self.overtime = 0
        self.is_delayed = False
        self.is_shortage = False
        self.checked_in = False
        self.checked_out = False

    def set_day_absent(self):
        """set current timesheet as absent  or timesheet shift unit """
        ts = self.env['ta.timesheet']
        shift_unit = self.env['ta.timesheet_shift_unit']
        if self._name == 'ta.timesheet':
            ts = self
        elif self._name == 'ta.timesheet_shift_unit':
            shift_unit = self

        current_date = datetime.datetime.combine(self.date, datetime.time(hour=0, minute=0))
        log_date = convert_to_utc(current_date, self._get_tz())

        if len(shift_unit) == 1:
            shift_unit.first_checkin_datetime = log_date
            shift_unit.last_checkout_datetime = log_date
            shift_unit.first_checkin_time = 0
            shift_unit.last_checkout_time = 0
            shift_unit.reset_time_calculations()
            shift_unit.timesheet_id.set_day_absent()  # to call second part of timesheet

        # update timesheet when call shift_unit.timesheet_id.set_day_absent() | timesheet_id.set_day_absent()
        if len(ts) == 1:
            ts.punch_logs = False
            ts.first_checkin_datetime = log_date
            ts.last_checkout_datetime = log_date
            ts.first_checkin_time = 0
            ts.last_checkout_time = 0
            ts.is_absent = True
            ts.is_attend_day = False
            ts.reset_time_calculations()
            ts._compute_flag_fields()

            # ts._compute_color()
            if ts.parent_id:
                ts.parent_id.punch_logs = False
                ts.parent_id.is_absent = True
                # self.shift_unit_id.timesheet_id.parent_id._clear_delay_shortage()
                ts.parent_id.first_checkin_datetime = log_date
                ts.parent_id.last_checkout_datetime = log_date
                ts.parent_id.first_checkin_time = 0
                ts.parent_id.last_checkout_time = 0
                ts.parent_id.is_attend_day = False
                ts.parent_id.reset_time_calculations()
                ts.parent_id._compute_flag_fields()
    # endregion
