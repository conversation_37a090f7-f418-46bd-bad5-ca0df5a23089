# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class ShiftRule(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.shift_rule"
    _description = "ta.shift_rule"
    _inherit = "ams_base.code_model"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    grace_in_time = fields.Float(string="Grace in time", help="Allowed minutes to delay in start of day", tracking=True)
    grace_out_time = fields.Float(string="Grace out time", help="Allowed minutes to leave early in end of day", tracking=True)

    half_work_checkin_criteria = fields.Float(string="Half day If Checkin After", tracking=True,
                                              help="Calculate half of working day if employee checkin after time")
    half_work_checkout_criteria = fields.Float(string="Half day If Checkout Before", tracking=True,
                                               help="Calculate half of working  if employee leave before time ...")
    calc_half_no_checkout_time = fields.Boolean(string="If No Checkout", tracking=True,
                                                help="Half day Calculate half of working day if no checkout ")

    overtime_factor = fields.Float(string="Overtime Factor", default=1, help="Factor", tracking=True)  # 1.5
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.constrains('grace_in_time', 'grace_out_time')
    def _constraint_on_time(self):
        if self.grace_out_time < 0 or self.half_work_checkin_criteria < 0:
            raise UserError("Time can't be less than Zero")

    @api.constrains('overtime_factor')
    def _constraint_on_time(self):
        if self.overtime_factor < 0:
            raise UserError("Overtime Factor can't be less than Zero")

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
