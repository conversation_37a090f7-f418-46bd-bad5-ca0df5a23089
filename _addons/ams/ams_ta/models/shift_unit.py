# -*- coding: utf-8 -*-
import datetime

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import timedelta
from odoo.addons.ta.helper.helper import *


class ShiftUnit(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.shift_unit"
    _description = "ta.shift_unit"
    _inherit = "ams_base.code_model"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    start_time = fields.Float(string="Start Time", help="", required=True, tracking=True)
    end_time = fields.Float(string="End Time", help="", required=True, tracking=True )
    start_limit = fields.Float(string="Max Start", tracking=True)
    shift_type = fields.Selection(selection=[('normal', 'Normal'), ('flexible', 'Flexible'), ('open', 'Open')],
                                  string="Shift Type", help="", default='normal', tracking=True)
    duration = fields.Float(string="Duration", help="Hours", compute='_compute_duration', readonly=False, store=True)
    is_overnight = fields.Boolean(string="Is Overnight", help="")
    color = fields.Char(string="Color", help="", required=True, tracking=True)

    apply_min_max = fields.Boolean(default=False, help="Apply Min Checkin Time & Max Checkout Time", tracking=True)
    min_checkin_time = fields.Float(string="Min Checkin Time", help="Minimum time allowed to checkin ", tracking=True)
    max_checkout_time = fields.Float(string="Max Checkout Time", help="Maximum time allowed to checkout", tracking=True)
    absent_time_criteria = fields.Float(string="Absent Time Criteria", tracking=True,
                                        help="make employee absent if working time less than (hours)")

    # endregion

    # region  Special
    # endregion

    # region  Relational
    rule_id = fields.Many2one(comodel_name="ta.shift_rule", string="Rule", help="", tracking=True)

    # endregion

    # region  Computed
    @api.depends('start_time', 'end_time', 'shift_type')
    def _compute_duration(self):
        for record in self:
            if record.start_time >= 0 and record.end_time >= 0 and record.shift_type in ['normal', 'open']:
                if record.start_time < record.end_time:
                    record.duration = (record.end_time - record.start_time)
                    record.is_overnight = False
                else:
                    record.duration = (24 - record.start_time) + record.end_time
                    record.is_overnight = True
            else:
                record.duration = record.duration

    @api.onchange('start_time', 'duration')
    def _onchange_duration(self):

        if self.duration:
            start_time = self.start_time if self.shift_type != 'flexible' else self.start_limit
            self.end_time = get_sum(start_time, self.duration * 60)
        self.is_overnight = True if self.start_time > self.end_time else False
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
