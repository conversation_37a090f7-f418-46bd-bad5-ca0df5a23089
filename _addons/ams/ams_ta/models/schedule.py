# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, time
from odoo.addons.ta.helper.helper import *


class Schedule(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.shift_schedule"
    _description = "ta.shift_schedule"
    _inherit = "ams_base.code_model"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    cycle_days = fields.Integer(string="Cycle Days", tracking=True, help="")
    schedule_type = fields.Selection(selection=[('daily', 'Daily'), ('weekly', 'Weekly')], string="Schedule Type",
                                     help="", tracking=True)
    # endregion

    # region  Special
    # endregion

    # region  Relational
    default_shift_unit = fields.Many2one("ta.shift_unit", tracking=True)
    day_ids = fields.One2many(comodel_name="ta.shift_schedule_day", inverse_name="schedule_id", string="Days", help="")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.onchange('day_ids')
    def _onchange_days(self):
        if self.day_ids:
            for day in self.day_ids:
                day._onchange_shift_units()
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_generate_days(self):
        self._generate_days()

    def action_activate(self):
        if self.day_ids:
            if any(self.day_ids.mapped('units_overlapped')):
                raise ValidationError("You can't activate schedule contains overlapped shifts.")
        else:
            raise ValidationError("You can't activate schedule without any shifts.")
        super(Schedule, self).action_activate()
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _generate_days(self):
        if self.schedule_type == 'weekly':
            self.cycle_days = 7
        days = range(1, self.cycle_days + 1)  # Ex: ['1', '2', '3', '4', '5', '6', '7']
        self.day_ids.unlink()
        self.update(
            {"day_ids": [(0, 0, {'index': day, 'week_day': str(day) if day <= 7 else str(day - 7),
                                 'shift_units_ids': [self.default_shift_unit.id] if self.default_shift_unit and not day in [7, 1] else False}
                          ) for day in days]})

    # endregion
