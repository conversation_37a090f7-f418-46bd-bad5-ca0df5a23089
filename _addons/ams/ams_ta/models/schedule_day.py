# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.addons.ta.helper.helper import *
from odoo.exceptions import UserError, ValidationError


class ScheduleDay(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ta.shift_schedule_day"
    _description = "ta.shift_schedule_day"
    _inherit = ["ams_base.abstract_model", "mail.thread", "mail.activity.mixin"]
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char(compute='_compute_name', store=True, readonly=False)
    week_day = fields.Selection(WEEK_DAYS, string='Day')
    index = fields.Integer(string="Index", help="")
    is_day_off = fields.Boolean(string="Is Day Off", compute='_compute_day_off', store=True, readonly=False)
    units_overlapped = fields.Boolean(string="Units Overlapped", help="")
    units_mixed_types = fields.Boolean(string="Units Mixed Types", help="")

    # endregion

    # region  Special
    # endregion

    # region  Relational
    schedule_id = fields.Many2one(comodel_name="ta.shift_schedule", string="Schedule", help="")
    shift_units_ids = fields.Many2many(comodel_name="ta.shift_unit", string="Shift units", help="")

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('index', 'week_day')
    def _compute_name(self):
        for record in self:
            if record.schedule_id:
                if record.schedule_id.schedule_type == 'weekly':
                    record.name = WEEK_DAYS[int(record.week_day) - 1][1]
                else:
                    record.name = "Day " + str(record.index)
            else:
                record.name = 'Week Day'

    @api.depends('shift_units_ids')
    def _compute_day_off(self):
        for record in self:
            if record.shift_units_ids:
                record.is_day_off = False
            else:
                record.is_day_off = True

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    @api.onchange('is_day_off')
    def _onchange_day_off(self):
        if self.is_day_off:
            self.shift_units_ids = False
        else:
            if self.schedule_id.default_shift_unit and not self.shift_units_ids:
                self.shift_units_ids = [self.schedule_id.default_shift_unit.id]

    @api.onchange('shift_units_ids')
    def _onchange_shift_units(self):
        """Check if there is any overlap between shifts"""
        if self.shift_units_ids:
            types = self.shift_units_ids.mapped('shift_type')
            # print(types)
            if 'open' in types and len(types) > 1:
                raise ValidationError("Open shift can't be added with any other shifts ")

            sorted_shifts = self.shift_units_ids.sorted('start_time')
            overlap = [True if not (convert_to_time_object(shift.end_time if shift.shift_type != 'flexible'
                                                           else get_sum(shift.start_time,
                                                                        shift.duration * 60)) <= convert_to_time_object(
                sorted_shifts[index + 1].start_time)
                                    and shift.end_time > shift.start_time) else False
                       for index, shift in enumerate(sorted_shifts) if index + 1 < len(sorted_shifts)]

            if not any(overlap):
                days = self.schedule_id.day_ids
                if self.index - 2 >= 0 and len(days) > 1:
                    previous_day = days[self.index - 2]
                    previous_unit = False
                    if len(previous_day.shift_units_ids) > 0 and not previous_day.is_day_off:
                        previous_unit = previous_day.shift_units_ids.sorted('start_time')[-1]
                    if previous_unit and previous_unit.is_overnight and previous_unit.end_time > sorted_shifts[0].start_time:
                        overlap.append(True)

                if self.index < len(days) and len(days) > 1:
                    next_day = days[self.index]
                    next_unit = False
                    if len(next_day.shift_units_ids) > 0 and not next_day.is_day_off:
                        next_unit = days[self.index].shift_units_ids.sorted('start_time')[0]

                    if next_unit and sorted_shifts[-1].is_overnight and sorted_shifts[-1].end_time > next_unit.start_time:
                        overlap.append(True)

            self.units_overlapped = True if any(overlap) else False

            # ---- Check if mixed types --------------------------
            self.units_mixed_types = True if len(set(self.shift_units_ids.mapped('shift_type'))) > 1 else False

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
