# -*- coding: utf-8 -*-
import threading

from odoo import api, fields, models, _
from odoo.addons.ta.api.dto_employee import EmployeeDTO, AuthDTO
from odoo.addons.ta.helper.helper import WEEK_DAYS_LIST
from odoo.addons.test_convert.tests.test_env import odoo
from odoo.exceptions import UserError, ValidationError
from odoo.tools import pytz


class Employee(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "hr.employee"
    _inherit = ["hr.employee", "mail.thread", "mail.activity.mixin"]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    test_date = fields.Date()  # TODO: Remove later after done testing
    test_day_index = fields.Char()  # TODO: Remove later after done testing
    # endregion

    # region  Special
    # endregion

    # region  Relational
    policy_group_id = fields.Many2one(comodel_name="ta.policy_group", string="Policy Group", help="", tracking=True)
    shift_id = fields.Many2one(related="policy_group_id.primary_shift_id", store=True, tracking=True)
    path_code = fields.Char(related='department_id.path_code', store=True)
    user_name = fields.Char(related='user_id.login')

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    #         if overlapped_shifts:
    #             raise ValidationError(_("There is an overlap in exception shifts of ") + self.display_name +
    #                                   " \n" +
    #                                   '\n'.join([str('- ' + shift.name + ':  ' +
    #                                                  str(shift.start_date) + '  --->  ' + str(shift.end_date)) for shift
    #                                              in overlapped_shifts]))

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_open_manual_attendance_wizard_employee(self):
        return {
            'name': 'Manual Attendance',
            'res_model': 'ta.manual_attendance_wizard',
            'view_mode': 'form',
            'target': 'new',
            'type': 'ir.actions.act_window',
            'context': {'default_employee_ids': self.ids}
        }

    def action_get_day_index(self):  # TODO: Remove later after done testing
        if self.test_date:
            shift_id = self.get_available_employee_shift(self.test_date)
            if shift_id:
                self.test_day_index = str(self._get_schedule_day(shift_id.schedule_id, self.test_date).name \
                                              if shift_id.schedule_id.schedule_type == 'weekly' else \
                                              self._get_schedule_index(shift_id.start_date, self.test_date,
                                                                       shift_id.schedule_id.cycle_days))

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def get_available_employee_shift(self, date):
        """exception  shift >> secondary shift >> primary shift >> default shift"""

        exception_shift = self.env["ta.shift"].search([("employee_id", "=", self.id),
                                                       ("start_date", "<=", date),
                                                       ("end_date", ">=", date),
                                                       ('status', '=', 'executed')
                                                       ], limit=1)
        if exception_shift:
            return exception_shift

        if self.policy_group_id:
            primary_shift = self.policy_group_id.primary_shift_id
            secondary_shift = self.policy_group_id.secondary_shift_id

            if secondary_shift and secondary_shift.is_running(date):
                return secondary_shift

            if primary_shift and primary_shift.is_running(date):
                return primary_shift

        # if no shift assigned to employee
        default_shift = self.env["ta.shift"].search([("is_default", "=", True)], limit=1)
        return default_shift

    def _get_schedule_index(self, start_date, current_date, cycle_days):  # employee_id, schedule_id ):
        """detect day index in schedule template"""
        days = (current_date - start_date).days + 1
        modulus = days % cycle_days

        return cycle_days if modulus == 0 else int(modulus)

    def _get_schedule_day(self, schedule, start_date):
        """detect day record in schedule template"""
        return schedule.day_ids.filtered(lambda day: day.index == WEEK_DAYS_LIST.index(start_date.strftime('%A')) + 1)

    # endregion

    # region ---------------------- TODO[IMP] API  Methods  -------------------------------------
    def action_for_test_get_employee_api_method(self):
        pass

    def get_employee(self, auth: AuthDTO) -> EmployeeDTO:
        """
         Retrieve the employee record of  the given authentication parameters.

         Args:
             auth (AuthDTO): An object containing the authentication parameters.

         Returns:
             EmployeeDTO: An object containing the Employee record.
         """
        employee = self.env['hr.employee'].search([self._get_employee_domain(auth)])
        emp_dto = EmployeeDTO(app_id=auth.app_id, app_version=auth.app_version, device_info=auth.device_info,
                              emp_no=auth.emp_no, hr_code=auth.hr_code, token=auth.token, username=auth.username)
        if employee:
            emp_dto.emp_no = employee.employee_number
            emp_dto.hr_code = employee.enroll_number
            emp_dto.username = employee.user_name
            emp_dto.email = employee.work_email or employee.user_id.email
            emp_dto.arabic_name = employee.name
            emp_dto.english_name = employee.name
            #emp_dto.email = employee.user_partner_id.email if employee.user_partner_id else ''
            emp_dto.phone_no = employee.work_phone if employee.work_phone else employee.phone
            emp_dto.area_arabic_name = employee.address_id.city if employee.address_id else ''
            emp_dto.area_english_name = employee.address_id.city if employee.address_id else ''
            emp_dto.bg_arabic_name = employee.policy_group_id.ar_name if employee.policy_group_id else ''  # policy_group
            emp_dto.bg_english_name = employee.policy_group_id.en_name if employee.policy_group_id else ''  # policy_group
            emp_dto.branch_arabic_name = employee.with_context(
                lang='ar_001').company_id.name if employee.company_id else ''
            emp_dto.branch_english_name = employee.with_context(
                lang='en_us').company_id.name if employee.company_id else ''
            emp_dto.dept_arabic_name = employee.with_context(
                lang='ar_001').department_id.name if employee.department_id else ''
            emp_dto.dept_english_name = employee.with_context(
                lang='en_us').department_id.name if employee.department_id else ''

            emp_dto.token = employee.mobile_token
            emp_dto.response_code = '1'
            emp_dto.response_message = 'OK'
        else:
            emp_dto.response_code = "111"
            emp_dto.response_message = "This user not registered in time attendance system"

        return emp_dto

    def register_app(self, auth: AuthDTO) -> str:
        """
         register mobile app of the employee based on given authentication parameters.
         Args:
             auth (AuthDTO): An object containing the authentication parameters.
         Returns:
             str: return '1' if success or return error message
        """
        """register unique app id for employee"""
        employee = self.env['hr.employee'].search([self._get_employee_domain(auth)])
        response_message = "0"
        if employee:
            # "validate app allowed for employee", "Not allowed for multiple registration"
            if employee.mobile_register_id:
                employee.mobile_register_id = employee.mobile_register_id.replace(' ', '')

            if not employee.mobile_app_allow:
                response_message = "Error:You don't have mobile app using permission"

            elif employee.mobile_register_id:

                if employee.mobile_register_id != auth.app_id:
                    response_message = "Error:Not allowed for multiple registration"
                else:
                    response_message = "Error:this app already register!! please contact administrator"

            elif not employee.mobile_register_id:
                employee.mobile_register_id = auth.app_id
                employee.mobile_token = auth.token
                employee.mobile_app_version = auth.app_version
                employee.mobile_register_date = fields.Datetime.now()
                response_message = "1"

        return response_message

    def authenticate(self, auth: AuthDTO, validate_app_id=False, database='dev15e_ta_20221015') -> str:
        """ call odoo user authentication from related user"""
        # self.env.cr.dbname
        action = "01"  # login or register
        employee = self.env['hr.employee'].search([self._get_employee_domain(auth)])
        if len(employee) != 1:
            response_message = "Error:Employee doesn't exist"
        else:
            response_message = "1"
            if not employee.user_id:
                response_message = "Error:Employee doesn't have user"
            else:
                if validate_app_id:
                    mobile_register_no = employee.mobile_register_id.strip() if employee.mobile_register_id else False
                    if employee.mobile_app_allow and not mobile_register_no:
                        response_message = "Error:You don't have registration for mobile app"
                    elif employee.mobile_app_allow and employee.mobile_register_id and employee.mobile_register_id != auth.app_id:
                        response_message = "Error:Not allowed for multiple registration"
                    elif not employee.mobile_app_allow:
                        response_message = "Error:You don't have mobile app using permission"

                if auth.auth_type == 1 and response_message == '1':
                    try:
                        self.env.user.authenticate(database, auth.username, auth.password, {})
                        # if employee.user_id.id != uid:
                        #     response_message = "App Error: Invalid Authentication"
                    except Exception as ex:
                        response_message = ex

        return response_message

    def mobile_authenticate(self, auth: AuthDTO, validate_app_id=False) -> EmployeeDTO:
        """ call odoo user authentication from related user"""
        # self.env.cr.dbname
        emp_dto = EmployeeDTO(app_id=auth.app_id, app_version=auth.app_version, device_info=auth.device_info,
                              emp_no=auth.emp_no, hr_code=auth.hr_code, token=auth.token, username=auth.username)
        emp_dto.response_code = "101"  # login fail
        emp_dto.response_message = ""  # login fail
        employee = self.env['hr.employee'].search([self._get_employee_domain(auth)])
        if len(employee) <= 0:
            emp_dto.response_code = "101"  # login fail
            emp_dto.response_message = "Error:Employee doesn't exist"
        else:
            if not employee.user_id:
                emp_dto.response_message = "Error:Employee doesn't have user"
            else:
                if validate_app_id:
                    mobile_register_no = employee.mobile_register_id.strip() if employee.mobile_register_id else False
                    if employee.mobile_app_allow and not mobile_register_no:
                        if auth.hr_code:  # in case user want to confirm register app by NID or EMP_NO
                            if auth.hr_code.strip() == employee.employee_number:  # register data
                                employee.mobile_register_id = auth.app_id
                                employee.mobile_token = auth.token
                                employee.mobile_app_version = auth.app_version
                                employee.mobile_register_date = fields.Datetime.now()
                            else:
                                emp_dto.response_code = "111"
                                emp_dto.response_message = "This user not registered in time attendance system"
                        else:
                            emp_dto.response_code = "111"
                            emp_dto.response_message = "This user not registered in time attendance system"

                    elif employee.mobile_app_allow and mobile_register_no and mobile_register_no != auth.app_id:
                        emp_dto.response_code = "109"
                        emp_dto.response_message = "This device already registered for another user,please contact " \
                                                   "administrator!! "
                    elif not employee.mobile_app_allow:
                        emp_dto.response_code = "103"
                        emp_dto.response_message = "This user does not have permission to access mobile app!!"

                if auth.auth_type == 1 and emp_dto.response_message == '':
                    try:
                        uid = self.env.user.authenticate(self.env.cr.dbname, auth.username, auth.password, {})
                        if uid:
                            user = self.env['res.users'].sudo().browse(uid)
                            employee.mobile_token = user._compute_session_token(auth.session_id)
                            employee.mobile_last_login_date = fields.Datetime.now()
                            emp_dto.token = employee.mobile_token
                            emp_dto.hr_code = employee.employee_number
                            if not self.env.user.tz:
                                self.env.user.sudo().tz = employee._get_tz()  # update tz of public user

                            emp_dto.response_code = "1"
                            emp_dto.response_message = "OK"
                            # if employee.user_id.id != uid:
                        #     response_message = "App Error: Invalid Authentication"
                    except Exception as ex:
                        emp_dto.response_code = "101"
                        emp_dto.response_message = ex
                        # response_message = ex

        return emp_dto

    # def mobile_authenticate_csharp(data: AuthDTO) -> EmployeeDTO:
    #     action = "01"  # login or register
    #     result = EmployeeDTO()
    #     isAuthenticated = False
    #
    #     # Assuming there's a Log system in Python similar to the log4net
    #     # Using contextlib for a similar 'using' pattern in C#
    #     with log_thread_context(GetType().FullName + "." + current_function_name()):
    #         try:
    #             if data and data.AppId:
    #                 with object_space() as os:
    #                     action = "02"
    #                     emp = os.find_object("Username=? or HRCode=?", data.Username, data.EmpNo)
    #                     if emp:
    #                         action = "03"
    #                         if data.IsMobileDevice and not emp.UseMobileApp:
    #                             action = "04"
    #                             log_error(
    #                                 "This user does not have permission to access mobile app, code:103, user:" + data.Username)
    #                             return EmployeeDTO(ResponseCode="103",
    #                                                ResponseMessage="This user does not have permission to access mobile app!!")
    #
    #                         if emp.User:
    #                             action = "05"
    #                             if emp.User.AciveDirectoryIsDefault:
    #                                 action = "06"
    #                                 domain = configuration.get("Domain")
    #                                 ADServer = configuration.get("ADServer")
    #                                 isAuthenticated = ADAuth.validate_user(emp.Username, data.Password, domain,
    #                                                                        ADServer)
    #                                 log_info("Success AD Authentication, user:" + emp.Username)
    #                             else:
    #                                 if data.ExternalAuth:
    #                                     action = "07"
    #                                     isAuthenticated = True
    #                                     log_info("Refer authentication to external service, user:" + emp.Username)
    #                                 elif emp.User.compare_password(data.Password):
    #                                     action = "08"
    #                                     isAuthenticated = True
    #                                     log_info("Success Form Authentication, user:" + emp.Username)
    #                                 else:
    #                                     action = "09"
    #                                     log_warn("Invalid Authentication Type, user:" + emp.Username)
    #
    #                     if data.AuthType == 2:
    #                         isAuthenticated = True
    #                         log_debug("Bypass Authentication, test user:" + emp.Username)
    #
    #                     # ... continue as in your original C# code
    #
    #                 # Handle other conditions and cases similarly...
    #
    #             else:
    #                 log_error("Input data not found, code:100")
    #                 return EmployeeDTO(ResponseCode="100", ResponseMessage="Input data not found")
    #
    #         except Exception as ex:
    #             log_error("login failed, code:101", ex)
    #             return EmployeeDTO(ResponseCode="101", ResponseMessage="login failed, tag:" + action)
    #
    #     return result

    def validate_ldap_user(self, username, password, domain) -> bool:
        ...

    def get_db_name(self):
        db = odoo.tools.config['db_name']
        # If the database name is not provided on the command-line,
        # use the one on the thread (which means if it is provided on
        # the command-line, this will break when installing another
        # database from XML-RPC).
        if not db and hasattr(threading.current_thread(), 'dbname'):
            return threading.current_thread().dbname
        return db

    def _get_employee_domain(self, data):
        employee_domain = []
        if data.username:
            return ('user_name', '=', data.username)
        elif data.emp_no:
            return ('employee_number', '=', data.emp_no)
        elif data.hr_code:
            return ('enroll_number', '=', data.hr_code)

        return employee_domain
    # endregion
