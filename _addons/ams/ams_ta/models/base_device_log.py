# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class BaseDeviceLog(models.AbstractModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "ams_base.device_log"
    # _inherit = "ams_base.abstract_model"
    _description = "Base Device Log"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _create_punch_log(self, enroll_no, device_id, log_time):
        """ create punch log when log event created from devices call from ams_suprema
               """
        employee = self.env['hr.employee'].search([('enroll_number', '=', enroll_no)], limit=1)
        if employee:
            log_vals = {
                'enroll_number': enroll_no,
                'device_number': device_id,
                'log_time': log_time,
                'employee_id': employee.id,
                'employee_number': employee.employee_number

            }
            punch_log = self.env['ta.punch_log'].create(log_vals)
            punch_log.action_execute_log()


        ...
    # endregion
