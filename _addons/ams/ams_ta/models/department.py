# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class Department(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _description = "hr.department"
    _inherit = "hr.department"

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    path_code = fields.Char(string="Path code",
                            help="Technical field used for employee time folow-up based on department hierarchy include parent ids .current department id like 1.5.6")

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------

    def create(self, vals):
        department = super(Department, self).create(vals)
        parent = department.parent_id
        department.path_code = self._compute_path_code(parent)
        return department

    def write(self, vals):
        parent_id = vals.get('parent_id')
        for department in self:
            parent = self.env['hr.department'].browse(parent_id) if parent_id else department.parent_id
            vals['path_code'] = self._compute_path_code(parent)
        return super(Department, self).write(vals)
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _compute_path_code(self, parent):
        """
        Helper method to compute the path_code based on the parent department.
        """
        parent_path_code = parent.path_code if parent else '.'
        return f"{parent_path_code}{self.id}."
    # endregion
