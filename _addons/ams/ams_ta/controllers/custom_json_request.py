
from odoo.http import JsonRequest as jr, Response
import json
from functools import wraps
from odoo.http import request

from odoo.tools import date_utils
from odoo.http import JsonRequest, Response



class JsonRequestPatch(JsonRequest):

    def _json_response(self, result=None, error=None):
        response = {
            'jsonrpc': '2.0',
            'id': self.jsonrequest.get('id')
        }
        if error is not None:
            response['error'] = error
        if result is not None:
            response['result'] = result

        mime = 'application/json'
        if request.session.get('custom_json_response', '0') == '1':
            body = json.dumps(result, default=date_utils.json_default)
            request.session['custom_json_response'] = '0'
        else:
            body = json.dumps(response, default=date_utils.json_default)

        return Response(
            body, status=error and error.pop('http_status', 200) or 200,
            headers=[('Content-Type', mime), ('Content-Length', len(body))]
        )
JsonRequest._json_response = JsonRequestPatch._json_response
