from odoo import http
from odoo.addons.ta.api.dto_emp_att_log import EmpAttendanceLogDTO
from odoo.addons.ta.api.dto_mobile_att import AttendanceParameterDTO, MobileAttendanceDTO
# from odoo.addons.ta.controllers.custom_json_request import JsonRequest
from odoo.http import request, Response
import json
from odoo.addons.ta.api.dto_employee import *

import logging

_logger = logging.getLogger('mobile_api')


class MobileAppController(http.Controller):

    @http.route('/test_json', type='http', auth='public', methods=['POST'], csrf=False, cors="*")
    def test_method(self, **kwargs):
        # Your logic to process the request and get the result
        result = {
            "key1": "value1",
            "key2": "value2",
        }
        data = request.get_json_data()
        print(data)
        return request.make_json_response(result, status=200)
        # return self.alternative_json_response(result=result)

    @http.route('/test', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def test_get_method(self, **kwargs):
        # Your logic to process the request and get the result
        _logger.info(f"Test method TODO {request.httprequest.url}")
        result = {
            "version": "17.0.0.1",
            "live": "Ok",
        }
        _logger.info(f"Test API result {result} ")
        # data = request.get_json_data()

        return request.make_json_response(result, status=200)
        # return self.alternative_json_response(result=result)

    @http.route('/MRegisterApp', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def register_app(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        try:
            _logger.info(f"MRegisterApp TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            _logger.info(f"MRegisterApp auth_data: {auth_data} ")
            auth_dto = AuthDTO.from_dict(auth_data)
            _logger.info("MRegisterApp AuthDTO.from_dict(auth_data)")
        except ValueError as ex:
            _logger.error(f"MRegisterApp return response error: {ex} ")
            return request.make_response('Invalid JSON data', status=200)
            # json.dumps({'error': 'Invalid JSON data'})

        # Access the 'authData' field from the JSON object
        if auth_dto:
            # auth_data = auth_data['authData']
            # Do something with the 'authData'
            res = request.env['hr.employee'].sudo().register_app(auth_dto)
            # Return a response as JSON
            # response = {'message': res}
            _logger.info(f"MRegisterApp return response string: {res} ")
            return request.make_response(res, status=200)
            # json.dumps(response)
            # http.Response(json.dumps(response), content_type='application/json',
            #                  status=response.get("response_code"))

        return request.make_response('Invalid request', status=200)  # json.dumps({'error': 'Invalid request'})

    @http.route('/MAuthenticate', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def authenticate(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        res_dict = {}
        try:
            _logger.info(f"MAuthenticate TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            _logger.info(f"MAuthenticate auth_data {auth_data} ")
            auth_dto = AuthDTO.from_dict(auth_data)
            _logger.info(f"MAuthenticate auth_dto {auth_dto} ")
            auth_dto.session_id = request.session.sid
            _logger.info(f"MAuthenticate session_id {auth_dto.session_id} ")
            if auth_dto:
                res = request.env['hr.employee'].sudo().mobile_authenticate(auth_dto, True)
                res_dict = res.to_dict()
                _logger.info(f"MAuthenticate success {res_dict} ")
                return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            res_dict = {
                'response_code': "100",
                'response_message': ex
            }
            _logger.error(f"MAuthenticate return response error: {res_dict} ")
            return request.make_json_response(res_dict, status=200)

        res_dict = {
            'response_code': "100",
            'response_message': 'Error: cannot access login information'
        }
        return request.make_json_response(res_dict, status=200)

        # Access the 'authData' field from the JSON object

    @http.route('/MGetEmployee', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def get_employee(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        try:
            _logger.info(f"MGetEmployee TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            _logger.info(f"MGetEmployee auth_data: {auth_data} ")
            auth_dto = AuthDTO.from_dict(auth_data)
            if auth_dto:
                res = request.env['hr.employee'].sudo().get_employee(auth_dto)
                res_dict = res.to_dict()
                _logger.info(f"MGetEmployee return response EmployeeDTO: {res_dict} ")
                return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            res_dict = {
                'response_code': "100",
                'response_message': ex
            }
            _logger.error(f"MGetEmployee return response error: {res_dict} ")
            return request.make_json_response(res_dict, status=200)

        res_dict = {
            'response_code': "100",
            'response_message': 'Error: cannot get employee data'
        }

        return request.make_json_response(res_dict, status=200)

    @http.route('/MGetEmployeeAttendance', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False,
                cors="*")
    def get_employee_attendance(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        try:
            _logger.info(f"MGetEmployeeAttendance TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            _logger.info(f"MGetEmployeeAttendance auth_data: {auth_data} ")
            auth_dto = AttendanceParameterDTO.from_dict(auth_data)
            if auth_dto:
                res = request.env['ta.timesheet'].sudo().get_employee_attendance(auth_dto)
                res_dict = res.to_dict()
                _logger.info(f"MGetEmployeeAttendance return response MobileAttendanceDTO: {res_dict} ")
                return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            res_dict = {
                'response_code': "100",
                'response_message': ex
            }
            _logger.error(f"MGetEmployeeAttendance return response error: {res_dict} ")
            return request.make_json_response(res_dict, status=200)

        res_dict = {
            'response_code': "100",
            'response_message': 'Error: cannot get employee attendance'
        }
        _logger.error(f"MGetEmployeeAttendance return response error: {res_dict} ")
        return request.make_json_response(res_dict, status=200)

    @http.route('/MAddAttendance', type='http', auth='public', methods=['POST', 'OPTIONS'], csrf=False, cors="*")
    def add_attendance_log(self, **kwargs):
        # Retrieve the JSON object from the request body
        auth_dto = False
        try:
            _logger.info(f"MAddAttendance TODO {request.httprequest.url} ")
            auth_data = request.get_json_data()
            _logger.info(f"MAddAttendance auth_data: {auth_data} ")
            auth_dto = EmpAttendanceLogDTO.from_dict(auth_data)
            res = request.env['ta.timesheet'].sudo().add_attendance_log(auth_dto)
            res_dict = res.to_dict()
            _logger.info(f"MAddAttendance return response EmpAttendanceLogDTO: {res_dict} ")
            return request.make_json_response(res_dict, status=200)

        except Exception as ex:
            res_dict = {
                'response_code': "100",
                'response_message': ex
            }
            _logger.error(f"MAddAttendance return response error: {res_dict} ")
            return request.make_json_response(res_dict, status=200)

        # res_dict = {
        #     'response_code': "100",
        #     'response_message': 'Error: cannot add  attendance'
        # }
        #
        # return request.make_json_response(res_dict, status=200)
