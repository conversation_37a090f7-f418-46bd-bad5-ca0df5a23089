<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_ta_shift_schedule_day_tree" model="ir.ui.view">
            <field name="name">ta_shift_schedule_day_tree</field>
            <field name="model">ta.shift_schedule_day</field>
            <field name="type">tree</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <list string="Schedule day">
                    <field name="name" />
                    <field name="index" />
                    <field name="is_day_off" />
                    <field name="units_overlapped" />
                    <field name="units_mixed_types" />
                    <field name="schedule_id" />
                </list>
            </field>
        </record>
        <!-- form view -->
        <record id="view_ta_shift_schedule_day_form" model="ir.ui.view">
            <field name="name">ta_shift_schedule_day_form</field>
            <field name="model">ta.shift_schedule_day</field>
            <field name="type">form</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <form string="Schedule day">
                    <header>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <!--button type="object" name="action_view_detail" class="oe_stat_button" icon="fa-pencil-square-o"-->
                                <!--field name="detail_count" widget="statinfo" string="Detail(s)"/-->
                                <!--field name="detail_ids" invisible="1"/-->
                            <!--/button-->
                        </div>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only" string="Schedule day Name"/>
                            <h1><field name="name"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="index" />
                                <field name="is_day_off" />
                                <field name="units_overlapped" />
                                <field name="units_mixed_types" />
                            </group>
                            <group>
                                <field name="schedule_id" />
                                <field name="shift_units_ids" widget="many2many_tags"/>
                            </group>
                        </group>
                        <notebook>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        <!-- search -->
        <record id="view_ta_shift_schedule_day_search" model="ir.ui.view">
            <field name="name">ta_shift_schedule_day</field>
            <field name="model">ta.shift_schedule_day</field>
            <field name="arch" type="xml">
                <search string="Schedule day">

                    <filter string="Name" name="filter_by_name" domain="[('name','=','')]" />
                    <filter string="Index" name="filter_by_index" domain="[('index','=','')]" />
                    <filter string="Is day off" name="filter_by_is_day_off" domain="[('is_day_off','=','')]" />
                    <filter string="Units overlapped" name="filter_by_units_overlapped" domain="[('units_overlapped','=','')]" />
                    <filter string="Units mixed types" name="filter_by_units_mixed_types" domain="[('units_mixed_types','=','')]" />
                    <field name="name" select="True"/>
                    <field name="index" select="True"/>
                    <field name="is_day_off" select="True"/>
                    <field name="units_overlapped" select="True"/>
                    <field name="units_mixed_types" select="True"/>
                    <field name="schedule_id" select="True"/>
                    <group string="Group By..">
                        <filter string="Schedule" domain="[]" name="group_by_schedule_id" context="{'group_by':'schedule_id'}"/>
                    </group>
                </search>
            </field>
        </record>
        <!-- kanban , to show image, please add image_small in the model -->
        <record id="view_ta_shift_schedule_day_kanban" model="ir.ui.view" >
            <field name="name">ta_shift_schedule_day</field>
            <field name="model">ta.shift_schedule_day</field>
            <field name="arch" type="xml">
                <kanban version="7.0">
                    <field name="id" />
                    <field name="name" />
                    <field name="index" />
                    <field name="is_day_off" />
                    <field name="units_overlapped" />
                    <field name="units_mixed_types" />
                    <field name="schedule_id" />
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click o_kanban_record_has_image_fill">
                                <div class="oe_kanban_details">
                                    <h4>
                                        <a type="open"><field name="name"/></a>
                                    </h4>
                                    <ul>

                                        <li>
                                            <span class="text-muted">Schedule</span> <field name="schedule_id"></field>
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <!-- calendar if a date field exists -->
        <!-- graph -->
        <record id="view_ta_shift_schedule_day_graph" model="ir.ui.view">
            <field name="name">ta_shift_schedule_day</field>
            <field name="model">ta.shift_schedule_day</field>
            <field name="arch" type="xml">
                <graph string="Schedule day" type="bar">
                    <field name="schedule_id"/>
                </graph>
            </field>
        </record>
        <!-- action window -->
        <record id="action_ta_shift_schedule_day" model="ir.actions.act_window">
            <field name="name">Schedule day</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.shift_schedule_day</field>
            <field name="view_mode">kanban,list,form,graph</field>
            <field name="context">{"search_default_fieldname":1}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                Click to add a new Schedule day
                </p><p>
                Click the Create button to add a new Schedule day
                </p>
            </field>
        </record>

    </data>
</odoo>