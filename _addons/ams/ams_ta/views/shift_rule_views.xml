<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_ta_shift_rule_tree" model="ir.ui.view">
            <field name="name">ta_shift_rule_tree</field>
            <field name="model">ta.shift_rule</field>
            <field name="type">tree</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <list string="Shift rule">
                    <field name="name"/>
                    <field name="grace_in_time" optional="show"/>
                    <field name="grace_out_time" optional="show"/>
                    <field name="half_work_checkin_criteria" optional="hide"/>
                    <field name="half_work_checkout_criteria" optional="hide"/>
                    <field name="calc_half_no_checkout_time" optional="hide"/>
                </list>
            </field>
        </record>
        <!-- form view -->
        <record id="view_ta_shift_rule_form" model="ir.ui.view">
            <field name="name">ta_shift_rule_form</field>
            <field name="model">ta.shift_rule</field>
            <field name="type">form</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <form string="Shift rule">
                    <header>
                        <button type="object" string="Activate" name="action_activate"
                                invisible="activate==True"
                                class="oe_highlight float-right" widget="toggle_button"
                        />
                        <button type="object" string="Deactivate" name="action_deactivate"
                                invisible="activate==False"
                                class="oe_highlight float-right"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                        </div>
                        <field name="active" invisible="1"/>
                        <field name="activate" invisible="1"/>
<!--                        <widget name="web_ribbon" title="Not Active" bg_color="bg-danger"-->
<!--                                attrs="{'invisible': [('activate', '=', True)]}"/>-->
<!--                        <widget name="web_ribbon" title="Active" bg_color="bg-success"-->
<!--                                attrs="{'invisible': [('activate', '=', False)]}"/>-->
                        <!--                        <div class="oe_title">-->
                        <!--                            <label for="name" class="oe_edit_only" string="Shift rule Name"/>-->
                        <!--                            <h1><field name="name"/></h1>-->
                        <!--                        </div>-->
                        <group>
                            <group>
                                <field name="code"/>
                                <field name="name" invisible="1"/>
                                <field name="en_name"/>
                                <field name="ar_name"/>
                            </group>


                        </group>
                        <notebook>
                            <page string="Setting">
                                <group>
                                    <group string="Grace Time">
                                        <field name="grace_in_time"/>
                                        <field name="grace_out_time"/>

                                    </group>
                                    <group string="Half Working Day :">
                                        <field name="half_work_checkin_criteria" string="If Checkin After"/>
                                        <field name="half_work_checkout_criteria" string="If Checkout Before"/>
                                        <field name="calc_half_no_checkout_time" string="If No Checkout"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Audit">
                                <group>
                                    <group>
                                        <field name="activate_date"/>
                                        <field name="activate_uid"/>
                                    </group>
                                    <group>
                                        <field name="deactivate_date"/>
                                        <field name="deactivate_uid"/>
                                    </group>
                                </group>

                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>
        <!-- search -->
        <record id="view_ta_shift_rule_search" model="ir.ui.view">
            <field name="name">ta_shift_rule</field>
            <field name="model">ta.shift_rule</field>
            <field name="arch" type="xml">
                <search string="Shift rule">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="activate"/>
                    <field name="activate_uid"/>
                    <field name="deactivate_uid"/>
                    <filter string="Activated" name="filter_activate" domain="[('activate','=',True)]"/>
                    <filter string="Not Activated" name="filter_activate" domain="[('activate','=',False)]"/>
                    <!--                    <filter string="Max checkout time" name="filter_by_max_checkout_time" domain="[('max_checkout_time','=','')]" />-->
                    <!--                    <filter string="Grace in time" name="filter_by_grace_in_time" domain="[('grace_in_time','=','')]" />-->
                    <!--                    <filter string="Grace out time" name="filter_by_grace_out_time" domain="[('grace_out_time','=','')]" />-->
                    <!--                    <filter string="Absent time criteria" name="filter_by_absent_time_criteria" domain="[('absent_time_criteria','=','')]" />-->
                    <!--                    <filter string="Half work checkin criteria" name="filter_by_half_work_checkin_criteria" domain="[('half_work_checkin_criteria','=','')]" />-->
                    <!--                    <filter string="Half work checkout criteria" name="filter_by_half_work_checkout_criteria" domain="[('half_work_checkout_criteria','=','')]" />-->
                    <!--                    <filter string="Calc half no checkout time" name="filter_by_calc_half_no_checkout_time" domain="[('calc_half_no_checkout_time','=','')]" />-->
                    <!--                    <field name="min_checkin_time" select="True"/>-->
                    <!--                    <field name="max_checkout_time" select="True"/>-->
                    <!--                    <field name="grace_in_time" select="True"/>-->
                    <!--                    <field name="grace_out_time" select="True"/>-->
                    <!--                    <field name="absent_time_criteria" select="True"/>-->
                    <!--                    <field name="half_work_checkin_criteria" select="True"/>-->
                    <!--                    <field name="half_work_checkout_criteria" select="True"/>-->
                    <!--                    <field name="calc_half_no_checkout_time" select="True"/>-->
                    <!--                    <group string="Group By..">-->
                    <!--                    </group>-->
                </search>
            </field>
        </record>

        <!-- action window -->
        <record id="action_ta_shift_rule" model="ir.actions.act_window">
            <field name="name">Shift Rules</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.shift_rule</field>
            <field name="view_mode">list,form</field>
            <field name="context">{"search_default_fieldname": 1}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to add a new Shift rule
                </p>
                <p>
                    Click the Create button to add a new Shift rule
                </p>
            </field>
        </record>
    </data>
</odoo>