<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_ta_timesheet_shift_unit_tree" model="ir.ui.view">
            <field name="name">ta_timesheet_shift_unit_tree</field>
            <field name="model">ta.timesheet_shift_unit</field>
            <field name="type">tree</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <list string="Timesheet shift unit">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="date"/>
                    <field name="first_checkin_time" widget="float_time"/>
                    <field name="first_checkin_datetime" optional="hide"/>
                    <field name="last_checkout_time" widget="float_time"/>
                    <field name="last_checkout_datetime" optional="hide"/>
                    <field name="working_time" widget="float_time"/>
                    <field name="required_time" widget="float_time"/>
                    <field name="diff_working_time" optional="hide" widget="float_time"/>
                    <field name="delay_time" widget="float_time"/>
                    <field name="shortage_time" widget="float_time"/>
                    <field name="total_delay_shortage" optional="hide" widget="float_time"/>
                    <field name="overtime" optional="hide" widget="float_time"/>
                    <field name="overtime_factor"/>
                    <field name="is_delayed" optional="hide"/>
                    <field name="is_shortage" optional="hide"/>
                    <field name="is_overnight" optional="hide"/>
                    <field name="employee_number" optional="hide"/>
                    <field name="enroll_number" optional="hide"/>
                    <field name="dept_path_code" optional="hide"/>
                    <field name="policy_group_id" optional="hide"/>
                    <field name="department_id" optional="hide"/>
                    <field name="timesheet_id" optional="hide"/>
                    <field name="notes" optional="hide"/>
                    <field invisible="1" name="from_mobile"/>
                    <field invisible="1" name="in_longitude"/>
                    <field invisible="1" name="in_latitude"/>
                    <field invisible="1" name="out_longitude"/>
                    <field invisible="1" name="out_latitude"/>
                </list></field>
        </record>
        <!-- form view -->
        <record id="view_ta_timesheet_shift_unit_form" model="ir.ui.view">
            <field name="name">ta_timesheet_shift_unit_form</field>
            <field name="model">ta.timesheet_shift_unit</field>
            <field name="type">form</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <form string="Timesheet shift unit">
                    <header>
                    </header>
                    <sheet>
                        <!--                        <div class="oe_button_box" name="button_box">-->
                        <!--button type="object" name="action_view_detail" class="oe_stat_button" icon="fa-pencil-square-o"-->
                        <!--field name="detail_count" widget="statinfo" string="Detail(s)"/-->
                        <!--field name="detail_ids" invisible="1"/-->
                        <!--/button-->
                        <!--                        </div>-->
                        <!--                        <div class="oe_title">-->
                        <!--                            <label for="name" class="oe_edit_only" string="Timesheet shift unit Name"/>-->
                        <!--                            <h1><field name="name"/></h1>-->
                        <!--                        </div>-->
                        <group>
                            <group>
                                <field name="employee_id"/>
                                <field name="date" readonly="1"/>
                                <field force_save="1" name="first_checkin_time" readonly="1" widget="float_time"/>
                                <field force_save="1" name="last_checkout_time" readonly="1" widget="float_time"/>
                            </group>
                            <group>
                                <field name="working_time" readonly="1" widget="float_time"/>
                                <field name="delay_time" readonly="1" widget="float_time"/>
                                <field name="shortage_time" readonly="1" widget="float_time"/>
                                <field name="overtime" readonly="1" widget="float_time"/>
                            </group>
                        </group>
                        <notebook>
                            <Page string="Employee Info">
                                <group>
                                    <group>
                                        <field name="name" readonly="1" string="Shift"/>
                                        <field name="employee_number" readonly="1"/>
                                        <field name="enroll_number" readonly="1"/>
                                        <field name="policy_group_id" readonly="1"/>
                                        <field name="department_id" readonly="1"/>
                                        <field name="dept_path_code" readonly="1"/>
                                        <field name="timesheet_id" readonly="1"/>
                                        <field name="parent_timesheet_id" readonly="1"/>
                                    </group>
                                </group>
                            </Page>
                            <page string="Time Detail">
                                <group>
                                    <group>
                                        <field name="required_time" readonly="1" widget="float_time"/>
                                        <field name="diff_working_time" readonly="1" widget="float_time"/>
                                        <field name="delay_time" readonly="1" widget="float_time"/>
                                        <field name="shortage_time" readonly="1" widget="float_time"/>
                                        <field name="total_delay_shortage" readonly="1" widget="float_time"/>
                                        <field name="overtime" readonly="1" widget="float_time"/>
                                        <field name="overtime_factor" readonly="1" widget="float_time"/>
                                        <field invisible="0" name="first_checkin_datetime" readonly="1"/>
                                        <field invisible="0" name="last_checkout_datetime" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="is_delayed" readonly="1"/>
                                        <field name="is_shortage" readonly="1"/>
                                        <field name="is_overnight" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Shift Rule">
                                <group>
                                    <group string="Half Working Day :">
                                        <field name="half_work_checkin_criteria" readonly="1" string="If Checkin After"/>
                                        <field name="half_work_checkout_criteria" readonly="1" string="If Checkout Before"/>
                                        <field name="calc_half_no_checkout_time" readonly="1" string="If No Checkout"/>
                                    </group>
                                    <group string="Allowed Time">
                                        <field name="min_checkin_time" readonly="1" widget="float_time"/>
                                        <field name="max_checkout_time" readonly="1" widget="float_time"/>
                                    </group>
                                    <group string="Grace Time">
                                        <field name="grace_in_time" readonly="1" widget="float_time"/>
                                        <field name="grace_out_time" readonly="1" widget="float_time"/>
                                    </group>
                                    <group>
                                        <field name="absent_time_criteria" readonly="0" widget="float_time"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Shift Unit">
                                <group>
                                    <group>
                                        <field name="shift_type" readonly="1"/>
                                        <field name="color" readonly="1" widget="color"/>
                                    </group>
                                    <group>
                                        <field name="start_time" readonly="1" widget="float_time"/>
                                        <field invisible="shift_type == 'open'" name="end_time" readonly="1" widget="float_time"/>
                                        <field name="required_time" readonly="shift_type == 'normal'" widget="float_time"/>
                                        <field name="is_overnight" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            <page invisible="1" string="Mobile Info">
                                <group>
                                    <field name="from_mobile" readonly="1"/>
                                    <field name="in_longitude" readonly="1"/>
                                    <field name="in_latitude" readonly="1"/>
                                    <field name="out_longitude" readonly="1"/>
                                    <field name="out_latitude" readonly="1"/>
                                </group>
                            </page>
                            <page string="Notes">
                                <group>
                                    <field name="notes"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field groups="base.group_user" name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form></field>
        </record>
        <!--        <record id="view_shift_unit_manual_attend_form" model="ir.ui.view">-->
        <!--            <field name="name">ta_timesheet_shift_unit_form</field>-->
        <!--            <field name="model">ta.timesheet_shift_unit</field>-->
        <!--            <field name="type">form</field>-->
        <!--            <field name="priority" eval="8"/>-->
        <!--            <field name="arch" type="xml">-->
        <!--                <form string="Timesheet shift unit">-->
        <!--                    <header>-->
        <!--                    </header>-->
        <!--                    <sheet>-->
        <!--                        <group>-->
        <!--                            <field name="first_checkin_datetime"/>-->
        <!--                            <field name="last_checkout_datetime"/>-->
        <!--                        </group>-->
        <!--                    </sheet>-->
        <!--                </form>-->
        <!--</field>-->
        <!--        </record>-->
        <!-- search -->
        <record id="view_ta_timesheet_shift_unit_search" model="ir.ui.view">
            <field name="name">ta_timesheet_shift_unit</field>
            <field name="model">ta.timesheet_shift_unit</field>
            <field name="arch" type="xml">
                <search string="Timesheet shift unit">
                    <field name="timesheet_id" select="True"/>
                    <group string="Group By..">
                        <filter context="{'group_by':'timesheet_id'}" domain="[]" name="group_by_timesheet_id" string="Timesheet"/>
                    </group>
                </search></field>
        </record>
        <record id="action_ta_timesheet_shift_unit" model="ir.actions.act_window">
            <field name="name">Timesheet shift unit</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.timesheet_shift_unit</field>
            <field name="view_mode">list,form</field>
            <field name="context">{"search_default_fieldname": 1}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to add a new Timesheet shift unit
                </p>
                <p>
                    Click the Create button to add a new Timesheet shift unit
                </p></field>
        </record>
    </data>
</odoo>
