<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_ta_policy_group_tree" model="ir.ui.view">
            <field name="name">ta_policy_group_tree</field>
            <field name="model">ta.policy_group</field>
            <field name="type">tree</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <list string="Policy group">
                    <field name="name"/>
                    <field name="primary_shift_id"  />
                    <field name="secondary_shift_id"  />
                </list>
            </field>
        </record>
        <!-- form view -->
        <record id="view_ta_policy_group_form" model="ir.ui.view">
            <field name="name">ta_policy_group_form</field>
            <field name="model">ta.policy_group</field>
            <field name="type">form</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <form string="Policy group">
                    <header>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <!--button type="object" name="action_view_detail" class="oe_stat_button" icon="fa-pencil-square-o"-->
                                <!--field name="detail_count" widget="statinfo" string="Detail(s)"/-->
                                <!--field name="detail_ids" invisible="1"/-->
                            <!--/button-->
                        </div>
                        <group>
                            <group>
                                 <field name="code"/>
                                <field name="name" invisible="1"/>
                                <field name="en_name"/>
                                <field name="ar_name"/>
                                <field name="active" invisible="1"/>
                                <field name="activate" invisible="1"/>
                            </group>
                            <group>
                                <field name="primary_shift_id" domain="[('is_exception','=',False)]" />
                                <field name="secondary_shift_id" domain="[('is_exception','=',False)]" />
                            </group>
                        </group>
                        <notebook>
                            <page string="Employees">
                                <field name="employee_ids"/>
                            </page>
                        </notebook>
                    </sheet>
                        <div class="oe_chatter">
                            <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
                            <field name="activity_ids"/>
                            <field name="message_ids" widget="mail_thread"/>
                        </div>
                </form>
            </field>
        </record>
        <!-- search -->
        <record id="view_ta_policy_group_search" model="ir.ui.view">
            <field name="name">ta_policy_group</field>
            <field name="model">ta.policy_group</field>
            <field name="arch" type="xml">
                <search string="Policy group">

                    <field name="primary_shift_id" select="True"/>
                    <field name="secondary_shift_id" select="True"/>
                    <group string="Group By..">
                        <filter string="Primary shift" domain="[]" name="group_by_primary_shift_id" context="{'group_by':'primary_shift_id'}"/>
                        <filter string="Secondary shift" domain="[]" name="group_by_secondary_shift_id" context="{'group_by':'secondary_shift_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- action window -->
        <record id="action_ta_policy_group" model="ir.actions.act_window">
            <field name="name">Policy Groups</field>
            <field name="res_model">ta.policy_group</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">list,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                Click to add a new Policy group
                </p><p>
                Click the Create button to add a new Policy group
                </p>
            </field>
        </record>
    </data>
</odoo>