<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="ta_timesheet_view_list" model="ir.ui.view">
            <field name="name">ta_timesheet_tree</field>
            <field name="model">ta.timesheet</field>
            <field name="type">tree</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <list decoration-muted="is_dayoff" string="Timesheet">
                    <header>
                         <button name="action_calc_timesheet" string="Re-Calculate Time" type="object"/>
                        <button name="action_update_shift_info" string="Recompute Shift" type="object"/>
                        <button groups="base.group_no_one" name="action_create_timesheet_automated"
                                string="Create Today's Timesheet" type="object"/>
                        <button groups="base.group_no_one" name="action_test_get_att" string="Get Timesheets Test"
                                type="object"/>
                        <button groups="base.group_no_one" name="action_test_attend_log" string="Get punch log Test"
                                type="object"/>
                    </header>
                    <field name="color" readonly="1" string="`" widget="color"/>
                    <field name="name" optional="hide"/>
                    <field name="employee_id"/>
                    <field name="date"/>
                    <field name="first_checkin_time" widget="float_time"/>
                    <field name="first_checkin_datetime" optional="hide"/>
                    <field name="last_checkout_time" widget="float_time"/>
                    <field name="last_checkout_datetime" optional="hide"/>
                    <field name="required_time" optional="show" widget="float_time"/>
                    <field name="working_time" optional="show" widget="float_time"/>
                    <field name="diff_working_time" optional="hide"/>
                    <field decoration-danger="delay_time > 0" name="delay_time" widget="float_time"/>
                    <field decoration-danger="shortage_time > 0" name="shortage_time" widget="float_time"/>
                    <field name="total_delay_shortage" optional="hide"/>
                    <field name="overtime" optional="hide" widget="float_time"/>
                    <field name="overtime_factored" optional="hide"/>
                    <field name="is_delayed" optional="hide"/>
                    <field name="is_shortage" optional="hide"/>
                    <field name="is_overnight" optional="hide"/>
                    <field name="employee_number" optional="hide"/>
                    <field name="enroll_number" optional="hide"/>
                    <field name="dept_path_code" optional="hide"/>
                    <field name="policy_group_id" optional="hide"/>
                    <field name="department_id" optional="hide"/>
                    <field name="notes" optional="hide"/>
                    <field name="total_deduction" optional="hide"/>
                    <field name="is_dayoff" optional="hide"/>
                    <field name="is_weekend" optional="hide"/>
                    <field name="is_vacation" optional="hide"/>
                    <field name="is_public_vacation" optional="hide"/>
                    <field name="is_absent" optional="hide"/>
                    <field name="is_attend_day" optional="hide"/>
                    <field name="is_working_day" optional="hide"/>
                    <field name="manual_edit" optional="hide"/>
                    <field name="delay_alert_sent" optional="hide"/>
                    <field name="shortage_alert_sent" optional="hide"/>
                    <field name="absent_alert_sent" optional="hide"/>
                    <field name="cycle_days" optional="hide"/>
                    <field name="schedule_type" optional="hide"/>
                    <field name="day_index" optional="hide"/>
                    <field name="current_timesheet_id" optional="hide"/>
                    <field name="shift_id" optional="hide"/>
                    <field name="parent_id" optional="hide"/>

                    <field name="required_time_min" optional="hide"/>
                    <field name="delay_time_min" optional="hide" />
                    <field name="checked_in" optional="hide"/>
                    <field name="checked_out" optional="hide"/>

                </list></field>
        </record>
        <!-- form view -->
        <record id="ta_timesheet_view_form" model="ir.ui.view">
            <field name="name">ta_timesheet_form</field>
            <field name="model">ta.timesheet</field>
            <field name="type">form</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <form string="Timesheet">
                    <header>
                        <button name="action_calc_timesheet" string="Re-Calculate Time" type="object"/>
                        <button name="action_update_shift_info" string="Recompute Shift" type="object"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" invisible="parent_id != False" name="button_box">
                            <button class="oe_stat_button" icon="fa-history" name="action_open_timesheet_history"
                                    string="Timesheet History" type="object"/>
                        </div>
                        <group>
                            <group>
                                <field name="employee_id" readonly="1"/>
                                <field name="date" readonly="1"/>
                                <field name="first_checkin_time" readonly="1" widget="float_time"/>
                                <field name="last_checkout_time" readonly="1" widget="float_time"/>
                            </group>
                            <group>
                                <field name="working_time" readonly="1" widget="float_time"/>
                                <field decoration-danger="delay_time > 0" name="delay_time" readonly="1"
                                       widget="float_time"/>
                                <field decoration-danger="shortage_time > 0" name="shortage_time" readonly="1"
                                       widget="float_time"/>
                                <field name="overtime" readonly="1" widget="float_time"/>
                                <field name="color" widget="color"/>
                            </group>
                        </group>
                        <group>
                            <field name="notes"/>
                        </group>
                        <notebook>
                            <page string="Shift Logs">
                                <field name="shift_unit_current_ids" nolabel="1">
                                    <list>
                                        <field name="name"/>
                                        <field name="date"/>
                                        <field name="first_checkin_time" widget="float_time"/>
                                        <field name="first_checkin_datetime" optional="hide"/>
                                        <field name="last_checkout_time" widget="float_time"/>
                                        <field name="last_checkout_datetime" optional="hide"/>
                                        <button icon="fa-edit" name="action_manual_checkin_checkout"
                                                title="Edit Attendance"
                                                type="object"/>
                                        <field name="working_time" widget="float_time"/>
                                        <field name="required_time" widget="float_time"/>
                                        <field name="diff_working_time" optional="hide" widget="float_time"/>
                                        <field name="delay_time" widget="float_time"/>
                                        <field name="shortage_time" widget="float_time"/>
                                        <field name="total_delay_shortage" optional="hide" widget="float_time"/>
                                        <field name="overtime" optional="hide" widget="float_time"/>
                                        <field name="overtime_factor"/>
                                        <field name="is_delayed" optional="hide"/>
                                        <field name="is_shortage" optional="hide"/>
                                        <field name="is_overnight" optional="hide"/>
                                        <field name="employee_number" optional="hide"/>
                                        <field name="enroll_number" optional="hide"/>
                                        <field name="dept_path_code" optional="hide"/>
                                        <field name="policy_group_id" optional="hide"/>
                                        <field name="department_id" optional="hide"/>
                                        <field name="timesheet_id" optional="hide"/>
                                        <field name="notes" optional="hide"/>
                                    </list></field>
                            </page>
                            <page string="Time Summary">
                                <group>
                                    <group>
                                        <field name="required_time" readonly="1" widget="float_time"/>
                                        <field name="working_time" readonly="1" widget="float_time"/>
                                        <field name="diff_working_time" readonly="1" string="Remaining Time"
                                               widget="float_time"/>
                                        <field decoration-danger="delay_time > 0" name="delay_time" readonly="1"
                                               widget="float_time"/>
                                        <field decoration-danger="shortage_time > 0" name="shortage_time" readonly="1"
                                               widget="float_time"/>
                                    </group>
                                    <group>
                                        <field name="total_delay_shortage" readonly="1" widget="float_time"/>
                                        <field name="overtime" readonly="1" widget="float_time"/>
                                        <field name="overtime_factored" readonly="1" widget="float_time"/>
                                        <field name="total_deduction" readonly="1" widget="float_time"/>
                                        <field name="time_off_hours" string="Total Permission" widget="float_time"/>
                                    </group>
                                </group>
                            </page>
                            <Page string="Employee Info">
                                <group>
                                    <group>
                                        <field name="employee_number" readonly="1"/>
                                        <field name="enroll_number" readonly="1"/>
                                        <field name="policy_group_id" readonly="1"/>
                                        <field name="department_id" readonly="1"/>
                                        <field name="dept_path_code" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="shift_id"/>
                                        <field name="schedule_type"/>
                                        <field name="cycle_days"/>
                                        <field name="day_index"/>
                                    </group>
                                </group>
                            </Page>


                            <page string="Shift Histories">
                                <field name="shift_unit_history_ids" nolabel="1">
                                    <list>
                                        <field name="name"/>
                                        <field name="date"/>
                                        <field name="first_checkin_time" widget="float_time"/>
                                        <field name="first_checkin_datetime" optional="hide"/>
                                        <field name="last_checkout_time" widget="float_time"/>
                                        <field name="last_checkout_datetime" optional="hide"/>
                                        <button icon="fa-edit" name="action_manual_checkin_checkout"
                                                title="Edit Attendance" type="object"/>
                                        <field name="working_time" widget="float_time"/>
                                        <field name="required_time" widget="float_time"/>
                                        <field name="diff_working_time" optional="hide" widget="float_time"/>
                                        <field name="delay_time" widget="float_time"/>
                                        <field name="shortage_time" widget="float_time"/>
                                        <field name="total_delay_shortage" optional="hide" widget="float_time"/>
                                        <field name="overtime" optional="hide" widget="float_time"/>
                                        <field name="overtime_factor" optional="hide"/>
                                        <field name="is_delayed" optional="hide"/>
                                        <field name="is_shortage" optional="hide"/>
                                        <field name="is_overnight" optional="hide"/>
                                        <field name="employee_number" optional="hide"/>
                                        <field name="enroll_number" optional="hide"/>
                                        <field name="dept_path_code" optional="hide"/>
                                        <field name="policy_group_id" optional="hide"/>
                                        <field name="department_id" optional="hide"/>
                                        <field name="timesheet_id" optional="hide"/>
                                        <field name="notes" optional="hide"/>
                                    </list></field>
                            </page>
                            <!--                            <page string="Timesheet Histories">-->
                            <!--                                <field name="timesheet_history_ids"/>-->
                            <!--                            </page>-->
                            <page invisible="1" string="Mobile Info">
                                <group>
                                    <field name="from_mobile" readonly="1"/>
                                    <field name="in_longitude" readonly="1"/>
                                    <field name="in_latitude" readonly="1"/>
                                    <field name="out_longitude" readonly="1"/>
                                    <field name="out_latitude" readonly="1"/>
                                </group>
                            </page>
                            <page string="Other Info">
                                <group>
                                    <group string="Timesheet Info">
                                        <field name="current_timesheet_id" readonly="1"/>
                                        <field invisible="1" name="parent_id"/>
                                        <field name="manual_edit"/>
                                    </group>
                                    <group string="Alerts">
                                        <field name="delay_alert_sent" readonly="1"/>
                                        <field name="shortage_alert_sent" readonly="1"/>
                                        <field name="absent_alert_sent" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="is_delayed" readonly="1"/>
                                        <field name="is_shortage" readonly="1"/>
                                        <field name="is_overnight" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="is_dayoff" readonly="1"/>
                                        <field name="is_weekend" readonly="1"/>
                                        <field name="is_vacation" readonly="0"/>
                                        <field name="is_public_vacation" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="is_absent" readonly="0"/>
                                        <field name="is_attend_day" readonly="0"/>
                                        <field name="is_working_day" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="punch_logs"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field groups="base.group_user" name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form></field>
        </record>
        <!-- search -->
        <record id="ta_timesheet_view_search" model="ir.ui.view">
            <field name="name">ta_timesheet</field>
            <field name="model">ta.timesheet</field>
            <field name="arch" type="xml">
                <search string="Timesheet">
                    <filter string="My Timesheet" domain="[('emp_user_id','=',uid)]" name="my_timesheet"/>
                    <separator/>

                    <filter domain="[('date', '=', datetime.date.today().strftime('%Y-%m-%d'))]" name="today"
                            string="Today"/>
                    <filter domain="[('date', '=', (datetime.date.today() - relativedelta(days=1)).strftime('%Y-%m-%d'))]"
                            name="yesterday" string="Yesterday"/>
                    <filter domain="[('is_delayed','=',True)]" name="filter_by_is_delayed" string="Delayed"/>
                    <filter domain="[('is_shortage','=',True)]" name="filter_by_is_shortage" string="Shortage"/>
                    <filter domain="[('is_dayoff','=',True)]" name="filter_by_is_dayoff" string="Is dayoff"/>
                    <filter domain="[('is_vacation','=','')]" name="filter_by_is_vacation" string="Is vacation"/>
                    <!--                    <filter string="Is public vacation" name="filter_by_is_public_vacation"-->
                    <!--                            domain="[('is_public_vacation','=','')]"/>-->
                    <filter domain="[('is_absent','=',True)]" name="filter_by_is_absent" string="Is absent"/>
                    <filter domain="[('is_attend_day','=',True)]" name="filter_by_is_attend_day"
                            string="Is attend day"/>
                    <filter domain="[('is_working_day','=',True)]" name="filter_by_is_working_day"
                            string="Is working day"/>
                    <filter domain="[('manual_edit','=',True)]" name="filter_by_manual_edit" string="Manual edit"/>
                    <filter domain="[('date','>=',datetime.datetime.today().replace(day=1)),('date','&lt;',datetime.datetime.today().replace(day=1) + datetime.timedelta(days=31))]"
                            name="filter_by_date_current" string="Current Month"/>
                    <filter domain="[('date','>=',datetime.datetime.today().replace(day=1) - datetime.timedelta(days=31)),('date','&lt;',datetime.datetime.today().replace(day=1))]"
                            name="filter_by_date_current" string="Previous Month"/>
                    <!--                    <field name="total_deduction" select="True"/>-->
                    <group string="Group By">
                        <filter context="{'group_by':'parent_department_id'}" name="groupby_parent_dept"
                                string="Parent Department"/>
                        <filter context="{'group_by':'department_id'}" name="groupby_dept" string="Department"/>
                        <filter context="{'group_by':'policy_group_id'}" name="groupby_policy" string="Policy Group"/>
                        <filter string="Date" name="date" context="{'group_by': 'date'}"/>
                        <filter context="{'group_by':'shift_id'}" name="groupby_shift" string="Shift"/>
                        <filter context="{'group_by':'employee_id'}" name="groupby_emp" string="Employee"/>
                        <filter context="{'group_by':'current_timesheet_id'}" name="groupby_timesheet"
                                string="Current Timesheet"/>
                        <filter context="{'group_by':'parent_id'}" name="groupby_parent_timesheet"
                                string="Parent Timesheet"/>
                    </group>
                </search></field>
        </record>
        <record id="ta_timesheet_view_pivot" model="ir.ui.view">
            <field name="name">ta_timesheet_pivot</field>
            <field name="model">ta.timesheet</field>
            <field name="arch" type="xml">
                <pivot sample="1" string="Timesheet Summary">
                    <field name="employee_id" type="row"/>
                    <!--                    <field name="date" interval="month" type="col"/>-->
                    <field name="required_time" type="measure" widget="float_time"/>
                    <field name="working_time" type="measure" widget="float_time"/>
                    <field name="delay_time" type="measure" widget="float_time"/>
                    <field name="shortage_time" type="measure" widget="float_time"/>
                    <field name="total_delay_shortage" type="measure" widget="float_time"/>
                    <field name="absent_count" type="measure"/>
                    <field name="vacation_count" type="measure"/>
                </pivot></field>
        </record>
        <!-- action window -->
        <record id="action_ta_all_timesheet" model="ir.actions.act_window">
            <field name="name">Timesheet</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.timesheet</field>
            <field name="view_mode">list,form,pivot</field>
            <field name="domain">[('parent_id', '=', False)]</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to add a new Timesheet
                </p>
                <p>
                    Click the Create button to add a new Timesheet
                </p>
            </field>
        </record>
        <record id="action_ta_timesheet" model="ir.actions.server">
            <field name="name">Timesheet</field>
            <field name="model_id" ref="ta.model_ta_timesheet"/>
            <field name="binding_model_id" ref="ta.model_ta_timesheet"/>
            <field name="binding_view_types">list,form,pivot</field>
            <field name="state">code</field>
            <field eval="[(4, ref('base.group_no_one'))]" name="groups_id"/>
            <field name="code">action = {
                'name': "Timesheets",
                'view_mode': 'list,form,pivot',
                'domain': [('employee_id', 'in', env.get('res.users')._get_allowed_employee_ids().ids),
                           ('parent_id', '=', False)],
                'res_model': 'ta.timesheet',
                'type': 'ir.actions.act_window',
                'context': {'custom_name': 1}
            }</field>
        </record>
        <record id="action_ta_timesheet_test" model="ir.actions.act_window">
            <field name="name">Timesheet</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.timesheet</field>
            <field name="view_mode">list,form,pivot</field>
            <field name="context">{'custom_name': 1}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to add a new Timesheet
                </p>
                <p>
                    Click the Create button to add a new Timesheet
                </p>
            </field>
        </record>
    </data>
</odoo>
