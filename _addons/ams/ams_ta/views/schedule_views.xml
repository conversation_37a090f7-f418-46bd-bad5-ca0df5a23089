<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_ta_shift_schedule_tree" model="ir.ui.view">
            <field name="name">ta_shift_schedule_tree</field>
            <field name="model">ta.shift_schedule</field>
            <field name="type">tree</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <list string="Schedule">
                    <field name="name" />
                    <field name="schedule_type" />
                    <field name="cycle_days" optional="show"/>
                    <field name="activate" optional="show"/>
                </list>
            </field>
        </record>
        <!-- form view -->
        <record id="view_ta_shift_schedule_form" model="ir.ui.view">
            <field name="name">ta_shift_schedule_form</field>
            <field name="model">ta.shift_schedule</field>
            <field name="type">form</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <form string="Schedule">
                    <header>
<!--                        <button type="object" name="action_activate" string="Activate" class="oe_highlight "/>-->
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <!--button type="object" name="action_view_detail" class="oe_stat_button" icon="fa-pencil-square-o"-->
                                <!--field name="detail_count" widget="statinfo" string="Detail(s)"/-->
                                <!--field name="detail_ids" invisible="1"/-->
                            <!--/button--></div>
                        <field name="active" invisible="1"/>
                        <field name="activate" invisible="1"/>
<!--                        <widget name="web_ribbon" title="Not Active" bg_color="bg-danger"-->
<!--                            attrs="{'invisible': [('activate', '=', True)]}"/>-->
<!--                        <widget name="web_ribbon" title="Active" bg_color="bg-success"-->
<!--                            attrs="{'invisible': [('activate', '=', False)]}"/>-->
                        <group>
                            <group>
                                <field name="code" invisible="0" required="0"/>
                                <field name="name" invisible="1"/>
                                <field name="en_name"/>
                                <field name="ar_name"/>
                            </group>
                            <group>
                                <field name="default_shift_unit"/>
                                <field name="schedule_type" />
                                <field name="cycle_days" invisible="schedule_type=='weekly'"/>

                            </group>
                            <group>
                                <button type="object" name="action_generate_days" class="oe_highlight" string="Generate"/>
                            </group>

                        </group>
                        <notebook>
                            <page string="Days">
                                    <field name="day_ids" nolabel="1">
                                        <list editable="bottom" create="0" delete="0" decoration-danger="units_overlapped" decoration-warning="units_mixed_types">
                                            <field name="index" string="#" optional="show" readonly="1" force_save="1" />
                                            <field name="name" readonly="1" force_save="1"/>
                                            <field name="week_day" optional="hide" readonly="1" force_save="1"/>
                                            <field name="is_day_off" string="Day Off" widget="boolean_toggle" force_save="1"/>
                                            <field name="shift_units_ids" widget="many2many_tags"/>
                                            <field name="units_mixed_types" string="Mix Types" optional="hide" readonly="1" force_save="1"/>
                                            <field name="units_overlapped" string="Overlap" optional="hide" readonly="1" force_save="1"/>
                                        </list>
                                    </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>
        <!-- search -->
        <record id="view_ta_shift_schedule_search" model="ir.ui.view">
            <field name="name">ta_shift_schedule</field>
            <field name="model">ta.shift_schedule</field>
            <field name="arch" type="xml">
                <search string="Schedule">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="activate"/>
                    <field name="activate_uid"/>
                    <field name="deactivate_uid"/>
                    <filter string="Activated" name="filter_activate" domain="[('activate','=',True)]" />
                    <filter string="Not Activated" name="filter_activate" domain="[('activate','=',False)]" />
<!--                    <filter string="Cycle days" name="filter_by_cycle_days" domain="[('cycle_days','=','')]" />-->
<!--                    <filter string="Schedule type" name="filter_by_schedule_type" domain="[('schedule_type','=','')]" />-->
<!--                    <field name="cycle_days" select="True"/>-->
<!--                    <field name="schedule_type" select="True"/>-->
<!--                    <group string="Group By..">-->
<!--                    </group>-->
                </search>
            </field>
        </record>
        <!-- action window -->
        <record id="action_ta_shift_schedule" model="ir.actions.act_window">
            <field name="name">Schedules</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.shift_schedule</field>
            <field name="view_mode">list,form</field>
            <field name="context">{"search_default_fieldname":1}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                Click to add a new Schedule
                </p><p>
                Click the Create button to add a new Schedule
                </p>
            </field>
        </record>
    </data>
</odoo>