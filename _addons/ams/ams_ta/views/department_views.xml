<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_hr_department_tree" model="ir.ui.view">
            <field name="name">hr_department_tree</field>
            <field name="model">hr.department</field>
            <field name="type">tree</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <list string="Department">
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="active"/>
                    <field name="total_employee"/>
                    <field name="manager_id"/>
                    <field name="path_code" optional="hide"/>
                </list>
            </field>
        </record>
        <!-- form view -->
        <record id="view_hr_department_form" model="ir.ui.view">
            <field name="name">hr_department_form</field>
            <field name="model">hr.department</field>
            <field name="type">form</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <form string="Department">
                    <header>
                    </header>
                    <sheet>

                        <field name="active"/>
                        <div class="oe_button_box" name="button_box">
<!--                            <button type="object" name="" class="oe_stat_button" string="Activate"/>-->
                        </div>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only" string="Department Name"/>
                            <h1><field name="name"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="color" widget="color_picker"/>
                                <field name="note"/>
                                <field name="total_employee"/>
                            </group>
                            <group>
                                <field name="parent_id"/>
                                <field name="company_id"/>
                                <field name="path_code" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Employees">
                                    <field name="member_ids" nolabel="1">
                                        <list>
                                            <field name="name"/>
                                            <field name="mobile_phone" optional="hide"/>
                                            <field name="user_id" optional="hide"/>
                                            <field name="job_id"/>
                                            <field name="employee_type"/>
                                            <field name="shift_id"/>
                                            <field name="policy_group_id"/>
                                        </list>
                                    </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        <!-- search -->
<!--        <record id="view_hr_department_search" model="ir.ui.view">-->
<!--            <field name="name">hr_department</field>-->
<!--            <field name="model">hr.department</field>-->
<!--            <field name="arch" type="xml">-->
<!--                <search string="Department">-->

<!--                    <group string="Group By..">-->
<!--                    </group>-->
<!--                </search>-->
<!--            </field>-->
<!--        </record>-->
        <!-- kanban , to show image, please add image_small in the model -->
<!--        <record id="view_hr_department_kanban" model="ir.ui.view" >-->
<!--            <field name="name">hr_department</field>-->
<!--            <field name="model">hr.department</field>-->
<!--            <field name="arch" type="xml">-->
<!--                <kanban version="7.0">-->
<!--                    <field name="id" />-->
<!--                    <templates>-->
<!--                        <t t-name="kanban-box">-->
<!--                            <div class="oe_kanban_global_click o_kanban_record_has_image_fill">-->
<!--                                <div class="oe_kanban_details">-->
<!--                                    <h4>-->
<!--                                        <a type="open"><field name="name"/></a>-->
<!--                                    </h4>-->
<!--                                    <ul>-->
<!--                                    </ul>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </t>-->
<!--                    </templates>-->
<!--                </kanban>-->
<!--            </field>-->
<!--        </record>-->
        <!-- calendar if a date field exists -->
        <!-- graph -->
<!--        <record id="view_hr_department_graph" model="ir.ui.view">-->
<!--            <field name="name">hr_department</field>-->
<!--            <field name="model">hr.department</field>-->
<!--            <field name="arch" type="xml">-->
<!--                <graph string="Department" type="bar">-->
<!--                </graph>-->
<!--            </field>-->
<!--        </record>-->
        <!-- action window -->
        <record id="action_hr_department" model="ir.actions.act_window">
            <field name="name">Department</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">hr.department</field>
            <field name="view_mode">list,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                Click to add a new Department
                </p><p>
                Click the Create button to add a new Department
                </p>
            </field>
        </record>

<!--        <menuitem id="menu_hr_department" name="Department" parent="ta_config_menu" action="action_hr_department" sequence="40"/>-->
    </data>
</odoo>