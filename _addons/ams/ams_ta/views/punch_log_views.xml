<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_ta_punch_log_tree" model="ir.ui.view">
            <field name="name">ta_punch_log_tree</field>
            <field name="model">ta.punch_log</field>
            <field name="type">tree</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <list string="Punch log">
                    <field name="name" optional="hide"/>
                    <field name="enroll_number" optional="hide"/>
                    <field name="employee_id"/>
                    <field name="employee_number" optional="hide"/>
                    <field name="date" optional="hide"/>
                    <field name="log_time"/>
                    <field name="execute_log_time" optional="hide"/>
                    <field name="device_number" optional="hide"/>
                    <field name="state"/>
                    <field name="department_id" optional="hide"/>
                    <field name="policy_group_id" optional="hide"/>
                    <field name="dept_path_code" optional="hide"/>
                    <field name="notes" optional="hide"/>
                    <field name="longitude" optional="hide"/>
                    <field name="latitude" optional="hide"/>
                    <field name="from_mobile" optional="hide"/>
                    <button name="action_execute_log" string="Execute Log" type="object"/>
                </list></field>
        </record>
        <!-- form view -->
        <record id="view_ta_punch_log_form" model="ir.ui.view">
            <field name="name">ta_punch_log_form</field>
            <field name="model">ta.punch_log</field>
            <field name="type">form</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <form string="Punch log">
                    <header>
                        <button invisible="state not in ['executed']" name="action_pending" string="Reset to Draft" type="object"/>
                        <button invisible="state not in ['pending']" name="action_execute_log" string="Execute Log" type="object"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <!--button type="object" name="action_view_detail" class="oe_stat_button" icon="fa-pencil-square-o"-->
                            <!--field name="detail_count" widget="statinfo" string="Detail(s)"/-->
                            <!--field name="detail_ids" invisible="1"/-->
                            <!--/button-->
                        </div>
                        <!--                        <div class="oe_title">-->
                        <!--                            <label for="name" class="oe_edit_only" string="Punch log Name"/>-->
                        <!--                            <h1></h1>-->
                        <!--                        </div>-->
                        <group>
                            <!--                            <group>-->
                            <!--                                <field name="name"/>-->
                            <!--                            </group>-->
                            <group>
                                <field name="employee_id"/>
                                <field name="log_time" readonly="0"/>
                            </group>
                        </group>
                        <group>
                            <field name="notes"/>
                        </group>
                        <notebook>
                            <page string="Employee Info">
                                <group>
                                    <group>
                                        <field name="policy_group_id" readonly="1"/>
                                        <field name="department_id" readonly="1"/>
                                        <field name="dept_path_code" readonly="1"/>
                                        <field name="employee_number" readonly="1"/>
                                        <field name="enroll_number" readonly="1"/>
                                        <field name="execute_log_time" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="device_number" readonly="1"/>
                                        <field invisible="1" name="from_mobile"/>
                                        <field invisible="1" name="longitude"/>
                                        <field invisible="1" name="latitude"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field groups="base.group_user" name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" />
                    </div>
                </form></field>
        </record>
        <!-- search -->
        <record id="view_ta_punch_log_search" model="ir.ui.view">
            <field name="name">ta_punch_log</field>
            <field name="model">ta.punch_log</field>
            <field name="arch" type="xml">
                <search string="Punch log">
                    <field name="name"/>
                    <field name="employee_number"/>
                    <field name="enroll_number" />
                    <field name="device_number"/>
                    <field name="state" />
                    <filter string="Pending" name="pending" domain="[('state', '=','pending')]"/>
                    <filter string="Executed" name="executed" domain="[('state', '=','executed')]"/>

                    <filter string="Today" name="today"
                        domain="[('date', '=', context_today().strftime('%Y-%m-%d'))]"/>

                <filter string="Yesterday" name="yesterday"
                        domain="[('date', '=', (context_today()- relativedelta(days=1)).strftime('%Y-%m-%d'))]"/>

                    <group string="Group By..">
                        <filter context="{'group_by':'employee_id'}" name="group_by_employee_id" string="Employee"/>
                        <filter context="{'group_by':'policy_group_id'}" name="group_by_policy_group_id" string="Policy group"/>
                        <filter context="{'group_by':'department_id'}" name="group_by_department_id" string="Department"/>
                        <filter string="Date" name="group_by_date" context="{'group_by': 'date'}" />
                    </group>
                </search></field>
        </record>
        <!-- calendar if a date field exists -->
        <record id="view_ta_punch_log_cal" model="ir.ui.view">
            <field name="name">ta_punch_log_cal</field>
            <field name="model">ta.punch_log</field>
            <field name="arch" type="xml">
                <calendar date_start="execute_log_time" string="Punch log">
                    <field name="name"/>
                </calendar></field>
        </record>
        <!-- action window -->
        <record id="action_ta_all_punch_log" model="ir.actions.act_window">
            <field name="name">Punch log</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.punch_log</field>
            <field name="view_mode">list,form</field>
            <field name="context">{"search_default_fieldname": 1}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to add a new Punch log
                </p>
                <p>
                    Click the Create button to add a new Punch log
                </p></field>
        </record>
        <record id="action_ta_punch_log" model="ir.actions.server">
            <field name="name">Punch log</field>
            <field name="model_id" ref="ta.model_ta_punch_log"/>
            <field name="binding_model_id" ref="ta.model_ta_punch_log"/>
            <field name="binding_view_types">list,form,pivot</field>
            <field name="state">code</field>
            <field eval="[(4, ref('base.group_no_one'))]" name="groups_id"/>
            <field name="code">action = {
                'name': "Punch logs",
                'view_mode':'list,form',
                'domain': [('employee_id', 'in', env.get('res.users')._get_allowed_employee_ids().ids)],
                'context':{'search_default_group_by_employee_id':1},
                'res_model': 'ta.punch_log',
                'type': 'ir.actions.act_window',
                }</field>
        </record>
    </data>
</odoo>
