<odoo>
    <record id="timesheet_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.inherit.view</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="1"/>
       <field name="inherit_id"
               ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='invite_users']" position="after">
<!--            <xpath expr="//div[hasclass('settings')]" position="inside">-->
                <div class="app_settings_block" data-string="Timesheet" string="Timesheet"
                     data-key="ta">
                    <h2>Timesheet</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-10 o_setting_box">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <div class="text-muted content-group mt16">
                                    <span class="o_form_label pr-3">Last update timesheet date </span>
                                        <field name="last_timesheet_date" groups="base.group_no_one"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="action_timesheet_config" model="ir.actions.act_window">
        <field name="name">Timesheet Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'ta'}</field>
    </record>
</odoo>