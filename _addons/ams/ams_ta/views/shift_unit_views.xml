<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_ta_shift_unit_tree" model="ir.ui.view">
            <field name="name">ta_shift_unit_tree</field>
            <field name="model">ta.shift_unit</field>
            <field name="type">tree</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <list string="Shift unit">
                    <field name="color" widget="color"/>
                    <field name="name"/>
                    <field name="shift_type"/>
                    <field name="start_time" widget="float_time"/>
                    <field name="end_time" widget="float_time"/>
                    <field name="duration"/>
                    <field name="is_overnight"/>
                    <field name="rule_id" />
                    <field name="code" optional="hide"/>
                    <field name="min_checkin_time" optional="hide"/>
                    <field name="max_checkout_time" optional="hide"/>
                    <field name="absent_time_criteria"  widget="float_time" optional="hide"/>

                </list>
            </field>
        </record>
        <!-- form view -->
        <record id="view_ta_shift_unit_form" model="ir.ui.view">
            <field name="name">ta_shift_unit_form</field>
            <field name="model">ta.shift_unit</field>
            <field name="type">form</field>
            <field name="priority" eval="8"/>
            <field name="arch" type="xml">
                <form string="Shift unit">
                    <header>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <!--button type="object" name="action_view_detail" class="oe_stat_button" icon="fa-pencil-square-o"-->
                            <!--field name="detail_count" widget="statinfo" string="Detail(s)"/-->
                            <!--field name="detail_ids" invisible="1"/-->
                            <!--/button-->
                        </div>
                        <!--                        <div class="oe_title">-->
                        <!--                            <label for="name" class="oe_edit_only" string="Shift unit Name"/>-->
                        <!--                            <h1><field name="name"/></h1>-->
                        <!--                        </div>-->
                        <field name="active" invisible="1"/>
                        <field name="activate" invisible="1"/>
<!--                        <widget name="web_ribbon" title="Not Active" bg_color="bg-danger"-->
<!--                                attrs="{'invisible': [('activate', '=', True)]}"/>-->
<!--                        <widget name="web_ribbon" title="Active" bg_color="bg-success"-->
<!--                                attrs="{'invisible': [('activate', '=', False)]}"/>-->

                        <group>
                            <group>
                                <field name="code" invisible="0"/>
                                <field name="name" invisible="1"/>
                                <field name="en_name"/>
                                <field name="ar_name"/>

                            </group>
                            <group>
                                <field name="shift_type"/>
                                <field name="rule_id" required="1"/>
                                <field name="color" widget="color"/>
                                <field name="activate" invisible="1"/>
                            </group>


                        </group>
                        <notebook>
                            <page string="Setting">
                                <group>
                                    <group string="Time">
                                        <field name="start_time" widget="float_time"/>
                                        <field name="start_limit"
                                               invisible="shift_type in ['open', 'normal', False]"
                                               widget="float_time"/>
                                        <field name="end_time"
                                               invisible="shift_type in ['open', 'flexible']"
                                               widget="float_time"/>
                                        <field name="duration" widget="float_time"/>
                                        <field name="is_overnight"/>
                                    </group>
                                    <group string="Allowed Time">
                                        <field name="apply_min_max"/>
                                        <field name="min_checkin_time" widget="float_time"
                                               invisible="apply_min_max==False"/>
                                        <field name="max_checkout_time" widget="float_time"
                                               invisible="apply_min_max==False"/>
                                        <field name="absent_time_criteria" widget="float_time"
                                               />
                                    </group>

                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>
        <!-- search -->
        <record id="view_ta_shift_unit_search" model="ir.ui.view">
            <field name="name">ta_shift_unit</field>
            <field name="model">ta.shift_unit</field>
            <field name="arch" type="xml">
                <search string="Shift unit">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="activate"/>
                    <field name="activate_uid"/>
                    <field name="deactivate_uid"/>
                    <filter string="Activated" name="filter_activate" domain="[('activate','=',True)]"/>
                    <filter string="Not Activated" name="filter_activate" domain="[('activate','=',False)]"/>
                    <filter string="Type Open" name="filter_by_shift_type_open" domain="[('shift_type','=','open')]"/>
                    <filter string="Type Normal" name="filter_by_shift_type_normal"
                            domain="[('shift_type','=','normal')]"/>
                    <filter string="Type Flexible" name="filter_by_shift_type_flexible"
                            domain="[('shift_type','=','flexible')]"/>
                    <filter string="Is overnight" name="filter_by_is_overnight" domain="[('is_overnight','=',True)]"/>

                    <!--                    <filter string="Start time" name="filter_by_start_time" domain="[('start_time','=','')]" />-->
                    <!--                    <filter string="End time" name="filter_by_end_time" domain="[('end_time','=','')]" />-->
                    <!--                    <filter string="Duration" name="filter_by_duration" domain="[('duration','=','')]" />-->
                    <!--                    <filter string="Color" name="filter_by_color" domain="[('color','=','')]" />-->
                    <!--                    <field name="start_time" select="True"/>-->
                    <!--                    <field name="end_time" select="True"/>-->
                    <!--                    <field name="shift_type" select="True"/>-->
                    <!--                    <field name="duration" select="True"/>-->
                    <!--                    <field name="is_overnight" select="True"/>-->
                    <!--                    <field name="color" select="True"/>-->
                    <!--                    <field name="rule_id" select="True"/>-->
                    <!--                    <group string="Group By..">-->
                    <!--                        <filter string="Rule" domain="[]" name="group_by_rule_id" context="{'group_by':'rule_id'}"/>-->
                    <!--                    </group>-->
                </search>
            </field>
        </record>

        <!-- action window -->
        <record id="action_ta_shift_unit" model="ir.actions.act_window">
            <field name="name">Shift Units</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.shift_unit</field>
            <field name="view_mode">list,form,graph</field>
            <field name="context">{"search_default_fieldname": 1}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to add a new Shift Unit
                </p>
                <p>
                    Click the Create button to add a new Shift unit
                </p>
            </field>
        </record>
    </data>
</odoo>