<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_ta_shift_tree" model="ir.ui.view">
            <field name="name">ta_shift_tree</field>
            <field name="model">ta.shift</field>
            <field name="type">tree</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <list string="Shift">
                    <field invisible="0" name="name"/>
                    <field name="start_date"/>
                    <field name="end_date"/>
                    <field name="state"/>
                    <field groups="base.group_no_one" name="state_fixed"/>
                    <field name="status"/>
<!--                    <field invisible="context.get('default_is_exception')" name="is_default"/>-->
<!--                    <field name="schedule_id"/>-->
<!--                    <field name="employee_id" optional="hide"/>-->
<!--                    <field name="department_id" optional="hide"/>-->
<!--                    <field name="policy_group_id" optional="hide"/>-->
                </list></field>
        </record>
        <!-- form view -->
        <record id="view_ta_shift_form" model="ir.ui.view">
            <field name="name">ta_shift_form</field>
            <field name="model">ta.shift</field>
            <field name="type">form</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <form string="Shift">
                    <header>
                        <!--                        <button string="Confirm" type="object" name="action_confirm" attrs="{'invisible': [('status', '=', 'executed')]}" />-->
                        <!--                        <button string="Pause" type="object" name="action_done" attrs="{'invisible': [('status', '=', 'pending')]}" />-->
                        <!--                        <button string="Reset" type="object" name="action_draft" />-->
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" icon="fa-file-text" invisible="not context.get('default_is_exception')" name="action_open_employee_timesheets" string="Employee Time-sheets" type="object"/>
                        </div>
                        <div class="oe_title">
                            <label class="oe_edit_only" for="name" string="Shift Name"/>
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="start_date"/>
                                <field name="end_date"/>
                                <field invisible="context.get('default_is_exception')" name="is_default"/>
                                <field invisible="1" name="is_exception"/>
                            </group>
                            <group>
                                <field name="schedule_id"/>
                                <field groups="base.group_no_one" name="state_fixed"/>
                            </group>
                        </group>
                        <group invisible="is_exception == False" string="Execution">
                            <group>
                                <field force_save="1" name="status" readonly="0"/>
                                <field name="employee_id" required="is_exception == True"/>
                                <!--                                <field name="exception_employee_ids" widget="many2many_tags"-->
                                <!--                                       attrs="{'readonly':[('status', '=', 'executed')]}" />-->
                            </group>
                            <group invisible="status == 'executed'">
                                <button name="action_execute_exception_emp_shift" string="Execute" title="Recalculate employee timesheet based on current shift" type="object"/>
                            </group>
                            <group invisible="status == 'pending'">
                                <button name="action_reset" string="Reset" title="Recalculate employee timesheet based on current shift" type="object"/>
                            </group>
                        </group>
                        <!--                        <notebook>-->
                        <!--                            <page string="Employees" attrs="{'invisible':[('is_exception', '=', True)]}">-->
                        <!--                                <field name="employee_ids">-->
                        <!--                                    <list>-->
                        <!--                                        <field name="name"/>-->
                        <!--                                        <field name="department_id"/>-->
                        <!--                                        <field name="color"/>-->
                        <!--                                        <field name="active"/>-->
                        <!--                                    </list>-->
                        <!--</field>-->
                        <!--                            </page>-->
                        <!--                        </notebook>-->
                    </sheet>
                    <div class="oe_chatter">
                        <field groups="base.group_user" name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form></field>
        </record>
        <!-- search -->
        <record id="view_ta_shift_search" model="ir.ui.view">
            <field name="name">ta_shift</field>
            <field name="model">ta.shift</field>
            <field name="arch" type="xml">
                <search string="Shift">
                    <field name="name"/>
                    <field name="employee_number"/>
                    <!--                    <filter string="Start date" name="filter_by_start_date" domain="[('start_date','=','')]" />-->
                    <!--                    <filter string="End date" name="filter_by_end_date" domain="[('end_date','=','')]" />-->
                    <filter domain="[('state_fixed','=','running')]" name="state_running" string="Running"/>
                    <!--                    <filter string="Is default" name="filter_by_is_default" domain="[('is_default','=','')]" />-->
                    <!--                    <field name="start_date" select="True"/>-->
                    <!--                    <field name="end_date" select="True"/>-->
                    <!--                    <field name="state" select="True"/>-->
                    <!--                    <field name="is_default" select="True"/>-->
                    <!--                    <field name="schedule_id" select="True"/>-->
                    <group string="Group By">
                        <filter context="{'group_by':'parent_department_id'}" name="groupby_parent_dept" string="Parent Department"/>
                        <filter context="{'group_by':'department_id'}" name="groupby_dept" string="Department"/>
                        <filter context="{'group_by':'policy_group_id'}" name="groupby_policy" string="Policy Group"/>
                        <filter context="{'group_by':'employee_id'}" name="groupby_emp" string="Employee"/>
                        <filter context="{'group_by':'schedule_id'}" domain="[]" name="group_by_schedule_id" string="Schedule"/>
                    </group>
                </search></field>
        </record>
        <!-- calendar if a date field exists -->
        <record id="view_ta_shift_calendar" model="ir.ui.view">
            <field name="name">ta_shift_cal</field>
            <field name="model">ta.shift</field>
            <field name="arch" type="xml">
                <calendar color="state" date_start="start_date" date_stop="end_date" event_open_popup="True"  quick_create="False" string="Shift">
                    <field name="name"/>
                    <field filters="1" invisible="1" name="employee_id"/>
                </calendar></field>
        </record>
        <!-- action window -->
        <record id="action_ta_shift" model="ir.actions.act_window">
            <field name="name">Shifts</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.shift</field>
            <field name="view_mode">list,form,calendar</field>
            <field name="context">{"search_default_fieldname":1, 'default_is_default':True}</field>
            <field name="domain">[('is_exception', '=', False)]</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to add a new Shift
                </p>
                <p>
                    Click the Create button to add a new Shift
                </p></field>
        </record>
        <record id="action_ta_shift_exception" model="ir.actions.act_window">
            <field name="name">Employees Shifts</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ta.shift</field>
            <field name="view_mode">list,form,calendar</field>
            <field name="context">{"search_default_fieldname":1,'default_is_exception':True}</field>
            <field name="domain">[('is_exception', '=', True)]</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to add a new Shift
                </p>
                <p>
                    Click the Create button to add a new Shift
                </p></field>
        </record>
    </data>
</odoo>
