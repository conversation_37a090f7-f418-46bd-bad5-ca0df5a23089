<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="view_hr_employee_tree" model="ir.ui.view">
            <field name="name">hr_employee_tree</field>
            <field name="model">hr.employee</field>
            <field name="type">tree</field>
            <field name="priority" eval="8"/>
             <field name="inherit_id" ref="hr.view_employee_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//list" position="inside">
                    <field name="shift_id" optional="hide"/>
                    <field name="policy_group_id" optional="hide"/>
                </xpath>
                <xpath expr="//list" position="inside">
                    <header>
                        <button type="object" name="action_open_manual_attendance_wizard_employee" string="open Manual Attendance"/>
                    </header>
                </xpath>
            </field>
        </record>
        <!-- form view -->
        <record id="view_hr_employee_form" model="ir.ui.view">
            <field name="name">hr_employee_form</field>
            <field name="model">hr.employee</field>
            <field name="type">form</field>
            <field name="priority" eval="8"/>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page string="Time Attendance">
                        <group>
                            <group>
                                <field name="policy_group_id" />
                                <field name="follow_up_level" />
                                <field name="employee_number" />
                                <field name="enroll_number" />
                                <field name="path_code" invisible="1"/>
                            </group>
                            <group string="Mobile Attendance Fields">
                                <field name="mobile_app_allow"/>
                                <field name="mobile_attendance_allow"/>
                                <field name="mobile_app_version"/>
                                <field name="mobile_register_id"/>
                                <field name="mobile_register_date"/>
                                <field name="mobile_token" readonly="1" force_save="1"/>
                                <field name="mobile_last_login_date" readonly="1" force_save="1"/>
                            </group>

                            <group string="Test Only" groups="base.group_no_one">
                                <button name="action_get_day_index" type="object" string="Get Shift Day"/>
                                <field name="test_date"/>
                                <field name="test_day_index"/>

                            </group>
                        </group>
                    </page>
                </xpath>

            </field>
        </record>
        <!-- search -->
        <record id="view_hr_employee_search" model="ir.ui.view">
            <field name="name">hr_employee</field>
            <field name="model">hr.employee</field>
            <field name="arch" type="xml">
                <search string="Employee">

                    <field name="shift_id" select="True"/>
                    <field name="policy_group_id" select="True"/>
                    <group string="Group By..">
                        <filter string="Shift" domain="[]" name="group_by_shift_id" context="{'group_by':'shift_id'}"/>
                        <filter string="Policy group" domain="[]" name="group_by_policy_group_id" context="{'group_by':'policy_group_id'}"/>
                    </group>
                </search>
            </field>
        </record>
        <!-- kanban , to show image, please add image_small in the model -->
        <record id="view_hr_employee_kanban" model="ir.ui.view" >
            <field name="name">hr_employee</field>
            <field name="model">hr.employee</field>
            <field name="arch" type="xml">
                <kanban version="7.0">
                    <field name="id" />
                    <field name="shift_id" />
                    <field name="policy_group_id" />
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click o_kanban_record_has_image_fill">
                                <div class="oe_kanban_details">
                                    <h4>
                                        <a type="open"><field name="name"/></a>
                                    </h4>
                                    <ul>

                                        <li>
                                            <span class="text-muted">Shift</span> <field name="shift_id"></field>
                                        </li>


                                        <li>
                                            <span class="text-muted">Policy group</span> <field name="policy_group_id"></field>
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <!-- calendar if a date field exists -->
        <!-- graph -->
        <record id="view_hr_employee_graph" model="ir.ui.view">
            <field name="name">hr_employee</field>
            <field name="model">hr.employee</field>
            <field name="arch" type="xml">
                <graph string="Employee" type="bar">
                    <field name="shift_id"/>
                </graph>
            </field>
        </record>

        <!-- action window -->
        <record id="action_hr_all_employee" model="ir.actions.act_window">
            <field name="name">Employee</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">hr.employee</field>
            <field name="view_mode">kanban,list,form,graph</field>
            <field name="context">{"search_default_fieldname":1}</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                Click to add a new Employee
                </p><p>
                Click the Create button to add a new Employee
                </p>
            </field>
        </record>

        <record id="action_hr_employee" model="ir.actions.server">
        <field name="name">Employees</field>
        <field name="model_id" ref="hr.model_hr_employee"/>
        <field name="binding_model_id" ref="hr.model_hr_employee"/>
        <field name="binding_view_types">kanban,list,form,graph</field>
        <field name="state">code</field>
        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>
        <field name="code">
             action = {
            'name': "Employees",
            'view_mode':'kanban,list,form,graph',
            'domain': [('id', 'in', env.get('res.users')._get_allowed_employee_ids().ids)],
            'res_model': 'hr.employee',
            'type': 'ir.actions.act_window',
            }
        </field>
    </record>
    </data>
</odoo>