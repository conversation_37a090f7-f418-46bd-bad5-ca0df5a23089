<odoo>
	<data>
		<record model="ir.module.category" id="module_category_time_attendance">
			<field name="name">Time Attendance</field>
			<field name="description">Time Attendance Groups</field>
			<field name="sequence">10</field>
		</record>
		<record id="group_read" model="res.groups">
			<field name="name">Read</field>
			<field name="category_id" ref="module_category_time_attendance"/>
		</record>
		<record id="group_time_attendance_user" model="res.groups">
			<field name="name">User</field>
			<field name="category_id" ref="module_category_time_attendance"/>
		</record>
		<record id="group_time_attendance_manager" model="res.groups">
			<field name="name">Manager</field>
			<field name="category_id" ref="module_category_time_attendance"/>
		</record>
	</data>
</odoo>