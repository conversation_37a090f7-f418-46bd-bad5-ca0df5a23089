# Methods Implementation with c#

### Employee ------------
#### register_app & authenticate
public string RegisterApp(AuthDTO auth)
        {
            var validAuth = Authenticate(auth);//validate login and allow mobile app permission
            if (validAuth == "1")
            {
                using (var os = osProvider.CreateObjectSpace())
                {
                    var emp = os.FindObject<Employee>(CriteriaOperator.Parse("Username=? or HRCode=?",
                    auth.Username, auth.EmpNo));
                    if (emp != null)
                    {
                        if (string.IsNullOrEmpty(emp.MobileRegisterId) ||emp.MobileRegisterId.Trim()=="")
                        {
                            emp.MobileRegisterId = auth.AppId;
                            try
                            {
                                os.CommitChanges();
                                return "1";
                            }
                            catch (Exception ex)
                            {
                                return ex.Message;
                            } 
                        }
                        else
                        {
                           return "Error:this app already register!! please contact administrator";
                        }
                    }
                }
            }
            else
                return validAuth;//return error
            return "0";
        }

public string Authenticate(AuthDTO auth ,bool validateMobileAppId=false)
        {
            if (auth != null)
            {
                using (var os = osProvider.CreateObjectSpace())
                {
                    var emp=os.FindObject<Employee>(CriteriaOperator.Parse("Username=? or HRCode=?",auth.Username,auth.EmpNo));
                    if (emp != null)
                    {
                        //validate user permission when authenticate from mobile app
                        if (auth.IsMobileDevice &&! emp.UseMobileApp) 
                        {
                           return "Error:You don't have mobile app using permission";
                        }
                        if (validateMobileAppId && auth.IsMobileDevice &&
                            (string.IsNullOrEmpty(emp.MobileRegisterId) || emp.MobileRegisterId.Trim() == ""))
                        {
                            return "Error:You don't have registeration for mobile app";
                        }
                        if (validateMobileAppId && auth.IsMobileDevice &&
                           (emp.MobileRegisterId!= auth.AppId))
                        {
                            return "Error:Not allowed for muliple registeration";
                        }
                        //AD/Form Authentication 
                        if (emp.User != null)
                        {
                            //Active direcory Authentication
                            if (emp.User.AciveDirectoryIsDefault)
                            {
                                string domain = System.Configuration.ConfigurationManager.AppSettings["Domain"];//< add key = "Domain" value = "GVITT.COM" />
                                string ADServer = System.Configuration.ConfigurationManager.AppSettings["ADServer"];    //< add key = "ADServer" value = "*************" />
                                var valid = ADAuth.ValidateUser(emp.Username, auth.Password, domain, ADServer);
                                if (valid)
                                {
                                    return "1";
                                }
                                else
                                {
                                    return "AD Error: Invalid Authentication";
                                }
                            }
                            else
                            { //  Form Authentication
                                if (auth.ExternalAuth)
                                    return "1";
                                if (emp.User.ComparePassword(auth.Password))
                                {
                                    return "1";
                                }
                                else
                                {
                                   return "App Error: Invalid Authentication";
                                }
                            }
                        }
                        else
                        {
                            return "Error:Employee doesn't have username";
                        }
                    }
                    else
                    {
                        return "Error:Employee doesn't exist";
                    }
                }
                }
            return "0";
        }

def authenticate(data: AuthDTO) -> EmployeeDTO:
    action = "01"  # login or register
    result = EmployeeDTO()
    isAuthenticated = False
    # Assuming there's a Log system in Python similar to the log4net
    # Using contextlib for a similar 'using' pattern in C#
    with log_thread_context(GetType().FullName + "." + current_function_name()): 
        try:
            if data and data.AppId:
                with object_space() as os:
                    action = "02"
                    emp = os.find_object("Username=? or HRCode=?", data.Username, data.EmpNo)
                    if emp:
                        action = "03"
                        if data.IsMobileDevice and not emp.UseMobileApp:
                            action = "04"
                            log_error("This user does not have permission to access mobile app, code:103, user:" + data.Username)
                            return EmployeeDTO(ResponseCode="103", ResponseMessage="This user does not have permission to access mobile app!!")
                        if emp.User:
                            action = "05"
                            if emp.User.AciveDirectoryIsDefault:
                                action = "06"
                                domain = configuration.get("Domain")
                                ADServer = configuration.get("ADServer")
                                isAuthenticated = ADAuth.validate_user(emp.Username, data.Password, domain, ADServer)
                                log_info("Success AD Authentication, user:" + emp.Username)
                            else:
                                if data.ExternalAuth:
                                    action = "07"
                                    isAuthenticated = True
                                    log_info("Refer authentication to external service, user:" + emp.Username)
                                elif emp.User.compare_password(data.Password):
                                    action = "08"
                                    isAuthenticated = True
                                    log_info("Success Form Authentication, user:" + emp.Username)
                                else:
                                    action = "09"
                                    log_warn("Invalid Authentication Type, user:" + emp.Username)
                    if data.AuthType == 2:
                        isAuthenticated = True
                        log_debug("Bypass Authentication, test user:" + emp.Username)
                    # ... continue as in your original C# code
                # Handle other conditions and cases similarly...
            else:
                log_error("Input data not found, code:100")
                return EmployeeDTO(ResponseCode="100", ResponseMessage="Input data not found")
        except Exception as ex:
            log_error("login failed, code:101", ex)
            return EmployeeDTO(ResponseCode="101", ResponseMessage="login failed, tag:" + action)

    return result

public static bool ValidateUser(string userName, string password, string domain,string server="")
        {
            bool validation;
            LdapConnection ldap;
            try
            {
                if(string.IsNullOrEmpty(server))
                 ldap = new LdapConnection(new LdapDirectoryIdentifier((string)null, false, false));
                else
                    ldap = new LdapConnection(server);
                // NetworkCredential nc = new NetworkCredential(Environment.UserName, "kals123", Environment.UserDomainName);
                NetworkCredential nc = new NetworkCredential(userName, password, domain);
                ldap.Credential = nc;
                ldap.AuthType = AuthType.Negotiate;
                ldap.Bind(nc); // user has authenticated at this point, as the credentials were used to login to the dc.
                validation = true;
            }
            catch (LdapException)
            {
                validation = false;
            }
            return validation;
        }

 public string AddAttendanceLog(EmpAttendanceLogDTO log ,bool isMobileApp=true)
        {
            try
            {
                if (log.Time == default(DateTime))
                {
                    return "Error: DateTimeString has incorrect format.";
                }
                using (var os = osProvider.CreateObjectSpace())
                {
                    string empCriteria = !String.IsNullOrEmpty(log.Username) ? "Username=?" : "HRCode=?";
                    string key = !String.IsNullOrEmpty(log.Username) ? log.Username : log.EmpNo;
                    var emp = os.FindObject<Employee>(CriteriaOperator.Parse(empCriteria, key));
                    //Check time between Min Allowed time and max Allowd time
                    if (isMobileApp)
                    {
                        //ToDO:Authenticate from mobile app
                        //string authRes = Authenticate(new AuthDTO
                        //{
                        //    Username = log.UserName,
                        //    EmpNo = log.EmpNo,
                        //    IsMobileDevice = true,
                        //    AppId = log.AppId
                        //}, true);
                        //if (authRes != "1")
                        //    return authRes;//return fauiler message
                        var unitShift=  Attendance.GetMyCurrentShiftUnit(emp, log.Time);
                        if (unitShift == null)
                        {
                            return string.Format("Error:attendance at this time not allowed!!");
                        }
                    }
                    if (emp != null)
                    {
                        var device = os.FindObject<Device>(CriteriaOperator.Parse("DeviceID=?", log.AppId));
                        var empLog = os.CreateObject<AttendanceLog>();
                        empLog.LogDateTime =isMobileApp?DateNow: log.Time; //take time from server in mobile app
                        empLog.Employee = emp;
                        empLog.Device = device;
                        empLog.Comment = "EXTERNAL_SERVICE";
                        empLog.Longitude = log.Longitude;
                        empLog.Latitude = log.Latitude;
                        os.CommitChanges();
                    }
                    else
                    {
                        return string.Format("Error: Employee {0} doesn't exist!!", key);
                    }
                }
            }
            catch (Exception ex)
            {
                return "Error:" + ex.Message;
            }
            return "1";//or errorMessage
        }

#### register_app