
### Mobile API summary
IsAlive	GET	Service at http://localhost:64951/FTAService.svc/IsAlive
MAddAttendance	POST	Service at http://localhost:64951/FTAService.svc/MAddAttendance
MAddAttendanceNative	POST	Service at http://localhost:64951/FTAService.svc/MAddAttendanceNative
MAuthenticate	POST	Service at http://localhost:64951/FTAService.svc/MAuthenticate
MAuthenticateNative	POST	Service at http://localhost:64951/FTAService.svc/MAuthenticateNative
MGetAtt	GET	Service at http://localhost:64951/FTAService.svc/MGetAtt?username={USERNAME}&from={FROM}&to={TO}
MGetEmployee	POST	Service at http://localhost:64951/FTAService.svc/MGetEmployee
MGetEmployeeAttendance	POST	Service at http://localhost:64951/FTAService.svc/MGetEmployeeAttendance
MGetEmployeeAttendanceByEmpNo	GET	Service at http://localhost:64951/FTAService.svc/MGetEmployeeAttendanceByEmpNo?empNo={EMPNO}&dateFrom={DATEFROM}&dateTo={DATETO}
MGetEmployeeAttendanceByUserName	GET	Service at http://localhost:64951/FTAService.svc/MGetEmployeeAttendanceByUserName?userName={USERNAME}&dateFrom={DATEFROM}&dateTo={DATETO}
MGetEmployeeByEmpNo	GET	Service at http://localhost:64951/FTAService.svc/MGetEmployeeByEmpNo?empNo={EMPNO}
MGetEmployeeByUserName	GET	Service at http://localhost:64951/FTAService.svc/MGetEmployeeByUserName?userName={USERNAME}
MRegisterApp	POST	Service at http://localhost:64951/FTAService.svc/MRegisterApp

### Dates format
   - DATE_DISPLAY_FORMAT = "dd/MM/yyyy HH:mm:ss";
   - DATE_SORT_FORMAT = "yyyyMMddHHmmss";
   - DATE_FORMAT = "yyyyMMdd";
   - DATE_TIME_FORMAT = "yyyyMMddHHmmss";
note 
   -DateTimeString is complete date and time formate yyyy/MM/dd HH:mm:ss
### End Point Details
#### MAddAttendance
Url: http://localhost:64951/FTAService.svc/MAddAttendance
HTTP Method: POST
c# EmpAttendanceLogDTO MAddAttendance(EmpAttendanceLogDTO data);
The following is an example request Json body: #

{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"DateTimeString":"String content",
	"Latitude":1.26743233E+15,
	"Longitude":1.26743233E+15,
	"Time":"\/Date(928138800000+0300)\/",
	"Type":2147483647
}

The following is an example response Json body:

{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"DateTimeString":"String content",
	"Latitude":1.26743233E+15,
	"Longitude":1.26743233E+15,
	"Time":"\/Date(928138800000+0300)\/",
	"Type":2147483647
}

#### MGetAtt
Url: http://localhost:64951/FTAService.svc/MGetAtt?username={USERNAME}&from={FROM}&to={TO}
HTTP Method: GET

The following is an example response Json body:

{
	"Attendance":[{
		"CheckInTimeString":"String content",
		"CheckOutTimeString":"String content",
		"DateString":"String content",
		"DayLength":2147483647,
		"DelayMinutes":2147483647,
		"DepartmentNameAr":"String content",
		"DepartmentNameEn":"String content",
		"DifferenceMinutes":2147483647,
		"EmployeeID":"String content",
		"NoteAr":"String content",
		"NoteEn":"String content",
		"OvertimeMinutes":2147483647,
		"PermissionMinutes":2147483647,
		"ShiftNameAr":"String content",
		"ShiftNameEn":"String content",
		"ShortageMinutes":2147483647,
		"Status":2147483647,
		"WorkingMinutes":2147483647
	}],
	"DeviceInfo":"String content",
	"Permissions":[{
		"EmployeeID":"String content",
		"EndDateString":"String content",
		"ID":"String content",
		"PermissionID":"String content",
		"PermissionNameAr":"String content",
		"PermissionNameEn":"String content",
		"Reason":"String content",
		"ResponseBy":"String content",
		"StartDateString":"String content",
		"Status":255,
		"TotalMinutes":2147483647,
		"Type":0
	}],
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"Summary":{
		"TotalAbsent":2147483647,
		"TotalDelayAndShortageMinutes":2147483647,
		"TotalDelayAndShortageMinutesString":"String content",
		"TotalDelayMinutes":2147483647,
		"TotalDelayMinutesString":"String content",
		"TotalOvertimeMinutes":2147483647,
		"TotalOvertimeMinutesString":"String content",
		"TotalPermissionMinutes":2147483647,
		"TotalPermissionMinutesString":"String content",
		"TotalShortageMinutes":2147483647,
		"TotalShortageMinutesString":"String content",
		"TotalVacation":2147483647,
		"TotalWorkingMinutes":2147483647,
		"TotalWorkingMinutesString":"String content"
	},
	"Vacations":[{
		"EmployeeID":"String content",
		"EndDateString":"String content",
		"ID":"String content",
		"Reason":"String content",
		"ResponseBy":"String content",
		"StartDateString":"String content",
		"Status":255,
		"VacationID":"String content",
		"VacationNameAr":"String content",
		"VacationNameEn":"String content"
	}]
}

#### MGetEmployeeAttendance
Url: http://localhost:64951/FTAService.svc/MGetEmployeeAttendance
HTTP Method: POST

The following is an example request Json body:
{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"Action":"String content",
	"DateFrom":"String content",
	"DateTo":"String content"
}

The following is an example response Json body:
same previous end point MGetAtt

#### MGetEmployee
Url: http://localhost:64951/FTAService.svc/MGetEmployee
HTTP Method: POST

The following is an example request Json body:
{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"AuthType":2147483647,
	"Domain":"String content",
	"ExternalAuth":true,
	"IsMobileDevice":true,
	"Password":"String content"
}

The following is an example response Json body:
{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"ArabicName":"String content",
	"AreaArabicName":"String content",
	"AreaEnglishName":"String content",
	"BGArabicName":"String content",
	"BGEnglishName":"String content",
	"BranchArabicName":"String content",
	"BranchEnglishName":"String content",
	"DeptArabicName":"String content",
	"DeptEnglishName":"String content",
	"Email":"String content",
	"EnglishName":"String content",
	"ErrorMessage":"String content",
	"PhoneNo":"String content",
	"UserName":"String content"
}

#### MGetEmployeeAttendanceByEmpNo
Url: http://localhost:64951/FTAService.svc/MGetEmployeeAttendanceByEmpNo?empNo={EMPNO}&dateFrom={DATEFROM}&dateTo={DATETO}
Url: http://localhost:64951/FTAService.svc/MGetEmployeeAttendanceByUserName?userName={USERNAME}&dateFrom={DATEFROM}&dateTo={DATETO}

HTTP Method: GET
HTTP Method: GET

The following is an example response Json body:
{
	"Emp":{
		"ResponseCode":"String content",
		"ResponseMessage":"String content",
		"AppId":"String content",
		"AppVersion":"String content",
		"DeviceInfo":"String content",
		"EmpNo":"String content",
		"HRCode":"String content",
		"Token":"String content",
		"Username":"String content",
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	},
	"ErrorMessage":"String content",
	"Logs":[{
		"Comment":"String content",
		"Date":"\/Date(928138800000+0300)\/",
		"DateString":"String content",
		"Delay":"String content",
		"InTime":"String content",
		"OutTime":"String content",
		"RequiredTime":"String content",
		"Shortage":"String content",
		"Status":2147483647,
		"WorkingTime":"String content"
	}]
}

#### MGetEmployeeByEmpNo
Url: http://localhost:64951/FTAService.svc/MGetEmployeeByEmpNo?empNo={EMPNO}
Url: http://localhost:64951/FTAService.svc/MGetEmployeeByUserName?userName={USERNAME}
HTTP Method: GET

The following is an example response Json body:
{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"ArabicName":"String content",
	"AreaArabicName":"String content",
	"AreaEnglishName":"String content",
	"BGArabicName":"String content",
	"BGEnglishName":"String content",
	"BranchArabicName":"String content",
	"BranchEnglishName":"String content",
	"DeptArabicName":"String content",
	"DeptEnglishName":"String content",
	"Email":"String content",
	"EnglishName":"String content",
	"ErrorMessage":"String content",
	"PhoneNo":"String content",
	"UserName":"String content"
}

#### MRegisterApp
Url: http://localhost:64951/FTAService.svc/MRegisterApp
HTTP Method: POST

app_id:32332433433
app_version:2.3
device_id_info:4334242dsds
emp_no:
hr_code:
token:
username:admin
auth_type:1
domain:
external_auth:False
is_mobile_device:True
password:1234
Authorization:Basic YWRtaW46MTIzNA==
Content-Type:application/json


The following is an example request Json body:
{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"AuthType":2147483647,
	"Domain":"String content",
	"ExternalAuth":true,
	"IsMobileDevice":true,
	"Password":"String content"
}

#### MAuthenticate
Url: http://localhost:64951/FTAService.svc/MAuthenticate
HTTP Method: POST

The following is an example request Json body:

{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"AuthType":2147483647,
	"Domain":"String content",
	"ExternalAuth":true,
	"IsMobileDevice":true,
	"Password":"String content"
}

The following is an example response Json body:
{
	"ResponseCode":"String content",
	"ResponseMessage":"String content",
	"AppId":"String content",
	"AppVersion":"String content",
	"DeviceInfo":"String content",
	"EmpNo":"String content",
	"HRCode":"String content",
	"Token":"String content",
	"Username":"String content",
	"ArabicName":"String content",
	"AreaArabicName":"String content",
	"AreaEnglishName":"String content",
	"BGArabicName":"String content",
	"BGEnglishName":"String content",
	"BranchArabicName":"String content",
	"BranchEnglishName":"String content",
	"DeptArabicName":"String content",
	"DeptEnglishName":"String content",
	"Email":"String content",
	"EnglishName":"String content",
	"ErrorMessage":"String content",
	"PhoneNo":"String content",
	"UserName":"String content"
}
