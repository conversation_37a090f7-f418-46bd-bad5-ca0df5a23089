from odoo import http
from odoo.http import request
import json

class MobileAppController(http.Controller):

    @http.route('/mobile_app/register', type='http', auth='public', methods=['POST'], csrf=False)
    def register_app(self, **kwargs):
        # Retrieve the JSON object from the request body
        try:
            auth_data = json.loads(request.httprequest.data)
        except ValueError:
            return json.dumps({'error': 'Invalid JSON data'})

        # Access the 'authData' field from the JSON object
        if 'authData' in auth_data:
            auth_data = auth_data['authData']
            # Do something with the 'authData'

            # Return a response as JSON
            response = {'message': 'Registration successful'}
            return json.dumps(response)

        return json.dumps({'error': 'Invalid request'})

In this example, we define a route /mobile_app/register with the routing name "MRegisterApp". The register_app() method is decorated with @http.route to specify the route details. We set type='http' to indicate that the response will be an HTTP response.

The method is configured to handle POST requests (methods=['POST']) and is accessible to the public (auth='public'). We also disable CSRF protection (csrf=False) for simplicity, but it's generally recommended to keep it enabled for security.

Inside the register_app() method, we retrieve the JSON object from the request body using request.httprequest.data. We then parse the JSON data using json.loads() and access the authData field from the JSON object. You can perform any desired processing or validation with the authData field.

Finally, we return a response as a JSON object using json.dumps().

Remember to install the required dependencies for Odoo and ensure that the controller is properly registered in your Odoo module.






