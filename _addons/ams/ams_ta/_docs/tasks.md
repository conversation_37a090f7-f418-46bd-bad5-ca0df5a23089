

skd_generate days
toggle button error 
  File "e:\abadr\work\01.laplace\026.emr\src\odoo17\addons_ta\ta\models\schedule_day.py", line 91, in _onchange_shift_units
    if not self.schedule_id.day_ids[self.index - 2].is_day_off else False
  File "E:\abadr\Work\01.laplace\026.EMR\src\odoo17\odoo\models.py", line 6574, in __getitem__
    return self.browse((self._ids[key],))
IndexError: tuple index out of range

- ts: recalculate issue after set day to absent , recalculate remove the absent 
- ts: shift - skd name incorrect when created from shift page

- ta: manual attendance cannot set 00:00 as update
- ta: shift unit , handle [min , max] configuration to add validation diff > 1 hour
- ta: update all security  group with new security in base module
- employee schedule day to identify time off in vacation / allocation
- ta: timesheet form view - update tracking log of manual attendance with user time 
