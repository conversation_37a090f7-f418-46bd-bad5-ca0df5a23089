
### API Tasks
#### Timesheet
    - implement method get_employee_attendance to retrive timesheet data according specified structure
    - implement method add_attendance_log to create new punch log based on specific time or time now
        check if emplyee mobile_app_allow,mobile_attendance_allow , 

#### Employee
    - implement method get_employee 
    - implement method register_app to rgister mobile app for the employee

## Controller
    - Register App 
    - Register app testing the cases in validation , search emo_no,hr_code,username [DON<PERSON>]

    - implement Auhenticate method endpoint [DONE]
    - implement get_employee_attendance method [DONE]
    - implement add_attendance_log method 
    - implement get_employee

