
**Fingerprint & Time Attendance (FTA)**

WCF - RESTful Web Service


<table>
  <tr>
   <td><strong>Service Name</strong>
   </td>
   <td>FTAService.svc
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td>Attendance RESTful web services are lightweight, highly scalable and maintainable and are very commonly used to create APIs for web-based applications.
   </td>
  </tr>
  <tr>
   <td><strong>Purpose</strong>
   </td>
   <td>Build API for integration between ERP and FTA
   </td>
  </tr>
  <tr>
   <td><strong>Integration Modules</strong>
   </td>
   <td>Employees, Attendance, Permissions  and Vacations
   </td>
  </tr>
  <tr>
   <td><strong>Version</strong>
   </td>
   <td>V.03
   </td>
  </tr>
  <tr>
   <td><strong>Service Url (Test)</strong>
   </td>
   <td><a href="http://server_ip/ftaint2/ftaservice.svc/isalive">http://server_ip/ftaint2/ftaservice.svc/isalive</a>
   </td>
  </tr>
  <tr>
   <td><strong>Service Url (Production )</strong>
   </td>
   <td><strong><a href="https://myattendance.stcsc.sa/ftaint/FTAService.svc/isalive">https://myattendance.stcsc.sa/ftaint/FTAService.svc/isalive</a></strong>
   </td>
  </tr>
</table>


**Note**: the test server will under HTTPS not HTTP

**This document contains:**

**Web Service  Methods **

       All service  methods which we need to build integration between ERP and FTA system depend on JSON formate 

**Note: \
 Base URL on Test Server: [http://server_ip/ftaint2/ftaservice.svc/](http://server_ip/ftaint2/ftaservice.svc/)**

**Table Content:**


[TOC]



# **Fast Start**

To get fast start, run the following URLs which are real examples on the test server 

**Employees:**

[https://server_ip/ftaint2/ftaservice.svc/isalive](https://server_ip/ftaint2/ftaservice.svc/isalive)

[http://server_ip/ftaint2/ftaservice.svc/GetEmployeeByEmpNo?empNo=00762](http://server_ip/ftaint2/ftaservice.svc/GetEmployeeByEmpNo?empNo=00762)

[http://server_ip/ftaint2/ftaservice.svc/GetEmployeeByusername?username=moataz.migled](http://server_ip/ftaint2/ftaservice.svc/GetEmployeeByusername?username=moataz.migled)

[http://server_ip/ftaint2/xaf.ftasvc/ftaservice.svc/GetEmployees](http://server_ip/ftaint2/xaf.ftasvc/ftaservice.svc/GetEmployees)

[http://server_ip/ftaint2/ftaservice.svc/GetEmployees](http://server_ip/ftaint2/ftaservice.svc/GetEmployees)

**Attendance:**

[http://server_ip/ftaint2/ftaservice.svc/GetEmployeeAttendanceByEmpNo?empNo=00762&dateFrom=01012019&dateTo=31012019](http://server_ip/ftaint2/ftaservice.svc/GetEmployeeAttendanceByEmpNo?empNo=00762&dateFrom=01012019&dateTo=31012019)

[http://server_ip/ftaint2/ftaservice.svc/GetEmployeeAttendanceByUserName?username=moataz.migled&dateFrom=01012019&dateTo=31012019](http://server_ip/ftaint2/ftaservice.svc/GetEmployeeAttendanceByUserName?username=moataz.migled&dateFrom=01012019&dateTo=31012019)

[http://server_ip/ftaint2/ftaservice.svc/GetEmployeesAttendance?dateFrom=01012019&dateTo=31012019](http://server_ip/ftaint2/ftaservice.svc/GetEmployeesAttendance?dateFrom=01012019&dateTo=31012019)<span style="text-decoration:underline;"> \
</span>

**Deduction**

[http://server_ip/ftaint2/ftaservice.svc/GetEmployeesAttendanceDeduction?dateFrom=01012019&dateTo=31012019](http://server_ip/ftaint2/ftaservice.svc/GetEmployeesAttendanceDeduction?dateFrom=01012019&dateTo=31012019)

[http://server_ip/ftaint2/ftaservice.svc/GetEmployeesAttendanceDeduction?empNo=00762&dateFrom=01012019&dateTo=31012019](http://server_ip/ftaint2/ftaservice.svc/GetEmployeesAttendanceDeduction?empNo=00762&dateFrom=01012019&dateTo=31012019)


# **Employees Module** 


## Employees Service Methods

1- AddEmployee

2- GetEmployeeByEmpNo

3- GetEmployeeByUserName

4- GetEmployees

5- GetEmployeesByDeptName


<table>
  <tr>
   <td colspan="2" >
<ol>

<li>  <strong>BaseURL/AddEmployee</strong>
</li>
</ol>
   </td>
   <td><strong>Type</strong>: Post
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >Add employee data or update data if the employee exists
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Request JSON Body </strong>
   </td>
  </tr>
  <tr>
   <td colspan="3" ><code>{
	"ArabicName":"String content",
	"AreaArabicName":"String content",
	"AreaEnglishName":"String content",
	"BGArabicName":"String content",
	"BGEnglishName":"String content",
	"BranchArabicName":"String content",
	"BranchEnglishName":"String content",
	"DeptArabicName":"String content",
	"DeptEnglishName":"String content",
	"Email":"String content",
	"EmpNo":"String content",
	"EnglishName":"String content",
	"ErrorMessage":"String content",
	"PhoneNo":"String content",
	"UserName":"String content"
}</code>
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body:</strong>
   </td>
  </tr>
  <tr>
   <td colspan="3" ><code>"String content"</code>
   </td>
  </tr>
  <tr>
   <td colspan="3" >Return “1” for success save or error message
   </td>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<ol>

<li>  <strong>BaseURL/GetEmployeeByEmpNo?empNo={EMPNO}</strong>
</li>
</ol>
   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employee data by employee number
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"ArabicName":"String content",
	"AreaArabicName":"String content",
	"AreaEnglishName":"String content",
	"BGArabicName":"String content",
	"BGEnglishName":"String content",
	"BranchArabicName":"String content",
	"BranchEnglishName":"String content",
	"DeptArabicName":"String content",
	"DeptEnglishName":"String content",
	"Email":"String content",
	"EmpNo":"String content",
	"EnglishName":"String content",
	"ErrorMessage":"String content",
	"PhoneNo":"String content",
	"UserName":"String content"
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeeByUserName?userName={USERNAME}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >Return employee data by employee username
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"ArabicName":"String content",
	"AreaArabicName":"String content",
	"AreaEnglishName":"String content",
	"BGArabicName":"String content",
	"BGEnglishName":"String content",
	"BranchArabicName":"String content",
	"BranchEnglishName":"String content",
	"DeptArabicName":"String content",
	"DeptEnglishName":"String content",
	"Email":"String content",
	"EmpNo":"String content",
	"EnglishName":"String content",
	"ErrorMessage":"String content",
	"PhoneNo":"String content",
	"UserName":"String content"
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<ol>

<li>  <strong>BaseURL/GetEmployees</strong>
</li>
</ol>
   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >Return all employee data 
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="4" colspan="3" ><code>{
	"Employees":[{
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EmpNo":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeesByDeptName?deptName={DEPTNAME}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >Return employees data by department name
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="4" colspan="3" ><code>{
	"Employees":[{
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EmpNo":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
</table>



# **Attendance** **Module**


## Employees Attendance Service Methods

1- GetEmployeeAttendanceByEmpNo

2- GetEmployeeAttendanceByUserName

3- GetEmployeesAttendance

4- GetEmployeesAttendanceByDeptName


<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeeAttendanceByEmpNo?empNo={EMPNO}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employee attendance data by employee number in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="6" colspan="3" ><code>{
	"Emp":{
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EmpNo":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	},
	"ErrorMessage":"String content",
	"Logs":[{
		"Comment":"String content",
		"Date":"\/Date(928138800000+0300)\/",
		"DateString":"String content",
		"Delay":"String content",
		"InTime":"String content",
		"OutTime":"String content",
		"RequiredTime":"String content",
		"Shortage":"String content",
		"Status":0,
		"WorkingTime":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseUrl/GetEmployeeAttendanceByUserName?userName={USERNAME}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employee attendance data by username in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="5" colspan="3" ><code>{
	"Emp":{
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EmpNo":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	},
	"ErrorMessage":"String content",
	"Logs":[{
		"Comment":"String content",
		"Date":"\/Date(928138800000+0300)\/",
		"DateString":"String content",
		"Delay":"String content",
		"InTime":"String content",
		"OutTime":"String content",
		"RequiredTime":"String content",
		"Shortage":"String content",
		"Status":0,
		"WorkingTime":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeesAttendance?dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="5" colspan="3" ><code>{
	"AttList":[{
		"Emp":{
			"ArabicName":"String content",
			"AreaArabicName":"String content",
			"AreaEnglishName":"String content",
			"BGArabicName":"String content",
			"BGEnglishName":"String content",
			"BranchArabicName":"String content",
			"BranchEnglishName":"String content",
			"DeptArabicName":"String content",
			"DeptEnglishName":"String content",
			"Email":"String content",
			"EmpNo":"String content",
			"EnglishName":"String content",
			"ErrorMessage":"String content",
			"PhoneNo":"String content",
			"UserName":"String content"
		},
		"ErrorMessage":"String content",
		"Logs":[{
			"Comment":"String content",
			"Date":"\/Date(928138800000+0300)\/",
			"DateString":"String content",
			"Delay":"String content",
			"InTime":"String content",
			"OutTime":"String content",
			"RequiredTime":"String content",
			"Shortage":"String content",
			"Status":0,
			"WorkingTime":"String content"
		}]
	}],
	"ErrorMessage":"String content"
}</code>
   </td>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeesAttendanceByDeptName?deptName={DEPTNAME}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employees attendance data in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="5" colspan="3" ><code>{
	"AttList":[{
		"Emp":{
			"ArabicName":"String content",
			"AreaArabicName":"String content",
			"AreaEnglishName":"String content",
			"BGArabicName":"String content",
			"BGEnglishName":"String content",
			"BranchArabicName":"String content",
			"BranchEnglishName":"String content",
			"DeptArabicName":"String content",
			"DeptEnglishName":"String content",
			"Email":"String content",
			"EmpNo":"String content",
			"EnglishName":"String content",
			"ErrorMessage":"String content",
			"PhoneNo":"String content",
			"UserName":"String content"
		},
		"ErrorMessage":"String content",
		"Logs":[{
			"Comment":"String content",
			"Date":"\/Date(928138800000+0300)\/",
			"DateString":"String content",
			"Delay":"String content",
			"InTime":"String content",
			"OutTime":"String content",
			"RequiredTime":"String content",
			"Shortage":"String content",
			"Status":0,
			"WorkingTime":"String content"
		}]
	}],
	"ErrorMessage":"String content"
}</code>
   </td>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeesAttendanceDeduction?dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employees deduction data in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="5" colspan="3" ><code>{
	"DeductionList":[{
		"DeductionHours":0,
		"DeductionMinutes":0,
		"EmpNo":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
  <tr>
  </tr>
</table>



# **Permissions** **Module**


## Employees Permissions Service Methods

1- AddPermissionRequest

1- GetEmployeePermissionsByEmpNo

2- GetEmployeePermissionsByUserName

3- GetEmployeesPermissions

4- GetEmployeesPermissionsByDeptName


<table>
  <tr>
   <td colspan="2" >
<h3> <strong>BaseURL/AddPermissionRequest</strong></h3>


   </td>
   <td><strong>Type</strong>: Post
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >Add employee permision request
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Request JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td colspan="3" ><code>{
	"EmpNo":"String content",
	"EndDateString":"String content",
	"ErrorMessage":"String content",
	"Reason":"String content",
	"ReplyComment":"String content",
	"RequestName":"String content",
	"RequestNo":"String content",
	"StartDateString":"String content",
	"Status":0,
	"TotalMinutes":0,
	"TransactionType":0,
	"UserName":"String content"
}</code>
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"EmpNo":"String content",
	"EndDateString":"String content",
	"ErrorMessage":"String content",
	"Reason":"String content",
	"ReplyComment":"String content",
	"RequestName":"String content",
	"RequestNo":"String content",
	"StartDateString":"String content",
	"Status":0,
	"TotalMinutes":0,
	"TransactionType":0,
	"UserName":"String content"
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeePermissionsByEmpNo?empNo={EMPNO}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employee permissions data by employee number in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"Emp":{
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EmpNo":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	},
	"ErrorMessage":"String content",
	"Logs":[{
		"EmpNo":"String content",
		"EndDateString":"String content",
		"ErrorMessage":"String content",
		"Reason":"String content",
		"ReplyComment":"String content",
		"RequestName":"String content",
		"RequestNo":"String content",
		"StartDateString":"String content",
		"Status":0,
		"TotalMinutes":0,
		"TransactionType":0,
		"UserName":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeePermissionsByUserName?userName={USERNAME}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employee permissions data by username in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"Emp":{
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EmpNo":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	},
	"ErrorMessage":"String content",
	"Logs":[{
		"EmpNo":"String content",
		"EndDateString":"String content",
		"ErrorMessage":"String content",
		"Reason":"String content",
		"ReplyComment":"String content",
		"RequestName":"String content",
		"RequestNo":"String content",
		"StartDateString":"String content",
		"Status":0,
		"TotalMinutes":0,
		"TransactionType":0,
		"UserName":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeesPermissions?dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >Return all employees permissions data in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"ErrorMessage":"String content",
	"Requests":[{
		"Emp":{
			"ArabicName":"String content",
			"AreaArabicName":"String content",
			"AreaEnglishName":"String content",
			"BGArabicName":"String content",
			"BGEnglishName":"String content",
			"BranchArabicName":"String content",
			"BranchEnglishName":"String content",
			"DeptArabicName":"String content",
			"DeptEnglishName":"String content",
			"Email":"String content",
			"EmpNo":"String content",
			"EnglishName":"String content",
			"ErrorMessage":"String content",
			"PhoneNo":"String content",
			"UserName":"String content"
		},
		"ErrorMessage":"String content",
		"Logs":[{
			"EmpNo":"String content",
			"EndDateString":"String content",
			"ErrorMessage":"String content",
			"Reason":"String content",
			"ReplyComment":"String content",
			"RequestName":"String content",
			"RequestNo":"String content",
			"StartDateString":"String content",
			"Status":0,
			"TotalMinutes":0,
			"TransactionType":0,
			"UserName":"String content"
		}]
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeesPermissionsByDeptName?deptName={DEPTNAME}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employees permission data in the specified period and department name
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"ErrorMessage":"String content",
	"Requests":[{
		"Emp":{
			"ArabicName":"String content",
			"AreaArabicName":"String content",
			"AreaEnglishName":"String content",
			"BGArabicName":"String content",
			"BGEnglishName":"String content",
			"BranchArabicName":"String content",
			"BranchEnglishName":"String content",
			"DeptArabicName":"String content",
			"DeptEnglishName":"String content",
			"Email":"String content",
			"EmpNo":"String content",
			"EnglishName":"String content",
			"ErrorMessage":"String content",
			"PhoneNo":"String content",
			"UserName":"String content"
		},
		"ErrorMessage":"String content",
		"Logs":[{
			"EmpNo":"String content",
			"EndDateString":"String content",
			"ErrorMessage":"String content",
			"Reason":"String content",
			"ReplyComment":"String content",
			"RequestName":"String content",
			"RequestNo":"String content",
			"StartDateString":"String content",
			"Status":0,
			"TotalMinutes":0,
			"TransactionType":0,
			"UserName":"String content"
		}]
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



# **Vacations** **Module**


## Employees Vacations Service Methods

1- AddVacationRequest

1- GetEmployeeVacationsByEmpNo

2- GetEmployeeVacationsByUserName

3- GetEmployeesVacations

4- GetEmployeesVacationsByDeptName


<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/ddVacationRequest</strong></h3>


   </td>
   <td><strong>Type</strong>: Post
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >Add employee vacation request
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Request JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td colspan="3" ><code>{
	"EmpNo":"String content",
	"EndDateString":"String content",
	"ErrorMessage":"String content",
	"Reason":"String content",
	"ReplyComment":"String content",
	"RequestName":"String content",
	"RequestNo":"String content",
	"StartDateString":"String content",
	"Status":0,
	"TotalMinutes":0,
	"TransactionType":0,
	"UserName":"String content"
}</code>
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"EmpNo":"String content",
	"EndDateString":"String content",
	"ErrorMessage":"String content",
	"Reason":"String content",
	"ReplyComment":"String content",
	"RequestName":"String content",
	"RequestNo":"String content",
	"StartDateString":"String content",
	"Status":0,
	"TotalMinutes":0,
	"TransactionType":0,
	"UserName":"String content"
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeeVacationsByEmpNo?empNo={EMPNO}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employee vacations data by employee number in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"Emp":{
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EmpNo":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	},
	"ErrorMessage":"String content",
	"Logs":[{
		"EmpNo":"String content",
		"EndDateString":"String content",
		"ErrorMessage":"String content",
		"Reason":"String content",
		"ReplyComment":"String content",
		"RequestName":"String content",
		"RequestNo":"String content",
		"StartDateString":"String content",
		"Status":0,
		"TotalMinutes":0,
		"TransactionType":0,
		"UserName":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeeVacationsByUserName?userName={USERNAME}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employee vacations data by username in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"Emp":{
		"ArabicName":"String content",
		"AreaArabicName":"String content",
		"AreaEnglishName":"String content",
		"BGArabicName":"String content",
		"BGEnglishName":"String content",
		"BranchArabicName":"String content",
		"BranchEnglishName":"String content",
		"DeptArabicName":"String content",
		"DeptEnglishName":"String content",
		"Email":"String content",
		"EmpNo":"String content",
		"EnglishName":"String content",
		"ErrorMessage":"String content",
		"PhoneNo":"String content",
		"UserName":"String content"
	},
	"ErrorMessage":"String content",
	"Logs":[{
		"EmpNo":"String content",
		"EndDateString":"String content",
		"ErrorMessage":"String content",
		"Reason":"String content",
		"ReplyComment":"String content",
		"RequestName":"String content",
		"RequestNo":"String content",
		"StartDateString":"String content",
		"Status":0,
		"TotalMinutes":0,
		"TransactionType":0,
		"UserName":"String content"
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeesVacations?dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >Return all employees vacations data in the specified period
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"ErrorMessage":"String content",
	"Requests":[{
		"Emp":{
			"ArabicName":"String content",
			"AreaArabicName":"String content",
			"AreaEnglishName":"String content",
			"BGArabicName":"String content",
			"BGEnglishName":"String content",
			"BranchArabicName":"String content",
			"BranchEnglishName":"String content",
			"DeptArabicName":"String content",
			"DeptEnglishName":"String content",
			"Email":"String content",
			"EmpNo":"String content",
			"EnglishName":"String content",
			"ErrorMessage":"String content",
			"PhoneNo":"String content",
			"UserName":"String content"
		},
		"ErrorMessage":"String content",
		"Logs":[{
			"EmpNo":"String content",
			"EndDateString":"String content",
			"ErrorMessage":"String content",
			"Reason":"String content",
			"ReplyComment":"String content",
			"RequestName":"String content",
			"RequestNo":"String content",
			"StartDateString":"String content",
			"Status":0,
			"TotalMinutes":0,
			"TransactionType":0,
			"UserName":"String content"
		}]
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>



<table>
  <tr>
   <td colspan="2" >
<h3><strong>BaseURL/GetEmployeesVacationsByDeptName?deptName={DEPTNAME}&dateFrom={DATEFROM}&dateTo={DATETO}</strong></h3>


   </td>
   <td><strong>Type</strong>: Get
   </td>
  </tr>
  <tr>
   <td><strong>Description</strong>
   </td>
   <td colspan="2" >return employees vacations data in the specified period and department name
   </td>
  </tr>
  <tr>
   <td colspan="3" ><strong>Response JSON Body</strong>
   </td>
  </tr>
  <tr>
   <td rowspan="2" colspan="3" ><code>{
	"ErrorMessage":"String content",
	"Requests":[{
		"Emp":{
			"ArabicName":"String content",
			"AreaArabicName":"String content",
			"AreaEnglishName":"String content",
			"BGArabicName":"String content",
			"BGEnglishName":"String content",
			"BranchArabicName":"String content",
			"BranchEnglishName":"String content",
			"DeptArabicName":"String content",
			"DeptEnglishName":"String content",
			"Email":"String content",
			"EmpNo":"String content",
			"EnglishName":"String content",
			"ErrorMessage":"String content",
			"PhoneNo":"String content",
			"UserName":"String content"
		},
		"ErrorMessage":"String content",
		"Logs":[{
			"EmpNo":"String content",
			"EndDateString":"String content",
			"ErrorMessage":"String content",
			"Reason":"String content",
			"ReplyComment":"String content",
			"RequestName":"String content",
			"RequestNo":"String content",
			"StartDateString":"String content",
			"Status":0,
			"TotalMinutes":0,
			"TransactionType":0,
			"UserName":"String content"
		}]
	}]
}</code>
   </td>
  </tr>
  <tr>
  </tr>
</table>

