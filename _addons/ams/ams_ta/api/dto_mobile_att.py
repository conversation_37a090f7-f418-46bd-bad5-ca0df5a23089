from .utils import *
from dataclasses import dataclass
from typing import Any, TypeVar, Type, cast


@dataclass
class Attendance:
    check_in_time_string: str  # yyyyMMddHHmmss
    check_out_time_string: str  # yyyyMMddHHmmss
    date_string: str
    day_length: int  # required time (Minutes)
    delay_minutes: int
    department_name_ar: str
    department_name_en: str
    difference_minutes: int
    employee_id: str
    note_ar: str
    note_en: str
    overtime_minutes: int
    permission_minutes: int
    shift_name_ar: str
    shift_name_en: str
    shortage_minutes: int
    status: int  # // 0=WorkingDay,1=DayOff,2=Absent,3=Vacation
    working_minutes: int

    @staticmethod
    def from_dict(obj: Any) -> 'Attendance':
        assert isinstance(obj, dict)
        check_in_time_string = from_str(obj.get("CheckInTimeString"))
        check_out_time_string = from_str(obj.get("CheckOutTimeString"))
        date_string = from_str(obj.get("DateString"))
        day_length = from_int(obj.get("DayLength"))
        delay_minutes = from_int(obj.get("DelayMinutes"))
        department_name_ar = from_str(obj.get("DepartmentNameAr"))
        department_name_en = from_str(obj.get("DepartmentNameEn"))
        difference_minutes = from_int(obj.get("DifferenceMinutes"))
        employee_id = from_str(obj.get("EmployeeID"))
        note_ar = from_str(obj.get("NoteAr"))
        note_en = from_str(obj.get("NoteEn"))
        overtime_minutes = from_int(obj.get("OvertimeMinutes"))
        permission_minutes = from_int(obj.get("PermissionMinutes"))
        shift_name_ar = from_str(obj.get("ShiftNameAr"))
        shift_name_en = from_str(obj.get("ShiftNameEn"))
        shortage_minutes = from_int(obj.get("ShortageMinutes"))
        status = from_int(obj.get("Status"))
        working_minutes = from_int(obj.get("WorkingMinutes"))
        return Attendance(check_in_time_string, check_out_time_string, date_string, day_length, delay_minutes,
                          department_name_ar, department_name_en, difference_minutes, employee_id, note_ar, note_en,
                          overtime_minutes, permission_minutes, shift_name_ar, shift_name_en, shortage_minutes, status,
                          working_minutes)

    def to_dict(self) -> dict:
        result: dict = {}
        result["CheckInTimeString"] = from_str(self.check_in_time_string)
        result["CheckOutTimeString"] = from_str(self.check_out_time_string)
        result["DateString"] = from_str(self.date_string)
        result["DayLength"] = from_int(self.day_length)
        result["DelayMinutes"] = from_int(self.delay_minutes)
        result["DepartmentNameAr"] = from_str(self.department_name_ar)
        result["DepartmentNameEn"] = from_str(self.department_name_en)
        result["DifferenceMinutes"] = from_int(self.difference_minutes)
        result["EmployeeID"] = from_str(self.employee_id)
        result["NoteAr"] = from_str(self.note_ar)
        result["NoteEn"] = from_str(self.note_en)
        result["OvertimeMinutes"] = from_int(self.overtime_minutes)
        result["PermissionMinutes"] = from_int(self.permission_minutes)
        result["ShiftNameAr"] = from_str(self.shift_name_ar)
        result["ShiftNameEn"] = from_str(self.shift_name_en)
        result["ShortageMinutes"] = from_int(self.shortage_minutes)
        result["Status"] = from_int(self.status)
        result["WorkingMinutes"] = from_int(self.working_minutes)
        return result


@dataclass
class Permission:
    employee_id: str
    end_date_string: str
    id: str  # hr.leave record id
    permission_id: str  # time of type id ()
    permission_name_ar: str  # read from dictionary
    permission_name_en: str
    reason: str
    response_by: str
    start_date_string: str
    status: int  # Wait=0,  Accept = 1,  Reject = 2, Canceled = 3,
    total_minutes: int
    type: int  # 0 = am , 1 = bm

    @staticmethod
    def from_dict(obj: Any) -> 'Permission':
        assert isinstance(obj, dict)
        employee_id = from_str(obj.get("EmployeeID"))
        end_date_string = from_str(obj.get("EndDateString"))
        id = from_str(obj.get("ID"))
        permission_id = from_str(obj.get("PermissionID"))
        permission_name_ar = from_str(obj.get("PermissionNameAr"))
        permission_name_en = from_str(obj.get("PermissionNameEn"))
        reason = from_str(obj.get("Reason"))
        response_by = from_str(obj.get("ResponseBy"))
        start_date_string = from_str(obj.get("StartDateString"))
        status = from_int(obj.get("Status"))
        total_minutes = from_int(obj.get("TotalMinutes"))
        type = from_int(obj.get("Type"))
        return Permission(employee_id, end_date_string, id, permission_id, permission_name_ar, permission_name_en,
                          reason, response_by, start_date_string, status, total_minutes, type)

    def to_dict(self) -> dict:
        result: dict = {}
        result["EmployeeID"] = from_str(self.employee_id)
        result["EndDateString"] = from_str(self.end_date_string)
        result["ID"] = from_str(self.id)
        result["PermissionID"] = from_str(self.permission_id)
        result["PermissionNameAr"] = from_str(self.permission_name_ar)
        result["PermissionNameEn"] = from_str(self.permission_name_en)
        result["Reason"] = from_str(self.reason)
        result["ResponseBy"] = from_str(self.response_by)
        result["StartDateString"] = from_str(self.start_date_string)
        result["Status"] = from_int(self.status)
        result["TotalMinutes"] = from_int(self.total_minutes)
        result["Type"] = from_int(self.type)
        return result


@dataclass
class Summary:
    total_absent: int
    total_delay_and_shortage_minutes: int
    total_delay_and_shortage_minutes_string: str
    total_delay_minutes: int
    total_delay_minutes_string: str
    total_overtime_minutes: int
    total_overtime_minutes_string: str
    total_permission_minutes: int
    total_permission_minutes_string: str
    total_shortage_minutes: int
    total_shortage_minutes_string: str
    total_vacation: int
    total_working_minutes: int
    total_working_minutes_string: str

    @staticmethod
    def from_dict(obj: Any) -> 'Summary':
        assert isinstance(obj, dict)
        total_absent = from_int(obj.get("TotalAbsent"))
        total_delay_and_shortage_minutes = from_int(obj.get("TotalDelayAndShortageMinutes"))
        total_delay_and_shortage_minutes_string = from_str(obj.get("TotalDelayAndShortageMinutesString"))
        total_delay_minutes = from_int(obj.get("TotalDelayMinutes"))
        total_delay_minutes_string = from_str(obj.get("TotalDelayMinutesString"))
        total_overtime_minutes = from_int(obj.get("TotalOvertimeMinutes"))
        total_overtime_minutes_string = from_str(obj.get("TotalOvertimeMinutesString"))
        total_permission_minutes = from_int(obj.get("TotalPermissionMinutes"))
        total_permission_minutes_string = from_str(obj.get("TotalPermissionMinutesString"))
        total_shortage_minutes = from_int(obj.get("TotalShortageMinutes"))
        total_shortage_minutes_string = from_str(obj.get("TotalShortageMinutesString"))
        total_vacation = from_int(obj.get("TotalVacation"))
        total_working_minutes = from_int(obj.get("TotalWorkingMinutes"))
        total_working_minutes_string = from_str(obj.get("TotalWorkingMinutesString"))
        return Summary(total_absent, total_delay_and_shortage_minutes, total_delay_and_shortage_minutes_string,
                       total_delay_minutes, total_delay_minutes_string, total_overtime_minutes,
                       total_overtime_minutes_string, total_permission_minutes, total_permission_minutes_string,
                       total_shortage_minutes, total_shortage_minutes_string, total_vacation, total_working_minutes,
                       total_working_minutes_string)

    def to_dict(self) -> dict:
        result: dict = {}
        result["TotalAbsent"] = from_int(self.total_absent)
        result["TotalDelayAndShortageMinutes"] = from_int(self.total_delay_and_shortage_minutes)
        result["TotalDelayAndShortageMinutesString"] = from_str(self.total_delay_and_shortage_minutes_string)
        result["TotalDelayMinutes"] = from_int(self.total_delay_minutes)
        result["TotalDelayMinutesString"] = from_str(self.total_delay_minutes_string)
        result["TotalOvertimeMinutes"] = from_int(self.total_overtime_minutes)
        result["TotalOvertimeMinutesString"] = from_str(self.total_overtime_minutes_string)
        result["TotalPermissionMinutes"] = from_int(self.total_permission_minutes)
        result["TotalPermissionMinutesString"] = from_str(self.total_permission_minutes_string)
        result["TotalShortageMinutes"] = from_int(self.total_shortage_minutes)
        result["TotalShortageMinutesString"] = from_str(self.total_shortage_minutes_string)
        result["TotalVacation"] = from_int(self.total_vacation)
        result["TotalWorkingMinutes"] = from_int(self.total_working_minutes)
        result["TotalWorkingMinutesString"] = from_str(self.total_working_minutes_string)
        return result


@dataclass
class Vacation:
    employee_id: str
    end_date_string: str
    id: str  # hr.leave record id
    reason: str
    response_by: str
    start_date_string: str
    status: int  # Wait=0,  Accept = 1,  Reject = 2, Canceled = 3,
    vacation_id: str  # time off type id
    vacation_name_ar: str
    vacation_name_en: str

    @staticmethod
    def from_dict(obj: Any) -> 'Vacation':
        assert isinstance(obj, dict)
        employee_id = from_str(obj.get("EmployeeID"))
        end_date_string = from_str(obj.get("EndDateString"))
        id = from_str(obj.get("ID"))
        reason = from_str(obj.get("Reason"))
        response_by = from_str(obj.get("ResponseBy"))
        start_date_string = from_str(obj.get("StartDateString"))
        status = from_int(obj.get("Status"))
        vacation_id = from_str(obj.get("VacationID"))
        vacation_name_ar = from_str(obj.get("VacationNameAr"))
        vacation_name_en = from_str(obj.get("VacationNameEn"))
        return Vacation(employee_id, end_date_string, id, reason, response_by, start_date_string, status, vacation_id,
                        vacation_name_ar, vacation_name_en)

    def to_dict(self) -> dict:
        result: dict = {}
        result["EmployeeID"] = from_str(self.employee_id)
        result["EndDateString"] = from_str(self.end_date_string)
        result["ID"] = from_str(self.id)
        result["Reason"] = from_str(self.reason)
        result["ResponseBy"] = from_str(self.response_by)
        result["StartDateString"] = from_str(self.start_date_string)
        result["Status"] = from_int(self.status)
        result["VacationID"] = from_str(self.vacation_id)
        result["VacationNameAr"] = from_str(self.vacation_name_ar)
        result["VacationNameEn"] = from_str(self.vacation_name_en)
        return result


@dataclass
class MobileAttendanceDTO:
    attendance: List[Attendance]  # timesheet
    permissions: List[Permission]
    vacations: List[Vacation]  # If Summary can be None. Otherwise, provide an appropriate default.
    summary: Summary = None
    response_code: str = ''
    response_message: str = ''
    device_info: str = ''

    @staticmethod
    def from_dict(obj: Any) -> 'MobileAttendanceDTO':
        assert isinstance(obj, dict)
        attendance = from_list(Attendance.from_dict, obj.get("Attendance"))
        device_info = from_str(obj.get("DeviceInfo"))
        permissions = from_list(Permission.from_dict, obj.get("Permissions"))
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        summary = Summary.from_dict(obj.get("Summary"))
        vacations = from_list(Vacation.from_dict, obj.get("Vacations"))
        return MobileAttendanceDTO(attendance, device_info, permissions, response_code, response_message, summary,
                                   vacations)

    def to_dict(self) -> dict:
        result: dict = {}
        result["Attendance"] = from_list(lambda x: to_class(Attendance, x), self.attendance)
        result["DeviceInfo"] = from_str(self.device_info)
        result["Permissions"] = from_list(lambda x: to_class(Permission, x), self.permissions)
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["Summary"] = to_class(Summary, self.summary)
        result["Vacations"] = from_list(lambda x: to_class(Vacation, x), self.vacations)
        return result


def mobile_attendance_dto_from_dict(s: Any) -> MobileAttendanceDTO:
    return MobileAttendanceDTO.from_dict(s)


def mobile_attendance_dto_to_dict(x: MobileAttendanceDTO) -> Any:
    return to_class(MobileAttendanceDTO, x)


@dataclass
class AttendanceParameterDTO:
    """used as a filter parameter when call  MGetEmployeeAttendance API (Post)"""
    response_code: str
    response_message: str
    app_id: str
    app_version: str
    device_info: str
    emp_no: str  # employee number
    hr_code: str  # enroll_no
    token: str
    username: str
    action: str  # "-1" =all[att,permissions,vacation] ,"0" =attendance,"1" =permission,"2"=vacations
    date_from: str  # DATE_FORMAT = "yyyyMMdd"; parameter
    date_to: str  # DATE_FORMAT = "yyyyMMdd";

    @staticmethod
    def from_dict(obj: Any) -> 'AttendanceParameterDTO':
        assert isinstance(obj, dict)
        response_code = from_str(obj.get("ResponseCode"))
        response_message = from_str(obj.get("ResponseMessage"))
        app_id = from_str(obj.get("AppId"))
        app_version = from_str(obj.get("AppVersion"))
        device_info = from_str(obj.get("DeviceInfo"))
        emp_no = from_str(obj.get("EmpNo"))
        hr_code = from_str(obj.get("HRCode"))
        token = from_str(obj.get("Token"))
        username = from_str(obj.get("Username"))
        action = from_str(obj.get("Action"))
        date_from = from_str(obj.get("DateFrom"))
        date_to = from_str(obj.get("DateTo"))
        return AttendanceParameterDTO(response_code, response_message, app_id, app_version, device_info, emp_no,
                                      hr_code,
                                      token, username, action, date_from, date_to)

    def to_dict(self) -> dict:
        result: dict = {}
        result["ResponseCode"] = from_str(self.response_code)
        result["ResponseMessage"] = from_str(self.response_message)
        result["AppId"] = from_str(self.app_id)
        result["AppVersion"] = from_str(self.app_version)
        result["DeviceInfo"] = from_str(self.device_info)
        result["EmpNo"] = from_str(self.emp_no)
        result["HRCode"] = from_str(self.hr_code)
        result["Token"] = from_str(self.token)
        result["Username"] = from_str(self.username)
        result["Action"] = from_str(self.action)
        result["DateFrom"] = from_str(self.date_from)
        result["DateTo"] = from_str(self.date_to)
        return result


def attendance_paramter_dto_from_dict(s: Any) -> AttendanceParameterDTO:
    return AttendanceParameterDTO.from_dict(s)


def attendance_paramter_dto_to_dict(x: AttendanceParameterDTO) -> Any:
    return to_class(AttendanceParameterDTO, x)
