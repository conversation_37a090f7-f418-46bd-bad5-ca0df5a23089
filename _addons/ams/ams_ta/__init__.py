#!/usr/bin/python
# from odoo.tools import date_utils
from . import models
from . import wizards
from . import controllers
# from odoo.http import JsonRequest, Response, request
# import json


# class JsonRequestPatch(JsonRequest):
#
#     def _json_response(self, result=None, error=None):
#         response = {
#             'jsonrpc': '2.0',
#             'id': self.jsonrequest.get('id')
#         }
#         if error is not None:
#             response['error'] = error
#         if result is not None:
#             response['result'] = result
#
#         mime = 'application/json'
#         if request.session.get('custom_json_response', '0') == '1':
#             body = json.dumps(result, default=date_utils.json_default)
#             request.session['custom_json_response'] = '0'
#         else:
#             body = json.dumps(response, default=date_utils.json_default)
#
#         return Response(
#             body, status=error and error.pop('http_status', 200) or 200,
#             headers=[('Content-Type', mime), ('Content-Length', len(body))]
#         )
#         # add your custom header to the response object
#         # response.headers['X-My-Custom-Header'] = 'Custom header value 112233'
#         #
#         # # return the modified response object
#         # return response
#         # def _json_response(self, result=None, error=None):
#         #     response = {
#         #         'jsonrpc': '2.0',
#         #         'id': self.jsonrequest.get('id')
#         #     }
#         #     default_code = 200
#         #     if error is not None:
#         #         response['error'] = error
#         #     if result is not None:
#         #         response['result'] = result
#         #         # you don't want to remove some key of another result by mistake
#         #         if isinstance(response, dict)
#         #             defautl_code = response.pop('very_very_rare_key_here', default_code)
#         #
#         #     mime = 'application/json'
#         #     body = json.dumps(response, default=date_utils.json_default)
#         #
#         #     return Response(
#         #         body, status=error and error.pop('http_status', defautl_code) or defautl_code,
#         #         headers=[('Content-Type', mime), ('Content-Length', len(body))]
#         #     )
#
#
# JsonRequest._json_response = JsonRequestPatch._json_response
