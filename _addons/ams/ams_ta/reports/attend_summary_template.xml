<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <template id="attend_summary_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
            <t t-set="o" t-value="o.with_context({'lang': 'ar_001'})"/>
                <t t-call="web.external_layout">
                     <style>
                        th {
                         border: 2px solid #922B21;
                         padding: 6px;
                         text-align:center;
                        }
                    </style>
                    <div style="direction:rtl; text-align:right; font-size:15px; font-weight:bold;">
                        <div class="row  ml-1 mr-1 mt-2 mb-2" style="direction:ltr; text-align:left; ">
                            <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M')" class=""/>
                        </div>
                        <div class="row ml-1 mr-1 mt-1 mb-2" style="border: 2px solid #2E86C1;">
                            <div class="col-6">
                                <span class=" pb-2 pt-2">تقرير الحضور والانصراف</span>
                            </div>
                            <div class="col-6" style="text-align:left;">
                                <span class=" pb-2 pt-2">Attendance Summary Report</span>
                            </div>
                        </div>

                        <div class="row  ml-1 mr-1 mt-1 mb-2">
                            <div class="col-6">
                                <span>من تاريخ : </span>
                                <span t-esc="o.from_date"/>
                            </div>
                            <div class="col-6">
                                <span>إلى تاريخ : </span>
                                <span t-esc="o.to_date"/>
                            </div>
                        </div>
                    </div>
                    <t t-foreach="o.department_ids or o.allowed_department_ids" t-as="department">

                        <div class="page p-3 mb-2 text-wrap" style="direction:rtl; text-align:right; font-size:12px; page-break-inside: avoid;">
                                <div style="font-size:15px; font-weight:bold;">
                                    <div class="row pb-3">
                                        <div class="col-6">
                                            <span>الإدارة : </span>
                                            <span t-esc="department.name"/>
                                        </div>
                                    </div>
                                </div>

                                <table class="w-100">
<!--                                        style="margin-left:auto; margin-right:auto; ">-->
                                    <tr>
                                        <th>الاسم</th>
                                        <th>عدد أيام الدوام الكلي</th>
                                        <th>عدد أيام الدوام الفعلي</th>
                                        <th>إجمالي ساعات العمل</th>
                                        <th>إجمالي التأخير</th>
                                        <th>إجمالي التقصير</th>
                                        <th>إجمالي التأخير والتقصير</th>
                                        <th>إجمالي الاستئذان </th>
                                        <th>إجمالي أيام الغياب </th>
                                        <th>إجمالي أيام الإجازات </th>
                                    </tr>

                                    <t t-set="all_timesheets" t-value="o.timesheet_ids.filtered(lambda t: department.path_code in t.department_id.path_code )"/>
                                    <t t-if="all_timesheets">
                                        <t t-foreach="all_timesheets.mapped('employee_id')" t-as="employee">
                                            <t t-set="timesheets" t-value="all_timesheets.filtered(lambda t: t.employee_id == employee) or []"/>
                                            <tr>
                                                <th><span t-field="employee.name"/></th>
                                                <th><span t-esc="len(timesheets)"/></th>
                                                <th><span t-esc="len(timesheets.filtered(lambda t: t.is_absent == False))"/></th>
                                                <th><span t-esc="o.convert_to_time_format(sum(timesheets.mapped('working_time')))"/></th>
                                                <th><span t-esc="o.convert_to_time_format(sum(timesheets.mapped('delay_time')))"/></th>
                                                <th><span t-esc="o.convert_to_time_format(sum(timesheets.mapped('shortage_time')))"/></th>
                                                <th><span t-esc="o.convert_to_time_format(sum(timesheets.mapped('total_delay_shortage')))"/></th>
                                                <th><span t-esc="o.convert_to_time_format(sum(timesheets.mapped('time_off_hours')))"/></th>
                                                <th><span t-esc="len(timesheets.filtered(lambda t: t.is_absent == True))"/></th>
                                                <th><span t-esc="len(timesheets.filtered(lambda t: t.is_dayoff == True))"/></th>
                                            </tr>
                                        </t>
                                    <tr>
                                        <th>المجموع الكلي للإدارة</th>
                                        <th><span t-esc="len(all_timesheets)"/></th>
                                        <th><span t-esc="len(all_timesheets.filtered(lambda t: t.is_absent == False))"/></th>
                                        <th><span t-esc="o.convert_to_time_format(sum(all_timesheets.mapped('working_time')))"/></th>
                                        <th><span t-esc="o.convert_to_time_format(sum(all_timesheets.mapped('delay_time')))"/></th>
                                        <th><span t-esc="o.convert_to_time_format(sum(all_timesheets.mapped('shortage_time')))"/></th>
                                        <th><span t-esc="o.convert_to_time_format(sum(all_timesheets.mapped('total_delay_shortage')))"/></th>
                                        <th><span t-esc="o.convert_to_time_format(sum(all_timesheets.mapped('time_off_hours')))"/></th>
                                        <th><span t-esc="len(all_timesheets.filtered(lambda t: t.is_absent == True))"/></th>
                                        <th><span t-esc="len(all_timesheets.filtered(lambda t: t.is_dayoff == True))"/></th>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </t>
                </t>
            </t>
        </t>
    </template>
</odoo>