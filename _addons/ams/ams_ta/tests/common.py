# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from datetime import datetime, timedelta, date, time
from dateutil.relativedelta import relativedelta

from odoo.fields import Date
from odoo.tests.common import TransactionCase


class TATestBase(TransactionCase):

    @classmethod
    def setUpClass(cls):
        super(TATestBase, cls).setUpClass()

        cls.env.user.tz = 'Asia/Riyadh'
        cls.tz_hr_offset = 3
        cls.env.ref('resource.resource_calendar_std').tz = 'Asia/Riyadh'

        cls.dep_test = cls.env['hr.department'].create({
            'name': 'Testing & Development - Test',
        })
        # region shift -----------------------------------
        # region rules----------
        # endregion-------------

        # region rules----------
        cls.shift_rule = cls.env['ta.shift_rule'].create({
            'en_name': 'TST Rule',
            'ar_name': 'TST Rule',
            'code': 'TST1000',
            'activate': True,
            'active': True,
            'grace_in_time': 10,
            'grace_out_time': 10
        })
        cls.shift_rule_half = cls.env['ta.shift_rule'].create({
            'en_name': 'TST Rule (No checkout)',
            'ar_name': 'TST Rule (No checkout)',
            'code': 'TST1001',
            'activate': True,
            'active': True,
            'grace_in_time': 10,
            'grace_out_time': 10,
            'calc_half_no_checkout_time': True
        })
        # endregion-------------

        # region unit----------
        cls.shift_unit_8_16 = cls.env['ta.shift_unit'].create({
            'en_name': 'TST_8_16',
            'ar_name': 'TST_8_16',
            'code': 'TST1000',
            'activate': True,
            'active': True,
            'start_time': 8,
            'end_time': 16,
            'duration': 8,
            'rule_id': cls.shift_rule.id,
            'color': '#117950'  # green:#117950 ,orange: #f25918 orange,red: #d51961
        })

        # overnight
        cls.shift_unit_23_7 = cls.env['ta.shift_unit'].create({
            'en_name': 'TST_23_7',
            'ar_name': 'TST_23_7',
            'code': 'TST10002',
            'activate': True,
            'active': True,
            'start_time': 23,
            'end_time': 7,
            'is_overnight': True,
            'duration': 8,
            'rule_id': cls.shift_rule.id,
            'color': '#000000'  # green:#117950 ,orange: #f25918 orange,red: #d51961
        })

        # endregion-------------

        # region schedule----------
        cls.skd_8_16 = cls.env['ta.shift_schedule'].create({
            'ar_name': 'TST_skd_8_16',
            'en_name': 'TST_skd_8_16',
            'code': 'TST1000',
            'activate': True,
            'active': True,
            'schedule_type': 'weekly',
            'default_shift_unit': cls.shift_unit_8_16.id
        })
        cls.skd_8_16._generate_days()

        # overnight
        cls.skd_23_7 = cls.env['ta.shift_schedule'].create({
            'ar_name': 'TST_skd_23_7',
            'en_name': 'TST_skd_23_7',
            'code': 'TST10001',
            'activate': True,
            'active': True,
            'schedule_type': 'weekly',
            'default_shift_unit': cls.shift_unit_23_7.id
        })
        cls.skd_23_7._generate_days()

        cls.shift_8_16 = cls.env['ta.shift'].create({
            'name': 'TST_shift_8_16',
            'start_date': '2023-01-01',
            'end_date': (datetime.now() + timedelta(days=365)).date(),
            'schedule_id': cls.skd_8_16.id
        })

        # overnight
        cls.shift_23_7 = cls.env['ta.shift'].create({
            'name': 'TST_shift_23_7',
            'start_date': '2023-01-01',
            'end_date': (datetime.now() + timedelta(days=365)).date(),
            'schedule_id': cls.skd_23_7.id
        })

        # endregion-------------
        cls.policy_8_16 = cls.env['ta.policy_group'].create({
            'en_name': 'TST_policy_8_16',
            'ar_name': 'TST_policy_8_16',
            'code': 'TST1000',
            'primary_shift_id': cls.shift_8_16.id,

        })

        # overnight
        cls.policy_23_7 = cls.env['ta.policy_group'].create({
            'en_name': 'TST_policy_23_7',
            'ar_name': 'TST_policy_23_7',
            'code': 'TST1001',
            'primary_shift_id': cls.shift_23_7.id,

        })
        # endregion

        # I create a new employee "Richard"
        cls.emp_8_16 = cls.env['hr.employee'].create({
            'name': 'TST EMP 8-16',
            'gender': 'male',
            'birthday': '1984-05-01',
            'country_id': cls.env.ref('base.sa').id,
            'department_id': cls.dep_test.id,
            'policy_group_id': cls.policy_8_16.id
        })

        cls.emp_23_7 = cls.env['hr.employee'].create({
            'name': 'TST EMP 23-7',
            'gender': 'male',
            'birthday': '1984-05-01',
            'country_id': cls.env.ref('base.sa').id,
            'department_id': cls.dep_test.id,
            'policy_group_id': cls.policy_23_7.id
        })

        cls.test_emp = cls.emp_8_16

    # region Helper----------------------------------
    def to_datetime_array(self, time_arr_str, day_index=0):
        start_date = self.shift_8_16.start_date
        # return [to_datetime(item, index) for index, item in enumerate(time_arr_str)]
        return list(map(lambda x: to_datetime(x, start_date, day_index), time_arr_str))

    def flatten_datetime_logs(self, logs, offset=0, dt_format='%A %m/%d/%Y %H:%M'):
        # Flatten the array of arrays into one array
        flat_logs = [dt + timedelta(hours=offset)
                     for sublist in logs for dt in sublist]

        # Map datetime objects to strings
        date_str = list(map(lambda dt: dt.strftime(dt_format), flat_logs))
        return date_str
    # endregion

    #
    # def create_work_entry(self, start, stop, work_entry_type=None):
    #     work_entry_type = work_entry_type or self.work_entry_type
    #     return self.env['hr.work.entry'].create({
    #         'contract_id': self.richard_emp.contract_ids[0].id,
    #         'en_name': "Work entry %s-%s" % (start, stop),
    #         'date_start': start,
    #         'date_stop': stop,
    #         'employee_id': self.richard_emp.id,
    #         'work_entry_type_id': work_entry_type.id,
    #     })


def time_to_float(time_str):
    """convert time string from format '%H:%M' to float """
    time_obj = datetime.datetime.strptime(time_str, "%H:%M")  # parse the time string to a datetime object
    time_float = time_obj.hour + time_obj.minute / 60.0  # calculate the floating-point representation of the time in hours
    return time_float


def to_datetime(time_str, start_date=datetime.today(), day_index=0, offset=3):
    # dummy_date = datetime.today()  # create a dummy date object
    time_obj = datetime.combine(start_date,
                                time(*map(int, time_str.split(":"))))
    # combine the dummy date with the time object
    time_obj = time_obj + timedelta(days=day_index, hours=-offset)
    return time_obj


def to_datetime_array(time_arr_str, day_index=0):
    # return [to_datetime(item, index) for index, item in enumerate(time_arr_str)]
    start_date = datetime.today()
    return list(map(lambda x: to_datetime(x, start_date, day_index), time_arr_str))


def min_to_float(time_str):
    """ function takes a string argument time_str representing a duration in the format "HH:MM".
     The function returns the equivalent duration as a floating-point number in decimal hours."""
    # Convert 00:11 to minutes
    time_val = time(*map(int, time_str.split(":")))
    minutes = time_val.hour * 60 + time_val.minute

    # Convert minutes to float hour
    float_hour = minutes / 60
    return round(float_hour, 2)


def date_diff(dt1, dt2):
    hours_diff = (dt2 - dt1).total_seconds() / 3600
    return round(hours_diff, 2)
