# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ta
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-21 08:13+0000\n"
"PO-Revision-Date: 2024-01-22 13:17+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"X-Generator: Poedit 3.4.2\n"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "<span class=\" pb-2 pt-2\">Attendance Summary Report</span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "<span class=\" pb-2 pt-2\">تقرير الحضور والانصراف</span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.timesheet_settings_view_form
msgid "<span class=\"o_form_label pr-3\">Last update timesheet date </span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_kanban
msgid "<span class=\"text-muted\">Policy group</span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_kanban
msgid "<span class=\"text-muted\">Schedule</span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_kanban
msgid "<span class=\"text-muted\">Shift</span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "<span>إلى تاريخ : </span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "<span>اسم الموظف : </span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "<span>الإدارة : </span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "<span>رقم الموظف : </span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "<span>من تاريخ : </span>"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.manual_attendance_wizard_view_form
msgid "Absent"
msgstr "غياب"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__absent_alert_sent
msgid "Absent Alert Sent"
msgstr "تم ارسال تنبية الغياب"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__absent_count
msgid "Absent Count"
msgstr "عدد الغياب"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__absent_time_criteria
msgid "Absent Time Criteria"
msgstr "شروط احتساب الغياب"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__absent_time_criteria
msgid "Absent time criteria"
msgstr "شروط احتساب الغياب"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_needaction
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activate
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activate
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activate
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activate
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
msgid "Activate"
msgstr "تفعيل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activate_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activate_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activate_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activate_date
msgid "Activate date"
msgstr "تاريخ التفعيل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activate_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activate_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activate_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activate_uid
msgid "Activate uid"
msgstr "تم التفعيل بواسطة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_search
msgid "Activated"
msgstr "نشط"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__active
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__active
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__active
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__active
msgid "Active"
msgstr "نشط"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_ids
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_exception_decoration
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_state
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_state
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_type_icon
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.manual_attendance_wizard_view_form
msgid "Add Attendance"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Alerts"
msgstr "تنبيات"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__is_dayoff
msgid "All types like vacation, public vacation and weekend"
msgstr ""
"كل الأنواع مثل الاجازة الشخصية و الاجازات الرسمية وعطلات نهاية الاسبوع"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__allowed_department_ids
msgid "Allowed Department"
msgstr "الإدارات المسموحة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__allowed_employee_ids
msgid "Allowed Employee"
msgstr "الموظفين المسموحين"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Allowed Time"
msgstr "الوقت المسموح"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_rule__grace_in_time
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__grace_in_time
msgid "Allowed minutes to delay in start of day"
msgstr "الوقت المسموح للتأخير في بداية اليوم"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_rule__grace_out_time
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__grace_out_time
msgid "Allowed minutes to leave early in end of day"
msgstr "الوقت المسموح للمغادرة مبكرا في نهاية اليوم"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_unit__apply_min_max
msgid "Apply Min Checkin Time & Max Checkout Time"
msgstr "تطبيق الحد الاقصي والحد الادني لوقت الحضور والانصراف"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__apply_min_max
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__apply_min_max
msgid "Apply Min Max"
msgstr "تطبيق وقت للحد الاقصي والحد الادني"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_policy_group__ar_name
#: model:ir.model.fields,help:ta.field_ta_shift_rule__ar_name
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__ar_name
#: model:ir.model.fields,help:ta.field_ta_shift_unit__ar_name
msgid "Arabic Name"
msgstr "الاسم العربي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__ar_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__ar_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__ar_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__ar_name
msgid "Arabic name"
msgstr "الاسم العربي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_attachment_count
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__attend_day_count
msgid "Attend Day Count"
msgstr "عدد أيام الحضور"

#. module: ta
#: model:ir.actions.report,name:ta.attend_details_report_action
msgid "Attendance Details Report"
msgstr "التقرير التفصيلي للحضور والانصراف"

#. module: ta
#: model:ir.actions.report,name:ta.attend_summary_report_action
msgid "Attendance Summary Report"
msgstr "تقرير ملخص الحضور والانصراف"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
msgid "Audit"
msgstr "تتبع الإجراءات"

#. module: ta
#: model:ir.model,name:ta.model_ams_base_device_log
msgid "Base Device Log"
msgstr "أساس سجل الاجهزة"

#. module: ta
#: model:ir.model,name:ta.model_ams_base_timesheet
msgid "Base Time Sheet"
msgstr "اساس سجل الدوام "

#. module: ta
#: model:ir.model,name:ta.model_ams_base_transaction
msgid "Base Timesheet Transactions"
msgstr "اساس حركات سجل الدوام"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_rule__half_work_checkout_criteria
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__half_work_checkout_criteria
msgid "Calculate half of working  if employee leave before time ..."
msgstr "احتسب نصف الوقت في حالة الحضور قبل"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_rule__half_work_checkin_criteria
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__half_work_checkin_criteria
msgid "Calculate half of working day if employee checkin after time"
msgstr "احتسب نصف الوقت في حالة الحضور بعد"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__checked_in
msgid "Checked In"
msgstr "دخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__checked_out
msgid "Checked Out"
msgstr "خروج"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__date
#: model:ir.model.fields,help:ta.field_ta_timesheet__date
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__date
msgid "Checkin Date"
msgstr "تاريخ الدخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__checkin_datetime
msgid "Checkin Datetime"
msgstr "تاريخ ووقت الدخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__checkin_time
msgid "Checkin Time"
msgstr "وقت الدخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__checkout_datetime
msgid "Checkout Datetime"
msgstr "تاريخ ووقت الخروج"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__checkout_time
msgid "Checkout Time"
msgstr "وقت الخروج"

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_hr_department
msgid "Click the Create button to add a new Department"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_hr_all_employee
msgid "Click the Create button to add a new Employee"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_policy_group
msgid "Click the Create button to add a new Policy group"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_all_punch_log
msgid "Click the Create button to add a new Punch log"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_schedule
msgid "Click the Create button to add a new Schedule"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_schedule_day
msgid "Click the Create button to add a new Schedule day"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_exception
msgid "Click the Create button to add a new Shift"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_rule
msgid "Click the Create button to add a new Shift rule"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_unit
msgid "Click the Create button to add a new Shift unit"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_all_timesheet
#: model_terms:ir.actions.act_window,help:ta.action_ta_timesheet_test
msgid "Click the Create button to add a new Timesheet"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_timesheet_shift_unit
msgid "Click the Create button to add a new Timesheet shift unit"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_hr_department
msgid "Click to add a new Department"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_hr_all_employee
msgid "Click to add a new Employee"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_policy_group
msgid "Click to add a new Policy group"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_all_punch_log
msgid "Click to add a new Punch log"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_schedule
msgid "Click to add a new Schedule"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_schedule_day
msgid "Click to add a new Schedule day"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_exception
msgid "Click to add a new Shift"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_unit
msgid "Click to add a new Shift Unit"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_shift_rule
msgid "Click to add a new Shift rule"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_all_timesheet
#: model_terms:ir.actions.act_window,help:ta.action_ta_timesheet_test
msgid "Click to add a new Timesheet"
msgstr ""

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_ta_timesheet_shift_unit
msgid "Click to add a new Timesheet shift unit"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__code
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__code
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__code
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__code
#: model:ir.model.fields,help:ta.field_ta_policy_group__code
#: model:ir.model.fields,help:ta.field_ta_shift_rule__code
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__code
#: model:ir.model.fields,help:ta.field_ta_shift_unit__code
msgid "Code"
msgstr "الكود"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__color
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__color
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__color
#: model:ir.model.fields,field_description:ta.field_ta_shift__color
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__color
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__color
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__color
msgid "Color"
msgstr "اللون"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__company_id
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__company_id
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__company_id
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__company_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__company_id
#: model:ir.model.fields,field_description:ta.field_ta_shift__company_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__company_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__company_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__company_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__company_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__company_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__company_id
msgid "Company"
msgstr "الشركة"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__is_delayed
#: model:ir.model.fields,help:ta.field_ta_timesheet__is_delayed
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__is_delayed
msgid "Compute field if employee delayed"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__is_shortage
#: model:ir.model.fields,help:ta.field_ta_timesheet__is_shortage
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__is_shortage
msgid "Compute field if employee leaved early "
msgstr ""

#. module: ta
#: model:ir.ui.menu,name:ta.ta_config_menu
msgid "Configurations"
msgstr "الاعدادات"

#. module: ta
#: model:ir.ui.menu,name:ta.menu_ta_timesheet_generator
msgid "Create Timesheet"
msgstr "انشاء سجل دوام"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_list
msgid "Create Today's Timesheet"
msgstr "انشاء سجل دوام لليوم"

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_manual_attendance_wizard
#: model_terms:ir.actions.act_window,help:ta.action_report_generator
msgid "Create a model"
msgstr "انشئ جديد"

#. module: ta
#: model_terms:ir.actions.act_window,help:ta.action_manual_attendance_wizard
#: model_terms:ir.actions.act_window,help:ta.action_report_generator
msgid "Create model"
msgstr "انشئ جديد"

#. module: ta
#: model:ir.actions.server,name:ta.dayoff_auto_activation_action_ir_actions_server
msgid "Create time sheets for day-off and absent"
msgstr "انشاء سجل دوام للغياب والعطلات"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__create_uid
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__create_uid
msgid "Created by"
msgstr "أٌنشيء بواسطة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__create_date
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__create_date
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__create_date
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__create_date
#: model:ir.model.fields,field_description:ta.field_ta_shift__create_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__create_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__create_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__create_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__create_date
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__create_date
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__create_date
msgid "Created on"
msgstr "تاريخ الانشاء"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__current_department_id
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__current_department_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__current_department_id
#: model:ir.model.fields,field_description:ta.field_ta_shift__current_department_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__current_department_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__current_department_id
msgid "Current Department"
msgstr "الإدارة الحالية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Current Month"
msgstr "الشهر الحالي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__shift_unit_current_ids
msgid "Current Shift Units"
msgstr "وحدات الدوام الحالية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Current Timesheet"
msgstr "سجل الدوام الحالي"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__current_timesheet_id
msgid "Current active  timesheet"
msgstr "سجل الدوام النشط"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__current_timesheet_id
msgid "Current timesheet"
msgstr "سجل الدام الحالي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__cycle_days
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__cycle_days
msgid "Cycle Days"
msgstr "أيام التكرار"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule__schedule_type__daily
#: model:ir.model.fields.selection,name:ta.selection__ta_timesheet__schedule_type__daily
msgid "Daily"
msgstr "يومي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__date
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__date
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__date
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Date"
msgstr "تاريخ"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__week_day
msgid "Day"
msgstr "اليوم"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__day_index
msgid "Day Index"
msgstr "#"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_form
msgid "Day Off"
msgstr "عطلة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__dayoff_count
msgid "Dayoff Count"
msgstr "عدد العطلات"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__day_ids
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_form
msgid "Days"
msgstr "الايام"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
msgid "Deactivate"
msgstr "الغاء التفعيل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__deactivate_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__deactivate_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__deactivate_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__deactivate_date
msgid "Deactivate date"
msgstr "تاريخ الغاء التفعيل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__deactivate_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__deactivate_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__deactivate_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__deactivate_uid
msgid "Deactivate uid"
msgstr "تم الغاء التفعيل بواسطة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__default_shift_unit
msgid "Default Shift Unit"
msgstr "وحدة الدوام الافتراضية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__delay_alert_sent
msgid "Delay Alert Sent"
msgstr "ارسال تنبية التأخير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__delay_count
msgid "Delay Count"
msgstr "عدد التأخير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__delay_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__delay_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__delay_time
msgid "Delay Time"
msgstr "مدة التأخير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__delay_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__delay_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__delay_time_min
msgid "Delay Time (Min)"
msgstr "مدة التأخير (د)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__delay_time
#: model:ir.model.fields,help:ta.field_ta_timesheet__delay_time
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__delay_time
msgid "Delay Time by hours"
msgstr "مدة التأخير (س)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__delay_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet__delay_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__delay_time_min
msgid "Delay Time by minutes"
msgstr "مدة التأخير (د)"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Delayed"
msgstr "تأخير"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_hr_department
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__department_id
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__department_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__department_id
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__department_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift__department_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__department_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__department_id
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
#: model_terms:ir.ui.view,arch_db:ta.view_hr_department_form
#: model_terms:ir.ui.view,arch_db:ta.view_hr_department_tree
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_search
msgid "Department"
msgstr "القسم"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_department_form
msgid "Department Name"
msgstr "اسم الادارة"

#. module: ta
#: model:ir.ui.menu,name:ta.menu_hr_department
msgid "Departments"
msgstr "الاقسام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__dept_path_code
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__dept_path_code
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__dept_path_code
#: model:ir.model.fields,field_description:ta.field_ta_shift__dept_path_code
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__dept_path_code
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__dept_path_code
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Dept path code"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_transaction__enroll_number
#: model:ir.model.fields,help:ta.field_ta_manual_attendance_wizard__enroll_number
#: model:ir.model.fields,help:ta.field_ta_punch_log__enroll_number
#: model:ir.model.fields,help:ta.field_ta_shift__enroll_number
#: model:ir.model.fields,help:ta.field_ta_timesheet__enroll_number
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__enroll_number
msgid "Device Enroll Number"
msgstr "رقم تسجيل "

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__device_number
msgid "Device Number"
msgstr "رقم الجهاز"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Device number"
msgstr "رقم الجهاز"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__diff_working_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__diff_working_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__diff_working_time
msgid "Diff Working Time"
msgstr "فرق ساعات العمل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__diff_working_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__diff_working_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__diff_working_time_min
msgid "Diff Working Time (Min)"
msgstr "فرق ساعات العمل (د)"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__display_name
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__display_name
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__display_name
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__display_name
#: model:ir.model.fields,field_description:ta.field_ta_shift__display_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__display_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__display_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__display_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__display_name
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__display_name
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__duration
msgid "Duration"
msgstr "المدة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Edit Attendance"
msgstr "تعديل الحضور"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_hr_all_employee
#: model:ir.model,name:ta.model_hr_employee
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__employee_id
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__employee_id
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__employee_id
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__employee_ids
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__employee_id
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__employee_id
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__employee_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift__employee_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__employee_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__employee_id
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_graph
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_search
msgid "Employee"
msgstr "الموظف"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Employee Info"
msgstr "معلومات الموظف"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__employee_number
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__employee_number
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__employee_number
#: model:ir.model.fields,field_description:ta.field_ta_shift__employee_number
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__employee_number
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__employee_number
msgid "Employee No"
msgstr "الرقم الوظيفي"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_form
msgid "Employee Time-sheets"
msgstr "سجل دوام الموظف"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Employee number"
msgstr "الرقم الوظيفي"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__vacation_count
msgid "Employee official vacations"
msgstr "اجازات الموظف الرسمية"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__permission_count
msgid "Employee permission"
msgstr "اذونات الموظف"

#. module: ta
#: model:ir.actions.server,name:ta.action_hr_employee
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__employee_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift__employee_ids
#: model:ir.ui.menu,name:ta.menu_hr_employee
#: model_terms:ir.ui.view,arch_db:ta.view_hr_department_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_policy_group_form
msgid "Employees"
msgstr "الموظفين"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_shift_exception
msgid "Employees Shifts"
msgstr "ورديات الموظفين"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__en_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__en_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__en_name
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__en_name
msgid "En name"
msgstr "الاسم بالإنجليزية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__end_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__end_time
msgid "End Time"
msgstr "وقت الانتهاء"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift__end_date
msgid "End date"
msgstr "تاريخ الانتهاء"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_policy_group__en_name
#: model:ir.model.fields,help:ta.field_ta_shift_rule__en_name
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__en_name
#: model:ir.model.fields,help:ta.field_ta_shift_unit__en_name
msgid "English Name"
msgstr "الاسم بالانجليزية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__enroll_number
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__enroll_number
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__enroll_number
#: model:ir.model.fields,field_description:ta.field_ta_shift__enroll_number
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__enroll_number
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__enroll_number
msgid "Enroll No"
msgstr "رقم التسجيل "

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Enroll number"
msgstr "رقم التسجيل "

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift__exception_employee_ids
msgid "Exception Employee"
msgstr "الموظف"

#. module: ta
#: model:ir.ui.menu,name:ta.menu_ta_shift_exception
msgid "Exceptions Shifts"
msgstr "الورديات الخاصة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_form
msgid "Execute"
msgstr "تنفيذ"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_tree
msgid "Execute Log"
msgstr "تنفيذ السجل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__execute_log_time
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Execute log time"
msgstr "وقت تنفيذ السجل"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_punch_log__state__executed
#: model:ir.model.fields.selection,name:ta.selection__ta_shift__status__executed
msgid "Executed"
msgstr "تم التنفيذ"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_form
msgid "Execution"
msgstr "تنفيذ"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_rule__overtime_factor
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__overtime_factor
msgid "Factor"
msgstr "عامل"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift__state__finished
#: model:ir.model.fields.selection,name:ta.selection__ta_shift__state_fixed__finished
msgid "Finished"
msgstr "تم الانتهاء"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__first_checkin_datetime
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__first_checkin_datetime
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__first_checkin_datetime
msgid "First Checkin Datetime"
msgstr "اول تاريخ حضور"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__first_checkin_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__first_checkin_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__first_checkin_time
msgid "First Checkin Time"
msgstr "اول وقت حضور"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__first_checkin_time_char
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__first_checkin_time_char
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__first_checkin_time_char
msgid "First Checkin Time Char"
msgstr ""

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_unit__shift_type__flexible
#: model:ir.model.fields.selection,name:ta.selection__ta_timesheet_shift_unit__shift_type__flexible
msgid "Flexible"
msgstr "مرن"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_follower_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_partner_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__activity_type_icon
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_policy_group__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_punch_log__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_shift__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_shift_rule__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_shift_unit__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_timesheet__activity_type_icon
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة من Font awesome مثال: fa-tasks "

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule_day__week_day__7
msgid "Friday"
msgstr "الجمعة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__from_date
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__from_date
msgid "From Date"
msgstr "من تاريخ"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__from_mobile
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__from_mobile
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__from_mobile
msgid "From Mobile"
msgstr "من الجوال"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__from_mobile
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "From mobile"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_form
msgid "Generate"
msgstr "انشئ"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_form
msgid "Get Shift Day"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_list
msgid "Get Timesheets Test"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_list
msgid "Get punch log Test"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Grace Time"
msgstr "المدة المرنة للدخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__grace_in_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__grace_in_time
msgid "Grace in time"
msgstr "المدة المرنة للدخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__grace_out_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__grace_out_time
msgid "Grace out time"
msgstr "المدة المرنة  للخروج"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_search
msgid "Group By"
msgstr "تجميع ب"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_policy_group_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_search
msgid "Group By.."
msgstr "تجميع ب"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Half Working Day :"
msgstr "حساب نصف ساعات العمل"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_rule__calc_half_no_checkout_time
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__calc_half_no_checkout_time
msgid "Half day Calculate half of working day if no checkout "
msgstr "احتسب نصف دوام في حالة وجود خروج"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__half_work_checkin_criteria
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__half_work_checkin_criteria
msgid "Half day If Checkin After"
msgstr "احتسب نصف دوام في دخول بعد"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__half_work_checkout_criteria
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__half_work_checkout_criteria
msgid "Half day If Checkout Before"
msgstr "احتسب نصف دوام في حالة خروج قبل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__has_message
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__has_message
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__has_message
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__has_message
#: model:ir.model.fields,field_description:ta.field_ta_shift__has_message
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__has_message
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__has_message
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__has_message
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__has_message
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__has_message
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_unit__duration
msgid "Hours"
msgstr "ساعات"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__id
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__id
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__id
#: model:ir.model.fields,field_description:ta.field_ta_shift__id
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__id
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__id
msgid "ID"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_exception_icon
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_policy_group__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_punch_log__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_shift__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_shift_rule__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_shift_unit__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_timesheet__activity_exception_icon
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى النشاط المستثنى. "

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "If Checkin After"
msgstr "في حالة الدخول بعد"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "If Checkout Before"
msgstr "في حالة الخروج قبل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__calc_half_no_checkout_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__calc_half_no_checkout_time
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "If No Checkout"
msgstr "في حالة عدم وجود خروج"

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__message_needaction
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__message_needaction
#: model:ir.model.fields,help:ta.field_ta_policy_group__message_needaction
#: model:ir.model.fields,help:ta.field_ta_punch_log__message_needaction
#: model:ir.model.fields,help:ta.field_ta_shift__message_needaction
#: model:ir.model.fields,help:ta.field_ta_shift_rule__message_needaction
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__message_needaction
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__message_needaction
#: model:ir.model.fields,help:ta.field_ta_shift_unit__message_needaction
#: model:ir.model.fields,help:ta.field_ta_timesheet__message_needaction
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__message_has_error
#: model:ir.model.fields,help:ta.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__message_has_error
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_policy_group__message_has_error
#: model:ir.model.fields,help:ta.field_ta_policy_group__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_punch_log__message_has_error
#: model:ir.model.fields,help:ta.field_ta_punch_log__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_shift__message_has_error
#: model:ir.model.fields,help:ta.field_ta_shift__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_shift_rule__message_has_error
#: model:ir.model.fields,help:ta.field_ta_shift_rule__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__message_has_error
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__message_has_error
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_shift_unit__message_has_error
#: model:ir.model.fields,help:ta.field_ta_shift_unit__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_timesheet__message_has_error
#: model:ir.model.fields,help:ta.field_ta_timesheet__message_has_sms_error
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__message_has_error
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__in_latitude
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__in_latitude
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__in_latitude
msgid "In Latitude"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__in_longitude
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__in_longitude
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__in_longitude
msgid "In Longitude"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__total_vacation_count
msgid "Include public vacations"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__index
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_search
msgid "Index"
msgstr "#"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_absent
msgid "Is Absent"
msgstr "غياب"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_attend_day
msgid "Is Attend Day"
msgstr "حضور"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__is_day_off
msgid "Is Day Off"
msgstr "عطلة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_dayoff
msgid "Is Dayoff"
msgstr "عطلة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__is_delayed
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_delayed
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__is_delayed
msgid "Is Delayed"
msgstr "تأخير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift__is_exception
msgid "Is Exception"
msgstr "مستثني"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_is_follower
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__is_overnight
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__is_overnight
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_overnight
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__is_overnight
msgid "Is Overnight"
msgstr "وردية ليلية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_public_vacation
msgid "Is Public Vacation"
msgstr "إجازة رسمية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__is_shortage
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_shortage
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__is_shortage
msgid "Is Shortage"
msgstr "تقصير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_vacation
msgid "Is Vacation"
msgstr "إجازة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_weekend
msgid "Is Weekend"
msgstr "عطلة نهاية الاسبوع"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Is absent"
msgstr "غياب"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Is attend day"
msgstr "حضور"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_search
msgid "Is day off"
msgstr "عطلة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Is dayoff"
msgstr "عطلة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift__is_default
msgid "Is default"
msgstr "افتراضي"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_search
msgid "Is overnight"
msgstr "وردية ليلية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Is vacation"
msgstr "إجازة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__is_working_day
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Is working day"
msgstr "يوم عمل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__last_checkout_datetime
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__last_checkout_datetime
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__last_checkout_datetime
msgid "Last Checkout Datetime"
msgstr "تاريخ ووقت آخر خروج"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__last_checkout_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__last_checkout_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__last_checkout_time
msgid "Last Checkout Time"
msgstr "وقت آخر خروج"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__last_checkout_time_char
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__last_checkout_time_char
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__last_checkout_time_char
msgid "Last Checkout Time Char"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_res_config_settings__last_timesheet_date
msgid "Last Timesheet Date"
msgstr "تاريخ آخر سجل دوام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__write_uid
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__write_uid
msgid "Last Updated by"
msgstr "آخر تعديل بواسطة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__write_date
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__write_date
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__write_date
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__write_date
#: model:ir.model.fields,field_description:ta.field_ta_shift__write_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__write_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__write_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__write_date
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__write_date
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__write_date
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__write_date
msgid "Last Updated on"
msgstr "وقت اخر تعديل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__latitude
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Latitude"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Log time"
msgstr "وقت السجل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__user_name
msgid "Login"
msgstr "دخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__longitude
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Longitude"
msgstr ""

#. module: ta
#: model:res.groups,name:ta.group_time_attendance_manager
msgid "Manager"
msgstr "مدير"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_manual_attendance_wizard
#: model:ir.model,name:ta.model_ta_manual_attendance_wizard
msgid "Manual Attendance"
msgstr "حضور يدوي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__manual_edit
msgid "Manual Edit"
msgstr "تعديل يدوي"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Manual edit"
msgstr "تعديل يدوي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__max_checkout_time
msgid "Max Checkout Time"
msgstr "اقصي وقت للخروج"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__start_limit
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__start_limit
msgid "Max Start"
msgstr "اقصي وقت للدخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__max_checkout_time
msgid "Max checkout time"
msgstr "اقصي وقت للخروج"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_unit__max_checkout_time
msgid "Maximum time allowed to checkout"
msgstr "اقصي وقت مسموح للخروج"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_has_error
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__min_checkin_time
msgid "Min Checkin Time"
msgstr "ادني وقت للدخول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__min_checkin_time
msgid "Min checkin time"
msgstr "ادني وقت للدخول"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_unit__min_checkin_time
msgid "Minimum time allowed to checkin "
msgstr "ادني وقت مسموح للدخول"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_form
msgid "Mix Types"
msgstr "ورديات مختلفة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_form
msgid "Mobile Attendance Fields"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Mobile Info"
msgstr ""

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule_day__week_day__3
msgid "Monday"
msgstr "الاثنين"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__my_activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "الموعد النهائي لنشاطاتي "

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "My Timesheet"
msgstr "سجل الدوام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__name
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__name
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__name
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__name
#: model:ir.model.fields,field_description:ta.field_ta_shift__name
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__name
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__name
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__name
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__name
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__name
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__name
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_search
msgid "Name"
msgstr "الاسم"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_calendar_event_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_date_deadline
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_summary
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_summary
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_type_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_unit__shift_type__normal
#: model:ir.model.fields.selection,name:ta.selection__ta_timesheet_shift_unit__shift_type__normal
msgid "Normal"
msgstr "طبيعي"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_search
msgid "Not Activated"
msgstr "غير نشط"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__notes
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__notes
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__notes
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__notes
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Notes"
msgstr "ملاحظات"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_needaction_counter
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_has_error_counter
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_policy_group__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_punch_log__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_shift__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_shift_rule__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_shift_unit__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_timesheet__message_needaction_counter
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_policy_group__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_punch_log__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_shift__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_shift_rule__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_shift_unit__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_timesheet__message_has_error_counter
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_unit__shift_type__open
#: model:ir.model.fields.selection,name:ta.selection__ta_timesheet_shift_unit__shift_type__open
msgid "Open"
msgstr "مفتوح"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Other Info"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__out_latitude
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__out_latitude
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__out_latitude
msgid "Out Latitude"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__out_longitude
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__out_longitude
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__out_longitude
msgid "Out Longitude"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_form
msgid "Overlap"
msgstr "تداخل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__overtime
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__overtime
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__overtime
msgid "Overtime"
msgstr "وقت اضافي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__overtime_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__overtime_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__overtime_min
msgid "Overtime (Min)"
msgstr "وقت إضافي (د)"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__overtime_day_count
msgid "Overtime Day Count"
msgstr "عدد ايام الاضافي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__overtime_factored
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__overtime_factor
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__overtime_factored
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__overtime_factor
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__overtime_factored
msgid "Overtime Factor"
msgstr "الإضافي x"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__overtime_factored_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__overtime_factored_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__overtime_factored_min
msgid "Overtime Factor (Min)"
msgstr "الإضافي(د) x"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__overtime
#: model:ir.model.fields,help:ta.field_ta_timesheet__overtime
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__overtime
msgid "Overtime by hours"
msgstr "الإضافي (س)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__overtime_min
#: model:ir.model.fields,help:ta.field_ta_timesheet__overtime_min
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__overtime_min
msgid "Overtime by minutes"
msgstr "الإضافي(د)"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__parent_id
msgid "Parent"
msgstr "السجل الرئيسي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__parent_department_id
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__parent_department_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__parent_department_id
#: model:ir.model.fields,field_description:ta.field_ta_shift__parent_department_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__parent_department_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__parent_department_id
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_search
msgid "Parent Department"
msgstr "الإدارة الرئيسية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__parent_timesheet_id
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Parent Timesheet"
msgstr "السجل الرئيسي"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_department__path_code
#: model:ir.model.fields,field_description:ta.field_hr_employee__path_code
msgid "Path code"
msgstr ""

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_punch_log__state__pending
#: model:ir.model.fields.selection,name:ta.selection__ta_shift__state__pending
#: model:ir.model.fields.selection,name:ta.selection__ta_shift__state_fixed__pending
#: model:ir.model.fields.selection,name:ta.selection__ta_shift__status__pending
msgid "Pending"
msgstr "قيد الانتظار"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__permission_count
msgid "Permission Count"
msgstr "عدد الاستئذان"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__policy_group_id
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__policy_group_id
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__policy_group_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__policy_group_id
#: model:ir.model.fields,field_description:ta.field_ta_shift__policy_group_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__policy_group_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__policy_group_id
#: model:ir.ui.menu,name:ta.menu_ta_policy_group
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_search
msgid "Policy Group"
msgstr "مجموعة العمل"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_policy_group
msgid "Policy Groups"
msgstr "مجموعات العمل"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_policy_group_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_policy_group_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_policy_group_tree
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "Policy group"
msgstr "مجموعة العمل"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Previous Month"
msgstr "الشهر السابق"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__shift_id
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__primary_shift_id
#: model_terms:ir.ui.view,arch_db:ta.view_ta_policy_group_search
msgid "Primary shift"
msgstr "الوردية الاساسية"

#. module: ta
#: model:ir.model,name:ta.model_ta_punch_log
#: model:ir.ui.menu,name:ta.menu_ta_punch_log
msgid "Punch Log"
msgstr "سجل البصمة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__punch_logs
msgid "Punch Logs"
msgstr "سجل البصمات"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_all_punch_log
#: model:ir.actions.server,name:ta.action_ta_punch_log
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_cal
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_tree
msgid "Punch log"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_punch_log__log_time
msgid "Punch time manually or from device"
msgstr "وقت البصمة | يدوي | جهاز البصمة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__is_random
msgid "Random"
msgstr "عشوائي"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Re-Calculate Time"
msgstr "إعادة احتساب الوقت"

#. module: ta
#: model:res.groups,name:ta.group_read
msgid "Read"
msgstr "قرأءة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.manual_attendance_wizard_view_form
msgid "Recalculate"
msgstr "إعادة حساب "

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_form
msgid "Recalculate employee timesheet based on current shift"
msgstr "إعادة حساب الدوم حسب اخر وردية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Recompute Shift"
msgstr "إعادة حساب الوردية"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__shift_unit_current_ids
msgid "Related field from current timesheet"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__shift_unit_history_ids
msgid "Related field from parent timesheet"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_punch_log__employee_number
msgid "Related from employee.registeration_number"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_transaction__emp_user_id
#: model:ir.model.fields,help:ta.field_ta_manual_attendance_wizard__emp_user_id
#: model:ir.model.fields,help:ta.field_ta_punch_log__emp_user_id
#: model:ir.model.fields,help:ta.field_ta_shift__emp_user_id
#: model:ir.model.fields,help:ta.field_ta_timesheet__emp_user_id
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__emp_user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Remaining Time"
msgstr "الوقت المتبقي"

#. module: ta
#: model:ir.ui.menu,name:ta.menu_ta_report_generator
msgid "Report"
msgstr "التقرير"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_report_generator
#: model:ir.model,name:ta.model_ta_report_generator_wizard
msgid "Report Generator"
msgstr "إنشاء تقرير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__required_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__required_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__required_time
msgid "Required Time"
msgstr "الوقت المطلوب"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__required_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__required_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__required_time_min
msgid "Required Time (Min)"
msgstr "الوقت المطلوب (د)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__required_time
#: model:ir.model.fields,help:ta.field_ta_timesheet__required_time
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__required_time
msgid "Required Time by hours"
msgstr "الوقت المطلوب (س)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__required_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet__required_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__required_time_min
msgid "Required Time by minutes"
msgstr "الوقت المطلوب (د)"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_form
msgid "Reset"
msgstr "إعادة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_form
msgid "Reset to Draft"
msgstr "إعادة الي قيد الانتظار"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_shift__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__activity_user_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__rule_id
msgid "Rule"
msgstr "قاعدة الدوام"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift__state__running
#: model:ir.model.fields.selection,name:ta.selection__ta_shift__state_fixed__running
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_search
msgid "Running"
msgstr "جاري"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_shift__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_shift_rule__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__message_has_sms_error
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule_day__week_day__1
msgid "Saturday"
msgstr "السبت"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift__schedule_id
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__schedule_id
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_tree
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_search
msgid "Schedule"
msgstr "جدول الدوام"

#. module: ta
#: model:ir.ui.menu,name:ta.menu_ta_shift_schedule_day
msgid "Schedule Day"
msgstr "يوم الجدول"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule__schedule_type
msgid "Schedule Type"
msgstr "نوع الجدول"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_shift_schedule_day
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_graph
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_tree
msgid "Schedule day"
msgstr "يوم الجدول"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_form
msgid "Schedule day Name"
msgstr "اسم اليوم"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__schedule_type
msgid "Schedule type"
msgstr "نوع الجدول"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_shift_schedule
#: model:ir.ui.menu,name:ta.menu_ta_shift_schedule
msgid "Schedules"
msgstr "جداول الدوام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_policy_group__secondary_shift_id
#: model_terms:ir.ui.view,arch_db:ta.view_ta_policy_group_search
msgid "Secondary shift"
msgstr "الوردية الثانوية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_form
msgid "Setting"
msgstr "الاعدادات"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__shift_id
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_calendar
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_tree
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Shift"
msgstr "الوردية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Shift Histories"
msgstr "تاريخ الوردية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Shift Logs"
msgstr "سجلات الوردية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_form
msgid "Shift Name"
msgstr "اسم الوردية"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Shift Rule"
msgstr "قاعدة الدوام"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_shift_rule
#: model:ir.ui.menu,name:ta.menu_ta_shift_rule
msgid "Shift Rules"
msgstr "قواعد الدوام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__shift_type
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__shift_type
msgid "Shift Type"
msgstr "نوع الوردية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__shift_unit_id
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Shift Unit"
msgstr "وحدة الدوام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__shift_unit_history_ids
msgid "Shift Unit historys"
msgstr "تاريخ وحدات الدوام"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_shift_unit
#: model:ir.ui.menu,name:ta.menu_ta_shift_unit
msgid "Shift Units"
msgstr "وحدات الدوام"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_rule_tree
msgid "Shift rule"
msgstr "قاعدة الدوام"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_tree
msgid "Shift unit"
msgstr "وحدة الدوام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__shift_units_ids
msgid "Shift units"
msgstr "وحدات الدوام"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_shift
#: model:ir.ui.menu,name:ta.menu_ta_shift
msgid "Shifts"
msgstr "الورديات"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Shortage"
msgstr "تقصير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__shortage_alert_sent
msgid "Shortage Alert Sent"
msgstr "ارسال تنبيه التقصير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__shortage_count
msgid "Shortage Count"
msgstr "عدد التقصير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__shortage_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__shortage_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__shortage_time
msgid "Shortage Time"
msgstr "مدة التقصير"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__shortage_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__shortage_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__shortage_time_min
msgid "Shortage Time (Min)"
msgstr "مدة التقصير (د)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__shortage_time
#: model:ir.model.fields,help:ta.field_ta_timesheet__shortage_time
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__shortage_time
msgid "Shortage Time by hours"
msgstr "مدة التقصير (س)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__shortage_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet__shortage_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__shortage_time_min
msgid "Shortage Time by minutes"
msgstr "مدة التقصير (د)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_policy_group__active
#: model:ir.model.fields,help:ta.field_ta_shift_rule__active
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__active
#: model:ir.model.fields,help:ta.field_ta_shift_unit__active
msgid ""
"Special field used to hide record , when execute action  archive  and "
"active value = False"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_unit__start_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__start_time
msgid "Start Time"
msgstr "وقت البداية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift__start_date
msgid "Start date"
msgstr "تاريخ البداية"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__state
#: model:ir.model.fields,field_description:ta.field_ta_shift__state
#: model_terms:ir.ui.view,arch_db:ta.view_ta_punch_log_search
msgid "State"
msgstr "الحالة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift__state_fixed
msgid "State Fixed"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift__status
msgid "Status"
msgstr "حالة الاجراء"

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__activity_state
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__activity_state
#: model:ir.model.fields,help:ta.field_ta_policy_group__activity_state
#: model:ir.model.fields,help:ta.field_ta_punch_log__activity_state
#: model:ir.model.fields,help:ta.field_ta_shift__activity_state
#: model:ir.model.fields,help:ta.field_ta_shift_rule__activity_state
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__activity_state
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__activity_state
#: model:ir.model.fields,help:ta.field_ta_shift_unit__activity_state
#: model:ir.model.fields,help:ta.field_ta_timesheet__activity_state
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الأنشطة المعتمدة على الحالة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__overtime_factored
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__overtime_factored_min
#: model:ir.model.fields,help:ta.field_ta_timesheet__overtime_factored
#: model:ir.model.fields,help:ta.field_ta_timesheet__overtime_factored_min
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__overtime_factored
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__overtime_factored_min
msgid "Sum of units (Overtime * Factor)"
msgstr ""

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule_day__week_day__2
msgid "Sunday"
msgstr "الأحد"

#. module: ta
#: model:ir.model,name:ta.model_res_config_settings
msgid "System Info"
msgstr "تهيئة الإعدادات "

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_punch_log__dept_path_code
msgid ""
"Technical field used for employee  time folow-up based on department "
"hierarchy"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_department__path_code
#: model:ir.model.fields,help:ta.field_hr_employee__path_code
#: model:ir.model.fields,help:ta.field_ams_base_transaction__dept_path_code
#: model:ir.model.fields,help:ta.field_ta_manual_attendance_wizard__dept_path_code
#: model:ir.model.fields,help:ta.field_ta_shift__dept_path_code
#: model:ir.model.fields,help:ta.field_ta_timesheet__dept_path_code
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__dept_path_code
msgid ""
"Technical field used for employee time folow-up based on department "
"hierarchy include parent ids .current department id like 1.5.6"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_punch_log__longitude
msgid ""
"Technical field when creating  multiple record at same time , like  import "
"from excel sheet "
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__in_latitude
#: model:ir.model.fields,help:ta.field_ta_timesheet__in_latitude
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__in_latitude
msgid "Technical field,  Checkin latitude from mobile"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__out_latitude
#: model:ir.model.fields,help:ta.field_ta_timesheet__out_latitude
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__out_latitude
msgid "Technical field,  Checkout latitude from mobile"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__out_longitude
#: model:ir.model.fields,help:ta.field_ta_timesheet__out_longitude
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__out_longitude
msgid "Technical field,  Checkout longitude  from mobile"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__in_longitude
#: model:ir.model.fields,help:ta.field_ta_timesheet__in_longitude
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__in_longitude
msgid "Technical field, Checkin longitude  from mobile"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__first_checkin_datetime
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__last_checkout_datetime
#: model:ir.model.fields,help:ta.field_ta_timesheet__first_checkin_datetime
#: model:ir.model.fields,help:ta.field_ta_timesheet__last_checkout_datetime
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__first_checkin_datetime
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__last_checkout_datetime
msgid "Technical fields for time calculation"
msgstr ""

#. module: ta
#: model:ir.ui.menu,name:ta.menu_ta_timesheet_test
msgid "Test"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__test_date
msgid "Test Date"
msgstr ""

#. module: ta
#: model:ir.model.fields,field_description:ta.field_hr_employee__test_day_index
msgid "Test Day Index"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_form
msgid "Test Only"
msgstr ""

#. module: ta
#. odoo-python
#: code:addons/ta/models/shift.py:0
#, python-format
msgid ""
"Their is an overlapped shift  at this dates , previous shifts: \n"
" %s"
msgstr ""

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule_day__week_day__6
msgid "Thursday"
msgstr "الخميس"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__log_time
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_form
msgid "Time"
msgstr "الوقت"

#. module: ta
#: model:ir.module.category,name:ta.module_category_time_attendance
#: model:ir.ui.menu,name:ta.ta_top_menu
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_form
msgid "Time Attendance"
msgstr "الحضور والانصراف"

#. module: ta
#: model:ir.module.category,description:ta.module_category_time_attendance
msgid "Time Attendance Groups"
msgstr "مجموعات الحضور والانصراف"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
msgid "Time Detail"
msgstr "تفاصيل الوقت"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__time_off_hours
msgid "Time Off Hours"
msgstr "ساعات الاستذان"

#. module: ta
#: model:ir.model,name:ta.model_ta_timesheet
msgid "Time Sheet"
msgstr "سجل الدوام"

#. module: ta
#: model:ir.model,name:ta.model_ta_timesheet_shift_unit
msgid "Time Sheet Shift Unit"
msgstr "وحدة سجل الدوام"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Time Summary"
msgstr "ملخص الوقت"

#. module: ta
#: model:ir.ui.menu,name:ta.ta_shift_menu_top
msgid "TimeSheet"
msgstr "سجل الدوام"

#. module: ta
#: model:ir.ui.menu,name:ta.menu_ta_timesheet_shift_unit
msgid "TimeSheet Shift Unit"
msgstr "وحدة سجل الدوام"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_all_timesheet
#: model:ir.actions.act_window,name:ta.action_ta_timesheet_test
#: model:ir.actions.server,name:ta.action_ta_timesheet
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__timesheet_ids
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__timesheet_ids
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__timesheet_id
#: model:ir.ui.menu,name:ta.menu_ta_timesheet
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_list
#: model_terms:ir.ui.view,arch_db:ta.timesheet_settings_view_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_search
msgid "Timesheet"
msgstr "سجل الدوام"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Timesheet History"
msgstr "تاريخ سجل الدوام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__timesheet_history_ids
msgid "Timesheet Historys"
msgstr "تاريخ سجل الدوام"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Timesheet Info"
msgstr "معلومات سجل الدوام"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_timesheet_config
msgid "Timesheet Settings"
msgstr "اعدادات سجل الدوام"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_pivot
msgid "Timesheet Summary"
msgstr "ملخص سجل الدوام"

#. module: ta
#: model:ir.actions.act_window,name:ta.action_ta_timesheet_shift_unit
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_form
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_search
#: model_terms:ir.ui.view,arch_db:ta.view_ta_timesheet_shift_unit_tree
msgid "Timesheet shift unit"
msgstr "وحدة وردية سجل الدوام"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__to_date
#: model:ir.model.fields,field_description:ta.field_ta_report_generator_wizard__to_date
msgid "To Date"
msgstr "الي تاريخ"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Today"
msgstr "اليوم"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__total_deduction
msgid "Total Deduction"
msgstr "اجمالي الخصم "

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__total_delay_shortage
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__total_delay_shortage
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__total_delay_shortage
msgid "Total Delay Shortage"
msgstr "اجمالي التأخير والتقصير (س)"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__total_delay_shortage_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__total_delay_shortage_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__total_delay_shortage_min
msgid "Total Delay|Shortage (Min)"
msgstr "اجمالي التأخير والتقصير (د)"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_form
msgid "Total Permission"
msgstr "اجمالي الاسئذان"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__total_vacation_count
msgid "Total Vacation Count"
msgstr "عدد الاجازات"

#. module: ta
#: model:ir.ui.menu,name:ta.menu_ta_transaction
msgid "Transactions"
msgstr "الحركات"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule_day__week_day__4
msgid "Tuesday"
msgstr "الثلاثاء"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_search
msgid "Type Flexible"
msgstr "النوع المرن"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_search
msgid "Type Normal"
msgstr "النوع الطبيعي"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_unit_search
msgid "Type Open"
msgstr "النوع المفتوح"

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_policy_group__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_punch_log__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_shift__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_shift_rule__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_shift_schedule_day__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_shift_unit__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_timesheet__activity_exception_decoration
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى في السجل. "

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__units_mixed_types
msgid "Units Mixed Types"
msgstr "وحدات مختلفة"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_shift_schedule_day__units_overlapped
msgid "Units Overlapped"
msgstr "وحدات متقاطعة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_search
msgid "Units mixed types"
msgstr "أنواع وحدات مختلفة"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_ta_shift_schedule_day_search
msgid "Units overlapped"
msgstr "وحدات متقاطعة"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_policy_group__activate
#: model:ir.model.fields,help:ta.field_ta_shift_rule__activate
#: model:ir.model.fields,help:ta.field_ta_shift_schedule__activate
#: model:ir.model.fields,help:ta.field_ta_shift_unit__activate
msgid ""
"Used to deactivate record without  hide from screen,  when value = False,"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_hr_employee__user_name
#: model:ir.model.fields,help:ta.field_ams_base_transaction__user_name
#: model:ir.model.fields,help:ta.field_ta_manual_attendance_wizard__user_name
#: model:ir.model.fields,help:ta.field_ta_punch_log__user_name
#: model:ir.model.fields,help:ta.field_ta_shift__user_name
#: model:ir.model.fields,help:ta.field_ta_timesheet__user_name
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__user_name
msgid "Used to log into the system"
msgstr ""

#. module: ta
#: model:res.groups,name:ta.group_time_attendance_user
msgid "User"
msgstr "مستخدم"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__emp_user_id
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__emp_user_id
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__emp_user_id
#: model:ir.model.fields,field_description:ta.field_ta_shift__emp_user_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__emp_user_id
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__emp_user_id
msgid "User ID"
msgstr "المستخدم"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_transaction__user_name
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__user_name
#: model:ir.model.fields,field_description:ta.field_ta_punch_log__user_name
#: model:ir.model.fields,field_description:ta.field_ta_shift__user_name
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__user_name
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__user_name
msgid "Username"
msgstr "اسم المستخدم"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__vacation_count
msgid "Vacation Count"
msgstr "عدد الاجازة"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule_day__week_day__5
msgid "Wednesday"
msgstr "الأربعاء"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__weekend_count
msgid "Weekend Count"
msgstr "عدد عطلات نهاية الاسبوع"

#. module: ta
#: model:ir.model.fields.selection,name:ta.selection__ta_shift_schedule__schedule_type__weekly
#: model:ir.model.fields.selection,name:ta.selection__ta_timesheet__schedule_type__weekly
msgid "Weekly"
msgstr "اسبوعيا"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_manual_attendance_wizard__with_time
msgid "With Time"
msgstr "مع الوقت"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__working_day_count
msgid "Working Day Count"
msgstr "عدد ايام العمل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__working_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__working_time
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__working_time
msgid "Working Time"
msgstr "مدة العمل"

#. module: ta
#: model:ir.model.fields,field_description:ta.field_ams_base_timesheet__working_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet__working_time_min
#: model:ir.model.fields,field_description:ta.field_ta_timesheet_shift_unit__working_time_min
msgid "Working Time (Min)"
msgstr "مدة العمل (د)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__working_time
#: model:ir.model.fields,help:ta.field_ta_timesheet__working_time
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__working_time
msgid "Working Time by hours"
msgstr "مدة العمل (س)"

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__working_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet__working_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__working_time_min
msgid "Working Time by minutes"
msgstr "مدة العمل (س)"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.ta_timesheet_view_search
msgid "Yesterday"
msgstr "أمس"

#. module: ta
#: model:ir.model,name:ta.model_hr_department
msgid "hr.department"
msgstr "القسم"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__is_attend_day
msgid "if found checkin or checkout"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_punch_log__state
msgid ""
"if log time  transferred to time sheet and time processing done, the state "
"would be executed"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__is_working_day
msgid "if not day off & not vacation"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_shift_unit__absent_time_criteria
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__absent_time_criteria
msgid "make employee absent if working time less than (hours)"
msgstr "احتسب غياب في حالة الوقت اقل من"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__max_checkout_time
msgid "maximum time allowed to checkout"
msgstr "اقصي وقت مسموح للخروج"

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__min_checkin_time
msgid "minimum time allowed to checkin "
msgstr "ادني وقت مسموح للدخول"

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.view_hr_employee_tree
msgid "open Manual Attendance"
msgstr "إضافة حضور وانصراف"

#. module: ta
#: model:ir.model,name:ta.model_res_users
msgid "res.users"
msgstr "المستخدم"

#. module: ta
#: model:ir.model,name:ta.model_ta_policy_group
msgid "ta.policy_group"
msgstr ""

#. module: ta
#: model:ir.model,name:ta.model_ta_shift
msgid "ta.shift"
msgstr ""

#. module: ta
#: model:ir.model,name:ta.model_ta_shift_rule
msgid "ta.shift_rule"
msgstr ""

#. module: ta
#: model:ir.model,name:ta.model_ta_shift_schedule
msgid "ta.shift_schedule"
msgstr ""

#. module: ta
#: model:ir.model,name:ta.model_ta_shift_schedule_day
msgid "ta.shift_schedule_day"
msgstr ""

#. module: ta
#: model:ir.model,name:ta.model_ta_shift_unit
msgid "ta.shift_unit"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_timesheet__total_deduction
msgid "total_delay_shortage or required time if absent day"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ta_punch_log__execute_log_time
msgid "when time proceed in time sheet"
msgstr ""

#. module: ta
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__diff_working_time
#: model:ir.model.fields,help:ta.field_ams_base_timesheet__diff_working_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet__diff_working_time
#: model:ir.model.fields,help:ta.field_ta_timesheet__diff_working_time_min
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__diff_working_time
#: model:ir.model.fields,help:ta.field_ta_timesheet_shift_unit__diff_working_time_min
msgid "working_time - required_time"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "إجمالي أيام الإجازات"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "إجمالي أيام العطلة"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "إجمالي أيام الغياب"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "إجمالي الاستئذان"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "إجمالي التأخير"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "إجمالي التأخير والتقصير"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "إجمالي التقصير"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "إجمالي ساعات الاستئذان"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "إجمالي ساعات العمل"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "الإضافي"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "الاسم"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "التأخير"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "التاريخ"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "التقصير"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "الفترة"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "المجموع الكلي للإدارة"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "جدول الدوام"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "ساعات العمل"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "عدد أيام الدوام الفعلي"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_summary_template
msgid "عدد أيام الدوام الكلي"
msgstr ""

#. module: ta
#: model_terms:ir.ui.view,arch_db:ta.attend_details_template
msgid "ملاحظات"
msgstr ""
