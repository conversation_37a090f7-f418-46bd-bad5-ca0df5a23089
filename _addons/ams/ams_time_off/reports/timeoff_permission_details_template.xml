<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <template id="timeoff_permission_details_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
            <t t-set="o" t-value="o.with_context({'lang': 'ar_001'})"/>
                <t t-call="web.external_layout">
                     <style>
                        th {
                         border: 2px solid #922B21;
                         padding: 6px;
                         text-align:center;
                        }
                    </style>
                    <t t-set="department_ids" t-value="o.department_ids or o.employee_ids.mapped('department_id') or o.allowed_employee_ids.mapped('department_id')"/>

                    <t t-foreach="department_ids" t-as="department">

                        <t t-if="o.employee_ids">
                            <t t-set="employee_ids" t-value="o.employee_ids.filtered(lambda e: department.path_code == e.department_id.path_code )"/>
                        </t>
                        <t t-if="not o.employee_ids">
                            <t t-set="employee_ids" t-value="o.allowed_employee_ids.filtered(lambda e: department.path_code == e.department_id.path_code)"/>
                        </t>
                        <t t-foreach="employee_ids" t-as="employee">

                            <div class="text-nowrap" style="direction:rtl; text-align:right; padding-top:150px; font-size:12px; page-break-inside: avoid;">
                                <div style="font-size:15px; font-weight:bold;">
                                    <div class="row ml-1 mr-1 mt-1 mb-2" style="border: 2px solid #2E86C1;">
                                        <div class="col-6">
                                            <span class=" pb-2 pt-2" t-esc="department.name"/>
                                        </div>
                                        <div class="col-6" style="text-align:left;">
                                            <span class=" pb-2 pt-2" t-esc="employee.policy_group_id.name"/>
                                        </div>
                                    </div>

                                    <div class="row pb-2">
                                        <div class="col-6">
                                            <span>من تاريخ : </span>
                                            <span t-esc="o.from_date"/>
                                        </div>
                                        <div class="col-6">
                                            <span>إلى تاريخ : </span>
                                            <span t-esc="o.to_date"/>
                                        </div>
                                    </div>
                                    <div class="row pb-3">
                                        <div class="col-6">
                                            <span>اسم الموظف : </span>
                                            <span t-esc="employee.name"/>
                                        </div>
                                        <div class="col-6">
                                            <span>رقم الموظف : </span>
                                            <span t-esc="employee.id"/>
                                        </div>
                                    </div>
                                </div>

                                <table class="w-100">
<!--                                        style="margin-left:auto; margin-right:auto; ">-->
                                    <tr>
                                        <th>الإجازة</th>
                                        <th>اليوم</th>
                                        <th>الساعات</th>
                                        <th>المستوى</th>
                                        <th>بداية\نهاية الدوام</th>
                                        <th class="w-25">ملاحظات</th>
                                        <th>المدير المسؤول</th>
                                    </tr>
                                    <t t-set="timesheet_ids" t-value="o.timesheet_ids.filtered(lambda t:
                                    t.employee_id == employee and not t.is_vacation and (t.time_off_id or t.time_off_id2))"/>
                                    <t t-set="total_permissions" t-value="0"/>
                                    <t t-if="timesheet_ids">

                                        <t t-foreach="timesheet_ids" t-as="ts">
                                            <t t-if="ts.time_off_id">
                                                <t t-set="total_permissions" t-value="total_permissions + 1"/>
                                                <tr>
                                                    <th><span t-field="ts.time_off_id.holiday_status_id.name"/></th>
                                                    <th><span t-esc="ts.time_off_id.date_from.strftime('%A , %d-%m-%Y')"/></th>

                                                    <th><span t-esc="o.convert_to_time_format(ts.time_off_id.manual_from_to_duration)
                                                                if ts.time_off_id.manual_from_to else o.convert_to_time_format(ts.time_off_id.number_of_hours_display)" /></th>
                                                    <th><span t-esc="'على مستوى الموظف' if ts.time_off_id.holiday_type == 'employee' else 'على مستوى القسم'" t-options='{"widget": "badge"}'/></th>
                                                    <th><span t-esc="'بداية الدوام'"></span></th>
                                                    <th><span t-field="ts.time_off_id.name" class="text-wrap"/></th>
                                                    <th>
                                                         <span t-if="ts.time_off_id.holiday_status_id.leave_validation_type in ['manager', 'both']"
                                                              t-esc="ts.time_off_id.leave_manager_id.name"/>
                                                         <span t-if="ts.time_off_id.holiday_status_id.leave_validation_type in ['both', 'hr']"
                                                              t-esc="ts.time_off_id.holiday_status_id.responsible_id.name"/>
                                                         <span t-if="ts.time_off_id.holiday_status_id.leave_validation_type == 'no_validation'"
                                                              t-esc="ts.time_off_id.create_uid.name"/>
                                                    </th>
                                                </tr>
                                            </t>
                                            <t t-if="ts.time_off_id2">
                                                <t t-set="total_permissions" t-value="total_permissions + 1"/>
                                                <tr>
                                                    <th><span t-field="ts.time_off_id.holiday_status_id.name"/></th>
                                                    <th><span t-esc="ts.time_off_id2.date_from.strftime('%A , %d-%m-%Y')"/></th>

                                                    <th><span t-esc="o.convert_to_time_format(ts.time_off_id2.manual_from_to_duration)
                                                                if ts.time_off_id2.manual_from_to else o.convert_to_time_format(ts.time_off_id2.number_of_hours_display)" /></th>
                                                    <th><span t-esc="'على مستوى الموظف' if ts.time_off_id2.holiday_type == 'employee' else 'على مستوى القسم'" t-options='{"widget": "badge"}'/></th>
                                                    <th><span t-esc="'نهاية الدوام'"></span></th>
                                                    <th><span t-field="ts.time_off_id2.name"  class="text-wrap"/></th>
                                                    <th>
                                                        <span t-if="ts.time_off_id2.holiday_status_id.leave_validation_type in ['manager', 'both']"
                                                              t-esc="ts.time_off_id2.leave_manager_id.name"/>
                                                        <span t-if="ts.time_off_id2.holiday_status_id.leave_validation_type in ['both', 'hr']"
                                                              t-esc="ts.time_off_id2.holiday_status_id.responsible_id.name"/>
                                                        <span t-if="ts.time_off_id2.holiday_status_id.leave_validation_type == 'no_validation'"
                                                              t-esc="ts.time_off_id2.create_uid.name"/>
                                                    </th>
                                                </tr>
                                            </t>
                                        </t>
                                    </t>
                                    </table>
                                    <table class="mt-5 w-50 text-center">
                                        <tr>
                                            <th>إجمالي ساعات الاستئذان </th>
                                            <th><span t-esc="sum(timesheet_ids.mapped('time_off_hours')) if timesheet_ids else 0"/></th>

                                            <th class="w-25">إجمالي الاستئذانات</th>
                                            <th><span t-esc="total_permissions"/></th>
                                        </tr>
                                    </table>
                            </div>
                        </t>
                    </t>
                </t>
            </t>
        </t>
    </template>
</odoo>