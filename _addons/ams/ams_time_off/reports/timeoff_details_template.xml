<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <template id="timeoff_details_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
            <t t-set="o" t-value="o.with_context({'lang': 'ar_001'})"/>
                <t t-call="web.external_layout">
                     <style>
                        th {
                         border: 2px solid #922B21;
                         padding: 6px;
                         text-align:center;
                        }
                    </style>
                    <t t-set="department_ids" t-value="o.department_ids or o.employee_ids.mapped('department_id') or o.allowed_employee_ids.mapped('department_id')"/>

                    <t t-foreach="department_ids" t-as="department">

                        <t t-if="o.employee_ids">
                            <t t-set="employee_ids" t-value="o.employee_ids.filtered(lambda e: department.path_code == e.department_id.path_code )"/>
                        </t>
                        <t t-if="not o.employee_ids">
                            <t t-set="employee_ids" t-value="o.allowed_employee_ids.filtered(lambda e: department.path_code == e.department_id.path_code)"/>
                        </t>
                        <t t-foreach="employee_ids" t-as="employee">

                            <div class="text-nowrap" style="direction:rtl; text-align:right; padding-top:150px; font-size:12px; page-break-inside: avoid;">
                                <div style="font-size:15px; font-weight:bold;">
                                    <div class="row ml-1 mr-1 mt-1 mb-2" style="border: 2px solid #2E86C1;">
                                        <div class="col-6">
                                            <span class=" pb-2 pt-2" t-esc="department.name"/>
                                        </div>
                                        <div class="col-6" style="text-align:left;">
                                            <span class=" pb-2 pt-2" t-esc="employee.policy_group_id.name"/>
                                        </div>
                                    </div>

                                    <div class="row pb-2">
                                        <div class="col-6">
                                            <span>من تاريخ : </span>
                                            <span t-esc="o.from_date"/>
                                        </div>
                                        <div class="col-6">
                                            <span>إلى تاريخ : </span>
                                            <span t-esc="o.to_date"/>
                                        </div>
                                    </div>
                                    <div class="row pb-3">
                                        <div class="col-6">
                                            <span>اسم الموظف : </span>
                                            <span t-esc="employee.name"/>
                                        </div>
                                        <div class="col-6">
                                            <span>رقم الموظف : </span>
                                            <span t-esc="employee.id"/>
                                        </div>
                                    </div>
                                </div>

                                <table class="w-100">
<!--                                        style="margin-left:auto; margin-right:auto; ">-->
                                    <tr>
                                        <th>الإجازة</th>
                                        <th>من</th>
                                        <th>الى</th>
                                        <th>الأيام</th>
                                        <th>نوع الإجازة</th>
                                        <th class="w-25">ملاحظات</th>
                                        <th>المدير المسؤول</th>
                                    </tr>
                                    <t t-set="timesheet_ids" t-value="o.timesheet_ids.filtered(lambda t: t.employee_id == employee
                                                                                        and (t.is_vacation or t.is_public_vacation))"/>

                                    <t t-if="timesheet_ids">
                                        <t t-set="timeoff_ids" t-value="timesheet_ids.mapped('time_off_id')"/>

                                        <t t-set="total_dayoff" t-value="len(timeoff_ids)"/>
                                        <t t-foreach="timeoff_ids" t-as="timeoff">
                                        <tr>
                                            <th><span t-field="timeoff.holiday_status_id.name"/></th>
                                            <th><span t-esc="timeoff.date_from.strftime('%A , %d-%m-%Y')"/></th>
                                            <th>
                                                <span t-esc="timeoff.date_to.strftime('%A , %d-%m-%Y')"/>
                                            </th>

                                            <th><span t-esc="int(timeoff.number_of_days)" /></th>
                                            <th><span t-esc="'على مستوى الموظف' if timeoff.holiday_type else 'على مستوى القسم'" t-options='{"widget": "badge"}'/></th>
                                            <th><span class="text-wrap" t-field="timeoff.name"/></th>
                                            <th>
                                                <span t-if="timeoff.holiday_status_id.leave_validation_type in ['manager', 'both']"
                                                      t-esc="timeoff.leave_manager_id.name"/>
                                                <span t-if="timeoff.holiday_status_id.leave_validation_type in ['both', 'hr']"
                                                      t-esc="timeoff.holiday_status_id.responsible_id.name"/>
                                                <span t-if="timeoff.holiday_status_id.leave_validation_type == 'no_validation'"
                                                      t-esc="timeoff.create_uid.name"/>
                                            </th>
                                        </tr>
                                        </t>

                                        <t t-set="public_timeoff_ids" t-value="timesheet_ids.mapped('public_vacation_id')"/>
                                        <t t-if="public_timeoff_ids">
                                            <tr>
                                               <th  class="border-0"> العطل الرسمية</th>
                                                <th class="border-0"></th>
                                                <th class="border-0"></th>
                                                <th class="border-0"></th>
                                                <th class="border-0"></th>
                                                <th class="border-0"></th>
                                                <th class="border-0"></th>
                                            </tr>
                                        </t>
                                        <t t-set="total_public_dayoff" t-value="len(timeoff_ids)"/>
                                        <t t-set="total_public_dayoff_days" t-value="0"/>
                                        <t t-foreach="public_timeoff_ids" t-as="timeoff">
                                        <tr>
                                            <th><span t-field="timeoff.name"/></th>
                                            <th><span t-esc="timeoff.date_from.strftime('%A , %d-%m-%Y')"/></th>
                                            <th>
                                                <span t-esc="timeoff.date_to.strftime('%A , %d-%m-%Y')"/>
                                            </th>
                                            <t  t-set='duration' t-value="(timeoff.date_to - timeoff.date_from).days or 1"/>
                                            <th><span t-esc="duration" /></th>
                                            <t t-set="total_public_dayoff_days" t-value="total_public_dayoff_days + duration"/>
                                            <th><span t-esc="'عطلة عامة'" t-options='{"widget": "badge"}'/></th>
                                            <th><span t-esc="' '"/></th>
                                            <th><span t-esc="' '"/></th>
                                        </tr>
                                        </t>
                                    </t>
                                    </table>
                                    <table class="mt-5 w-50 text-center">
                                        <tr>
                                            <th>إجمالي طلبات العطل </th>
                                            <th><span t-esc="total_dayoff"/></th>

                                            <th class="w-25">إجمالي أيام العطل الغير رسمية</th>
                                            <th><span t-if="timeoff_ids and timeoff_ids.mapped('number_of_days')" t-esc="int(sum(timeoff_ids.mapped('number_of_days')))"/></th>

                                             <th class="w-25">إجمالي أيام العطل الرسمية</th>
                                            <th><span t-esc="total_public_dayoff_days"/></th>

                                        </tr>
                                    </table>
                            </div>
                        </t>
                    </t>
                </t>
            </t>
        </t>
    </template>
</odoo>