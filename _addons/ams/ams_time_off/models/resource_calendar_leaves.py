# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools import pytz
from pytz import timezone


class ResourceCalendarLeaves(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "resource.calendar.leaves"
    _description = "Public Holidays"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    is_active = fields.Boolean()
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_activate(self):
        self.is_active = True
        # if self.date_from and self.date_to and self.date_from < fields.Datetime.now(): # commented for test needs
        # TODO Review  date with user context
        old_timesheets = self.env["ta.timesheet"].search([('date', '>=', self.date_from.date()),
                                                          ('date', '<=', self.date_to.date()),
                                                          ('is_weekend', '=', False),
                                                          ])
        if old_timesheets:
            old_timesheets.write({'is_absent': False,
                                  'is_dayoff': True,
                                  'is_public_vacation': True,
                                  'is_vacation': False,
                                  'is_working_day': False,
                                  'public_vacation_id': self.id,
                                  'notes': self.name
                                  })

    def action_deactivate(self):
        domain = [('public_vacation_id', '=', self.id)]

        timeoff_timesheets = self.env['ta.timesheet'].search(domain)
        if timeoff_timesheets:
            for timesheet in timeoff_timesheets:
                timesheet.write({'public_vacation_id': False,
                                 'is_public_vacation': False,
                                 'is_dayoff': True if timesheet.is_weekend else False,
                                 'is_working_day': False if timesheet.is_weekend else True,
                                 })
                timesheet.notes = timesheet.notes.replace(self.name, ' ')
                if timesheet.parent_id:
                    timesheet.parent_id.action_calc_timesheet()

        self.is_active = False
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion
