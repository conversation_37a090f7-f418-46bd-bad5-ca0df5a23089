# -*- coding: utf-8 -*-

import logging
import pytz

from collections import namedtuple, defaultdict

from datetime import datetime, timedelta, time
from pytz import timezone, UTC

from odoo import api, fields, models, tools, SUPERUSER_ID

from odoo.addons.ta.helper.helper import *
# from odoo.addons.base.models.res_partner import _tz_get
# from odoo.addons.resource.models.resource import float_to_time, HOURS_PER_DAY
# from odoo.exceptions import AccessError, UserError, ValidationError
# from odoo.tools import float_compare
# from odoo.tools.float_utils import float_round
from odoo.tools.translate import _
from odoo.osv import expression


class HolidaysRequest(models.Model):

    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "hr.leave"
    _description = "Time off Request"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    request_date_from_period = fields.Selection([
        ('am', 'Shift Start'), ('pm', 'Shift End')],
        string="Date Period Start", default='am')
    leave_manager_id = fields.Many2one("res.users")
    manual_from_to = fields.Boolean()
    manual_from_to_duration = fields.Float(string="Duration", default=0,
                                           help="The duration which will be taken from the attendance every selected day in hours")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # todo (mig) review onchange on fields , 'request_unit_custom'
    @api.depends('holiday_status_id', 'request_unit_hours')
    def _compute_request_unit_half(self):
        for holiday in self:
            if holiday.holiday_status_id.request_unit != 'day':
                holiday.request_unit_half = True
            else:
                holiday.request_unit_half = False

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------


    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model_create_multi
    def create(self, vals_list):
        """ Override to change the get responsible for approval """
        holidays = super(HolidaysRequest, self).create(vals_list)

        for holiday in holidays:
            if not self._context.get('leave_fast_create'):
                holiday_sudo = holiday.sudo()

                if holiday.validation_type == 'manager':
                    if holiday.leave_manager_id:
                        holiday_sudo.message_subscribe(partner_ids=holiday.leave_manager_id.partner_id.ids)
                    else:
                        holiday_sudo.message_subscribe(partner_ids=holiday.employee_id.leave_manager_id.partner_id.ids)

        return holidays

    def write(self, values):
        result = super(HolidaysRequest, self).write(values)
        for rec in self:
            if not rec.env.context.get('leave_fast_create'):
                domain = []
                if values.get('holiday_status_id') or (values.get('state') and values.get('state') == 'refuse'):
                    domain = [ ('parent_id', '=', False), '|', ('time_off_id', '=', rec.id), ('time_off_id2', '=', rec.id)]

                elif 'date_from' in values or 'date_to' in values:
                    domain = [('parent_id', '=', False), '|', ('time_off_id', '=', rec.id), ('time_off_id2', '=', rec.id),
                                                         '|', ('date', '<', rec.date_from), ('date', '>', rec.date_to)]

                if domain:
                    timeoff_timesheets = self.env['ta.timesheet'].search(domain)
                    if timeoff_timesheets:
                        for timesheet in timeoff_timesheets:
                            timeoff_field = 'time_off_id'
                            if timesheet.time_off_id2.id == rec.id:
                                timeoff_field = 'time_off_id2'

                            timesheet.write({timeoff_field: False,
                                             'is_vacation': False,
                                             'is_dayoff': True if timesheet.is_weekend or timesheet.is_public_vacation else False,
                                             'is_working_day': False if timesheet.is_weekend or timesheet.is_public_vacation else True,
                                             'notes':timesheet.notes.replace(rec.holiday_status_id.name, ' ') \
                                                             if timesheet.notes and rec.holiday_status_id.name else timesheet.notes
                                             })

                            if timesheet.parent_id:
                                timesheet.parent_id.action_calc_timesheet()
        return result
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def _get_leaves_on_public_holiday(self):
        if len(self) == 1:
            return False
        return self.filtered(lambda l: l.employee_id and not l.number_of_days)

    def action_approve(self):
        result = super(HolidaysRequest, self).action_approve()
        if result:
            self._link_with_old_timesheets()
        return result

    def action_confirm(self):
        result = super(HolidaysRequest, self).action_confirm()
        if result:
            self._link_with_old_timesheets()
        return result

    def _link_with_old_timesheets(self):
        employees = self.employee_ids
        if self.holiday_type != "employee":
            domain = ('id', '=', False)
            if self.holiday_type == 'company':
                domain = ('company_id', '=', self.mode_company_id.id)
            elif self.holiday_type == 'department':
                domain = ('department_id', '=', self.department_id.id)
            elif self.holiday_type == 'category':
                domain = ('category_ids', 'like', self.category_id.id)

            employees = self.env["hr.employee"].search([domain])

        if employees:
            # if self.date_from and self.date_to and self.date_from < fields.Datetime.now():
            old_timesheets = self.env["ta.timesheet"].search([('employee_id', 'in', employees.ids),
                                                              ('date', '>=', self.date_from.date()),
                                                              ('date', '<=', self.date_to.date()),
                                                              ('is_weekend', '=', False),
                                                              ])
            if old_timesheets and self.leave_type_request_unit == 'day':
                old_timesheets.write({'is_absent': False,
                                      'is_dayoff': True,
                                      'is_vacation': True,
                                      'is_working_day': False,
                                      'time_off_id': self.id,
                                      'notes': self.holiday_status_id.name
                                      })
            elif old_timesheets:
                parents_timesheets = old_timesheets.filtered(lambda ts: not ts.parent_id)

                for ts in parents_timesheets:
                    if self.request_unit_half:
                        period = ' بداية '
                        if self.request_date_from_period == 'am':
                            ts.time_off_id = self.id
                        else:
                            ts.time_off_id2 = self.id
                            period = " نهاية "
                        note = 'استئذان من' + period + " الشيفت لمدة " + str(
                            self.manual_from_to_duration or self.number_of_hours_display) + " ساعات " + '. ,'
                        ts.notes = ts.notes + note if ts.notes else note
                parents_timesheets.action_calc_timesheet()
                # all_shift_units = parents_timesheets.mapped('shift_unit_current_ids')
                # time_off_shifts = all_shift_units.filtered(lambda shift: (self.from_time <= shift.start_time <= self.to_time)
                #                                         or
                #                                         (self.from_time <= shift.end_time <= self.to_time)
                #                                         or
                #                                         (shift.start_time <= self.from_time and shift.end_time >= self.to_time))
                # if time_off_shifts:
                #     time_off_shifts.write({'time_off_id': self.id})
                #     parents_timesheets = time_off_shifts.mapped('parent_timesheet_id')
                #     parents_timesheets.write({'time_off_id':self.id, 'time_off_hours': self.manual_from_to_duration,
                #                                   'notes': "استئذان من الساعة " + str(convert_to_time_object(self.from_time)) +
                #                                            " حتى الساعة " + str(convert_to_time_object(self.to_time))})

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _get_responsible_for_approval(self):
        self.ensure_one()
        responsible = self.leave_manager_id
        if not responsible:
            responsible = super(HolidaysRequest, self)._get_responsible_for_approval()
        return responsible

    def name_get(self):

        res = []

        if not self.env.context.get('hide_employee_name:') and not self.env.context.get('short_name'):
            for leave in self:
                if leave.leave_type_request_unit in ['hour', 'half_day']:
                    res.append((leave.id, _("%s : \n %.2f hours") % (
                    leave.name or leave.holiday_status_id.name, leave.manual_from_to_duration or leave.number_of_hours_display)))
                else:
                    res.append((leave.id, _("%s : \n %.2f days") % (leave.name or leave.holiday_status_id.name, leave.number_of_days)))
        else:
            res = super(HolidaysRequest, self).name_get()
        return res
    # def _get_number_of_days(self, date_from, date_to, employee_id):
    #     """ Returns a float equals to the timedelta between two dates given as string."""
    #     if employee_id:
    #         employee = self.env['hr.employee'].browse(employee_id)
    #         result = employee._get_work_days_data_batch(date_from, date_to)[employee.id]
    #         if self.request_unit_half and result['hours'] > 0:
    #             result['days'] = 0.5
    #         return result
    #
    #     today_hours = self.env.company.resource_calendar_id.get_work_hours_count(
    #         datetime.combine(date_from.date(), time.min),
    #         datetime.combine(date_from.date(), time.max),
    #         False)
    #
    #     hours = self.env.company.resource_calendar_id.get_work_hours_count(date_from, date_to)
    #     days = hours / (today_hours or HOURS_PER_DAY) if not self.request_unit_half else 0.5
    #     return {'days': days, 'hours': hours}
    #
    #     # endregion
