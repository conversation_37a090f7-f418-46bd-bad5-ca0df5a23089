# -*- coding: utf-8 -*-


from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta, time
from odoo.addons.ta.helper.helper import *
from odoo.addons.ta.api.dto_mobile_att import *
from odoo.addons.ta.api.dto_emp_att_log import *
import logging
_logger = logging.getLogger(__name__)


class Timesheet(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "ta.timesheet"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    time_off_id = fields.Many2one("hr.leave", tracking=True)
    time_off_id2 = fields.Many2one("hr.leave", tracking=True)
    time_off_hours = fields.Float(compute='_compute_total_timeoff_duration', store=True)
    public_vacation_id = fields.Many2one('resource.calendar.leaves')
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('time_off_id', 'time_off_id2')
    def _compute_total_timeoff_duration(self):
        for rec in self:
            rec.time_off_hours = 0
            if rec.time_off_id and rec.time_off_id.holiday_status_id.request_unit != 'day':
                rec.time_off_hours += rec.time_off_id.manual_from_to_duration if rec.time_off_id.manual_from_to \
                    else rec.time_off_id.number_of_hours_display

            if rec.time_off_id2 and rec.time_off_id2.holiday_status_id.request_unit != 'day':
                rec.time_off_hours += rec.time_off_id2.manual_from_to_duration if rec.time_off_id2.manual_from_to \
                    else rec.time_off_id2.number_of_hours_display

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    @api.model
    def create(self, values):
        timesheet = super(Timesheet, self).create(values)
        if not timesheet.is_weekend and timesheet.parent_id:
            day_off = False
            time_off = self.env['hr.leave'].search([('employee_ids', 'like', timesheet.employee_id.id),
                                                   ('request_date_from', '<=', timesheet.date),
                                                   ('request_date_to', '>=', timesheet.date),
                                                   ('state', '=', 'validate')])

            if time_off:
                day_off = time_off.filtered(lambda time_off: time_off.leave_type_request_unit == 'day')
                time_offs = time_off.filtered(lambda time_off: time_off.leave_type_request_unit in ['hour', 'half_day'])
                if day_off:
                    timesheet.is_vacation = True
                    timesheet.time_off_id = day_off[0]
                    timesheet.notes = day_off[0].holiday_status_id.name
                    timesheet.parent_id.is_vacation = True
                    timesheet.parent_id.time_off_id = day_off[0]
                    timesheet.parent_id.notes = day_off[0].holiday_status_id.name

                if time_offs:
                   for time_off in time_offs:
                        if time_off.request_unit_half:
                            period = ' بداية '
                            if time_off.request_date_from_period == 'am':
                                timesheet.parent_id.time_off_id = time_off.id
                            else:
                                timesheet.parent_id.time_off_id2 = time_off.id
                                period = " نهاية "
                            note = 'استئذان من' + period + " الشيفت لمدة " \
                                        + str(time_off.manual_from_to_duration or time_off.number_of_hours_display) + " ساعات " + '. ,'

                            timesheet.parent_id.notes = timesheet.parent_id.notes + note if timesheet.parent_id.notes else note
                   timesheet.parent_id.action_calc_timesheet()
                    # if shift_unit:
                    #     shift_unit.time_off_id = time_off[0]
            date = datetime.combine(timesheet.date, time(hour=0, minute=0))
            public_vacation = self.env["resource.calendar.leaves"].search([('is_active', '=', True),
                                                                           ('date_from', '<=', date),
                                                                           ('date_to', '>=', date)
                                                                           ], limit=1)
            if public_vacation:
                timesheet.is_public_vacation = True
                timesheet.public_vacation_id = public_vacation
                timesheet.notes = public_vacation.name
                timesheet.parent_id.is_public_vacation = True
                timesheet.parent_id.public_vacation_id = public_vacation
                timesheet.parent_id.notes = public_vacation.name

            if public_vacation or day_off:
                timesheet.is_dayoff = True
                timesheet.is_absent = False
                timesheet.is_working_day = False
                timesheet.parent_id.is_dayoff = True
                timesheet.parent_id.is_absent = False
                timesheet.parent_id.is_working_day = False

        return timesheet
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _check_half_work_criteria(self, shift_unit):
        if self.time_off_id or self.time_off_id2:
            if shift_unit.calc_half_no_checkout_time and not shift_unit.last_checkout_datetime:
                shift_unit.working_time = shift_unit.required_time / 2 if shift_unit.working_time > shift_unit.required_time / 2 \
                    else shift_unit.working_time
        else:
            super(Timesheet, self)._check_half_work_criteria(shift_unit)

    def _check_absent(timesheet, absent_time_criteria):
        timesheet._recalculate_with_time_off()
        timesheet.is_absent = False
        if timesheet.current_timesheet_id:
            timesheet.current_timesheet_id.is_absent = False
        time_off = timesheet.time_off_id or timesheet.time_off_id2
        if timesheet.working_time < absent_time_criteria and not timesheet.is_dayoff and not time_off:
            timesheet.is_absent = True
            timesheet.current_timesheet_id.is_absent = True

    def _recalculate_with_time_off(self):
        if self.time_off_id and self.time_off_id.leave_type_request_unit != 'day':
            t_off_duration = self.time_off_id.manual_from_to_duration or self.time_off_id.number_of_hours_display
            if self.delay_time > t_off_duration:
                self.delay_time = self.delay_time - t_off_duration
            else:
                self.delay_time = 0

        if self.time_off_id2 and self.time_off_id2.leave_type_request_unit != 'day':
            t_off_duration = self.time_off_id.manual_from_to_duration or self.time_off_id.number_of_hours_display
            if self.shortage_time > t_off_duration:
                self.shortage_time = self.shortage_time - t_off_duration
            else:
                self.shortage_time = 0

        self.total_delay_shortage = self.delay_time + self.shortage_time

    # endregion

    # region ---------------------- TODO[IMP] API  Methods  -------------------------------------
    def _prepare_time_off(self, employee_domain, att_param, permissions: [], vacations: []):
        # override method in time off & return dto
        # action : 87654321 "-1" = all[att,permissions,vacation] , "0" = attendance, "1" = permission, "2" = vacations
        employee_id = self.env['hr.employee'].search([employee_domain])

        if employee_id:
            employee_domain = [('employee_ids', 'like', employee_id.id)]
            if employee_id.company_id:
                employee_domain.insert(0, '|')
                employee_domain.extend(['&', ('holiday_type', '=', 'company'),
                                        ('mode_company_id', '=', employee_id.company_id.id)])
            if employee_id.department_id:
                employee_domain.insert(0, '|')
                employee_domain.extend(['&', ('holiday_type', '=', 'department'),
                                        ('department_id', '=', employee_id.department_id.id)])

            domain = employee_domain

            if att_param.action == '1':
                domain.extend([('leave_type_request_unit', '!=', 'day'),
                               ('request_date_from', '>=', datetime.strptime(att_param.date_from, '%Y%m%d').date()),
                               ('request_date_from', '<=', datetime.strptime(att_param.date_to, '%Y%m%d').date()),
                               ])
                time_off_ids = self.env['hr.leave'].search(domain)
                for time_off in time_off_ids:
                    duration_min = get_total_minutes(time_off.manual_from_to_duration) if time_off.manual_from_to else 4*60
                    state = 0 if time_off.state in ['draft', 'confirm'] else \
                            1 if time_off.state in ['validate1', 'validate'] else \
                            2 if time_off.state == 'refuse' else 3  # note no state = cancel

                    time_off_type_ar_name = time_off.with_context(lang='ar_001').holiday_status_id.name
                    time_off_type_en_name = time_off.with_context(lang='en_us').holiday_status_id.name
                    permissions.append(Permission(str(time_off.employee_id.id), str(time_off.date_to), str(time_off.id),
                                                  str(time_off.holiday_status_id.id), time_off_type_ar_name,
                                                  time_off_type_en_name, time_off.name,
                                                  time_off.first_approver_id.name, str(time_off.date_from), state, duration_min
                                                  , 0 if time_off.request_date_from_period == 'am' else 1))
            else:
                domain.extend([('leave_type_request_unit', '=', 'day'),
                               '|', '|', '&',
                               ('request_date_from', '<=', datetime.strptime(att_param.date_from, '%Y%m%d').date()),
                               ('request_date_to', '>=', datetime.strptime(att_param.date_from, '%Y%m%d').date()),
                               '&',
                               ('request_date_from', '<=', datetime.strptime(att_param.date_to, '%Y%m%d').date()),
                               ('request_date_to', '>=', datetime.strptime(att_param.date_to, '%Y%m%d').date()),
                               '&',
                               ('request_date_from', '>=', datetime.strptime(att_param.date_from, '%Y%m%d').date()),
                               ('request_date_to', '<=', datetime.strptime(att_param.date_to, '%Y%m%d').date())
                               ])
                day_off_ids = self.env['hr.leave'].search(domain)
                for day_off in day_off_ids:
                    state = 0 if day_off.state in ['draft', 'confirm'] else \
                            1 if day_off.state in ['validate1', 'validate'] else \
                            2 if day_off.state == 'refuse' else 3  # note no state = cancel
                    time_off_type_ar_name = day_off.with_context(lang='ar_001').holiday_status_id.name
                    time_off_type_en_name = day_off.with_context(lang='en_us').holiday_status_id.name
                    vacations.append(Vacation(str(day_off.employee_id.id),str(day_off.date_to),str(day_off.id),
                                              day_off.name, day_off.first_approver_id.name, str(day_off.date_from), state,
                                              str(day_off.holiday_status_id.id), time_off_type_ar_name,
                                              time_off_type_en_name))

    # endregion