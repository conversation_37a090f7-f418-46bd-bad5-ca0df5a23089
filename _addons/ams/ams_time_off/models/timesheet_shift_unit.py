import datetime

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.tools import pytz
from odoo.addons.ta.helper.helper import *


class TimeSheetShiftUnit(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _inherit = "ta.timesheet_shift_unit"
    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
   # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
     # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion

