<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form          hr_holidays.hr_leave_view_form-->
    <record id="model_view_form" model="ir.ui.view">
        <field name="name">module.model.form</field>
        <field name="model">module.model</field>
        <field name="arch" type="xml">
            <form>
                <header>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <!--<field name="name"/>-->
                        </h1>

                    </div>
                    <group>
                        <group>

                        </group>
                        <group>

                        </group>
                    </group>
                    <notebook>
                        <page string="Page1">
                            <group>
                                <group>

                                </group>
                            </group>
                        </page>
                        <page string="Page2">
                        </page>

                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_list-->
    <record id="model_view_list" model="ir.ui.view">
        <field name="name">module.model.list</field>
        <field name="model">module.model</field>
        <field name="arch" type="xml">
            <list string="Desc">
                <!--<field name="name"/>-->
            </list>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_search-->
    <record id="model_view_search" model="ir.ui.view">
        <field name="name">module.model.search</field>
        <field name="model">module.model</field>
        <field name="arch" type="xml">
            <search>
                <!--<field name="name"/>-->
                <!--<field name="living_area" filter_domain="[('living_area', '>=', self)]"/>-->
                <!--<filter string="Available" name="available" domain="[('state', 'in', ('new', 'offer_received'))]"/>-->
                <group expand="1" string="Group By">
                    <!--<filter string="Postcode" name='postcode' context="{'group_by':'postcode'}"/>-->
                </group>
            </search>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_kanban-->
    <record id="model_view_kanban" model="ir.ui.view">
        <field name="name">model.kanban</field>
        <field name="model">module.model</field>
        <field name="arch" type="xml">
            <kanban default_group_by="field_name" records_draggable="0">
                <!--<field name="state"/>-->
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click">
                            <div>
                                <strong class="o_kanban_record_title">
                                    <!--<field name="name"/>-->
                                </strong>
                            </div>
                            <!--<div>
                                Expected Price: <field name="expected_price"/>
                            </div>
                            <div t-if="record.state.raw_value == 'offer_received'">
                                Best Offer: <field name="best_price"/>
                            </div>
                            <div t-if="record.selling_price.raw_value">
                                Selling Price: <field name="selling_price"/>
                            </div>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>-->
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_calendar-->
    <record id="model_view_calendar" model="ir.ui.view">
        <field name="name">module.model.calendar</field>
        <field name="model">module.model</field>
        <field name="arch" type="xml">
            <calendar string="Appointment" date_start="start_date" date_stop="end_date" event_limit="5"
                      quick_add="False" color="state" event_open_popup="True">
                <!--<field name="name" />
                <field name="company_id" filters="1" />
                <field name="partner_id" filters="1" />
                <field name="patient_id" avatar_field="image_128" />
                <field name="start_date" options='{"datepicker": {"daysOfWeekDisabled": [5, 6]}}'/>-->
            </calendar>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_graph-->
    <record id="model_view_graph" model="ir.ui.view">
        <field name="name">module.model.graph</field>
        <field name="model">module.model</field>
        <field name="arch" type="xml">
            <graph string="Appointment" type="bar">
                <!--<field name="slot_id"/>-->
            </graph>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _action-->
    <record id="model_action" model="ir.actions.act_window">
        <field name="name">Models</field>
        <field name="res_model">module.model</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
