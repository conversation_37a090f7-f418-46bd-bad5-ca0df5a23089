<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- list view -->
        <record id="ta_timesheet_view_list_timeoff" model="ir.ui.view">
            <field name="name">ta_timesheet_tree</field>
            <field name="model">ta.timesheet</field>
            <field name="type">tree</field>
            <field name="inherit_id" ref="ta.ta_timesheet_view_list"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='notes']" position="after">
                    <field name="time_off_id" optional="hide"/>
                    <field name="time_off_id2" optional="hide"/>
                    <field name="public_vacation_id" optional="hide"/>
                </xpath></field>
        </record>
        <!-- form view -->
        <record id="ta_timesheet_view_form_timeoff" model="ir.ui.view">
            <field name="name">ta_timesheet_form</field>
            <field name="model">ta.timesheet</field>
            <field name="type">form</field>
            <field name="inherit_id" ref="ta.ta_timesheet_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='notes']" position="after">
                    <field invisible="time_off_id == False or is_vacation == False" name="time_off_id" string="Day-Off"/>
                    <field help="Permission at the start of the shift" invisible="time_off_id == False or is_vacation == True" name="time_off_id" string="Permission Start"/>
                    <field help="Permission at the end of the shift" invisible="time_off_id2 == False" name="time_off_id2" string="Permission End"/>
                    <field invisible="public_vacation_id == False" name="public_vacation_id"/>
                </xpath>
                <!--                <xpath expr="//field[@name='shift_unit_current_ids']//list"-->
                <!--                       position="inside">-->
                <!--                    <field name="time_off_id" optional="hide"/>-->
                <!--                </xpath>-->
                <!--                <xpath expr="//field[@name='shift_unit_history_ids']//list"-->
                <!--                       position="inside">-->
                <!--                    <field name="time_off_id" optional="hide"/>-->
                <!--                </xpath>--></field>
        </record>
        <!-- search -->
        <record id="ta_timesheet_view_search_timeoff" model="ir.ui.view">
            <field name="name">ta_timesheet</field>
            <field name="model">ta.timesheet</field>
            <field name="inherit_id" ref="ta.ta_timesheet_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='groupby_shift']">
                    <filter context="{'group_by':'time_off_id'}" name="groupby_timeoff_id" string="Time-off"/>
                    <filter context="{'group_by':'public_vacation_id'}" name="groupby_public_vacation_id" string="Public Vacation"/>
                </xpath></field>
        </record>
    </data>
</odoo>
