# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, time
from odoo.addons.ta.helper.helper import *


class SupremaUser(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.user"
    _inherit = ["ams_suprema.user"]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_link_users(self):
        """ create group users for selected users from tree wizard"""
        group_users = []
        ac_group = self.env['ams_base.ac_group']
        if self._context.get('ac_group_id'):
            ac_group = self.env['ams_base.ac_group'].browse(self._context.get('ac_group_id'))

        for rec in self:
            group_user_vals = {
                'ac_group_id': self._context.get('ac_group_id'),
                'user_enroll_id': rec.id
            }
            group_user = ac_group.users_ids.filtered(lambda x: x.user_enroll_id.id == rec.id)
            if not group_user:
                group_users.append(group_user_vals)  # append user if not exist in users list

            ac_group.unlink_user_ids -= rec  # if user recently added then remove from unlink user

        if group_users:
            self.env['ams_suprema_ac.group_user'].create(group_users)

    def action_unlink_users(self):
        """ delete group users for selected users from tree wizard"""
        group_users = self.env['ams_suprema_ac.group_user'].search([
            ('ac_group_id', '=', self._context.get('ac_group_id')),
            ('user_enroll_id', 'in', self.ids)
        ])
        if group_users:
            group_users.unlink()

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    # endregion
