# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, time
from odoo.addons.ta.helper.helper import *


class AccessLevel(models.Model):
    _name = 'ams_suprema_ac.level'
    _inherit = ['ams_suprema_ac.base_model', "mail.thread", "mail.activity.mixin"]
    _description = 'Access Level'
    _sql_constraints = [('name_unique', 'unique(name)', 'Name must be unique!')]

    # region fields declaration ---------------------------------------------------
    level_number = fields.Integer(string="Level Number")
    name = fields.Char(required=True)
    # endregion

    # region relations fields declaration ------------------------------------------

    door_schedules_ids = fields.One2many('ams_suprema_ac.door_schedule', 'access_level_id',
                                         string="Door Schedules")

    ac_group_id = fields.Many2one('ams_base.ac_group', "Access Group")

    # endregion

    # region CRUD methods declaration -------------------------------------------------
    # def write(self, values):
    #     res = super(AccessLevel, self).write(values)
    #     return res
    #
    # def unlink(self):
    #     res = super(AccessLevel, self).unlink()
    #     return res

    # endregion
