# -*- coding: utf-8 -*-
import json

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class Door(models.Model):
    _name = 'ams_suprema_ac.door'
    _inherit = ['ams_suprema_ac.base_model', 'ams_suprema.connector', "mail.thread", "mail.activity.mixin"]
    _description = 'Door'
    _sql_constraints = [('name_unique', 'unique(name)', 'Name must be unique!')]

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    door_number = fields.Integer(string="Door No")
    name = fields.Char(string="Name", required=True)
    description = fields.Char(string="Description")

    auto_lock_timeout = fields.Integer(string="Auto Lock Timeout", default=5, help="Auto Lock Timeout in seconds")
    held_open_timeout = fields.Integer(string="Held Open Timeout", default=10, help="Held Open Timeout in seconds")
    instant_lock = fields.Boolean(string="Instant Lock")
    unlock_flags = fields.Integer(string="Unlock Flags")
    lock_flags = fields.Integer(string="Lock Flags")
    unconditional_lock = fields.Boolean(string="Unconditional Lock")
    entry_device_number = fields.Char(string="Entry Device No", related='entry_device_id.device_id', store=True)
    exit_device_number = fields.Char(string="Exit Device No", related='exit_device_id.device_id', store=True)
    entry_device_domain = fields.Char(string="Entry Device Domain", compute='_compute_entry_device_domain',
                                      help="compute field to filter relay,sensor,exit_button based on entry device")
    # endregion

    # region  Special
    dual_auth_schedule_id = fields.Integer(string="Dual Auth Schedule ID")
    dual_auth_device = fields.Selection([('entry_only', 'Entry Only'), ('exit_only', 'Exit Only'), ('both', 'Both')],
                                        string="Dual Auth Device")
    dual_auth_type = fields.Selection([('none', 'None'), ('last', 'Last')], string="Dual Auth Type")
    dual_auth_timeout = fields.Integer(string="Dual Auth Timeout", default=5, help="Dual Auth Timeout in seconds")
    # endregion

    # region  Relational
    entry_device_id = fields.Many2one('ams_suprema.device', string="Entry Device", required=True)
    exit_device_id = fields.Many2one('ams_suprema.device', string="Exit Device")

    relay_id = fields.Many2one('ams_suprema_ac.relay', string="Relay")
    sensor_id = fields.Many2one('ams_suprema_ac.sensor', string="Sensor")
    sensor_switch_type = fields.Selection(related="sensor_id.type", string="Sensor Switch Type")
    exit_button_id = fields.Many2one('ams_suprema_ac.exit_button', string="Exit Button",
                                     help="The ID of the exit device. You have to set it only if the door consists of"
                                          " two devices connected by RS485.")
    exit_button_switch_type = fields.Selection(related="exit_button_id.type", string="Exit Button Switch Type",store=True)

    # forced_open_actions = fields.One2many('ams_suprema_ac.device_action', 'door_id', string="Forced Open Actions")
    # held_open_actions = fields.One2many('ams_suprema_ac.device_action', 'door_id', string="Held Open Actions")
    dual_auth_group_ids = fields.Many2many('ams_base.device_group', string="Dual Auth Group IDs")

    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------

    @api.depends('entry_device_id')
    def _compute_entry_device_domain(self):
        for rec in self:
            domain = []
            if rec.entry_device_id:
                domain.append(('suprema_device_id', '=', rec.entry_device_id.id))

            rec.entry_device_domain = json.dumps(domain)

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------

    def write(self, values):
        # check configuration before update and after update if not equal notify user to apply changes
        if len(self) == 1 and 'sync' not in values and not self._context.get('error', False):
            old_door_config = self.prepare_door_info_dict()
            res = super(Door, self).write(values)

            new_door_config = self.prepare_door_info_dict()
            if old_door_config != new_door_config:
                res = super(Door, self).write({'sync': False})

        else:
            res = super(Door, self).write(values)

        return res
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_add_door(self):
        """ Apply configuration changes on door to entry or exit device"""
        for rec in self:
            door_config = rec.prepare_door_info_dict()
            if rec.entry_device_id:
                self.add_door(rec.entry_device_id.device_id_info, door_config)
            if rec.exit_device_id:
                self.add_door(rec.exit_device_id.device_id_info, door_config)

    def add_door(self, device_id_info, door_config={}):
        """call API gateway to add door configuration on device"""
        door_config = door_config or self.prepare_door_info_dict()
        response = self.client_gateway.add_door(device_id_info, self.name, door_config)
        if response.response_code == '1':
            self.reset_error()
            self.write({'sync': True, 'last_sync_time': fields.Datetime.now()})
        else:
            self.entry_device_id.handle_device_state(response)
            self.assign_error(response)

    def action_delete_door(self):
        """ Delete configuration door from entry device or exit device"""
        for rec in self:
            if rec.entry_device_id:
                rec.delete_door(rec.entry_device_id.device_id_info)

            if rec.exit_device_id:
                rec.delete_door(rec.exit_device_id.device_id_info)

    def delete_door(self, device_id_info):
        """call API gateway to delete door configuration from device"""
        response = self.client_gateway.delete_doors(device_id_info, [self.door_number])
        if response.response_code == '1':
            self.sync = False
            self.reset_error()
        else:
            self.entry_device_id.handle_device_state(response)
            self.assign_error(response)

    def action_get_doors(self):
        """ Get doors list from entry device or exit device"""
        for rec in self:
            if rec.entry_device_id:
                response = rec.client_gateway.get_doors(rec.entry_device_id.device_id_info)
                if response.response_code == '1':
                    # rec.sync = False
                    rec.logger.info(response.result)
                    rec.reset_error()
                else:
                    rec.entry_device_id.handle_device_state(response)
                    rec.assign_error(response)

            if rec.exit_device_id:
                response = rec.client_gateway.get_doors(rec.exit_device_id.device_id_info)
                if response.response_code == '1':
                    rec.logger.info(response.result)
                    rec.reset_error()
                else:
                    rec.entry_device_id.handle_device_state(response)
                    rec.assign_error(response)

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def prepare_door_info_dict(self):
        door_info_dict = {
            "door_id": self.door_number,
            "name": self.name,
            "entry_device_id": self.entry_device_id.device_number if self.entry_device_id else 0,
            "exit_device_id": self.entry_device_id.device_number if self.exit_device_id else 0,
            "auto_lock_timeout": self.auto_lock_timeout,
            "held_open_timeout": self.held_open_timeout,
            "instant_lock": self.instant_lock,
            "unlock_flags": self.unlock_flags,
            "lock_flags": self.lock_flags,
            "unconditional_lock": self.unconditional_lock,  # default value
            "forced_open_actions": [],  # TODO implement
            "held_open_actions": [],  # TODO implement
            "dual_auth_schedule_id": 0,
            "dual_auth_device": 0,
            "dual_auth_type": 0,
            "dual_auth_timeout": 15,
            "dual_auth_group_ids": [],
        }
        if self.relay_id:
            relay = {
                "device_id": self.relay_id.device_id,
                "port": self.relay_id.port,
            }
            door_info_dict.update({"relay": relay})

        if self.sensor_id:
            sensor = {
                "device_id": self.sensor_id.device_id,
                "port": self.sensor_id.port,
                "type": self.sensor_id.type,
            }
            door_info_dict.update({"sensor": sensor})

        if self.exit_button_id:
            button = {
                "device_id": self.exit_button_id.device_id,
                "port": self.exit_button_id.port,
                "type": self.exit_button_id.type,  # todo convert to enum
            }
            door_info_dict.update({"button": button})

        return door_info_dict
    # endregion
