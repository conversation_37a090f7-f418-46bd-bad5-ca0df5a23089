# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class DoorStatus(models.Model):
    _name = 'ams_suprema_ac.door_status'
    _inherit = ['ams_suprema_ac.base_model']
    _description = 'Door Status'

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    door_number = fields.Integer(string="Door No", index=True)
    is_open = fields.<PERSON><PERSON><PERSON>(string="Is Open")
    is_unlocked = fields.<PERSON><PERSON><PERSON>(string="Is Unlocked")
    held_open = fields.Bo<PERSON>an(string="Held Open")
    unlock_flags = fields.Integer(string="Unlock Flags")
    lock_flags = fields.Integer(string="Lock Flags")
    alarm_flags = fields.Integer(string="Alarm Flags")
    last_open_time = fields.Integer(string="Last Open Time")
    # endregion

    # region  Special
    # endregion

    # region  Relational
    door_id = fields.Many2one('ams_suprema_ac.door', string="Door")
    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # endregion