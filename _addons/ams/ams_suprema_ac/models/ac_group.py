# -*- coding: utf-8 -*-
import odoo
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, time
from odoo.addons.ta.helper.helper import *


class AccessGroup(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_base.ac_group"
    _inherit = ["ams_base.ac_group", "ams_suprema.connector"]

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic

    # endregion

    # region  Special
    # endregion

    # region  Relational
    ac_levels_ids = fields.One2many('ams_suprema_ac.level', 'ac_group_id', string='Access Levels')

    device_ids = fields.Many2many('ams_suprema.device', string="Devices")
    applied_device_ids = fields.Many2many('ams_suprema.device', string="Applied Devices",
                                          relation="ams_base_ac_group_applied_device_rel")

    users_ids = fields.One2many('ams_suprema_ac.group_user', 'ac_group_id', string="Users")
    # applied_users_ids = fields.One2many('ams_suprema_ac.group_user', 'ac_group_id',
    #                                     string="Applied Users")
    sync = fields.Boolean(tracking=True, readonly=False, compute="_compute_sync", store=True)
    unlink_user_ids = fields.Many2many('ams_suprema.user', string="Users To Unlink",
                                       help="when delete group user add it temporary in unlink_user_ids unitll apply config")
    update_door_config = fields.Boolean(store=True, help="if checked update door config on this group devices"
                                                         " without need to apply from door view")
    reset_ac_groups = fields.Boolean(store=True, help="if checked delete all ac groups | levels | doors from selected "
                                                      "devices before apply config")

    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------

    @property
    def users(self):
        """return current selected users"""
        if self.apply_on_all_users:
            users = self.env['ams_suprema.user'].search([('activate', '=', True)])
        else:
            users = self.users_ids.mapped('user_enroll_id')

        return users

    @property
    def users_to_add(self):
        """ return users should be added to this access group compared with applied users """
        # self.env['ams_suprema_ac.group_user'].search([('ac_group_id', '=', self.id),])
        # self.users_ids.filtered(lambda x: not x.config_applied).mapped('user_id')
        users = self.users_ids.filtered(lambda x: not x.config_applied).mapped('user_enroll_id')
        return users

    @property
    def users_to_unlink(self):
        """
        return users should be unlinked from this access group
        """
        users = self.unlink_user_ids  # self.applied_users_ids.mapped('user_id') - self.users
        return users

    @property
    def devices(self):
        """return current selected users"""
        devices = self.device_ids
        if self.apply_on_all_devices:
            devices = self.env['ams_suprema.device'].search([('activate', '=', True)])

        return devices

    @property
    def devices_to_add(self):
        """ return users should be added to this access group compared with applied devices """
        devices = self.devices - self.applied_device_ids
        return devices

    @property
    def devices_to_unlink(self):
        """
        return users should be unlinked from this access group
        """
        devices = self.applied_device_ids - self.devices
        return devices

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('users_ids', 'device_ids', 'applied_device_ids', 'unlink_user_ids')
    def _compute_sync(self):
        for rec in self:
            if (
                    len(rec.users_to_add) > 0
                    or len(rec.devices_to_add) > 0
                    or len(rec.users_to_unlink) > 0
                    or len(rec.devices_to_unlink) > 0):

                rec.sync = False
            else:
                rec.sync = True
                rec.last_sync_time = fields.Datetime.now()

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------

    def action_open_devices_tree_wizard(self):
        """open devices tree wizard to assign devices to this access group"""
        view_id = self.env.ref('ams_suprema_ac.device_view_list_wizard', raise_if_not_found=False)
        return {
            'name': _('Assign Devices'),
            'res_model': 'ams_suprema.device',
            'view_id': view_id.id if view_id else False,
            'view_mode': 'list',
            'target': 'new',
            'context': {
                'ac_group_id': self.id,
                'active_ids': self.device_ids.ids,
                'active_model': 'ams_suprema.device',
            },
            'type': 'ir.actions.act_window',
        }

    def action_open_users_tree_wizard(self):
        """open users tree wizard to assign users to this access group"""
        view_id = self.env.ref('ams_suprema_ac.user_view_list_wizard', raise_if_not_found=False)
        return {
            'name': _('Assign Users'),
            'res_model': 'ams_suprema.user',
            'view_mode': 'list',
            'view_id': view_id.id if view_id else False,
            'target': 'new',
            'context': {'ac_group_id': self.id},
            'type': 'ir.actions.act_window'
        }

    def action_open_device_action_views(self):
        """to display device action related to this group in list view"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Device Actions'),
            'res_model': 'ams_suprema.device_action',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': [('ac_group_id', '=', self.id)]
        }

    def action_apply_ac_group_config(self):
        """
        create device action for users on selected device with command set_ac_group to add access group
        for users on selected device
        """
        # if new devices added then call client SKD  to link device with access group
        new_added_devices = self._link_ac_group()

        # device removed then call client SKD  to unlink device with access group
        new_unlinked_devices = self._unlink_ac_group()

        # applied_devices = self.applied_device_ids + new_added_devices - new_unlink_devices

        self.applied_device_ids += new_added_devices
        self.applied_device_ids -= new_unlinked_devices

        for rec in self:
            # add ac group in devices
            device_action_vals = {
                'ac_group_id': rec.id,
                'action_type': 'add',
                'command_type': 'set_ac_group',
            }
            users_to_add = rec.users_to_add
            users_to_unlink = rec.users_to_unlink
            if users_to_add:
                self.env['ams_suprema.device_action']._create_device_action(rec.devices, users_to_add,
                                                                           device_action_vals)
                # self.users_ids.config_applied = True
            if users_to_unlink:
                device_action_vals.update(action_type='unlink')
                self.env['ams_suprema.device_action']._create_device_action(rec.devices, users_to_unlink,
                                                                           device_action_vals)

    def action_delete_ac_group(self):
        """ create device action for users on selected device with command set_ac_group to delete access group"""

        for rec in self:
            for device in rec.devices:
                # delete this group or all groups from device
                rec._delete_ac_config(device)

            # create device action to unlink this group for selected users
            new_device_action_vals = {
                'ac_group_id': rec.id,
                'action_type': 'unlink',
                'command_type': 'set_ac_group',
            }
            self.env['ams_suprema.device_action']._create_device_action(rec.devices, rec.users, new_device_action_vals)

    def action_get_ac_group(self):
        ...

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    def prepare_ac_group_dict(self):
        ac_group_dict = {
            'id': self.group_number,
            'name': self.name,
        }
        levels = []
        for level in self.ac_levels_ids:
            level_dict = {
                'id': level.level_number,
                'name': level.name,
                'door_schedules': [
                    {'door_id': dskd.door_id.door_number,
                     'schedule_id': dskd.schedule_id.schedule_number}
                    for dskd in level.door_schedules_ids],
            }
            levels.append(level_dict)

        ac_group_dict.update({'levels': levels})

        return ac_group_dict

    def _unlink_ac_group(self):
        """call client gateway to delete access group from devices
        return record set of success deleted ac_group from devices"""
        success_res_devices_ids = []
        error_res_devices_ids = []
        for rec in self:
            devices_to_unlink = rec.devices_to_unlink
            for device in devices_to_unlink:
                deleted_device = rec._delete_ac_config(device)
                if deleted_device:
                    success_res_devices_ids.append(device.id)
                else:
                    error_res_devices_ids.append(device.id)
                    # rec.assign_error(response)
        success_res_devices = self.env['ams_suprema.device'].browse(success_res_devices_ids)
        return success_res_devices

    def _delete_ac_config(self, device):
        """return device if success deleted ac_groups from device"""
        self.ensure_one()

        device = device or self.env['ams_suprema.device']
        ac_group_ids = [] if self.reset_ac_groups else [self.group_number]
        response = self.client_gateway.delete_ac_group(device.device_id_info, ac_group_ids)

        if response.response_code == '1':
            if self.update_door_config:
                self._delete_doors_config(self, device)

            self.applied_device_ids -= device  # remove device from applied devices on success delete
            if self.reset_ac_groups:
                device.ac_groups_ids = False  # remove all groups from device

            else:
                device.ac_groups_ids -= self  # remove  current group from device

            return device
        else:
            device.handle_device_state(response)
            return False

    def _link_ac_group(self):
        """call client gateway to add access group to devices
        return record set of success added ac_group to devices"""
        success_res_devices_ids = []
        error_res_devices_ids = []
        for rec in self:
            devices_to_add = rec.devices_to_add
            for device in devices_to_add:
                if rec.update_door_config:
                    self._add_doors_config(rec, device)

                response = self.client_gateway.add_ac_group(device.device_id_info, rec.name,
                                                            rec.prepare_ac_group_dict())

                if response.response_code == '1':
                    # rec.reset_error()
                    # TODO : add this group from device page many2many relation
                    device.ac_groups_ids += rec
                    success_res_devices_ids.append(device.id)
                else:
                    device.handle_device_state(response)
                    error_res_devices_ids.append(device.id)
                    # rec.assign_error(response)
        success_res_devices = self.env['ams_suprema.device'].browse(success_res_devices_ids)
        return success_res_devices

    def _add_doors_config(self, ac_group, device):
        """add doors config based in access level to specific device in specific access group"""
        ac_group = ac_group or self.env['ams_base.ac_group']
        device = device or self.env['ams_suprema.device']
        for level in ac_group.ac_levels_ids:
            for door_schedule in level.door_schedules_ids:
                door_schedule.door_id.add_door(device.device_id_info)

    def _delete_doors_config(self, ac_group, device):
        """delete door config based on access  to specific device in specific access group"""
        ac_group = ac_group or self.env['ams_base.ac_group']
        device = device or self.env['ams_suprema.device']
        for level in ac_group.ac_levels_ids:
            for door_schedule in level.door_schedules_ids:
                door_schedule.door_id.delete_door(device.device_id_info)

    def apply_user(self, user_enroll, apply=True):
        """apply_user to set value config_applied that indicate success link user with access group or not"""
        user_enroll = user_enroll or self.env['ams_suprema.user']
        user = self.users_ids.filtered(lambda r: r.user_enroll_id.id == user_enroll.id)
        if user:
            user.config_applied = apply

        # when (apply=False) unlink process finished remove user from unlink_user_ids
        if apply is False:
            self.unlink_user_ids -= user_enroll

    def _reset_ac_config(self):
        self.update_door_config = False
        # self.devices_to_add = []
        # self.devices_to_unlink = []

    """
      python write method prepare_ac_group_dict
   to convert grpc proto AccessGroup to python dictionary
  given:
  message AccessGroup {
    uint32 ID = 1;
    string name = 2;
    repeated uint32 levelIDs = 3;
  }

  message DoorSchedule {
    uint32 doorID = 1;
    uint32 scheduleID = 2;
  }

  message AccessLevel {
    uint32 ID = 1;
    string name = 2;
    repeated DoorSchedule doorSchedules = 3;
  }
  message UserAccessGroup {
    string userID = 1;
    repeated uint32 accessGroupIDs = 2;
  }
  message AccessGroup {
    uint32 ID = 1;
    string name = 2;
    repeated uint32 levelIDs = 3;
  }

  message DoorSchedule {
    uint32 doorID = 1;
    uint32 scheduleID = 2;
  }

  message AccessLevel {
    uint32 ID = 1;
    string name = 2;
    repeated DoorSchedule doorSchedules = 3;
  }
  message UserAccessGroup {
    string userID = 1;
    repeated uint32 accessGroupIDs = 2;
  }
      """
    # endregion
