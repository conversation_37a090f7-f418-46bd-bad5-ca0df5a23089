# -*- coding: utf-8 -*-

import odoo
from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class Device(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema.device"
    _inherit = ['ams_suprema.device']

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    exit_buttons_ids = fields.One2many('ams_suprema_ac.exit_button', 'suprema_device_id',
                                       string="Exit Buttons")
    sensors_ids = fields.One2many('ams_suprema_ac.sensor', 'suprema_device_id',
                                  string="Sensors")

    relays_ids = fields.One2many('ams_suprema_ac.relay', 'suprema_device_id',
                                 string="Relays")

    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_add_ac_group(self):
        """ Apply configuration changes on door to entry or exit device this action called from device form view"""
        for rec in self:
            for group in rec.ac_groups_ids:
                ac_group_config = group.prepare_ac_group_dict()
                response = rec.client_gateway.add_ac_group(rec.device_id_info, group.name, ac_group_config, delete=True)
                if response.response_code == '1':
                    rec.reset_error()
                    rec.update({'sync': True, 'last_sync_time': fields.Datetime.now()})
                else:
                    rec.handle_device_state(response)
                    rec.assign_error(response)

    def action_delete_ac_group(self):
        """ Delete AC group configuration  from device this action called from device form view"""
        for rec in self:
            rec.ac_groups_ids = False
            response = rec.client_gateway.delete_ac_group(rec.device_id_info)
            if response.response_code == '1':
                rec.reset_error()
            else:
                rec.handle_device_state(response)
                rec.assign_error(response)

    def action_get_ac_group(self):
        """ Get AC group configuration  from device """
        for rec in self:
            response = rec.client_gateway.get_ac_groups(rec.device_id_info)
            if response.response_code == '1':
                rec.logger.info(response.result)
                rec.reset_error()
            else:
                rec.handle_device_state(response)
                rec.assign_error(response)

            response = rec.client_gateway.get_ac_levels(rec.device_id_info)
            if response.response_code == '1':
                rec.logger.info(response.result)
                rec.reset_error()
            else:
                rec.handle_device_state(response)
                rec.assign_error(response)

    def action_link_ac_group(self):
        """ execute action from device tree wizard to select multiple devices and link with ac group """
        # read ac group from context
        ac_group_id = self.env.context.get('ac_group_id')
        devices = self
        if ac_group_id:
            ac_group = self.env['ams_base.ac_group'].browse(ac_group_id)
            ac_group.device_ids += devices

    def action_unlink_ac_group(self):
        """ execute action from device tree wizard to select multiple devices and unlink with ac group """
        # read ac group from context
        ac_group_id = self.env.context.get('ac_group_id')
        devices = self
        if ac_group_id:
            ac_group = self.env['ams_base.ac_group'].browse(ac_group_id)
            ac_group.device_ids -= devices

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def prepare_port_vals(self, name, max_count=1, port_type=False):
        """prepare port vals like relay ports, sensor ports,
         exit button ports based on max count of device capability """
        ports = []
        for i in range(max_count):
            port_vals = {
                'suprema_device_id': self.id,
                'port': i,
                'name': f'{name} {i}'
            }
            if port_type:
                port_vals.update(type=port_type)

            ports.append(port_vals)
        return ports

    def create_default_relays(self, max_count=1):
        relays_vals = self.prepare_port_vals('Relay')
        if relays_vals:
            self.env['ams_suprema_ac.relay'].create(relays_vals)

    def create_default_sensors(self, max_count=2):
        sensors_vals = self.prepare_port_vals('Sensor Input', max_count=max_count, port_type='normally_open')
        if sensors_vals:
            self.env['ams_suprema_ac.sensor'].create(sensors_vals)

    def create_default_exit_buttons(self, max_count=2):
        exit_buttons_vals = self.prepare_port_vals('ExitButton Input', max_count=max_count, port_type='normally_open')
        if exit_buttons_vals:
            self.env['ams_suprema_ac.exit_button'].create(exit_buttons_vals)

    def create_default_ports(self):
        """ override to create default ports for device based on device capability"""
        relay_max_count = self.device_id_info.max_relays
        max_input_ports = self.device_id_info.max_input_ports

        if not self.relays_ids:
            self.create_default_relays(max_count=relay_max_count or 1)
        if not self.sensors_ids:
            self.create_default_sensors(max_count=max_input_ports or 2)
        if not self.exit_buttons_ids:
            self.create_default_exit_buttons(max_count=max_input_ports or 2)

    # endregion
