# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, time
from odoo.addons.ta.helper.helper import *


class DoorSchedule(models.Model):
    _name = 'ams_suprema_ac.door_schedule'
    _inherit = 'ams_base.ac_schedule'
    _description = 'Door Schedule'

    # region Fields declaration ------------------------------------------
    door_number = fields.Integer(related='door_id.door_number', store=True)
    schedule_number = fields.Integer(related='schedule_id.schedule_number', store=True)

    # endregion

    # region relational fields declaration ---------------------------------

    access_level_id = fields.Many2one('ams_suprema_ac.level', string="Access Level")
    schedule_id = fields.Many2one('ams_base.ac_schedule', string="Schedule", required=True)
    door_id = fields.Many2one('ams_suprema_ac.door', string="Door", required=True)

    # endregion

    # def write(self, values):
    #     res = super(DoorSchedule, self).write(values)
    #     return res
    #
    # def unlink(self):
    #     res = super(DoorSchedule, self).unlink()
    #     return res
