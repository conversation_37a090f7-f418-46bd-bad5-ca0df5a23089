# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, time
from odoo.addons.ta.helper.helper import *


class AccessGroupUser(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema_ac.group_user"
    _description = "Access Group User"
    _sql_constraints = [('ac_group_user_unique', 'unique(user_enroll_id,ac_group_id)', 'User/Group must be unique!')]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------

    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic

    # endregion

    # region  Special
    # endregion

    # region  Relational
    # suprema.user

    user_enroll_id = fields.Many2one(string="User", comodel_name='ams_suprema.user', required=True, ondelete="cascade",
                                     index=True)
    ac_group_id = fields.Many2one('ams_base.ac_group', required=True, ondelete="cascade", index=True)
    enroll_number = fields.Char(related='user_enroll_id.enroll_number', store=True, index=True)
    ac_group_number = fields.Integer(related='ac_group_id.group_number', store=True)
    config_applied = fields.Boolean()  # config applied

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    def _compute_display_name(self):
        for rec in self:
            rec.display_name = f"[{rec.ac_group_id.name}] - {rec.user_id.name}"

    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    def unlink(self):
        for rec in self:
            if rec.config_applied:
                rec.ac_group_id.unlink_user_ids += rec.user_enroll_id

        return super().unlink()
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------

    # self.ac_group_id.users_ids |= group_users
    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------

    # endregion
