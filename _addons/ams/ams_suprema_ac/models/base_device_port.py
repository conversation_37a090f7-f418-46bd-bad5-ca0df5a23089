# -*- coding: utf-8 -*-


from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError


class BaseDevicePort(models.AbstractModel):
    _name = 'ams_suprema_ac.base_device_port'
    _description = 'Base Device Port'
    _inherit = ['ams_suprema_ac.base_model']

    # _sql_constraints = [('unique_device_port', 'unique(device_id, port)', 'Device Port must be unique')]

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    name = fields.Char()
    device_number = fields.Char(related='suprema_device_id.device_id', string="Device No",
                                help="Device Serial Number", index=True, store=True)
    port = fields.Integer(string="Port")
    type = fields.Selection(string="Switch Type",
                            selection=[('normally_open', 'Normally Open'), ('normally_closed', 'Normally Closed')])
    # endregion

    # region  Relational
    suprema_device_id = fields.Many2one('ams_suprema.device', string="Device")

    @property
    def device_id(self):
        try:
            return int(self.suprema_device_id.device_id)
        except:
            return 0

    # endregion
    # endregion

    # region ---------------------- TODO[IMP]: Compute Methods ---------------------------------

    def _compute_display_name(self):
        for record in self:
            name = record.name  #
            # str(record.device_number) + ' - ' + str(record.port)
            if record.suprema_device_id and name:
                name = f"[{record.suprema_device_id.name}] {record.name}"

            record.display_name = name
    # endregion
