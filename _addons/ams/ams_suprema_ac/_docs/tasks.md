

### AC Group
- assign device wizard from tree wizard
- add push_state (pending,fail,success)
- apply hierarchy groups
- action to open user related to ac_group

### Door
- add validation  on port number 
- add push_state (pending,fail,success)
- apply hierarchy

### Device
- add slave device 
https://supremainc.github.io/g-sdk/api/wiegand/#searchdevice

- add transfer user wizard
- add upload user button [done]
- enhance device search serial, name ,ip 
- base port override search with device id name,port 
- prevent delete online devices / or dedicated just enable archiving 
- enable archiving if there is data prevent delete

### User
- batch setting edit , assign ac_groups  , custom authentication ,devices , others
- batch enroll multi user
- group = policy groups
- add setting default enrollment year for employee
- add setting default enrollment days for visitor




Done  ---------------------------------------------------------------
### AC Group
- add model user_ac_groups , action_open_user_view to view enrollment data [done]
- assign device wizard from tree wizard
- assign users from wizard
- add push_state (pending,fail,success)
- apply hierarchy groups
- action to open user related to ac_group

### Door
- add push_state (pending,fail,success)
- apply hierarchy

### Device
- add model device_user with enrollment_state card,finger,face,ac_group 
- add one2many device_ac_levels to add push_state (pending,fail,success)
- add one2many device_ac_groups to add push_state (pending,fail,success)
- enhance device search serial, name ,ip 
- base port override search with device id name,port 
- prevent delete online devices / or dedicated just enable archiving 
- enable archiving if there is data prevent delete

### User
- batch setting edit , assign ac_groups  , custom authentication , others
- batch enroll multi user
- add one2many user_ac_groups to add push_state (pending,fail,success)
- group = policy groups
- add setting default enrollment year for employee
- add setting default enrollment days for visitor

