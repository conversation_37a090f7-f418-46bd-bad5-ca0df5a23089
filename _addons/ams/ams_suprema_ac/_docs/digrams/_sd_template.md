
### Sequence Diagram

```mermaid
sequenceDiagram
    participant A
    participant B
    
    A->>B: Message 1
    B-->>A: Message 2
```


### Sequence Diagram (Advance Example)

```mermaid
sequenceDiagram
    participant User
    participant ShoppingCart
    
    User->>ShoppingCart: create instance
    User->>ShoppingCart: add_item(item)
    User->>ShoppingCart: remove_item(item)
    User->>ShoppingCart: get_total()
    User->>ShoppingCart: checkout()
```
#### How to covert to code
    `class ShoppingCart:
        def __init__(self):
            self.items = []
    
        def add_item(self, item):
            self.items.append(item)
    
        def remove_item(self, item):
            if item in self.items:
                self.items.remove(item)
    
        def get_total(self):
            total = 0
            for item in self.items:
                total += item.price
            return total
    
        def checkout(self):
            total = self.get_total()
            print("Total amount:", total)
            # Perform payment and other checkout operations
    `


