### Access group

    An access group consists of access levels, which specify the accessible doors for specific schedules.
    Each user can belong to maximum 16 access groups. To assign access groups to users, you have to do the followings;
    
        1- Make schedules.
        2- Make doors.
        3- Make access levels using the schedules and doors.
        4- Make access groups using the access levels.
        5- Assign access groups to users using User.SetAccessGroup or User.SetAccessGroupMulti.
    
    In addition to doors, you can use access groups to limit access to specific floors in a lift.
    In this case, floor levels, which specify the accessible floors for specific schedules, should be defined.























enum Enum {
  option allow_alias = true;

  FIRST_ENUM_VALUE_MUST_BE_ZERO = 0;

  MAX_LEVELS_IN_GROUP = 128;
  MAX_SCHEDULES_IN_LEVEL = 128;
  MAX_NAME_LENGTH = 144;
}

python write method prepare_ac_group_dict
 to convert grpc proto AccessGroup to python dictionary
given:
message AccessGroup {
  uint32 ID = 1;
  string name = 2;
  repeated uint32 levelIDs = 3;
}

message DoorSchedule {
  uint32 doorID = 1;
  uint32 scheduleID = 2;
}

message AccessLevel {
  uint32 ID = 1;
  string name = 2;
  repeated DoorSchedule doorSchedules = 3;
}
message UserAccessGroup {
  string userID = 1;
  repeated uint32 accessGroupIDs = 2;
}
message AccessGroup {
  uint32 ID = 1;
  string name = 2;
  repeated uint32 levelIDs = 3;
}

message DoorSchedule {
  uint32 doorID = 1;
  uint32 scheduleID = 2;
}

message AccessLevel {
  uint32 ID = 1;
  string name = 2;
  repeated DoorSchedule doorSchedules = 3;
}
message UserAccessGroup {
  string userID = 1;
  repeated uint32 accessGroupIDs = 2;
}