


# Classess

from odoo import models, fields

### class Sensor
class Sensor(models.Model):
    _name = 'ams_suprema_ac.sensor'

    deviceID = fields.Integer()
    port = fields.Integer()
    type = fields.Selection([('normally_open', 'Normally Open'), ('normally_closed', 'Normally Closed')])

### class ExitButton
class ExitButton(models.Model):
    _name = 'ams_suprema_ac.exit_button'

    deviceID = fields.Integer()
    port = fields.Integer()
    type = fields.Selection([('normally_open', 'Normally Open'), ('normally_closed', 'Normally Closed')])

### class Status
class Status(models.Model):
    _name = 'ams_suprema_ac.status'

    doorID = fields.Integer()
    isOpen = fields.Boolean()
    isUnlocked = fields.Boolean()
    heldOpen = fields.Boolean()
    unlockFlags = fields.Integer()
    lockFlags = fields.Integer()
    alarmFlags = fields.Integer()
    lastOpenTime = fields.Integer()

### class DoorInfo
class DoorInfo(models.Model):
    _name = 'ams_suprema_ac.door_info'

    doorID = fields.Integer()
    name = fields.Char()

    entryDeviceID = fields.Integer()
    exitDeviceID = fields.Integer()

    relay_id = fields.Many2one('ams_suprema_ac.relay', string="Relay")
    sensor_id = fields.Many2one('ams_suprema_ac.sensor', string="Sensor")
    button_id = fields.Many2one('ams_suprema_ac.exit_button', string="Exit Button")

    autoLockTimeout = fields.Integer()
    heldOpenTimeout = fields.Integer()

    instantLock = fields.Boolean()
    unlockFlags = fields.Integer()
    lockFlags = fields.Integer()
    unconditionalLock = fields.Boolean()

    forcedOpenActions = fields.One2many('ams_suprema_ac.action', 'door_id', string="Forced Open Actions")
    heldOpenActions = fields.One2many('ams_suprema_ac.action', 'door_id', string="Held Open Actions")

    dualAuthScheduleID = fields.Integer()
    dualAuthDevice = fields.Selection([('entry_only', 'Entry Only'), ('exit_only', 'Exit Only'), ('both', 'Both')], string="Dual Auth Device")
    dualAuthType = fields.Selection([('none', 'None'), ('last', 'Last')], string="Dual Auth Type")
    dualAuthTimeout = fields.Integer()
    dualAuthGroupIDs = fields.Many2many('ams_suprema_ac.group', string="Dual Auth Group IDs")

### class Relay
class Relay(models.Model):
    _name = 'ams_suprema_ac.relay'

    # Define fields as needed

### class Action
class Action(models.Model):
    _name = 'ams_suprema_ac.action'

    # Define fields as needed

### class Group
class Group(models.Model):
    _name = 'ams_suprema_ac.group'

    # Define fields as needed
