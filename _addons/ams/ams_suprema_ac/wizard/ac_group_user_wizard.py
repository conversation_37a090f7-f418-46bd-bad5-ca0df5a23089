# -*- coding: utf-8 -*-
import odoo
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class AccessGroupUserWizard(models.TransientModel):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "ams_suprema_ac.group_user_wizard"
    _description = "AC Group User Wizard"
    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # todo add policy groups or user tags
    ac_group_id = fields.Many2one('ams_base.ac_group')
    user_levels = fields.Selection([('users', 'Users'), ('device_groups', 'Device Groups')]
                                   , default='device_groups')
    users_ids = fields.Many2many('ams_suprema.user')
    device_groups_ids = fields.Many2many('ams_base.device_group')
    operation_type = fields.Selection([('link', 'Link'), ('unlink', 'Unlink')], default='link')

    # endregion

    # region  Computed
    # endregion

    # endregion
    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------

    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_assign_users(self):
        """not used yet"""
        if self.operation_type == 'link':
            self._link_users()
        elif self.operation_type == 'unlink':
            self._unlink_users()

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    def _link_users(self):
        if self.operation_type == 'link':
            group_users = []
            for user in self.users_ids:
                group_user_vals = {
                    'ac_group_id': self.ac_group_id.id,
                    'user_id': user.id
                }
                group_users.append(group_user_vals)
                # group_users.append(odoo.Command.create(group_user_vals))

            self.env['ams_suprema_ac.group_user'].create(group_users)

            # self.ac_group_id.users_ids |= group_users

    def _unlink_users(self):
        if self.operation_type == 'unlink':
            group_users = []
            group_user_ids = self.env['ams_suprema_ac.group_user'].search([
                ('ac_group_id', '=', self.ac_group_id.id),
                ('user_id', 'in', self.users_ids.ids)
            ]).ids
            for group_user_id in group_user_ids:
                group_users.append(odoo.Command.unlink(group_user_id))

            self.ac_group_id.users_ids -= group_users

    # endregion
