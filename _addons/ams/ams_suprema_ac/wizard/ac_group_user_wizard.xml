<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="ac_group_user_view_wizard" model="ir.ui.view">
        <field name="name">ams_suprema_ac.group_user.form</field>
        <field name="model">ams_suprema_ac.group_user_wizard</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <!--                    <button name="action_reset" type="object" string="Reset" class="btn-outline-primary"/>-->
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="user_levels"/>
                            <field name="operation_type"/>
                        </group>

                    </group>
                    <group string="Device Groups" invisible="user_levels not in ['device_groups']">
                        <field name="device_groups_ids" widget="many2many_tags"/>
                    </group>

                    <group string="Users" invisible="user_levels not in ['users']">
                        <field name="users_ids" widget="many2many_tags"/>
                    </group>

                    <div>
                        <span>
                            <p class="text-muted">
                                <i class="fa fa-info-circle"></i>
                            </p>

                        </span>
                    </div>


                </sheet>
                <footer>
                    <button type="object" name="action_assign_users" string="Save"/>
                    <button string="Cancel | Close" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
