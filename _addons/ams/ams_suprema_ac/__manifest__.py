# -*- coding: utf-8 -*-

{
    "name": "ams_suprema_ac",
    "version": "1.0.0",
    "depends": [
        'base', 'ams_suprema'
    ],
    "author": "laplacesoftware",
    "category": "TA",
    "website": "https://www.laplacesoftware.com/",
    "images": ["static/description/images/main_screenshot.jpg"],
    "price": "0",
    "license": "OPL-1",
    "currency": "USD",
    "summary": "This module contain access control system for suprema devices.",
    "description": """

        An access group consists of access levels, which specify the accessible doors for specific schedules.
         Each user can belong to maximum 16 access groups. To assign access groups to users,
          you have to do the followings;
            
                1- Make schedules.
                2- Make doors.
                3- Make access levels using the schedules and doors.
                4- Make access groups using the access levels.
                5- Assign access groups to users using User.SetAccessGroup or User.SetAccessGroupMulti.
            
            In addition to doors, you can use access groups to limit access to specific floors in a lift. In this case,
             floor levels, which specify the accessible floors for specific schedules, should be defined.



""",
    "data": [
        # "security/groups.xml",
        "security/ir.model.access.csv",
        "views/ac_group_views.xml",
        "views/ac_level_views.xml",
        "views/ac_schedule_views.xml",
        "views/door_views.xml",
        "views/device_views.xml",
        "views/ac_user_views.xml",

        "wizard/transfer_wizard.xml",
        "wizard/ac_group_user_wizard.xml",

        "views/menu.xml",
    ],
    "installable": True,
    "auto_install": False,
    "application": True,
}
