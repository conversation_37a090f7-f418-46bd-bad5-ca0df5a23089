<odoo>
	<data>
		<record model="ir.module.category" id="module_category_emr_base">
			<field name="name">emr_base</field>
			<field name="description">emr_base Groups</field>
			<field name="sequence">10</field>
		</record>
		<record id="group_emr_base_user" model="res.groups">
			<field name="name">User</field>
			<field name="category_id" ref="module_category_emr_base"/>
		</record>
		<record id="group_emr_base_manager" model="res.groups">
			<field name="name">Manager</field>
			<field name="category_id" ref="module_category_emr_base"/>
		</record>
	</data>
</odoo>