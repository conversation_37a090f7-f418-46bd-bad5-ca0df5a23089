<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="ac_group_view_form" model="ir.ui.view">
        <field name="name">ams_base.ac_group.form</field>
        <field name="model">ams_base.ac_group</field>
        <field name="arch" type="xml">
            <form>
                <header>
                        <button name="action_apply_ac_group_config" string="Apply AC Config" icon="fa-rocket"
                                class="btn-outline-primary mx-1"
                                type="object" title="Apply AC group configuration on selected devices"></button>

                        <button name="action_delete_ac_group" string="Delete AC Config" icon="fa-remove"
                                class="btn-outline-danger mx-1"
                                type="object" title="Delete AC group configuration from selected devices"></button>

                        <button name="action_get_ac_group" string="Load AC Config" icon="fa-cloud-download"
                                class="btn-outline-primary mx-1" groups="base.group_no_one"
                                type="object" title="Load AC group configuration from device"></button>

                </header>
                <sheet>
                     <div class="alert alert-warning" invisible="sync==True">
                        <strong>Warning! </strong> You should apply configuration changes to this group.
                    </div>
                    <div name="button_box" class="oe_button_box">
                        <button type="object" name="action_open_devices_tree_wizard" class="oe_stat_button"
                                icon="fa-exchange" style="width: 100px !important;" string="Assign Devices"
                        title="You can assign or remove batch devices based on device group ">

                        </button>
                         <button type="object" name="action_open_users_tree_wizard" class="oe_stat_button"
                                icon="fa-user-o" style="width: 100px !important;" string="Assign Users"
                                 invisible="apply_on_all_users"
                        title="You can assign or remove batch users from this group ">

                        </button>
                        <button type="object" name="action_open_device_action_views" class="oe_stat_button"
                                icon="fa-list" style="width: 100px !important;" string="Action Queue">

                        </button>

                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="description"/>
                            <field name="group_number"/>
                            <field name="is_predefined" readonly="1"/>
                        </group>
                        <group>
                            <field name="apply_on_all_users"></field>
                            <field name="update_door_config"/>
                            <field name="sync" readonly="1"/>
                            <field name="last_sync_time" readonly="True"/>



                        </group>
                    </group>
                    <notebook>
                        <page string="Access Rules">
                            <group>
                                <field name="ac_levels_ids" widget="many2many_tags"/>

                            </group>

                            <!--                            <div class="row"> <span class="col-3">Devices</span>  <hr class="col-8"/></div>-->
                            <group string="Devices">
                                <div colspan="2" invisible="apply_on_all_devices">
                                    <span>
                                        <p class="text-muted">
                                            <i class="fa fa-info-circle"></i>
                                            click on action button to add or delete devices in this access group
                                            <button type="object" name="action_open_devices_tree_wizard"
                                                    icon="fa-exchange" class="btn-sm btn-link">

                                            </button>
                                        </p>

                                    </span>
                                </div>

                                <group>
                                    <field name="apply_on_all_devices"/>
                                    <field force_save="1" name="device_ids" readonly="1" widget="many2many_tags"
                                           invisible="apply_on_all_devices"/>
                                </group>


                            </group>


                        </page>

                        <page name="users" string="Users" invisible="apply_on_all_users">
                            <field name="users_ids" context="{'default_ac_group_id': id}"  >
                                <list create="0" edit="0">
                                    <field name="user_enroll_id"/>
                                    <field name="enroll_number" optional="show" readonly="1"/>
                                    <field name="config_applied" optional="hide" readonly="0"/>
                                    <field name="ac_group_id" optional="hide" readonly="1" />
                                    <field name="ac_group_number" optional="hide" readonly="1"/>
                                </list>
                            </field>
                        </page>

                        <page string="Access Levels List" groups="base.group_no_one">
                            <field name="ac_levels_ids"/>

                        </page>

                        <page string="Technical" groups="base.group_no_one">
                            <group>
                                <field name="reset_ac_groups"/>
                                <field name="applied_device_ids" widget="many2many_tags"/>
                                <field name="unlink_user_ids" widget="many2many_tags"/>
                            </group>
                            <group>

                            </group>


                        </page>


                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" groups="base.group_user"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_list-->
    <record id="ac_group_view_list" model="ir.ui.view">
        <field name="name">ams_base.ac_group.list</field>
        <field name="model">ams_base.ac_group</field>
        <field name="arch" type="xml">
            <list string="Desc">
                <field name="name"/>
                <field name="description"/>
                <field name="group_number" optional="hide"/>
                <field name="is_predefined" optional="show"/>

            </list>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_search-->
    <record id="ac_group_view_search" model="ir.ui.view">
        <field name="name">ams_base.ac_group.search</field>
        <field name="model">ams_base.ac_group</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="description"/>
                <field name="group_number"/>
                <field name="is_predefined"/>

            </search>
        </field>
    </record>


    <!--TODO[IMP]: <model_name> _action-->
    <record id="ac_group_action" model="ir.actions.act_window">
        <field name="name">Access Groups</field>
        <field name="res_model">ams_base.ac_group</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
