<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="door_view_form" model="ir.ui.view">
        <field name="name">Door Form</field>
        <field name="model">ams_suprema_ac.door</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_add_door" string="Apply Config" icon="fa-rocket"
                            class="btn-outline-primary"
                            type="object" title="Apply door configuration changes on door to entry or exit device"></button>

                    <button name="action_delete_door" string="Delete Config" icon="fa-remove"
                            class="btn-outline-danger"
                            type="object" title="Delete door configuration  from entry device or exit device"></button>

                    <button name="action_get_doors" string="Load Config" icon="fa-cloud-download"
                            class="btn-outline-primary"
                            type="object" title="Load door configuration  from entry device or exit device"></button>
                </header>
                <sheet>
                    <div class="alert alert-warning" invisible="sync==True">
                        <strong>Warning! </strong> You should apply configuration changes to entry or exist devices.
                    </div>

                    <group>
                        <group>
                            <field name="door_number"/>
                            <field name="name"/>
                            <field name="description"/>
                            <field name="error" invisible="True"/>
                        </group>
                        <group>
                            <field name="sync" readonly="True"></field>
                            <field name="last_sync_time" readonly="True" />
                        </group>
                    </group>
                    <group invisible="error==False">
                        <field name="error_msg" class="text-danger text-wrap"></field>
                    </group>
                    <notebook>
                        <page string="Configuration">
                            <group>
                                <group>
                                    <field name="entry_device_domain" invisible="1"/>
                                    <field name="entry_device_id"/>
                                    <field name="exit_device_id"/>
                                    <field name="relay_id" domain="entry_device_domain"/>
                                    <field name="sensor_id" domain="entry_device_domain"/>
                                    <field name="exit_button_id" domain="entry_device_domain"/>


                                </group>
                                <group>
                                    <field name="auto_lock_timeout"/>
                                    <field name="held_open_timeout"/>
                                    <field name="instant_lock"/>
                                    <field name="unlock_flags"/>
                                    <field name="lock_flags"/>
                                    <field name="unconditional_lock"/>
                                </group>

                            </group>

                        </page>
                        <page string="Dual Auth">
                            <group>
                                <group>
                                    <field name="dual_auth_schedule_id"/>
                                    <field name="dual_auth_device"/>
                                    <field name="dual_auth_type"/>
                                    <field name="dual_auth_timeout"/>

                                </group>
                                <group>
                                    <field name="dual_auth_group_ids" widget="many2many_tags"/>
                                </group>
                            </group>


                        </page>
                    </notebook>

                </sheet>
                <div class="oe_chatter">
                    <field groups="base.group_user" name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <record id="door_view_list" model="ir.ui.view">
        <field name="name">Door Tree</field>
        <field name="model">ams_suprema_ac.door</field>
        <field name="arch" type="xml">
            <list string="Doors">
                <field name="name"/>
                <field name="door_number" optional="hide"/>
                <field name="entry_device_id" optional="show"/>
                <field name="exit_device_id" optional="show"/>
                <field name="auto_lock_timeout" optional="hide"/>
                <field name="dual_auth_device" optional="hide"/>
            </list>
        </field>
    </record>
    <record id="door_action" model="ir.actions.act_window">
        <field name="name">Doors</field>
        <field name="res_model">ams_suprema_ac.door</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>
