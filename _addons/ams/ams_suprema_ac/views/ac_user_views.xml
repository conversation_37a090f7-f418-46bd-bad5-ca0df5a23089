<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="user_view_form_inherit" model="ir.ui.view">
        <field name="name">ams_suprema_ac.user.form_inherit</field>
        <field name="model">ams_suprema.user</field>
        <field name="inherit_id" ref="ams_suprema.user_view_form"/>

        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Access Groups">
                    <group>
                        <field name="ac_groups_ids" widget="many2many_tags"/>
                    </group>
                </page>

            </xpath>

        </field>
    </record>

    <record id="user_view_list_wizard" model="ir.ui.view">
        <field name="name">ams_suprema_ac.user.list</field>
        <field name="model">ams_suprema.user</field>
        <field name="arch" type="xml">
            <list string="Users">
                <header>
                    <button name="action_link_users" type="object" icon ="fa-plus"
                            string="Link User" class="btn-primary" ></button>

                    <button name="action_unlink_users" type="object" icon ="fa-remove"
                            string="Unlink User" class="btn-danger" ></button>

                </header>
                <field name="name"/>
                <field name="enroll_number"/>
                <field name="device_group_id"/>
                <field name="user_type"/>
                <field name="start_datetime" optional="hide"/>
                <field name="end_datetime" optional="hide"/>
                <field name="card_no" optional="hide"/>
                <field name="finger_quality" optional="hide"/>
                <field name="card_type" optional="hide"/>
                <field name="sync_user" optional="hide"/>
            </list></field>
    </record>
</odoo>
