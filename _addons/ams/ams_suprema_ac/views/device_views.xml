<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="device_view_form_inherit" model="ir.ui.view">
        <field name="name">ams_suprema_ac.device.form_inherit</field>
        <field name="model">ams_suprema.device</field>
        <field name="inherit_id" ref="ams_suprema.device_view_form"/>

        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Exit Buttons">
                    <field name="exit_buttons_ids">
                        <list editable="bottom">
                            <field name="name"></field>
                            <field name="port"></field>
                            <field name="type"></field>
                        </list>
                    </field>
                </page>
                <page string="Sensors">
                    <field name="sensors_ids">
                        <list editable="bottom">
                            <field name="name"></field>
                            <field name="port"></field>
                            <field name="type"></field>
                        </list>
                    </field>
                </page>
                <page string="Relays">
                    <field name="relays_ids">
                        <list editable="bottom">
                            <field name="name"></field>
                            <field name="port"></field>
                        </list>
                    </field>
                </page>
                <page string="Access Groups">
                    <group>
                        <field name="ac_groups_ids" widget="many2many_tags"/>
                        <field name="last_sync_time" readonly="1"/>
                    </group>
                    <div class="pt-3">
                        <button name="action_add_ac_group" string="Apply AC Config" icon="fa-rocket"
                                class="btn-outline-primary mx-2"
                                type="object" title="Apply AC group configuration changes device"></button>

                        <button name="action_delete_ac_group" string="Delete AC Config" icon="fa-remove"
                                class="btn-outline-danger mx-2"
                                type="object" title="Delete AC group configuration from device"></button>

                        <button name="action_get_ac_group" string="Load AC Config" icon="fa-cloud-download"
                                class="btn-outline-primary mx-2"
                                type="object" title="Load AC group configuration from device"></button>
                    </div>

                </page>
            </xpath>
        </field>
    </record>
    <record id="device_view_list_wizard" model="ir.ui.view">
        <field name="name">ams_suprema.device.list</field>
        <field name="model">ams_suprema.device</field>
        <field name="arch" type="xml">
            <list string="Desc">
                <header>
                    <button string="Link AC Group" name="action_link_ac_group" icon="fa-plus" type="object"
                    class="btn-primary" title="select multiple devices and link with AC group"></button>
                 <button string="Unlink AC Group" name="action_unlink_ac_group" icon="fa-remove" type="object"
                    class="btn-danger" title="select multiple devices and unlink with AC group"></button>
               </header>
                <field name="name"/>
                 <field name="state" decoration-success="state=='online'"
                decoration-danger="state=='offline'"
                decoration-warning="state=='pending'"/>
                <field name="device_group_id"/>
                <field name="device_id"/>
                <field name="ip"/>
                <field name="status" optional="hide"/>
                <field name="type" optional="hide"/>
                <field name="mac_addr" optional="hide"/>
                <field name="model_name" optional="hide"/>
                <field name="use_private_auth" optional="hide"/>
            </list>
        </field>
    </record>
</odoo>
