<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ks_create_dashboard_wizard_form" model="ir.ui.view">
        <field name="name">ks.dashboard.wizard.form.view</field>
        <field name="model">ks.dashboard.wizard</field>
        <field name="arch" type="xml">
            <form string="Dashboard Manager">
                <div class="d-flex flex-column w-100 h-100 pb-lg-3 mb-3 pb-0  ">
                    <label for="name" class="form-label">Dashboard Name</label>
                    <field name="name" placeholder="Enter Dashboard Name" class="form-control form-input-box validation"/>
                </div>
                <group class="row ks_dash_row">
                    <group class="d-flex flex-column w-95 h-95">
                        <div class="mb-3 mb-lg-0">
                            <label for="ks_menu_name" class="form-label">Menu Name</label>
                            <field name="ks_menu_name" placeholder="Enter Menu Name" class="form-control form-input-box validation"/>
                        </div>
                        <div>
                            <label for="ks_top_menu_id" class="form-label">Show Under Menu</label>
                            <field name="ks_top_menu_id" placeholder="Select Show Under Menu" class="form-control form-input-box validation"/>
                        </div>
                    </group>
                    <group class="d-flex flex-column w-90 h-90">
                        <div class="mb-3 mb-lg-0">
                            <label for="ks_template" class="form-label">Template</label>
                            <field name="ks_template" placeholder="Select Template" class="form-control form-input-box validation"/>
                        </div>
                        <div>
                            <label for="ks_sequence" class="form-label">Sequence</label>
                            <field name="ks_sequence" placeholder="Enter Sequence" class="form-control form-input-box validation"/>
                        </div>
                    </group>
                </group>

                <h3 class="oe_grey" invisible="('ks_template','!=',8)or('ks_template','!=',2)or
                ('ks_template','!=',3) or ('ks_template','!=',4) or ('ks_template','!=',5) or ('ks_template','!=',6) or ('ks_template','!=',7)">
                    Preview
                </h3>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_template1.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',2)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_template2.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',3)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_template3.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',4)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_account_template.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',5)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_crm_template.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',6)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_inventory_template.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',7)"/>

                <img src="/ks_dashboard_ninja/static/description/templates/ks_sale_template.png"
                     alt="#" class="img img-fluid"
                     style="padding: 10px; border: 2px solid;"
                     invisible="('ks_template','!=',8)"/>

                <footer>
                    <button class="dash-btn-red" name="ks_create_record" type="object" string="Save"/>
                    <button string="Cancel" class="oe_link dash-default-btn bg-white" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="ks_create_dashboard_wizard" model="ir.actions.act_window">
        <field name="name">Add New Dashboard</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">ks.dashboard.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="ks_create_dashboard_wizard_form"/>
        <field name="target">new</field>
    </record>
</odoo>
