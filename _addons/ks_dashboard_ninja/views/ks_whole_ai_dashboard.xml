<odoo>
    <data>

        <record id="ks_ai_whole_dashboard_form_view" model="ir.ui.view">
            <field name="name">ks_dashboard_ninja.ai.dashboard form</field>
            <field name="model">ks_dashboard_ninja.ai_dashboard</field>
            <field name="arch" type="xml">
                <form string="AI Dashboard" class="ks_import_dashboard_d_none">
                    <group class="row ks_dash_row">
                        <group class="d-flex flex-column w-95 h-95">
                            <div class="mb-3 mb-lg-0">
                                <label for="ks_import_model_id" class="form-label">Model</label>
                                <field name="ks_import_model_id" placeholder="Enter Model" class="form-control form-input-box validation"/>
                            </div>
                            <div>
                                <label for="ks_dash_name" class="form-label">Dashboard Name</label>
                                <field name="ks_dash_name" placeholder="Enter Dashboard Name" class="form-control form-input-box validation"/>
                            </div>
                        </group>
                        <group class="d-flex flex-column w-90 h-90">
                            <div class="mb-3 mb-lg-0">
                                <label for="ks_menu_name" class="form-label">Menu Name</label>
                                <field name="ks_menu_name" placeholder="Enter Menu Name" class="form-control form-input-box validation"/>
                            </div>
                            <div>
                                <label for="ks_top_menu_id" class="form-label">Show Under Menu</label>
                                <field name="ks_top_menu_id" placeholder="Show Under Menu" class="form-control form-input-box validation"/>
                            </div>
                            <field name="ks_template" invisible="1"/>
                        </group>
                    </group>
                    <footer>
                        <button name="ks_do_action" string="Generate dashboard with AI" type="object"
                                class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>


        <record id="ks_dashboard_ninja_ai_dashboardaction" model="ir.actions.act_window">
            <field name="name">AI whole Dashboard</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ks_dashboard_ninja.ai_dashboard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="ks_ai_whole_dashboard_form_view"/>
            <field name="target">new</field>
        </record>


    </data>

</odoo>