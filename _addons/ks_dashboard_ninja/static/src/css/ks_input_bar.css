.input_bar{
    border-radius: 8px;
    border: 0.5px solid #DADADA;
    background: #FF;
    color: #A8A8A8 !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    height: 40px;
    padding-left: 24px !important;
}
input#ks_selection_field:focus {
    border: 1px solid #797979;
}
.ks_input_custom{
    position: relative;
}
.ks_input_custom svg{
    position: absolute;
    right: 0px;
    top: 9px;
}
.search_style{
    text-align:center;
    font-size:14px;
    color: #444;
    border-radius: 5px;
}

.search_style{
     overflow-y: scroll;
     max-height: calc(100vh - 400px);
}
.search_style::-webkit-scrollbar {
  width: 6px;
}

/* Track */
.search_style::-webkit-scrollbar-track {
  background: transparent;
}

/* Handle */
.search_style::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
.search_style::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.ks_response_container_list{
    border-bottom: 1px solid #d4d4d4;
    border-left: 1px solid transparent;
    padding: 17px 6px 18px 24px;
    background-color: #fff;
    color: #A8A8A8;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
.ks_response_container_list:hover{
    color: #9C5789;
    border-left: 2px solid #9C5789;
}
.ks_response_container_list:hover svg path{
    stroke: #9C5789;
}
.ks_response_container_list:hover svg line{
    stroke: #9C5789;
}

.ks_dashboard_option_category span.ks_dashboard_option{
color: #9C5789;
height: 21px;
padding: 3px;
font-size: 12px;
font-style: normal;
font-weight: 400;
line-height: normal;
}
.ks_dashboard_option_category span{
    display: flex;
    align-items: center;
    justify-content: start;
    width: fit-content;
    font-size: 8px;
    background: #EEE;
    padding: 0px 10px;
    margin: 0px;
    border-radius: 4px;
    color: #888;
    font-weight: 600;
    text-transform: uppercase;
    margin-right: 6px;
    letter-spacing: 0.5px;
    margin-top: 0px;
    }
    .ks_dashboard_ai_chart_icons{
    display: flex;
    align-items: center;
    justify-content: start;
    width: fit-content;
    padding: 0px 10px;
    margin: 0px;
    font-weight: 600;
    margin-top: 0px;
    }
    .ks_border_class{
        border-radius: 8px !important;
        border: 1px solid #DADADA !important;
        background: #FFF !important;
        margin-top: 8px !important;
        height: 40px !important;
        padding: 11px 11px 11px 24px !important;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }
    .ks_border_class .ks_response{
        width: 100%;
    }