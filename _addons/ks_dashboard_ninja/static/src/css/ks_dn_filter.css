.dropdown-menu.show.ks_dn_filter_dropdown_container {
    display: flex !important;
}

.dn_dynamic_filter_selected > span{
    font-weight: bold;
}

.dn_dynamic_filter_selected > span::before{
    font-family: FontAwesome;
    position: absolute;
    left: 6px;
    content: "\f00c";
}

.ks_dn_pre_filter_menu .df_selection_text {
    padding: 3px 4px;
}

.ks_dn_pre_filter_menu {
    list-style: none;
    padding-left: 0px;
}

.ks_dn_pre_filter_menu > li {
    padding-left: 20px;
}

.ks_dn_pre_filter_menu > li:hover {
    color: #333333;
    background-color: #f5f5f5;
}

.ks_dn_filter_dropdown_container > div  {
/*
    width: 225px;
*/
min-width :240px
}

.ks_dn_filter_dropdown_container > div > div.o_generator_menu > span {
    margin-left: 20px;
    font-weight: bold;
}

.ks_dn_filter_dropdown_container > div > div.o_generator_menu .ks_dn_custom_filter_input_container_section {
    position: relative;
}

.ks_custom_filter_section_delete {
    cursor: pointer;
    position: absolute;
    top: 5px;
    left: auto;
    bottom: auto;
    right: -15px;
}

.o_or_filter {
    position: absolute;
    top: 0px;
    left: -14px;
    bottom: auto;
    right: auto;
}

.ks_dn_or_container {
    margin-top: 10px;
}

.ks_dn_filter_applied_container {
    width: 500px !important;
    padding: 0px 10px;
    margin-left: 5px;
    border-width: 1px;
    border-color: #e5e5e5;
}


.ks_dn_pre_model_text {

}
@media screen and (max-width: 575px) {
  .ks_o_add_favorite.o_add_favorite {
    display: none;
}
   .ks_dashboard_top_menu{
    margin:0 auto;
   }
}