.encapsulated-form-view {
    background-color: $color-white !important;
    padding: 24px 40px !important;

    .o_dropdown_button::after {
        display: none !important;
    }

    .input-full {
        .o_input {
            width: 100% !important;
        }
    }

    .o-checkbox {
        &.form-check .form-check-input {
            border-color: $color-placeholders;
        }

        .form-check-input[type="checkbox"]:checked {
            background-color: $color-E7495E !important;
            border-color: $color-E7495E !important;
        }
    }

    .disabled {
        opacity: 50%;
        pointer-events: none;
    }

    // buttons
    .o_field_domain_dialog_button,
    .o_form_button_save {
        background-color: $color-primary-main !important;
        border: 1px solid $color-primary-main !important;
        border-radius: 10px;
        min-height: 40px;
        padding: 0 12px;
        font-size: $font-16;
        font-weight: $f-w-400;
        line-height: 19.6px;
        color: $color-white;
        cursor: pointer;
        height: 100%;

        &.disabled {
            opacity: 50%;
            pointer-events: none;
            cursor: not-allowed;
        }

        &:focus-visible {
            outline: none !important;
        }
    }

    .o_field_domain_dialog_button {
        min-height: 34px !important;
        padding: 0 7px !important;
    }

    .o_form_button_save {
        min-width: 80px;
        margin-left: 10px;
        min-height: 35px;
    }

    .o_form_button_cancel {
        border: 0.81px solid $color-E5E7EB;
        font-size: $font-16;
        font-weight: $f-w-500;
        line-height: 22.4px;
        text-align: left;
        color: $color-1E1E1E;
        border-radius: 8px;
        min-height: 35px;
        text-align: center;
        height: 100%;
        padding: 0 20px;
        background-color: $color-white;
        min-width: 80px;

        &:hover {
            background-color: $color-secondary-bg;
        }

        &:focus {
            border: 1px solid $color-E7495E;
        }
    }

    // buttons-end



    .dash-btn-red {


        & .btn.btn-primary {
            background-color: $color-primary-main !important;
            border: 1px solid $color-primary-main !important;
            border-radius: 10px;
            min-height: 40px;
            padding: 0 12px;
            font-size: $font-16;
            font-weight: $f-w-400;
            line-height: 19.6px;
            color: $color-white;
            cursor: pointer;

            &:hover {
                background-color: $btn-hover !important;
                color: $color-white !important;
            }

            span {
                color: inherit !important;
                font-size: $font-16;
                font-weight: $f-w-500;
            }
        }

        &.disabled {
            opacity: 50%;
            pointer-events: none;
            cursor: not-allowed;
        }

        &:focus-visible {
            outline: none !important;
        }


    }

    .o_control_panel {
        background-color: transparent !important;
        border-bottom: none !important;
        padding-bottom: 28px !important;

        .o_form_status_indicator_buttons {
            gap: 8px;
        }
    }

    .o_content {
        border: 1px solid $color-E5E7EB;
        background-color: $color-F5F8FB !important;
        border-radius: 20px;
        padding: 20px 24px !important;
        overflow: visible !important;
    }

    .left-70 {
        width: 97% !important;
    }

    .o_form_renderer {
        background-color: transparent !important;
        max-height: calc(100vh - 231px);
        overflow: auto;
        @include custom-scrollbar;

        .encapsulated-input-group {
            grid-template-columns: 1fr !important;
        }

        .o_cell {
            width: 100% !important;


        }


        .o_form_label {
            font-size: $font-12;
            font-weight: $f-w-400 !important;
            // line-height: 9.66px;
            text-align: left;
            color: $color-black !important;
        }

        .o_field_ks_dashboard_item_type {
            .chart-list {
                grid-template-columns: repeat(auto-fill, minmax(68px, 1fr));
                // grid-template-rows: repeat(3, 1fr);
                grid-auto-rows: auto;
                padding-left: 0;
                max-height: initial;
                overflow: visible;
                background-color: transparent;
                gap: 12px !important;
                width: 97% !important;

                @include max-1500 {
                    width: 100% !important;
                }

                @include custom-scrollbar;

                .chart-content {
                    font-family: "Poppins", sans-serif;
                    font-size: $font-12 !important;
                    font-weight: $f-w-500 !important;
                    line-height: 15px;
                    color: $color-black !important;
                }

                .chart-card {
                    border: 1px solid $color-E5E7EB !important;
                    min-height: 69px;
                    margin-bottom: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .chart-thum {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 47px !important;

                        svg {
                            stroke: $color-737791;
                        }

                    }

                    &:hover {
                        background-color: $color-FFF5F5 !important;


                    }






                }

                .active {
                    .chart-card {
                        background-color: $color-FFF5F5 !important;
                        border: 0.76px solid $color-E7495E !important;

                        .chart-thum {
                            svg {
                                stroke: $color-E7495E;
                            }
                        }
                    }

                    .chart-content {
                        // color: $color-E7495E !important;
                    }
                }
            }
        }

        .chart-form-detail {
            row-gap: 24px;
        }
    }

    .ks_dashboard_theme_input_container {
        height: 40px;
        width: 40px;
        border-radius: 50%;

        input[type="checkbox"] {
            height: 100%;
            width: 100%;
            border-radius: 50%;
            overflow: hidden;

            border: 1px solid $color-E5E7EB !important;

            &::before {
                height: 100% !important;
                align-items: center;
                width: 100% !important;
            }

            // &:checked::before {
            //     content: "" !important;
            // }

            // &:checked::after {
            //     content: '';
            //     display: block;
            //     height: 20px;
            //     width: 20px;
            // }
        }
    }

    input[type="range"] {
        -webkit-appearance: none;
        width: 100%;
        background: #E7C6C9;
        outline: none;
        transition: opacity 0.3s;
        opacity: 0.7;
        border-radius: 8px;

        &:hover {
            opacity: 1;
        }

        &::-webkit-slider-runnable-track {
            width: 100%;
            height: 8px;
            background-color: transparent;
            border-radius: 5px;
        }

        &::-ms-track {
            width: 100%;
            height: 6px;
            background: transparent;
            border-color: transparent;
            color: transparent;
        }

        &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: #E7495E;
            border-radius: 50%;
            margin-top: -5px;
            cursor: pointer;
            transition: background-color 0.3s;
            box-shadow: 0px 0px 4px 0px #00000040;
        }

        &::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: #E7495E;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0px 0px 4px 0px #00000040;

        }

        &::-ms-thumb {
            width: 18px;
            height: 18px;
            background: #E7495E;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0px 0px 4px 0px #00000040;

        }
    }

    // radio
    input[type="radio"] {
        &.form-check-input:checked {
            background-color: #E7495E !important;
            border-color: #E7495E !important;
        }
    }

    .encapsulated-preview {
        font-size: $font-16;
        font-weight: $f-w-500;
        line-height: 24px;
        letter-spacing: 0.01em;
        text-align: left;
        color: $color-black;
        margin-bottom: 12px;
    }

    .encapsulated-preview-box {
        border-radius: 20px;
        // text-align: cent.ks_dashboard_item_header_l6: center;
        // align-items: center;

        .ks_dashboard_kpi {
            padding: 12px 10px;
        }

        .encapsulated-tile-container {
            position: relative !important;
            border-radius: 16px;

            .layout-6-box {
                padding: 0 !important;

                .layout-6-container {
                    flex: 1;
                    width: 100% !important;
                    display: flex;
                    align-items: end;
                    flex-direction: row;
                    justify-content: flex-start;

                    .ks_dashboard_icon_l2 {
                        padding: 20px 0 10px 13px;
                        display: flex;
                        justify-content: flex-start;
                        width: fit-content;

                        span {
                            font-size: 31px;

                        }
                    }
                }
            }

            .dashboard-item-name {
                text-align: justify !important;
                font-size: 16px !important;
                font-weight: 500;
                line-height: 24px;
                text-align: left;
                // color: #241C1D !important;
                margin-top: 10px !important;
                margin-left: 0;
            }

            .dashboard-item-icon {
                top: 0 !important;
                left: 0 !important;
                position: relative;
            }

            .layout-4-icon {
                right: -4px;
            }

            .dashboard-item-data {
                text-align: justify !important;
                font-size: $font-24;
                font-weight: $f-w-600;
                line-height: 32px;
                text-align: left;
                // color: $color-black !important;
                margin-left: 0;
                padding: 0 !important;

                .ks_dashboard_item_domain_count_l5 {
                    font-size: inherit !important;
                    margin-left: 0 !important;
                }
            }

            .img-bg {
                background-color: $color-white;
            }

            .ks_dashboard_item_main_body_l2 {
                flex: 1;
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            .ks_dashboard_icon_l3 {
                border-top-left-radius: 6px !important;
            }

            .layout-4-icon {
                border-bottom-right-radius: 6px !important;
            }

            .ks_dashboard_item_header {
                position: absolute;
                right: 10px;
                top: 10px;
            }

            .encapsulated-tile-1 {
                .ks_dashboard_icon {
                    span {
                        font-size: 4em !important;
                    }
                }
            }
        }

        .min-h-180 {
            min-height: 180px;
            height: 100%;
        }



        .minimum-size {
            min-width: 180px;
            min-height: 185px;
            max-width: 250px;
        }
    }

    .o_form_image_controls {
        button {
            border: 1px solid $color-CDAAAA;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            background: $color-white;
            margin-right: 8px;
        }
    }

    .note-para {
        font-size: $font-12;
        font-weight: $f-w-400;
        line-height: 9.66px;
        text-align: left;
        color: $color-black;
    }
}

.encapsulated-textarea {

    // border: 1px solid $color-D1D5DB;
    // padding: 16px 12px;
    // border-radius: 8px;
    // background-color: $color-white;
    &.o_field_invalid {
        textarea {
            border-color: $color-danger !important;
        }
    }

    textarea {
        border: 1px solid $color-D1D5DB !important;
        padding: 16px 12px !important;
        border-radius: 8px;
        background-color: $color-white;
        min-height: 179px;
        max-height: 179px;
        overflow: auto !important;
        @include custom-scrollbar;

        // border: none !important;

        // &:hover {
        //     border: none !important;
        // }
        &::placeholder {
            font-size: $font-14;
            font-weight: $f-w-400;
            line-height: 20.99px;
            color: $color-placeholders;
        }
    }

}

.encapsulated-textarea-box {
    grid-template-columns: 1fr !important;
}

.encapsulated-multiliner {
    table {
        thead {
            tr {
                th {
                    width: 50% !important;

                    & .o_list_number_th {
                        text-align: left !important;
                    }
                }
            }
        }
    }
}

.encapsulated-col-box {
    .o_form_label {
        font-size: $font-16 !important;
        font-weight: $f-w-500 !important;
        line-height: 24px !important;
        text-align: left;
        color: $color-dark-black !important;
    }
}

.encapsulated-r-g-48 {
    row-gap: 48px;
}

.r-g-24 {
    row-gap: 24px;
}

.col-g-3 {
    column-gap: 16px;
}

.encapsulated-icons-box {
    display: flex;
    gap: 8px;
    margin-bottom: 18px !important;

    .o_vertical {
        display: flex;
        flex-direction: row;
        gap: 8px;
    }
}

.encapsulated-ks-icon {
    display: block;
    width: 100% !important;

    >div {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    .o_form_image_controls {
        display: flex;
        column-gap: 8px;
        align-items: center !important;
    }
}

.r-g-16 {
    row-gap: 16px;
}

.encapsulated-input-common {
    border: 0.81px solid $color-D1D5DB !important;
    border-radius: 6px;
    padding: 12px 10px !important;
    font-size: 14px;
    font-weight: 400;
    line-height: 18.52px;
    text-align: left;
    color: $color-black;
    background-color: $color-white !important;
}

.encapsulated-d-content {
    display: contents !important;
}

.ks_body_class .nav-tabs-wrapper {
    word-break: break-word;
}