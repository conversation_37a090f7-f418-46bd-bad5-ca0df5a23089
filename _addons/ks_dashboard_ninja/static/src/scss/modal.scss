.ks_body_class .modal-dialog {
    max-width: 764px;

    .modal-content {
        border-radius: 16px;

        .modal-header {
            padding: 12px 16px;
            background-color: $color-bg-main;
            border-radius: 16px 16px 0 0;
            border-bottom: 0;

            .modal-title {
                font-size: $font-16;
                font-weight: $f-w-500;
                line-height: 22.4px;
                text-align: left;
                color: $color-black;
            }

            .btn-close {}
        }

        .modal-body {
            .o_form_label {
                font-size: $font-12;
                font-weight: $f-w-400;
                line-height: 9.66px;
                letter-spacing: 0.0015em;
                text-align: left;
                color: $color-black;
                margin-bottom: 8px;

                // &:not(:focus-within) {
                //     color: $color-danger !important;
                // }
                &:focus {
                    border: 1px solid $color-black;
                }
            }

            .ks_dash_row {
                .o_cell {
                    width: 100% !important;
                }

                .o_inner_group {
                    gap: 32px 16px !important;
                }
            }

        }

        .modal-footer {
            border: 0;

            footer {
                justify-content: flex-start !important;

                .btn {
                    @include max-768 {
                        width: auto !important;
                    }
                }
            }
        }
    }
}

.ks_body_class .form-input-box.form-control {
    border: none;
    padding: 0;

    input {
        border: 0.81px solid $color-D1D5DB !important;
        border-radius: 6px;
        padding: 12px 10px !important;
        font-size: $font-14;
        font-weight: $f-w-400;
        line-height: 18.52px;
        text-align: left;
        color: $color-black;

        &::placeholder {
            font-size: $font-16;
            font-weight: $f-w-400;
            line-height: 18.52px;
            text-align: left;
            color: $color-placeholders;
        }
    }

    .o_field_many2one_selection {
        .o_dropdown_button {
            visibility: visible !important;
        }
    }

}