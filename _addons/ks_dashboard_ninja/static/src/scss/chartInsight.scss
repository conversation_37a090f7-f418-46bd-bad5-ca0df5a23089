.common-tab-pills {
    .nav {
        border-bottom: 1px solid $color-E5E7EB !important;

        .nav-link {
            font-size: $font-14;
            font-weight: $f-w-400;
            line-height: 24px;
            letter-spacing: 0.01em;
            text-align: left;
            color: $color-paragraph !important;
            padding: 16px !important;
            &:hover {
                transition: 0.2s linear;
                transform: scale(1.1);
                cursor: pointer !important;
                }

            svg {
                stroke: $color-black;
            }

            &.active {
                color: $color-E7495E !important;
                background-color: $color-white;
                border-bottom: 2px solid $color-E7495E;
                border-radius: 0 !important;
                transition: 0.2s linear;
                transform: scale(1.1);
                cursor: pointer !important;

                svg {
                    stroke: $color-E7495E;
                }
            }
        }
    }


}

.share-switch {
    .bg-F5F8FB {
        background-color: $color-bg-main;
    }

}

.expalin-content-box {
//     border: 1px solid $color-E5E7EB;
//     background-color: $color-bg-main;
    min-height: 581px;
    border-radius: 20px;
    position: relative;

    .title {
        font-size: $font-16;
        font-weight: $f-w-500;
        line-height: 22.4px;
        text-align: left;
        color: $color-black;

        @include max-992 {
            font-size: $font-12;
        }
    }

    .description-box {
        max-height: calc(100vh - 441px);
        overflow: auto;

        @include max-992 {
            max-height: calc(100vh - 387px);

        }

        p {
            font-size: 12px;
            font-weight: 400;
            line-height: 19.2px;
            text-align: left;
            color: $color-black;
        }
    }

}

.chat-ai-box {
//     max-height: calc(100vh - 415px);
//     overflow: auto;
    padding-right: 11px;
     height: auto;
    padding: 0 10px !important;

    @include max-992 {
        max-height: calc(100vh - 387px);

    }

    .chat-sec {
                max-height: calc(100vh - 350px);
               overflow: auto;
               @include custom-scrollbar;
        .left,
        .right {
            .title {
                font-size: $font-14;
                font-weight: $f-w-500;
                line-height: 19.6px;
                text-align: left;
                color: $color-black;

                @include max-992 {
                    font-size: $font-12;
                }
            }

            .answers,
            .questions {
                font-size: $font-12;
                font-weight: $f-w-400;
                line-height: 19.2px;
                text-align: left;
                color: $color-black;

                @include max-992 {
                    font-size: $font-10;
                }
            }



            .user-icon {
                height: 30px;
                width: 30px;
                border-radius: 5px;
                font-size: $font-14;
                font-weight: $f-w-500;
                line-height: 32px;
                text-align: left;
                color: $color-black;
                background-color: $color-white;
            }

        }
    }

    .typer-box {
        position: absolute;
        bottom: 0px;
        left: 0;
        padding: 12px;
        width: 100%;
        height:auto !important;

        .typer {
            border-radius: 16px;
            background-color: $color-white;
            padding: 10px 46px 10px 10px;
            position: relative;

            textarea {
                min-height: 79px;
                border: none;
                flex: 1;
                resize: none;
                position: relative;
                padding: 6px;
                @include custom-scrollbar;

                &:focus {
                    outline: none;
                }

                &::placeholder {
                    font-size: $font-14;
                    font-weight: $f-w-400;
                    line-height: 18.52px;
                    text-align: left;
                    color: $color-placeholders;
                    position: absolute;
                    bottom: 0;
                }
            }

            .chat-logo {
                border-radius: 5px;
                background-color: $color-E7495E;
                height: 30px;
                width: 30px;
                cursor: pointer;
                border: none;
                position: absolute;
                right: 10px;


                &:focus {
                    outline: none;
                    border: none;
                }
            }
        }
    }
}


.cursor-pointer {
    cursor: pointer;
}

.tooltip-container {
    position: fixed;
    bottom: 30px;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 30;

    .tooltip-box {
        box-shadow: 0px 4px 40px 0px #DBCACA80;
        border-radius: 10px;
        padding: 20px;
        background-color: $color-white;


        .tooltip-content {
            margin-right: 20px;
            height: 69;
            width: 100px;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        .tooltip-title {
            font-size: $font-24;
            font-weight: $f-w-500;
            line-height: 33.6px;
            text-align: left;
            color: $color-paragraph;
        }
    }
}

.chat_explain_ai {
    .ks_chart_container {
        height:100%;
        .ks_dashboarditem_chart_container {
            height:100%;
            }
        }
    }

// message-with-ai-ui

.ks_body_class .message_with_ai {
        .modal-dialog {
            width:50% !important;
            height: calc(100% - 90%) !important;
            min-height: calc(100% - 26%) !important;
            }
    .modal-content {
         left:auto !important;
         border: 1px solid $color-E5E7EB !important;
          background-color: $color-F5F8FB;
        }
        .modal-header {
            border-bottom:1px solid  $color-E5E7EB !important;
            }
    .chat-ai-box {
        height:auto !important;
        padding:0 !important;
        .chat-sec {
            padding:0 8px !important;
            }
        }
    }

// not draggable-modal-css

.ks_body_class #chatid {
.modal-dialog {
            height: calc(100vh - 40px) !important;
    right: 0;
    border-radius: 10px !important;
    margin: 0 !important;
    padding: 0;
    position: absolute;
    }
    height: 100%;
}

// new chat-ai-css

.ks_body_class .answers {
    .chart-table-wrapper {
        overflow: auto;
        max-height: calc(100vh - 615px);
        border: 0.66px solid $color-EDEDED;
        box-shadow: 1.32px 1.32px 2.64px 0px #0000000F;
        border-radius: 10px;
        @include custom-scrollbar;

        table {
            // overflow: hidden;
            width: 100%;
            border-collapse: collapse;

            thead {

                tr {
                    th {
                        background-color: $color-secondary-bg;
                        padding: 10px;
                        font-size: $font-10;
                        font-weight: $f-w-600;
                        line-height: 13.88px;
                        text-align: left;
                        color: $color-2C2D35;
                        border-right: 0.66px solid $color-FFFCFC;
                        position: sticky;
                        top: 0;
                        z-index: 20;
                    }
                }
            }

            tbody {
                tr {
                    td {
                        font-size: $font-10;
                        font-weight: $f-w-400;
                        line-height: 15.86px;
                        text-align: left;
                        color: $color-black;
                        padding: 10px;
                        border-right: 0.66px solid $color-FFFCFC;


                        &:nth-child(2) {
                            color: $color-727378;
                        }
                    }

                    &:nth-of-type(odd)>* {
                        --bs-table-bg-type: $color-white !important;
                    }

                    &:nth-of-type(even)>* {
                        --bs-table-bg-type: $color-secondary-bg !important;
                    }
                }
            }

        }
    }
}


// chart-insight


    .ks_body_class .chart-insight {
        border: 1.63px solid $color-E5E7EB;
        min-height: 579px;
        box-shadow: 0px 6.05px 30.23px 0px #EEEEEE80;
        border-radius: 30px;
        padding: 24px;

        h4 {
            font-size: $font-20;
            font-weight: $f-w-500;
            line-height: 48.38px;
            text-align: left;
            color: $color-05004E;

            @include max-992 {
                font-size: $font-14;
            }
        }

        .chart-img {
            height: 462px;
            width: 100%;

            img {
                height: 100%;
                width: 100%;
                object-fit: contain;
            }
        }
    }
