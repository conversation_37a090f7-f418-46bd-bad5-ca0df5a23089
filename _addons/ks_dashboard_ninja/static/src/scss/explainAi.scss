.explain-ai {

    //     max-height: calc(100vh - 200px);
    //     overflow: auto;
    //
    //     &.ks_ai_explain_body{
    //         height: auto !important;
    //         }
    .ks_ai_explain_tile {
        box-shadow: none;
    }

    @include custom-scrollbar;

    .row {
        margin-bottom: 20px;
        row-gap: 16px;

        .charts-sec {
            border: 0.82px solid $color-E5E7EB;
            border-radius: 16px;
            min-height: 290px;
            height: 100%;
            padding: 9px 16px;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: start;

            .encapsulated-tile-container {
                position: relative !important;
                min-height: 185px !important;
            }

            .encap-layout-2-box {
                display: flex !important;
                flex-direction: column !important;
                .layout-2-icon {
                    border-bottom-left-radius: 16px;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        border-bottom-right-radius: 16px;
        padding: 17px;
        height: 100%;
        width: 100%;

        span {
            font-size: 4rem !important;
        }
                    }
            }

            & .ks_list_explain {
                min-height: 290px;
                height: 100%;
                max-height: 290px;
            }

            &.ks_explain_ai {
                display: flex;
                align-items: center;
                justify-content: center;

                & .ks_explain_ai_view {
                    width: 100% !important;
                    flex: unset !important;
                }
            }

            &.ks_list_card_body {
                max-width: 100%;
            }

            .ks_ai_dashboard_item {
                flex: 0 0 100%;
                max-width: 100%;
            }

            .card-body {
                width: 100%;
                max-width: 100%;
                flex: 1;
                border: none;

                &>div {
                    width: 100%;
                }
            }

            h3 {
                font-size: $font-14;
                font-weight: $f-w-500;
                line-height: 26.35px;
                text-align: left;
                color: $color-05004E;
                margin-bottom: 12px;
                padding: 12px;
            }

            .chart-preview {
                padding: 12px;
                width: 100%;
                height: 226px;

                img {
                    object-fit: contain;
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .charts-data {
            border: 1px solid $color-E5E7EB;
            border-radius: 20px;
            min-height: 290px;
            font-size: $font-12;
            font-weight: $f-w-400;
            line-height: 19.2px;
            text-align: left;
            color: $color-black;
            position: relative;
            background-color: $color-bg-main;
            padding: 18px 14px;

            .ks_ai_explanation {
                max-width: 100% !important;
            }

            .charts-content-box {
                max-height: calc(100vh - 549px);
                overflow: auto;
                max-width: 100% !important;

                .ks_ai_explanation {
                    max-width: 100% !important;
                    height: auto !important;
                    padding: 9px 33px !important;
                }

                @include custom-scrollbar;
            }

            .voice-button {
                height: 40px;
                width: 40px;
                border-radius: 50%;
                background-color: $color-white;
                display: flex;
                justify-content: center;
                align-items: center;
                position: absolute;
                right: 16px;
                bottom: 16px;
            }
        }
    }
}


.ks_ai_explain_tile {
    position: relative;
}

.explain-ai {

    &+.ks_explain_todo {
        display: none !important;
    }
}

.modal-body {
    .explain-ai-header {
        display: none !important;
    }
}