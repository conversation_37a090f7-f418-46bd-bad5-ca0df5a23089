<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="ks_dashboard_ninja.Ksdashboardlistview">

        <div class="grid-stack-item ks_tile_carousel ks_dashboarditem_id" t-att-id="item_id" t-ref="ks_list_view">
            <div class="grid-stack-item-content ks_list_view_container ks_dashboarditem_chart_container ks_dashboard_item_hover card border-0"
                 t-att-title="ks_info">

                <t t-if="props.explain_ai_whole">
                <div class="p-3 py-3 d-flex flex-row align-items-center justify-content-between">
                    <div class="d-flex align-items-center ">
                        <h6 class="m-0 font-weight-bold h3 mr-3 ks_list_view_heading" t-att-title="ks_chart_title">
                            <t t-esc="ks_chart_title"/>
                        </h6>
                        <t t-if="ks_breadcrumb.length >= 1">
                            <nav class="ks_breadcrumb">
                            <ul>
                                <li class="d-none" t-att-id="'item'+'_'+'-1'">
                                       <span t-att-data-sequence = '-1' t-att-data-item-id="item_id" t-on-click="ksOnDrillUp">
                                           <t t-esc="ks_chart_title"/>
                                       </span>
                                </li>
                                <t t-foreach="ks_breadcrumb" t-as="chart_breadcrumb" t-key="chart_breadcrumb_index">
                                        <li class="d-none" t-att-id="chart_breadcrumb['name'] + '_' + chart_breadcrumb_index">
                                        <span t-att-data-sequence="chart_breadcrumb_index" t-att-data-item-id="item_id" t-on-click="ksOnDrillUp">
                                           <t t-esc="chart_breadcrumb['name']"/>
                                        </span>
                                    </li>
                                </t>
                            </ul>
                            </nav>
                        </t>
                    </div>
<!--                    for ai dashboard-->
                    <img src="ks_dashboard_ninja/static/images/selected.svg" class="ks_img_display d-none" width="30"/>
                    <div class="select-btn">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M10.0001 18.3332C14.5834 18.3332 18.3334 14.5832 18.3334 9.99984C18.3334 5.4165 14.5834 1.6665 10.0001 1.6665C5.41675 1.6665 1.66675 5.4165 1.66675 9.99984C1.66675 14.5832 5.41675 18.3332 10.0001 18.3332Z"
                                stroke="" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M6.45825 9.99993L8.81659 12.3583L13.5416 7.6416" stroke="" stroke-width="1.5"
                                stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </div>
<!--                    -->
                    <!--  Dashboard Item Move/Copy Feature -->
                    <div class="ks_dashboard_item_button_container ks_dropdown_container ks_dashboard_item_header ks_dashboard_item_header_hover chart_button_container d-md-flex d-none"
                         t-att-data-item_id="item_id">

                        <t t-if="ksIsDashboardManager and props.hideButtons">
                            <button title="Move/Duplicate" data-bs-toggle="dropdown" t-att-data-item-id="item_id"
                                    class="ks_dashboard_item_action dropdown-toggle img-bg hover-item me-lg-2" type="button"
                                    aria-expanded="true">
 <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.59351 3.92004H3.40682C2.26682 3.92004 1.3335 4.85337 1.3335 5.99337V13.5667C1.3335 14.5334 2.02683 14.9467 2.87349 14.4734L5.49349 13.0134C5.77349 12.86 6.22683 12.86 6.50016 13.0134L9.12016 14.4734C9.96682 14.9467 10.6602 14.5334 10.6602 13.5667V5.99337C10.6668 4.85337 9.73351 3.92004 8.59351 3.92004Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M10.6668 5.99337V13.5667C10.6668 14.5334 9.9735 14.94 9.12683 14.4734L6.50684 13.0134C6.22684 12.86 5.77349 12.86 5.49349 13.0134L2.87349 14.4734C2.02683 14.94 1.3335 14.5334 1.3335 13.5667V5.99337C1.3335 4.85337 2.26682 3.92004 3.40682 3.92004H8.59351C9.73351 3.92004 10.6668 4.85337 10.6668 5.99337Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M14.6668 3.4067V10.98C14.6668 11.9467 13.9735 12.3534 13.1268 11.8867L10.6668 10.5134V5.99337C10.6668 4.85337 9.73351 3.92004 8.59351 3.92004H5.3335V3.4067C5.3335 2.2667 6.26682 1.33337 7.40682 1.33337H12.5935C13.7335 1.33337 14.6668 2.2667 14.6668 3.4067Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>

                            </button>
                            <ul role="menu" class="ks_dashboard_menu_container form-input-box form-control encapsulated-form-arrow dropdown-menu dropdown-menu-right">
                                <li class="ks_md_heading m-2">
                                    <span>Select Dashboard</span>
                                </li>
                                <li class="m-2">
                                    <select class="o_input o_group_selector o_add_group ks_dashboard_select">
                                        <t t-foreach="ks_dashboard_list" t-as="ks_dashboard" t-key="ks_dashboard_index">
                                            <option t-att-value="ks_dashboard['id']">
                                                <t t-esc="ks_dashboard['name']"/>
                                            </option>
                                        </t>
                                    </select>
                                </li>
                                <li class="m-2">
                                    <button class="dash-btn-red o_apply_group o_add_group ks_duplicate_item"
                                            tabindex="-1" type="button">Duplicate
                                    </button>
                                    <button class="dash-btn-red o_apply_group o_add_group ks_move_item" tabindex="-1"
                                            type="button">Move
                                    </button>
                                </li>
                            </ul>
                            <button title="Quick Customize"
                                    class="ks_dashboard_quick_edit_action_popup  img-bg hover-item me-lg-2 d-sm-block d-none"
                                    type="button" t-att-data-item-id="item_id">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                    <path d="M9.16675 1.66663H7.50008C3.33341 1.66663 1.66675 3.33329 1.66675 7.49996V12.5C1.66675 16.6666 3.33341 18.3333 7.50008 18.3333H12.5001C16.6667 18.3333 18.3334 16.6666 18.3334 12.5V10.8333" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="M13.3666 2.51663L6.7999 9.0833C6.5499 9.3333 6.2999 9.82497 6.2499 10.1833L5.89157 12.6916C5.75823 13.6 6.3999 14.2333 7.30823 14.1083L9.81657 13.75C10.1666 13.7 10.6582 13.45 10.9166 13.2L17.4832 6.6333C18.6166 5.49997 19.1499 4.1833 17.4832 2.51663C15.8166 0.849966 14.4999 1.3833 13.3666 2.51663Z" stroke="currentColor" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="M12.425 3.45837C12.9834 5.45004 14.5417 7.00837 16.5417 7.57504" stroke="currentColor" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </button>

<!--                            <button class="ks_dashboard_item_customize d-block d-sm-none" title="Customize Item"-->
<!--                                    type="button" t-att-data-item-id="item_id">-->
<!--                                    <img src="/ks_dashboard_ninja/static/images/dashboardOverview/edit.svg" alt="create" class="img-fluid" loading="lazy"/>-->
<!--                            </button>-->
                        </t>
                        <t t-if="props.hideButtons">
                            <div class="ks_chart_inner_buttons dropdown">
                            <button title="Info" data-bs-toggle="dropdown"
                                    class="ks_item_description dropdown-toggle d-md-block d-none img-bg hover-item me-lg-2 o-no-caret"
                                    type="button"
                                    t-att-style="'color:'+ ks_rgba_button_color + ';'"
                                    aria-expanded="true">
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.00004 14.6667C11.6667 14.6667 14.6667 11.6667 14.6667 8.00004C14.6667 4.33337 11.6667 1.33337 8.00004 1.33337C4.33337 1.33337 1.33337 4.33337 1.33337 8.00004C1.33337 11.6667 4.33337 14.6667 8.00004 14.6667Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 5.33337V8.66671" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7.99646 10.6666H8.00245" stroke="currentColor" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            </button>

                            <div role="menu" class="dropdown-menu dropdown-menu-right" style="width:20rem">
                            <!--Dynamic Rendering-->
                                <div class="ks_chart_export_menu">
                                <div class="ks_chart_export_menu_header" style="margin-left:-10px">
                                    <span>Info</span>
                                </div>
                                    <div class="ks_info" style="margin-left:10px">
                                    <span>Company: <t t-esc="ks_company"/></span>
                                </div>
                                    <div class="ks_info" style="margin-left:10px">
                                    <t t-if="ks_info">
                                        <t t-foreach="ks_info" t-as="ks_description" t-key="ks_description_index">
                                            <span><t t-esc="ks_description"/></span>
                                            <br></br>
                                         </t>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                        </t>
                        <t t-if="ksIsUser and props.hideButtons">
                            <div class="ks_chart_inner_buttons">
                                <button title="Export Item" data-bs-toggle="dropdown"
                                        class="ks_dashboard_item_action_export img-bg hover-item me-lg-2 dropdown-toggle o-no-caret ks_next_previous_bg_color"
                                        type="button"
                                        aria-expanded="true">
                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/document-upload.svg" alt="document-upload"
                                                    class="img-fluid" loading="lazy"/>
                                </button>
                                <div role="menu" class="dropdown-menu dropdown-menu-right">
                                    <!--Dynamic Rendering-->
                                    <div class="ks_chart_export_menu">
                                        <div class="ks_chart_export_menu_header">
                                            <span>Export</span>
                                        </div>
                                        <div class="chart_xls_export ks_chart_xls_csv_export ks_chart_export_menu_item"
                                             t-att-data-chart-id="item_id"
                                             data-format="list_xls">
                                            <i class="fa fa-file-excel-o"/>
                                            <span>Export to Excel</span>
                                        </div>
                                        <div class="ks_chart_xls_csv_export ks_chart_export_menu_item"
                                             t-att-data-chart-id="item_id"
                                             data-format="list_csv">
                                            <i class="fa fa-file-code-o"/>
                                            <span>Export to CSV</span>
                                        </div>
                                        <div class="ks_chart_json_export ks_chart_export_menu_item"
                                             t-att-data-item-id="item_id"
                                             data-format="chart_xls">
                                            <img src="ks_dashboard_ninja/static/images/dashboardOverview/document-upload.svg" alt="document-upload" class="img-fluid" loading="lazy"/>
                                            <span>Export Item</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>

                        <t t-if="ksIsDashboardManager and props.hideButtons">
                            <div class="ks_chart_inner_buttons ks_dashboard_more_action">
                                <button title="More" data-bs-toggle="dropdown"
                                        class="ks_dashboard_item_action dropdown-toggle img-bg hover-item me-lg-2 kpi-tile-img"
                                        type="button"
                                        aria-expanded="true">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12.2916 15.8333C12.2916 17.1 11.2666 18.125 9.99992 18.125C8.73325 18.125 7.70825 17.1 7.70825 15.8333C7.70825 14.5667 8.73325 13.5417 9.99992 13.5417C11.2666 13.5417 12.2916 14.5667 12.2916 15.8333ZM8.95825 15.8333C8.95825 16.4083 9.42492 16.875 9.99992 16.875C10.5749 16.875 11.0416 16.4083 11.0416 15.8333C11.0416 15.2583 10.5749 14.7917 9.99992 14.7917C9.42492 14.7917 8.95825 15.2583 8.95825 15.8333Z" fill="currentColor"/>
                                    <path d="M12.2916 4.16671C12.2916 5.43337 11.2666 6.45837 9.99992 6.45837C8.73325 6.45837 7.70825 5.43337 7.70825 4.16671C7.70825 2.90004 8.73325 1.87504 9.99992 1.87504C11.2666 1.87504 12.2916 2.90004 12.2916 4.16671ZM8.95825 4.16671C8.95825 4.74171 9.42492 5.20837 9.99992 5.20837C10.5749 5.20837 11.0416 4.74171 11.0416 4.16671C11.0416 3.59171 10.5749 3.12504 9.99992 3.12504C9.42492 3.12504 8.95825 3.59171 8.95825 4.16671Z" fill="currentColor"/>
                                    <path d="M12.2916 9.99996C12.2916 11.2666 11.2666 12.2916 9.99992 12.2916C8.73325 12.2916 7.70825 11.2666 7.70825 9.99996C7.70825 8.73329 8.73325 7.70829 9.99992 7.70829C11.2666 7.70829 12.2916 8.73329 12.2916 9.99996ZM8.95825 9.99996C8.95825 10.575 9.42492 11.0416 9.99992 11.0416C10.5749 11.0416 11.0416 10.575 11.0416 9.99996C11.0416 9.42496 10.5749 8.95829 9.99992 8.95829C9.42492 8.95829 8.95825 9.42496 8.95825 9.99996Z" fill="currentColor"/>
                                    </svg>
                                </button>
                                <div role="menu" class="ks_chart_export_menu dropdown-menu dropdown-menu-right">
                                    <div class="d-flex align-items-center">
                                        <button class="ks_dashboard_item_delete img-bg hover-item me-lg-2 kpi-tile-img" title="Remove Item" type="button">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M14 3.98665C11.78 3.76665 9.54667 3.65332 7.32 3.65332C6 3.65332 4.68 3.71999 3.36 3.85332L2 3.98665" fill="#241C1D"/>
                                                <path d="M14 3.98665C11.78 3.76665 9.54667 3.65332 7.32 3.65332C6 3.65332 4.68 3.71999 3.36 3.85332L2 3.98665" stroke="#241C1D" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M5.66699 3.31337L5.81366 2.44004C5.92033 1.80671 6.00033 1.33337 7.12699 1.33337H8.87366C10.0003 1.33337 10.087 1.83337 10.187 2.44671L10.3337 3.31337" stroke="#241C1D" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M12.5669 6.09338L12.1336 12.8067C12.0603 13.8534 12.0003 14.6667 10.1403 14.6667H5.86026C4.00026 14.6667 3.94026 13.8534 3.86693 12.8067L3.43359 6.09338" stroke="#241C1D" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M6.88672 11H9.10672" stroke="#241C1D" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M6.33301 8.33337H9.66634" stroke="#241C1D" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg> Remove
                                        </button>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <button id='ks_ai_item_exp_dash' class="img-bg hover-item me-lg-2 kpi-tile-img"
                                                t-on-click="_onButtonClick" title="AI provides the insights of the item">
                                            <img src="/ks_dashboard_ninja/static/images/favorite-chart.svg" alt="create" class="img-fluid" loading="lazy"/>
                                            Explain With AI
                                        </button>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <button class="ks_dashboard_item_chatter_wizard img-bg hover-item me-lg-2 kpi-tile-img" title="Channel" type="button" t-on-click="_openChatWizard">
                                            <img src="/ks_dashboard_ninja/static/images/dashboardOverview/messages.svg" alt="messages" class="img-fluid" loading="lazy"/>
                                            Chat
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                    <t t-if="ksIsDashboardManager and props.hideButtons">
                        <div class="dropdown d-md-none dn-setting-panel">
                            <button class="bg-transparent border-0" type="button" id="dropdownSettingButton"
                                    data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z" fill="#4B5563" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M1.33301 8.58667V7.41333C1.33301 6.72 1.89967 6.14667 2.59967 6.14667C3.80634 6.14667 4.29967 5.29333 3.69301 4.24667C3.34634 3.64667 3.55301 2.86667 4.15967 2.52L5.31301 1.86C5.83967 1.54666 6.51967 1.73333 6.83301 2.26L6.90634 2.38666C7.50634 3.43333 8.49301 3.43333 9.09967 2.38666L9.17301 2.26C9.48634 1.73333 10.1663 1.54666 10.693 1.86L11.8463 2.52C12.453 2.86667 12.6597 3.64667 12.313 4.24667C11.7063 5.29333 12.1997 6.14667 13.4063 6.14667C14.0997 6.14667 14.673 6.71333 14.673 7.41333V8.58667C14.673 9.28 14.1063 9.85333 13.4063 9.85333C12.1997 9.85333 11.7063 10.7067 12.313 11.7533C12.6597 12.36 12.453 13.1333 11.8463 13.48L10.693 14.14C10.1663 14.4533 9.48634 14.2667 9.17301 13.74L9.09967 13.6133C8.49967 12.5667 7.51301 12.5667 6.90634 13.6133L6.83301 13.74C6.51967 14.2667 5.83967 14.4533 5.31301 14.14L4.15967 13.48C3.55301 13.1333 3.34634 12.3533 3.69301 11.7533C4.29967 10.7067 3.80634 9.85333 2.59967 9.85333C1.89967 9.85333 1.33301 9.28 1.33301 8.58667Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownSettingButton">
                                 <a class="dropdown-item ks_dashboard_item_customize" t-att-data-item-id="item_id">
                                    <i class="fa fa-pencil"></i>Customize Item
                                 </a>
                                 <a class="dropdown-item ks_dashboard_item_delete">
                                    <i class="fa fa-times"></i>Remove Item
                                 </a>
                            </div>
                        </div>
                    </t>
                </div>
                </t>
<!--                <t t-if="ks_ai_analysis">-->
<!--                    <div class="ks_ai_explain_body">-->
<!--                        <div name="ks_list_div" class="card-body table-responsive ks_list_card_body ks_list_item_table">-->
<!--                            <t t-call="ks_dashboard_ninja.ks_list_view_table"/>-->
<!--                        </div>-->
<!--                        <div class="ks_ai_explanation">-->
<!--                            <p><t t-esc="ks_ai_analysis_1"/></p>-->
<!--                            <p><t t-esc="ks_ai_analysis_2"/></p>-->
<!--                        </div>-->
<!--                        <div class="ks_speaker" t-on-click="props.ks_speak">-->
<!--                            <span class="fa fa-volume-up"/>-->
<!--                            <audio></audio>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </t>-->
<!--                <t t-else="">-->
<!--                    <div name="ks_list_div" class="card-body table-responsive ks_list_item_table">-->
<!--                        <t t-call="ks_dashboard_ninja.ks_list_view_table"/>-->
<!--                    </div>-->
<!--                </t>-->

                <t t-if="ks_ai_analysis">
                    <div class="explain-ai pt-3">
                        <div class="container">
                            <div class="row ks_ai_explain_body">
                                 <div class="col-xl-6 col-12">
                                     <div class="charts-sec">
                                         <div name="ks_list_div" class="card-body table-responsive ks_list_card_body ks_list_item_table ks_list_explain">
                                            <t t-call="ks_dashboard_ninja.ks_list_view_table"/>
                                         </div>
                                     </div>
                                 </div>
                                <div class="col-xl-6 col-12">
                                    <div class="charts-data">
                                        <div class="charts-content-box">
                                            <div class="ks_ai_explanation">
                                                <p><t t-esc="ks_ai_analysis_1"/></p>
                                                <p><t t-esc="ks_ai_analysis_2"/></p>
                                            </div>
                                        </div>

                                        <div class="voice-button" t-on-click="props.ks_speak" title="Text to speech">
                                            <div class="voice-cricle">
                                                <img src="/ks_dashboard_ninja/static/images/voice-cricle.svg" height="24" width="24" alt="voice-img" loading="lazy"
                                                class="img-fluid"/>
                                            </div>
                                            <div class="comp-gif d-none">
                                                <img src="/ks_dashboard_ninja/static/images/Comp.gif" alt="loading gif" height="24" width="24"
                                                     class="img-fluid"/>
                                            </div>
<!--                                        <span class="fa fa-volume-up"/>-->
                                            <audio></audio>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </t>
                <t t-else="">
                    <div name="ks_list_div" class="card-body table-responsive ks_list_item_table">
                        <t t-call="ks_dashboard_ninja.ks_list_view_table"/>
                    </div>
                    <div class="d-flex justify-content-end">
                        <div class="ks_pager_name">
                            <t t-if="ks_pager" t-call="ks_pager_template"/>
                        </div>
                    </div>
                </t>
            </div>
        </div>
    </t>

    <t t-name="ks_dashboard_ninja.ks_list_view_tmpl">
        <t t-foreach="state.list_view_data['data_rows']" t-as="table_row" t-key="table_row_index">
            <tr class="ks_tr" t-att-data-record-id="table_row['id']" t-att-data-domain="table_row['domain']"
                t-att-data-item-Id="item_id"
                t-att-data-sequence="table_row['sequence']" t-att-data-last_seq="table_row['last_seq']">
                <t t-set="ks_rec_count" t-value="0"/>
                <t t-foreach="table_row['data']" t-as="row_data" t-key="row_data_index">
                    <t t-if="table_row['ks_column_type'][ks_rec_count]=='html'">
                        <td class="ks_list_canvas_click" t-on-click="onChartCanvasClick">
<!--                            <t t-out="row_data"/>-->
                            <t t-raw="row_data"/>
                        </td>
                        <t t-set="ks_rec_count" t-value="ks_rec_count+1"/>
                    </t>
                    <t t-else="">
                        <td class="ks_list_canvas_click" t-on-click="onChartCanvasClick">
                            <t t-esc="row_data"/>
                        </td>
                        <t t-set="ks_rec_count" t-value="ks_rec_count+1"/>
                    </t>
                </t>
                <td class="ks_info">
                    <t t-if="ks_show_records">
                    <div id="ks_item_info" t-att-data-model="state.list_view_data['model']"
                       t-att-data-list-type="state.list_view_data['list_view_type']"
                       t-att-data-groupby="state.list_view_data['groupby']"
                       t-att-data-record-id="table_row['id']" t-att-data-item-id="item_id"
                       t-att-data-list-view-type="tmpl_list_type"
                       t-att-data-domain="table_row['domain']"
                       t-on-click="ksOnListItemInfoClick">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                            <path d="M9.16675 1.66663H7.50008C3.33341 1.66663 1.66675 3.33329 1.66675 7.49996V12.5C1.66675 16.6666 3.33341 18.3333 7.50008 18.3333H12.5001C16.6667 18.3333 18.3334 16.6666 18.3334 12.5V10.8333" stroke="#241C1D" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M13.3666 2.51663L6.7999 9.0833C6.5499 9.3333 6.2999 9.82497 6.2499 10.1833L5.89157 12.6916C5.75823 13.6 6.3999 14.2333 7.30823 14.1083L9.81657 13.75C10.1666 13.7 10.6582 13.45 10.9166 13.2L17.4832 6.6333C18.6166 5.49997 19.1499 4.1833 17.4832 2.51663C15.8166 0.849966 14.4999 1.3833 13.3666 2.51663Z" stroke="#241C1D" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.425 3.45837C12.9834 5.45004 14.5417 7.00837 16.5417 7.57504" stroke="#241C1D" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </div>
                    </t>
                </td>
            </tr>
        </t>
    </t>

    <t t-name="ks_dashboard_ninja.ks_list_view_table">
        <t t-if="state.list_view_data">
            <table id="ksListViewTable" class="table table-hover ks_list_view_layout_1"
                   t-att-data-model="state.list_view_data['model']">
                <thead>
                    <t t-call="ks_list_view_header"/>
                </thead>
                <tbody class="ks_table_body">
                    <t t-call="ks_dashboard_ninja.ks_list_view_tmpl"/>
                </tbody>
            </table>
        </t>
        <t t-else="">
            No Data Present
        </t>
    </t>
        <t t-name="ks_list_view_header">
        <tr>
            <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                <th>
                    <t t-esc="table_header"/>
                </th>
            </t>
            <th/>
        </tr>
    </t>
        <div t-name="ks_pager_template" class="ks_pager">
        <span class="ks_counter">
            <span class="ks_value">
                <t t-esc="count"/>
            </span>
        </span>
        <span class="btn-group" aria-atomic="true" t-att-data-next_offset="intial_count"
              t-att-data-prev-offset="offset">
            <button type="button"
                    class="fa fa-chevron-left btn  ks_load_previous ks_event_offer_list"
                    t-att-data-item-id="item_id" title="Previous" t-on-click="ksLoadPreviousRecords"/>
            <button type="button" class="fa fa-chevron-right btn  ks_load_next"
                    t-att-data-item-id="item_id" title="Next" t-on-click="ksLoadMoreRecords"/>
        </span>
    </div>

</templates>