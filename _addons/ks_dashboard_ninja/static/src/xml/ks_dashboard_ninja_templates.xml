<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
  

    <t t-name="ks_dashboard_ninja.KsDashboardNinjaHeader" owl="1">

        <main class="main-box">
            <section class="screen-info mb-2">
                <div class="container-fluid ks_dashboard_top_menu-new">
                    <div class="d-flex align-items-center justify-content-between" t-ref="ks_dashboard_header">
                        <div class="info d-flex gap-1 flex-wrap align-items-center w-100">
                            <button t-if="state.ks_dashboard_manager &amp;&amp; ks_dashboard_data.ks_dashboard_items_ids.length" class="img-bg info me-lg-2 me-1 d-md-flex d-none ks_dashboard_edit_layout"
                                title="Edit Dashboard" t-on-click="onKsEditLayoutClick">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M9.16675 1.66663H7.50008C3.33341 1.66663 1.66675 3.33329 1.66675 7.49996V12.5C1.66675 16.6666 3.33341 18.3333 7.50008 18.3333H12.5001C16.6667 18.3333 18.3334 16.6666 18.3334 12.5V10.8333"
                                        stroke="" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M13.3666 2.51663L6.7999 9.0833C6.5499 9.3333 6.2999 9.82497 6.2499 10.1833L5.89157 12.6916C5.75823 13.6 6.3999 14.2333 7.30823 14.1083L9.81657 13.75C10.1666 13.7 10.6582 13.45 10.9166 13.2L17.4832 6.6333C18.6166 5.49997 19.1499 4.1833 17.4832 2.51663C15.8166 0.849966 14.4999 1.3833 13.3666 2.51663Z"
                                        stroke="" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path d="M12.425 3.45837C12.9834 5.45004 14.5417 7.00837 16.5417 7.57504" stroke=""
                                        stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </button>


<!--                            <button class="img-bg info me-lg-3 me-1 d-md-block d-none" title="Dashboard TV">-->
<!--                                <svg width="20" height="20" viewBox="0 0 20 20" fill="" xmlns="http://www.w3.org/2000/svg">-->
<!--                                    <path-->
<!--                                        d="M7.58301 9.99999V8.76666C7.58301 7.17499 8.70801 6.53333 10.083 7.32499L11.1497 7.94166L12.2163 8.55833C13.5913 9.34999 13.5913 10.65 12.2163 11.4417L11.1497 12.0583L10.083 12.675C8.70801 13.4667 7.58301 12.8167 7.58301 11.2333V9.99999Z"-->
<!--                                        stroke="" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round"-->
<!--                                        stroke-linejoin="round" />-->
<!--                                    <path-->
<!--                                        d="M10.0001 18.3333C14.6025 18.3333 18.3334 14.6023 18.3334 9.99996C18.3334 5.39759 14.6025 1.66663 10.0001 1.66663C5.39771 1.66663 1.66675 5.39759 1.66675 9.99996C1.66675 14.6023 5.39771 18.3333 10.0001 18.3333Z"-->
<!--                                        stroke="" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />-->
<!--                                </svg>-->
<!--                            </button>-->

                            <button class="img-bg info me-lg-2 me-1 d-md-flex d-none" t-att-class="ks_dashboard_data.is_bookmarked ? ' active' : ''"
                                             t-on-click="updateBookmark" title="Bookmark Dashboard">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.70825 7.54163C9.19159 8.08329 10.8083 8.08329 12.2916 7.54163" stroke=""
                                        stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M14.0168 1.66663H5.98351C4.20851 1.66663 2.76685 3.11663 2.76685 4.88329V16.625C2.76685 18.125 3.84185 18.7583 5.15851 18.0333L9.22518 15.775C9.65851 15.5333 10.3585 15.5333 10.7835 15.775L14.8502 18.0333C16.1668 18.7666 17.2418 18.1333 17.2418 16.625V4.88329C17.2335 3.11663 15.7918 1.66663 14.0168 1.66663Z"
                                        stroke="" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M14.0168 1.66663H5.98351C4.20851 1.66663 2.76685 3.11663 2.76685 4.88329V16.625C2.76685 18.125 3.84185 18.7583 5.15851 18.0333L9.22518 15.775C9.65851 15.5333 10.3585 15.5333 10.7835 15.775L14.8502 18.0333C16.1668 18.7666 17.2418 18.1333 17.2418 16.625V4.88329C17.2335 3.11663 15.7918 1.66663 14.0168 1.66663Z"
                                        stroke="" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </button>

                            <button class="img-bg info me-lg-2 me-1 d-md-flex d-none" t-on-click="dashboardImageUpdate" title="Update the current dashboard photo in the overview">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.75992 22H17.2399C19.9999 22 21.0999 20.31 21.2299 18.25L21.7499 9.99C21.8899 7.83 20.1699 6 17.9999 6C17.3899 6 16.8299 5.65 16.5499 5.11L15.8299 3.66C15.3699 2.75 14.1699 2 13.1499 2H10.8599C9.82992 2 8.62992 2.75 8.16992 3.66L7.44992 5.11C7.16992 5.65 6.60992 6 5.99992 6C3.82992 6 2.10992 7.83 2.24992 9.99L2.76992 18.25C2.88992 20.31 3.99992 22 6.75992 22Z" stroke="" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M10.5 8H13.5" stroke="" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12 18C13.79 18 15.25 16.54 15.25 14.75C15.25 12.96 13.79 11.5 12 11.5C10.21 11.5 8.75 12.96 8.75 14.75C8.75 16.54 10.21 18 12 18Z" stroke="" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>

<!--                            <button class="img-bg info me-lg-3 me-1 d-md-block d-none" title="Rearrange Dashboard">-->
<!--                                <svg width="20" height="20" viewBox="0 0 20 20" fill="" xmlns="http://www.w3.org/2000/svg">-->
<!--                                    <path-->
<!--                                        d="M18.3333 9.08341V3.41675C18.3333 2.16675 17.8 1.66675 16.475 1.66675H13.1083C11.7833 1.66675 11.25 2.16675 11.25 3.41675V9.08341C11.25 10.3334 11.7833 10.8334 13.1083 10.8334H16.475C17.8 10.8334 18.3333 10.3334 18.3333 9.08341Z"-->
<!--                                        stroke="" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />-->
<!--                                    <path-->
<!--                                        d="M18.3333 16.5833V15.0833C18.3333 13.8333 17.8 13.3333 16.475 13.3333H13.1083C11.7833 13.3333 11.25 13.8333 11.25 15.0833V16.5833C11.25 17.8333 11.7833 18.3333 13.1083 18.3333H16.475C17.8 18.3333 18.3333 17.8333 18.3333 16.5833Z"-->
<!--                                        stroke="" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />-->
<!--                                    <path-->
<!--                                        d="M8.75008 10.9166V16.5833C8.75008 17.8333 8.21675 18.3333 6.89175 18.3333H3.52508C2.20008 18.3333 1.66675 17.8333 1.66675 16.5833V10.9166C1.66675 9.66663 2.20008 9.16663 3.52508 9.16663H6.89175C8.21675 9.16663 8.75008 9.66663 8.75008 10.9166Z"-->
<!--                                        stroke="" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />-->
<!--                                    <path-->
<!--                                        d="M8.75008 3.41663V4.91663C8.75008 6.16663 8.21675 6.66663 6.89175 6.66663H3.52508C2.20008 6.66663 1.66675 6.16663 1.66675 4.91663V3.41663C1.66675 2.16663 2.20008 1.66663 3.52508 1.66663H6.89175C8.21675 1.66663 8.75008 2.16663 8.75008 3.41663Z"-->
<!--                                        stroke="" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />-->
<!--                                </svg>-->
<!--                            </button>-->



                            <t t-if="state.ks_dashboard_manager and !this.ks_dashboard_data.ks_ai_explain_dash">
                                <div class="dropdown dash-dd-2 d-md-flex d-none">
                                    <a class="text-decoration-none dropdown-toggle img-bg d-flex info me-lg-2 me-1" href="#"
                                        role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Settings">
                                         <svg width="20" height="20" viewBox="0 0 20 20" fill=""
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18.3333 14.5833H12.5" stroke="" stroke-width="1.25" stroke-miterlimit="10"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M4.16675 14.5834H1.66675" stroke="" stroke-width="1.25"
                                                stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M18.3333 5.41663H15.8333" stroke="" stroke-width="1.25"
                                                stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M7.50008 5.41663H1.66675" stroke="" stroke-width="1.25"
                                                stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                            <path
                                                d="M5.83341 12.0834H10.8334C11.7501 12.0834 12.5001 12.5 12.5001 13.75V15.4167C12.5001 16.6667 11.7501 17.0834 10.8334 17.0834H5.83341C4.91675 17.0834 4.16675 16.6667 4.16675 15.4167V13.75C4.16675 12.5 4.91675 12.0834 5.83341 12.0834Z"
                                                stroke="" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path
                                                d="M9.16667 2.91675H14.1667C15.0833 2.91675 15.8333 3.33341 15.8333 4.58341V6.25008C15.8333 7.50008 15.0833 7.91675 14.1667 7.91675H9.16667C8.25 7.91675 7.5 7.50008 7.5 6.25008V4.58341C7.5 3.33341 8.25 2.91675 9.16667 2.91675Z"
                                                stroke="" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                    </a>

                                    <ul class="dropdown-menu">
                                        <li class="d-md-block d-none">
                                            <a class="dropdown-item" href="#" t-on-click="ksOnDashboardSettingClick">
                                                <span class="me-2">
                                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/setting-2.svg" alt="Settings" class="img-fluid"
                                                        loading="lazy"/>
                                                </span>
                                                Dashboard Settings
                                            </a>
                                        </li>
                                        <li class="d-md-block d-none">
                                            <a class="dropdown-item" href="#" t-on-click="ksOnDashboardDeleteClick">
                                                <span class="me-2">
                                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/trash.svg" alt="trash" class="img-fluid" loading="lazy"/>
                                                </span>
                                                Delete the Dashboard
                                            </a>
                                        </li>
                                        <li class="d-md-block d-none">
                                            <a class="dropdown-item" href="#" t-on-click="ksOnDashboardCreateClick">
                                                <span class="me-2">
                                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/add-square.svg" alt="add-square" class="img-fluid"
                                                        loading="lazy"/>
                                                </span>
                                                Create New Dashboard
                                            </a>
                                        </li>
                                        <li class="d-md-block d-none">
                                            <a class="dropdown-item" href="#" t-on-click="kscreateaidashboard">
                                                <span class="me-2">
                                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/illustrator.svg" alt="illustrator" class="img-fluid"
                                                        loading="lazy"/>
                                                </span>
                                                Generate Dashboard with AI
                                            </a>
                                        </li>
                                        <li class="d-md-block d-none">
                                            <a class="dropdown-item" href="#" t-on-click="ksOnDashboardDuplicateClick">
                                                <span class="me-2">
                                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/copy.svg" alt="copy" class="img-fluid" loading="lazy"/>
                                                </span>
                                                Duplicate Current Dashboard
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </t>


                            <div class="dropdown dash-dd-2 d-md-flex d-none">
                                <button class="img-bg info me-lg-2 more-img me-1 dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="More Options">
                                    <svg width="20" height="20"
                                        viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M12.2916 15.8333C12.2916 17.1 11.2666 18.125 9.99992 18.125C8.73325 18.125 7.70825 17.1 7.70825 15.8333C7.70825 14.5667 8.73325 13.5417 9.99992 13.5417C11.2666 13.5417 12.2916 14.5667 12.2916 15.8333ZM8.95825 15.8333C8.95825 16.4083 9.42492 16.875 9.99992 16.875C10.5749 16.875 11.0416 16.4083 11.0416 15.8333C11.0416 15.2583 10.5749 14.7917 9.99992 14.7917C9.42492 14.7917 8.95825 15.2583 8.95825 15.8333Z"
                                            fill="" />
                                        <path
                                            d="M12.2916 4.16671C12.2916 5.43337 11.2666 6.45837 9.99992 6.45837C8.73325 6.45837 7.70825 5.43337 7.70825 4.16671C7.70825 2.90004 8.73325 1.87504 9.99992 1.87504C11.2666 1.87504 12.2916 2.90004 12.2916 4.16671ZM8.95825 4.16671C8.95825 4.74171 9.42492 5.20837 9.99992 5.20837C10.5749 5.20837 11.0416 4.74171 11.0416 4.16671C11.0416 3.59171 10.5749 3.12504 9.99992 3.12504C9.42492 3.12504 8.95825 3.59171 8.95825 4.16671Z"
                                            fill="" />
                                        <path
                                            d="M12.2916 9.99996C12.2916 11.2666 11.2666 12.2916 9.99992 12.2916C8.73325 12.2916 7.70825 11.2666 7.70825 9.99996C7.70825 8.73329 8.73325 7.70829 9.99992 7.70829C11.2666 7.70829 12.2916 8.73329 12.2916 9.99996ZM8.95825 9.99996C8.95825 10.575 9.42492 11.0416 9.99992 11.0416C10.5749 11.0416 11.0416 10.575 11.0416 9.99996C11.0416 9.42496 10.5749 8.95829 9.99992 8.95829C9.42492 8.95829 8.95825 9.42496 8.95825 9.99996Z"
                                            fill="" />
                                    </svg>
                                </button>
                                <ul class="dropdown-menu">
                                    <li t-if="state.ks_dashboard_manager">
                                        <a class="dropdown-item" href="#" t-on-click="ksImportItemJson">
                                            <span class="me-2">
                                                <img src="ks_dashboard_ninja/static/images/dashboardOverview/document-download.svg" alt="document-download"
                                                    class="img-fluid" loading="lazy"/>
                                            </span>
                                            Import Item
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" t-on-click="ksOnDashboardExportClick">
                                            <span class="me-2">
                                                <img src="ks_dashboard_ninja/static/images/dashboardOverview/document-upload.svg" alt="document-upload"
                                                    class="img-fluid" loading="lazy"/>
                                            </span>
                                            Export Dashboard
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" t-on-click="ksOnDashboardImportClick">
                                            <span class="me-2">
                                                <img src="ks_dashboard_ninja/static/images/dashboardOverview/document-download.svg" alt="document-download"
                                                    class="img-fluid" loading="lazy"/>
                                            </span>
                                            Import Dashboard
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <t t-if="!this.ks_dashboard_data.ks_ai_explain_dash">
                                <div class="ks_dashboard_link ks_am_content_element ks_custom_date_filter ks_hide">
                                    <div class="dropdown dash-dd-2 me-lg-2 me-1">
                                        <a class="text-decoration-none dropdown-toggle ks_date_filter_dropdown"
                                            role="button" data-bs-toggle="dropdown"
                                                        aria-expanded="false">
                                            <span id="ks_date_filter_selection"/>
                                            <span class="caret"/>
                                        </a>

                                        <ul class="dropdown-menu" id="ks_date_selector_container" t-on-click="_ksOnDateFilterMenuSelect">
                                            <li id="l_none">
                                                <a class="df_selection_text dropdown-item">All Time</a>
                                            </li>
                                            <li class="divider"/>
                                            <t t-foreach="state.date_selection_order" t-as="date_id" t-key="date_id">
                                                <li t-att-id="date_id">
                                                    <a class="df_selection_text dropdown-item" href="#">
                                                        <t t-esc="state.date_selection_data[date_id]"/>
                                                    </a>
                                                </li>
                                            </t>
                                        </ul>
                                    </div>
                                </div>


                                <t t-if="state.ks_dashboard_manager">
                                      <div class="ks_dashboard_top_settings dropdown d-none d-lg-block">
                                            <input accept=".json " t-attf-id="file_#{_id}"
                                                   name="file" class="ks_input_import_item_button" type="file" style="display:none;"
                                                   t-on-change="ksImportItem"/>
                                      </div>
                                </t>
                            </t>


                            <t t-set="isShowFavBtn" t-value="state.ks_dn_pre_defined_filters?.length || Object.keys(state.ks_dashboard_data.ks_dashboard_custom_domain_filter).length"/>
                            <div class="dropdown dash-dd-2 filter filters_section active d-md-block d-none" t-if="isShowFavBtn">
                                <a class="text-decoration-none d-flex dropdown-toggle" data-bs-toggle="collapse"
                                    data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                                    <span class="d-flex align-items-center">
                                        <svg width="20" class="me-lg-2 me-0" height="20" viewBox="0 0 20 20" fill=""
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M18.0249 12.2917C18.0249 13.0334 17.8249 13.7334 17.4583 14.3334C16.7749 15.4751 15.5166 16.25 14.0666 16.25C13.2833 16.25 12.5499 16.0167 11.9333 15.6084C11.4166 15.2917 10.9916 14.8501 10.6833 14.3334C10.3166 13.7334 10.1083 13.0334 10.1083 12.2917C10.1083 10.1084 11.8833 8.33337 14.0666 8.33337C14.3666 8.33337 14.6583 8.3667 14.9333 8.43337C16.7083 8.82504 18.0249 10.4084 18.0249 12.2917Z"
                                                stroke="" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path d="M12.5253 12.2917L13.5003 13.2667L15.6086 11.3167" stroke=""
                                                stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
                                            <path
                                                d="M17.2413 3.34995V5.19995C17.2413 5.87495 16.8163 6.71663 16.3996 7.14163L14.9329 8.43329C14.6579 8.36662 14.3663 8.33329 14.0663 8.33329C11.8829 8.33329 10.1079 10.1083 10.1079 12.2916C10.1079 13.0333 10.3163 13.7333 10.6829 14.3333C10.9913 14.85 11.4163 15.2916 11.9329 15.6083V15.8916C11.9329 16.4 11.5996 17.075 11.1746 17.325L9.9996 18.0833C8.90794 18.7583 7.39127 18 7.39127 16.65V12.1916C7.39127 11.6 7.0496 10.8416 6.71627 10.425L3.51625 7.05829C3.09958 6.63329 2.75793 5.87497 2.75793 5.37497V3.43329C2.75793 2.42496 3.51628 1.66663 4.44128 1.66663H15.5579C16.4829 1.66663 17.2413 2.42495 17.2413 3.34995Z"
                                                stroke="" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                        <div class="d-lg-none d-flex justify-content-center filters-amount">
                                            2
                                        </div>
                                    </span>
                                    <span class="d-lg-block d-none">Filters</span>
                                </a>



                                 <div class="collapse custom-dash-collapse" id="collapseExample">
                                    <div class="card card-body">
                                        <ul class="nav nav-pills mb-3 border-bottom border-2" id="pills-tab" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button
                                                    class="nav-link text-primary fw-semibold position-relative show active"
                                                    id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home"
                                                    type="button" role="tab" aria-controls="pills-home" title="Apply already defined filter domains"
                                                    aria-selected="true">Predefined Filter
                                                </button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link text-primary fw-semibold position-relative"
                                                    id="pills-profile-tab" data-bs-toggle="pill"
                                                    data-bs-target="#pills-profile" type="button" role="tab" title="Create domain and apply filter"
                                                    aria-controls="pills-profile" aria-selected="false">Custom Filter
                                                </button>
                                            </li>
                                            <div class="position-relative ms-auto encapsulated-bottom" t-att-title="isShowFavBtn ? 'Save all current filters as favourite' : 'Create some filters to save as favourite'">
                                                <button class="favorite-btn me-3 d-flex align-items-center justify-content-center"
                                                        t-on-click="favFilterLayoutToggle"
                                                        t-att-class="isShowFavBtn  ? '' : 'disabled'">
                                                    <img src="/ks_dashboard_ninja/static/images/dashboardOverview/ranking.png" alt="ranking"
                                                        class="img-fluid me-1" loading="lazy"/>
                                                    Save as Favourite
                                                </button>
                                            </div>
                                            <div t-on-click="hideFilterTab" class="position-relative d-flex align-items-center me-2 encapsulated-bottom">
                                                 <img src="/ks_dashboard_ninja/static/images/dashboardOverview/close-circle.svg" alt="cross-icon" style="height: 20px; width: 20px;"
                                                            class="img-fluid me-2" loading="lazy"/>
                                            </div>
                                        </ul>


                                        <div class="tab-content" id="pills-tabContent">
                                            <!-- Home tab pane (default visible tab) -->
                                            <div class="tab-pane fade show active" id="pills-home" role="tabpanel"
                                                aria-labelledby="pills-home-tab">
                                                <div class="">
                                                     <div class="tab-pane fade show active" id="pills-home" role="tabpanel"
                                                        aria-labelledby="pills-home-tab">
                                                        <div class="predefined-filters">
                                                            <div t-if="Object.keys(state.ks_dashboard_data.ks_dashboard_domain_data).length &amp;&amp; !isFavFilter" t-att-class="Object.keys(state.ks_dashboard_data.ks_dashboard_domain_data).length ? 'ks_dn_filter_applied_container': 'ks_dn_filter_applied_container ks_hide'">
                                                                <t t-foreach="Object.keys(state.ks_dashboard_data.ks_dashboard_domain_data)" t-as="model_key" t-key="model_key_index">
                                                                    <t t-call="ks_dn_filter_facets_section">
                                                                        <t t-set="ks_domain_data"
                                                                           t-value="state.ks_dashboard_data.ks_dashboard_domain_data[model_key]"/>
                                                                        <t t-set="ks_model" t-value="model_key"/>
                                                                    </t>
                                                                </t>
                                                            </div>
                                                            <div t-elif="isFavFilter">
                                                                <h4 for="multi-dropdown">Select Filter</h4>
                                                                <div class="dropdown dash-dd-2 me-lg-2 me-1" id="multi-dropdown">
                                                                    <a class="text-decoration-none dropdown-toggle" href="#"
                                                                        role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                        <div class="selcted-opt opted">
                                                                            <div class="opt-title">
                                                                                <t t-esc="activeFavFilterName"/>
                                                                            </div>
                                                                            <div t-on-click="ksFavFilterFacetRemove">
                                                                                <img src="ks_dashboard_ninja/static/images/dashboardOverview/close-circle-two.svg"
                                                                                     alt="close-img" loading="lazy" class="img-fluid"/>
                                                                            </div>
                                                                        </div>
                                                                    </a>
                                                                    <t t-call="filter_dropdown">
                                                                        <t t-set="searchPredefinedFilter" t-value="searchPredefinedFilter"/>
                                                                        <t t-set="predefinedSearchFocusout" t-value="predefinedSearchFocusout"/>
                                                                    </t>
                                                                </div>
                                                            </div>
                                                            <div t-else="">
                                                                <h4 for="multi-dropdown">Select Filter</h4>
                                                                <div class="dropdown dash-dd-2 me-lg-2 me-1" id="multi-dropdown">
                                                                    <a class="text-decoration-none dropdown-toggle" href="#"
                                                                        role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                        <div class="placeholder-custom">Select Multiple Filters</div>
                                                                    </a>
                                                                    <t t-call="filter_dropdown">
                                                                        <t t-set="searchPredefinedFilter" t-value="searchPredefinedFilter"/>
                                                                        <t t-set="predefinedSearchFocusout" t-value="predefinedSearchFocusout"/>
                                                                    </t>
                                                                </div>
                                                            </div>
                                                        </div>
                                                     </div>
                                                </div>
                                            </div>

                                            <!-- Profile tab pane -->
                                            <div class="tab-pane fade" id="pills-profile" role="tabpanel"
                                                aria-labelledby="pills-profile-tab">
                                                <!-- Your second tab content here -->
                                                <div class="d-flex align-items-center mb-3" t-if="Object.keys(state.ks_dashboard_data.ks_dashboard_custom_domain_filter).length">
                                                    <h4 class="mb-2">Custom Filters</h4>
                                                    <button id="clearAll" class="clear-all-btn ms-auto" t-on-click="clear_filters">
                                                        <img src="/ks_dashboard_ninja/static/images/dashboardOverview/close-circle.svg" alt="cross-icon"
                                                            class="img-fluid me-1" loading="lazy"/>
                                                        Clear All
                                                    </button>
                                                </div>
                                        <div class="o_generator_menu" t-if="Object.keys(state.ks_dashboard_data.ks_dashboard_custom_domain_filter).length">
                                            <div class="table-custom mb-2" t-on-show-bs-dropdown="handleEvent">
                                                <div class="w-100" id="ks_custom_filter_table">
                                                    <div id="ks_dn_custom_filters_container">
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="customFilterFooter" class="btn-box d-flex justify-content-end align-items-center">
                                                <button class="dash-default-btn bg-white" t-on-click="ksOnCustomFilterConditionAdd">
                                                    <img src="/ks_dashboard_ninja/static/images/dashboardOverview/add-btn.svg" alt="add-icon" loading="lazy"
                                                        class="img-fluid me-1"/>Add a condition
                                                </button>
                                                <button class="dash-btn-red ms-2" t-on-click="ksOnCustomFilterApply">
                                                    Apply Filter
                                                </button>
                                            </div>
                                        </div>
                                        <div t-else="" class="no-data-avilable">
                                            <div class="d-flex align-items-center justify-content-center flex-column">
                                                <div class="no-data-img">
                                                    <img src="/ks_dashboard_ninja/static/images/dashboardOverview/no-data.png" alt="no-data-available" loading="lazy" class="img-fluid"/>
                                                </div>
                                                <div class="title mb-1">
                                                    No Filter Available
                                                </div>
                                                <p>
                                                    No custom filter, Create some from dashboard settings
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                         </div>
                            </div>


                            <div id="favFilterMain" title="Favourite Filter" t-att-class="Object.keys(ks_dashboard_data.ks_dashboard_favourite_filter).length ? 'me-2' : 'ks_hide me-2'">
                                <t t-if="Object.keys(ks_dashboard_data.ks_dashboard_favourite_filter).length">
                                    <t t-call="dn_favourite_filter_dropdown"/>
                                </t>
                                <t t-else="">
                                </t>
                            </div>

                            <div id="ks_dashboard_title">
                                <t t-if="state.ks_show_layout">
                                    <t t-call="ks_dn_layout_container"/>
                                </t>
                                <t t-else="">
                                    <span id="ks_dashboard_title_label" class="ks_am_element dash-dd-2">
                                        <t t-esc="state.ks_dashboard_name"/>
                                    </span>
                                </t>


                                <input id="ks_dashboard_title_input" typ="text" maxlength="30"
                                       class="form-control form-control-lg ks_em_element ks_hide"
                                />
                            </div>



                            <div class="d-flex align-items-center ms-auto">
                                <div class="ks_dashboard_edit_mode_settings ks_hide">
                                    <div class="oe_dashboard_links d-flex">
                                        <!--                            <button t-if="ks_show_create_layout_option" type="button"-->
                                        <!--                                    class="button ks_dashboard_create_new_layout btn btn-primary o_form_button_save"-->
                                        <!--                                    title="Save Changes as a New Layout">-->
                                        <!--                                <span>Save as New Layout</span>-->
                                        <!--                            </button>-->
                                        <t t-if="state.ks_multi_layout">
                                            <button t-if="state.ks_show_create_layout_option" type="button"
                                                    class="ks_dashboard_create_new_layout dash-btn-red o_form_button_save"
                                                    title="Save Changes as a New Layout" t-on-click="_onKsCreateLayoutClick"
                                                    style="position:relative; right:8px;">
                                                <span>Save as New Layout</span>
                                            </button>
                                        </t>

                                        <button type="button"
                                                class="ks_dashboard_save_layout dash-btn-red o_form_button_save"
                                                title="Save Changes"
                                                t-on-click="_onKsSaveLayoutClick" style="position:relative; left:-4px">
                                            <span>Save</span>
                                        </button>

                                        <button type="button"
                                                class="ks_dashboard_cancel_layout dash-default-btn bg-white o_form_button_cancel"
                                                title="Discard Changes"
                                                t-on-click="_onKsCancelLayoutClick" style="margin-right:8px">
                                            <span>Discard</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="ks_dashboard_layout_edit_mode_settings ks_hide">
                                    <div class="oe_dashboard_links d-flex gap-2">
                                        <button type="button"
                                                class="ks_dashboard_set_current_layout dash-btn-red o_form_button_save"
                                                title="Save Changes"
                                                t-on-click="_ksSetCurrentLayoutClick">
                                            <span>Set Current Layout</span>
                                        </button>
                                        <button type="button"
                                                class=" ks_dashboard_cancel_current_layout dash-default-btn bg-white o_form_button_cancel"
                                                title="Discard Changes" t-on-click="_ksSetDiscardCurrentLayoutClick">
                                            <span>Discard</span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <t t-if="state.ks_dashboard_manager">
                                <div class="new-features ks_dashboard_top_settings d-flex align-items-center ms-1">
                                    <div class="d-flex">
                                        <t t-if="this.ks_dashboard_data.ks_ai_explain_dash">
<!--                                            <button id='ks_ai_item_exp_dash' class="btn btn-primary mr-1"-->
<!--                                                    t-on-click="ks_switch_default_dashboard">-->
<!--                                                <img src="/ks_dashboard_ninja/static/description/images/icons/generate-ai.svg" alt=""/>-->
<!--                                                <span class="fa fa-lg"/>-->
<!--                                                Switch to Default Dashboard-->
<!--                                                <span class="caret"/>-->
<!--                                            </button>-->
                                        </t>
                                        <t t-else="">

                                            <div class="dropdown dash-dd-2 magic-star-dd">
                                                <a class="text-decoration-none dropdown-toggle img-bg info me-lg-2 me-1" href="#"
                                                    role="button" data-bs-toggle="dropdown" aria-expanded="false">
<!--                                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/magic-star.svg" alt="candle-2" class="img-fluid" loading="lazy"/>-->
                                                    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M8.01757 5.03498C8.24044 4.43268 9.09232 4.43268 9.31519 5.03498L10.7982 9.04271C10.8683 9.23208 11.0176 9.38137 11.2069 9.45144L15.2147 10.9344C15.817 11.1573 15.817 12.0092 15.2147 12.2321L11.2069 13.7151C11.0176 13.7851 10.8683 13.9344 10.7982 14.1238L9.31519 18.1315C9.09232 18.7338 8.24044 18.7338 8.01757 18.1315L6.53457 14.1238C6.4645 13.9344 6.31521 13.7851 6.12585 13.7151L2.11811 12.2321C1.51581 12.0092 1.51581 11.1573 2.11811 10.9344L6.12585 9.45144C6.31521 9.38137 6.4645 9.23208 6.53457 9.04271L8.01757 5.03498Z" fill="" stroke="" stroke-width="1.25"/>
                                                        <path d="M17.6239 5L13.6239 5" stroke="" stroke-width="1.25" stroke-linecap="round"/>
                                                        <path d="M15.6244 7L15.6244 3" stroke="" stroke-width="1.25" stroke-linecap="round"/>
                                                    </svg>

                                                </a>

                                                <ul class="dropdown-menu py-0">
                                                    <li>
                                                        <button class="feature-btn dropdown-item" t-on-click="kscreateaiitem">
                                                            <span>
                                                                <img src="ks_dashboard_ninja/static/images/dashboardOverview/box.png" alt="box" class="img-fluid me-2"
                                                                    loading="lazy"/>
                                                            </span>
                                                            Generate with AI
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button class="feature-btn dropdown-item" t-on-click="ks_gen_ai_analysis">
                                                            <span>
                                                                <img src="ks_dashboard_ninja/static/images/dashboardOverview/convertshape.png" alt="convertshape"
                                                                    class="img-fluid me-2" loading="lazy"/>
                                                            </span>
                                                            Explain with AI
                                                        </button>
                                                    </li>
<!--                                                    <li>-->
<!--                                                          <button class="feature-btn dropdown-item" t-on-click="ks_chat_with_ai">-->
<!--                                                                <span>-->
<!--                                                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/chat-ai.svg" alt="Graph" class="img-fluid me-2"-->
<!--                                                                        loading="lazy"/>-->
<!--                                                                </span>-->
<!--                                                                Chat with AI-->
<!--                                                          </button>-->
<!--                                                    </li>-->

                                                    <li>
                                                        <button class="feature-btn d-xl-none d-block dark dropdown-item" t-on-click="onAddItemTypeClick">
                                                            <span>
                                                                <img src="ks_dashboard_ninja/static/images/dashboardOverview/Graph 1.svg" alt="Graph" class="img-fluid me-1"
                                                                    loading="lazy"/>
                                                            </span>
                                                            Create New Chart
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>

                                            <button class="feature-btn dark d-xl-block d-none me-2" t-on-click="onAddItemTypeClick" title="Add the Charts to Dashboard">
                                                <span>
                                                    <img src="ks_dashboard_ninja/static/images/dashboardOverview/Graph 1.svg" alt="Graph" class="img-fluid me-1" loading="lazy"/>
                                                </span>
                                                Create New Chart
                                            </button>
                                        </t>
                                    </div>
                                </div>
                            </t>
                            <div class="d-flex custom_date_filter_section align-items-center">
                                <div class="ks_date_input_fields ks_hide d-flex gap-2">
                                    <div class="form-input-box form-control start-date">
                                        <DateTimeInput value="state.ksDateFilterStartDate" type="'datetime'"
                                                   id="'ks_btn_middle_child'" placeholder="'Start Date...'"
                                                   onChange="(date) => this.loadDashboardData(date)"/>
                                    </div>

                                    <div class="form-input-box form-control start-date">
                                        <DateTimeInput value="state.ksDateFilterEndDate" type="'datetime'"
                                                         id="'ks_btn_last_child'"
                                                         placeholder="'End Date...'"
                                                         onChange="(date) => this.loadDashboardData(date)"/>
                                    </div>
                                </div>

                                <div class="ks_date_apply_clear_print">
                                        <!--Apply and Clear buttons will only be shown when Date filter : Custom-->
                                      <button type='button'
                                              class='dash-btn-red apply-dashboard-date-filter ks_hide'
                                              t-on-click="_onKsApplyDateFilter">Apply
                                      </button>
                                      <button type='button'
                                              class='dash-default-btn bg-white clear-dashboard-date-filter ks_hide'
                                              t-on-click="_onKsClearDateValues">Clear
                                      </button>
<!--                                      <button type='button'-->
<!--                                              class='btn btn-primary d-none d-lg-block fa fa-print print-dashboard-btn m-1 ml-3 ks_hide'-->
<!--                                              title="Print"/>-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <t t-call="ks_dashboard_ninja.ks_main_body_container"/>
        </main>



    </t>


    <t t-name="ks_dashboard_ninja.dashboardNinja" owl="1">
         <main class="main-box">
            <section class="overview-sec-one mb-4 pb-1">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <h1>Welcome <span t-esc="data.user_name"/></h1>
                    <div class="d-flex align-items-center">
                        <!-- custom-dd-2-start -->
                        <div class="dropdown dash-dd-2">
                            <a t-ref="overviewFilter" class="text-decoration-none dropdown-toggle" href="#" role="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <t t-esc="state.filter"/>
                            </a>

                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" t-on-click="onFilterChange">All Dashboards</a></li>
                                <li><a class="dropdown-item" href="#" t-on-click="onFilterChange">Bookmarked</a></li>
                            </ul>
                        </div>
                        <!-- custom-dd-2-end -->
                        <button class="dash-btn-red d-flex align-items-center ms-3 d-none d-md-flex" t-on-click="createDashboard">
                            <span>
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="transparent" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#fff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M8 12H16" stroke="#fff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12 16V8" stroke="#fff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                            <span class="ms-1">
                                Add New Dashboard
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </section>
            <section class="overview-sec-tiles mb-4 pb-1 container-fluid">
                <div class="d-flex align-items-center row-gap-3 row">
                    <t t-foreach="[0,1,2,3,4]" t-as="i" t-key="i">
                        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-12">
                            <div t-attf-class="tile {{overviewTilesNames[i][1]}} d-flex justify-content-between align-items-center">
                                <div>
                                    <div t-attf-id="{{overviewTilesNames[i][0]}}" class="num mb-1">
                                        <t t-esc="data.overviewInfo[i]"/>
                                    </div>
                                    <p class="mb-1"><t t-esc="overviewTilesNames[i][0]"/></p>
                                </div>
                                <div>
                                    <img t-attf-src="ks_dashboard_ninja/static/images/dashboardOverview/{{overviewTilesNames[i][0]}}.svg" alt="category" loading="lazy" class="img-fluid"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </section>
            <section class="overview-sec-dasboards container-fluid">
                <t t-set="filteredDashboardsInfo" t-value="filteredDashboards"/>
                <!-- no-data-found -->
                <div t-if="!filteredDashboardsInfo" class="d-flex flex-column justify-content-center align-items-center h-100 no-data">
                    <div>
                        <img src="ks_dashboard_ninja/static/images/dashboardOverview/no-data.png" alt="no-data-files" class="img-fluid" loading="lazy"/>
                    </div>
                    <h3 class="mb-0">
                        No Data Found!!
                    </h3>
                    <p class="mb-3">No Dashboard’s have been Found</p>
                    <button class="dash-btn-red d-flex align-items-center" t-on-click="createDashboard">
                        <span>
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="transparent" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#fff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 12H16" stroke="#fff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 16V8" stroke="#fff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>
                        <span class="ms-2">
                            Add New Dashboard
                        </span>
                    </button>
                </div>

                <div class="row row-gap-34 data-added" t-if="filteredDashboardsInfo">
                    <t t-foreach="filteredDashboardsInfo" t-as="dashboard" t-key="dashboard">
                        <div class="col-xl-4 col-md-6 col-12">
                            <div class="dashboard-box flex-column d-flex align-items-center justify-content-center">
                                <div class="mb-2 dash-preview-box">
                                    <img t-att-src="data.dashboardsInfo[dashboard].image" alt="dashboard-img" class="img-fluid"
                                         loading="lazy"/>
                                </div>
                                <div class="d-flex w-100 justify-content-between align-items-center dashboard-info">
                                    <div class="left">
                                        <h4><t t-esc="data.dashboardsInfo[dashboard].name"/></h4>
                                        <p><t t-esc="data.dashboardsInfo[dashboard].chartCount"/>
                                            Charts</p>
                                    </div>
                                    <!--  -->
                                    <div class="right">
                                        <div class="marked mb-2 d-flex justify-content-end align-items-center">
                                            <div t-attf-id="unBookmark{{data.dashboardsInfo[dashboard].id}}" t-att-class="data.dashboardsInfo[dashboard].is_bookmarked ? 'd-none' : ''"
                                                 t-on-click="updateBookmark" t-att-data-dashboard-id="data.dashboardsInfo[dashboard].id"
                                                 title="Bookmark the dashboard">
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" class="bookmark"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.70831 7.54163C9.19165 8.08329 10.8083 8.08329 12.2916 7.54163"
                                                      stroke="#292D32" stroke-width="1.25" stroke-linecap="round"
                                                      stroke-linejoin="round"/>
                                                <path
                                                        d="M14.0169 1.66669H5.98357C4.20857 1.66669 2.76691 3.11669 2.76691 4.88335V16.625C2.76691 18.125 3.84191 18.7584 5.15857 18.0334L9.22524 15.775C9.65857 15.5334 10.3586 15.5334 10.7836 15.775L14.8502 18.0334C16.1669 18.7667 17.2419 18.1334 17.2419 16.625V4.88335C17.2336 3.11669 15.7919 1.66669 14.0169 1.66669Z"
                                                        stroke="#292D32" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round"/>
                                                <path
                                                        d="M14.0169 1.66669H5.98357C4.20857 1.66669 2.76691 3.11669 2.76691 4.88335V16.625C2.76691 18.125 3.84191 18.7584 5.15857 18.0334L9.22524 15.775C9.65857 15.5334 10.3586 15.5334 10.7836 15.775L14.8502 18.0334C16.1669 18.7667 17.2419 18.1334 17.2419 16.625V4.88335C17.2336 3.11669 15.7919 1.66669 14.0169 1.66669Z"
                                                        stroke="#292D32" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round"/>
                                            </svg>
                                            </div>

                                            <!-- bookmarked -->
                                            <div t-attf-id="bookmark{{data.dashboardsInfo[dashboard].id}}" t-on-click="updateBookmark"
                                                 t-att-class="data.dashboardsInfo[dashboard].is_bookmarked ? '' : 'd-none'"
                                                 t-att-data-dashboard-id="data.dashboardsInfo[dashboard].id"
                                                 title="Bookmarked">
                                                <svg width="16" height="18" viewBox="0 0 16 18" fill="none"
                                                 class="bookmarked cursor-pointer" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12.0165 0.666687H3.98318C2.20818 0.666687 0.766518 2.11669 0.766518 3.88335V15.625C0.766518 17.125 1.84152 17.7584 3.15818 17.0334L7.22485 14.775C7.65818 14.5334 8.35818 14.5334 8.78318 14.775L12.8499 17.0334C14.1665 17.7667 15.2415 17.1334 15.2415 15.625V3.88335C15.2332 2.11669 13.7915 0.666687 12.0165 0.666687ZM10.5082 7.12502C9.69985 7.41669 8.84985 7.56669 7.99985 7.56669C7.14985 7.56669 6.29985 7.41669 5.49152 7.12502C5.16652 7.00835 4.99985 6.65002 5.11652 6.32502C5.24152 6.00002 5.59985 5.83335 5.92485 5.95002C7.26652 6.43335 8.74152 6.43335 10.0832 5.95002C10.4082 5.83335 10.7665 6.00002 10.8832 6.32502C10.9999 6.65002 10.8332 7.00835 10.5082 7.12502Z"
                                                        fill="#6789C6"/>
                                                </svg>
                                            </div>

                                            <button class="img-bg bookmark-capture ms-lg-2 ms-1 d-md-block d-none"
                                                    t-att-data-dashboard-id="data.dashboardsInfo[dashboard].id" t-on-click="viewDashboard"
                                                    title="To update the Dashboard image, go to dashboard and update through camera icon. We can also scroll in the dashboard and capture only the visible part.">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M6.75992 22H17.2399C19.9999 22 21.0999 20.31 21.2299 18.25L21.7499 9.99C21.8899 7.83 20.1699 6 17.9999 6C17.3899 6 16.8299 5.65 16.5499 5.11L15.8299 3.66C15.3699 2.75 14.1699 2 13.1499 2H10.8599C9.82992 2 8.62992 2.75 8.16992 3.66L7.44992 5.11C7.16992 5.65 6.60992 6 5.99992 6C3.82992 6 2.10992 7.83 2.24992 9.99L2.76992 18.25C2.88992 20.31 3.99992 22 6.75992 22Z" stroke="" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M10.5 8H13.5" stroke="" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M12 18C13.79 18 15.25 16.54 15.25 14.75C15.25 12.96 13.79 11.5 12 11.5C10.21 11.5 8.75 12.96 8.75 14.75C8.75 16.54 10.21 18 12 18Z" stroke="" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </button>

                                        </div>
                                        <a class="dash-link d-flex align-items-center" t-att-data-dashboard-id="data.dashboardsInfo[dashboard].id"
                                           t-on-click="viewDashboard" title="Click to view the Dashboard">
                                            View Dashboard
                                            <i class="fa fa-angle-right ms-1" aria-hidden="true"/>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </section>
         </main>
    </t>

<!--    <t t-name="ks_dn_favourite_filter">-->
<!--        <div class="ks_dashboard_top_menu ks_select_none ks_dn_favourite_filters" style="margin: auto;">-->
<!--            <div class="ks_dashboard_link ks_am_content_element ks_hide mr-4" t-if="Object.keys(ks_dashboard_data.ks_dashboard_custom_domain_filter).length || Object.keys(ks_dashboard_data.ks_dashboard_pre_domain_filter).length"-->
<!--            >-->
<!--                <div class="ks_dn_filter_selection_input">-->
<!--                    <div class="ks_dn_selection_box">-->
<!--                        <div class="btn-group ">-->
<!--                            <button class="o_dropdown_toggler_btn btn btn-secondary dropdown-toggle ks_favourite_filter_dropdown"-->
<!--                                    data-toggle="dropdown"-->
<!--                                    aria-expanded="false">-->
<!--                                <span class="fa fa-lg fa-star"/>-->
<!--                                <span class="ks_dn_filter_selection">Favourite</span>-->
<!--                                <span class="caret"/>-->
<!--                            </button>-->
<!--                            <div class="dropdown-max-height ks_date_filters_menu_drop_down ks_favourite_filters_menu_drop_down ks_dashboard_custom_srollbar" role="menu">-->
<!--                                <div class="ks_dn_fav_filters">-->
<!--                                    <t t-call="ks_dashboard_fav_filters"/>-->
<!--                                </div>-->
<!--                                <div class="dropdown dropright o-dropdown ks_o_add_favorite show o_add_favorite position-relative">-->
<!--                                    <button class="dropdown-toggle o_dropdown_toggler_btn btn btn-secondary dropdown-item"-->
<!--                                        >-->
<!--                                        <span>Save Current Search</span>-->
<!--                                    </button>-->
<!--                                    <div role="menu" class="o-popper-position o-popper-position&#45;&#45;re ks_dropdown_hover">-->
<!--                                        <div class="px-3 py-2">-->
<!--                                            <input type="text"-->
<!--                                                class="o_input ks_fav_filter_name"-->
<!--                                                autofocus=""-->
<!--                                                />-->
<!--                                            <div class="custom-control custom-checkbox ks_close_dropdown_menu">-->
<!--                                                <input id="ks_fav_share_other" type="checkbox" class="custom-control-input"/>-->
<!--                                                <label for="ks_fav_share_other" class="custom-control-label"> Share with all users </label>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                        <div class="px-3 py-2">-->
<!--                                            <button class="ks_o_save_favorite btn btn-primary" t-on-click="ks_save_favourite">-->
<!--                                                Save-->
<!--                                            </button>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </t>-->


<!--<t t-name="ks_dashboard_fav_filters">-->
<!--    <div>-->
<!--        <t t-foreach="ks_favourite_filters" t-as="filter" t-key="filter">-->
<!--            <span class="dropdown-item o_menu_item ks_fav_filters_selector" t-on-click="onksfavfilterselected" t-att-fav-name="ks_favourite_filters[filter].name">-->
<!--                <span class="d-flex p-0 align-items-center justify-content-between">-->
<!--                    <t t-esc="filter"/>-->
<!--                    <i class="o_icon_right fa fa-trash-o ks_del_favourite_filter" t-on-click="ks_delete_favourite_filter" title="Delete item" t-att-fav-id="ks_favourite_filters[filter].id" t-att-fav-name="ks_favourite_filters[filter].name"></i>-->
<!--                </span>-->

<!--            </span>-->
<!--        </t>-->
<!--    </div>-->
<!--    </t>-->








    <t t-name="ks_dn_layout_container">
        <!--        <div class="ks_am_element">-->
        <!--            <button id="ks_dn_layout_button" class="o_dropdown_toggler_btn btn btn-secondary dropdown-toggle"-->
        <!--                    data-toggle="dropdown" aria-expanded="false">-->
        <!--                <span>-->
        <!--                    <t t-esc="ks_child_boards[ks_selected_board_id][0]"/>-->
        <!--                </span>-->
        <!--                <span class="caret"></span>-->
        <!--            </button>-->
        <!--            <ul id="ks_dashboard_layout_dropdown_container"-->
        <!--                class="dropdown-menu ks_dashboard_custom_srollbar dropdown-max-height"-->
        <!--                role="menu">-->
        <!--                <t t-foreach="_(ks_child_boards).keys()" t-as="layout_id">-->
        <!--                    <li t-att-class="ks_selected_board_id === layout_id ? 'ks_dashboard_layout_event ks_layout_selected': 'ks_dashboard_layout_event'"-->
        <!--                        t-att-data-ks_layout_id="layout_id">-->
        <!--                        <span class="df_selection_text">-->
        <!--                            <t t-esc="ks_child_boards[layout_id][0]"/>-->
        <!--                        </span>-->
        <!--                    </li>-->
        <!--                </t>-->
        <!--            </ul>-->
        <!--        </div>-->
        <t t-if="state.ks_multi_layout">
            <div class="ks_am_element dash-dd-2">
                <button id="ks_dn_layout_button" class="o_dropdown_toggler_btn dashboard_name_bg dropdown-toggle"
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <span>
                        <t t-esc="state.ks_child_boards[state.ks_selected_board_id][0]"/>
                    </span>
                    <span class="caret"></span>
                </button>
                <ul id="ks_dashboard_layout_dropdown_container"
                    class="dropdown-menu ks_dashboard_custom_srollbar dropdown-max-height"
                    role="menu">
                    <t t-foreach="Object.keys(state.ks_child_boards)" t-as="layout_id" t-key="layout_id_index">
                        <li t-att-class="state.ks_selected_board_id === layout_id ? 'ks_dashboard_layout_event ks_layout_selected dropdown-item': ' dropdown-item ks_dashboard_layout_event'"
                            t-att-data-ks_layout_id="layout_id" t-on-click="_ksOnDnLayoutMenuSelect">
                            <span class="df_selection_text">
                                <t t-esc="state.ks_child_boards[layout_id][0]"/>
                            </span>
                        </li>
                    </t>
                </ul>
            </div>
        </t>
        <t t-else="">
            <span id="ks_dashboard_title_label" class="ks_am_element dash-dd-2">
                <t t-esc="state.ks_dash_name"/>
            </span>
        </t>
    </t>
    <!--    Dashboard Main Body Container -->
    <t t-name="ks_dashboard_ninja.ks_main_body_container" owl="1">

        <div class="ks_dashboard_main_content explain-ai-main-content" t-ref="ks_main_body">
            <t t-if="state.ks_dashboard_item_length != 0">
                <div class="ks_dashboard_item_content grid-stack ks_dashboard_items_list m-3" gs-w="36"/>
                <t t-call="ks_dashboard_item_template"/>
            </t>
        </div>
        <t t-if="state.ks_dashboard_item_length == 0">
            <t t-call="ksNoItemView"/>
        </t>
    </t>

    <!--    Empty Dashboard View Layout-->
    <t t-name="ksNoItemView">
        <div class="o_view_nocontent">
            <div class="o_nocontent_help">
                <p class="o_view_nocontent_neutral_face">
                    Your personal dashboard is empty
                </p>
                <p>
                    To add dashboard item, use
                    <a>
                        <strong class="ks_add_dashboard_item_on_empty">Add button</strong>
                    </a>
                    on top right corner.
                </p>
            </div>
        </div>
    </t>

    <!--Item Layouts : -->
    <t t-name="ks_dashboard_item_template">
        <t t-foreach="state.ks_dashboard_items" t-as="items" t-key="items.id">
            <t t-if="items.ks_dashboard_item_type === 'ks_tile'">
                <Ksdashboardtile item="items" hideButtons="1" on_dialog="on_dialog" dashboard_data="ks_dashboard_data" ksdatefilter = "state.ksDateFilterSelection" pre_defined_filter = "state.pre_defined_filter" custom_filter="state.custom_filter" ks_speak="(ev)=>this.speak_once(ev,items)"/>
            </t>
            <t t-elif="items.ks_dashboard_item_type === 'ks_kpi'">
                <Ksdashboardkpiview item="items" hideButtons="1" on_dialog="on_dialog" dashboard_data="ks_dashboard_data" ksdatefilter="state.ksDateFilterSelection" pre_defined_filter = "state.pre_defined_filter" custom_filter="state.custom_filter" ks_speak="(ev)=>this.speak_once(ev,items)"/>
            </t>
            <t t-elif="items.ks_dashboard_item_type === 'ks_to_do'">
                <Ksdashboardtodo item="items" hideButtons="1" dashboard_data="ks_dashboard_data" explain_ai_whole="explain_ai_whole"/>
            </t>
            <t t-else="">
                <Ksdashboardgraph item="items" hideButtons="1" explain_ai_whole="explain_ai_whole" dashboard_data="ks_dashboard_data" ksdatefilter="state.ksDateFilterSelection" pre_defined_filter = "state.pre_defined_filter" custom_filter="state.custom_filter" ks_speak="(ev)=>this.speak_once(ev,items)"/>
            </t>

        </t>
    </t>


    <t t-name="ksQuickEditButtonContainer">
        <div class="ks_dashboard_quick_edit_action grid-stack-item" t-att-gs-x="grid.x"
             t-att-gs-y="grid.y" t-att-gs-w="grid.w" t-att-gs-h="grid.h">
            <button title="Quick Customize" data-bs-toggle="dropdown"
                    class="ks_dashboard_item_action  btn dropdown-toggle btn-xs o-no-caret btn"
                    type="button"
                    aria-expanded="true">
                <i class="fa fa-cog"/>
            </button>
            <div role="menu" class="dropdown-menu ks_qe_dropdown_menu ">
            </div>
        </div>
    </t>
(ev)=>self.kslayout(ev)
    <t t-name="ks_dn_layout_container_new">
        <t t-if="ks_multi_layout">
            <div class="ks_am_element dash-dd-2">
                <button id="ks_dn_layout_button" class="o_dropdown_toggler_btn dashboard_name_bg dropdown-toggle"
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <span>
                        <t t-esc="ks_child_boards[ks_selected_board_id][0]"/>
                    </span>
                    <span class="caret"></span>
                </button>
                <ul id="ks_dashboard_layout_dropdown_container"
                    class="dropdown-menu ks_dashboard_custom_srollbar dropdown-max-height"
                    role="menu">
                    <t t-foreach="Object.keys(ks_child_boards)" t-as="layout_id" t-key="layout_id_index">
                        <li t-att-class="ks_selected_board_id === layout_id ? 'ks_dashboard_layout_event ks_layout_selected': 'ks_dashboard_layout_event'"
                            t-att-data-ks_layout_id="layout_id" t-on-click="(ev)=>self._ksOnDnLayoutMenuSelect(ev)">
                            <span class="df_selection_text">
                                <t t-esc="ks_child_boards[layout_id][0]"/>
                            </span>
                        </li>
                    </t>
                </ul>
            </div>
        </t>
        <t t-else="">
            <span id="ks_dashboard_title_label" class="ks_am_element dash-dd-2">
                <t t-esc="ks_dash_name"/>
            </span>
        </t>
    </t>


</templates>