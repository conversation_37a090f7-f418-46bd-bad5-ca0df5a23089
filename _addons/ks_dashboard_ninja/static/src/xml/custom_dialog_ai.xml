<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
    <t t-name="ks_dashboard_ninja.CustomDialog">
        <Dialog title="''" footer="false">
            <div class="container-fluid">
                <div class="row" style="min-height: 60vh;">
                    <div class="col-6 d-flex flex-column align-items-stretch chat_explain_ai">
                        <t t-if="props.ks_dashboard_item_type === 'ks_tile'">
                            <Ksdashboardtile item="props.item"
                                             dashboard_data="props.dashboard_data"
                                             ksdatefilter="props.ksdatefilter"
                                             pre_defined_filter="props.pre_defined_filter"
                                             custom_filter="props.custom_filter"
                                             hideButtons="0"
                                             on_dialog="true"
                                             ks_speak="props.ks_speak"/>
                        </t>
                        <t t-elif="props.ks_dashboard_item_type === 'ks_kpi'">
                            <Ksdashboardkpiview item="props.item"
                                                dashboard_data="props.dashboard_data"
                                                ksdatefilter="props.ksdatefilter"
                                                pre_defined_filter="props.pre_defined_filter"
                                                custom_filter="props.custom_filter"
                                                hideButtons="0"
                                                on_dialog="true"
                                                ks_speak="props.ks_speak"/>
                        </t>
                        <t t-elif="props.ks_dashboard_item_type === 'ks_to_do'">
                            <Ksdashboardtodo item="props.item"
                                             dashboard_data="props.dashboard_data"
                                             hideButtons="0"
                            />
                        </t>
                        <t t-else="">
                            <t t-if="props.item.ks_dashboard_item_type != 'ks_list_view'">
                                <div class="common-tab-pills">
                                    <ul class="nav nav-pills mb-3 border-bottom border-2" id="pills-tab" role="tablist">
                                        <t t-set="chart_name" t-value="props.ks_dashboard_item_type"/>
                                        <li class="nav-item" role="presentation">
                                            <button t-attf-class="nav-link text-primary fw-semibold position-relative #{chart_name =='ks_bar_chart'?'show active':''}"
                                                id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-one" type="button"
                                                role="tab" aria-controls="pills-home" aria-selected="true"
                                                t-on-click="()=>this.switch_bar_chart('ks_bar_chart')">

                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M2.5 18.3334H17.5" stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M4.66665 6.9834H3.33333C2.875 6.9834 2.5 7.3584 2.5 7.81673V15.0001C2.5 15.4584 2.875 15.8334 3.33333 15.8334H4.66665C5.12498 15.8334 5.49998 15.4584 5.49998 15.0001V7.81673C5.49998 7.3584 5.12498 6.9834 4.66665 6.9834Z"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M10.6666 4.32507H9.33333C8.875 4.32507 8.5 4.70007 8.5 5.15841V15.0001C8.5 15.4584 8.875 15.8334 9.33333 15.8334H10.6666C11.125 15.8334 11.5 15.4584 11.5 15.0001V5.15841C11.5 4.70007 11.125 4.32507 10.6666 4.32507Z"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M16.6666 1.66675H15.3333C14.875 1.66675 14.5 2.04175 14.5 2.50008V15.0001C14.5 15.4584 14.875 15.8334 15.3333 15.8334H16.6666C17.125 15.8334 17.5 15.4584 17.5 15.0001V2.50008C17.5 2.04175 17.125 1.66675 16.6666 1.66675Z"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>

                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button t-attf-class="nav-link text-primary fw-semibold position-relative #{chart_name =='ks_area_chart'?'show active':''}"
                                                id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-two"
                                                type="button" role="tab" aria-controls="pills-profile" aria-selected="false"
                                                t-on-click="()=>this.switch_bar_chart('ks_area_chart')">
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.50033 18.3333L12.5003 18.3333C16.667 18.3333 18.3337 16.6666 18.3337 12.5L18.3337 7.49996C18.3337 3.33329 16.667 1.66663 12.5003 1.66663L7.50033 1.66663C3.33366 1.66663 1.66699 3.33329 1.66699 7.49996L1.66699 12.5C1.66699 16.6666 3.33366 18.3333 7.50033 18.3333Z"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M6.1084 12.075L8.09173 9.50005C8.37507 9.13338 8.90007 9.06672 9.26673 9.35005L10.7917 10.55C11.1584 10.8334 11.6834 10.7667 11.9667 10.4084L13.8917 7.92505"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>

                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button t-attf-class="nav-link text-primary fw-semibold position-relative #{chart_name =='ks_pie_chart'?'show active':''}"
                                                id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-three"
                                                type="button" role="tab" aria-controls="pills-profile" aria-selected="false"
                                                t-on-click="()=>this.switch_bar_chart('ks_pie_chart')">
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M15.2667 10.0002C17.4333 10.0002 18.3333 9.16686 17.5333 6.43352C16.9917 4.59186 15.4083 3.00852 13.5667 2.46686C10.8333 1.66686 10 2.56686 10 4.73352V7.13352C10 9.16686 10.8333 10.0002 12.5 10.0002H15.2667Z"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M16.6672 12.2498C15.8922 16.1081 12.1922 18.9081 7.98384 18.2248C4.82551 17.7164 2.28384 15.1748 1.76718 12.0164C1.09218 7.82476 3.87551 4.12476 7.71718 3.34143"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button t-attf-class="nav-link text-primary fw-semibold position-relative #{chart_name =='ks_radialBar_chart'?'show active':''}"
                                                id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-four"
                                                type="button" role="tab" aria-controls="pills-profile" aria-selected="false"
                                                t-on-click="()=>this.switch_bar_chart('ks_radialBar_chart')">
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M3.35033 4.97496C2.29199 6.37496 1.66699 8.11663 1.66699 9.99996C1.66699 14.6 5.40033 18.3333 10.0003 18.3333C14.6003 18.3333 18.3337 14.6 18.3337 9.99996C18.3337 5.39996 14.6003 1.66663 10.0003 1.66663"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M4.16699 9.99996C4.16699 13.225 6.77533 15.8333 10.0003 15.8333C13.2253 15.8333 15.8337 13.225 15.8337 9.99996C15.8337 6.77496 13.2253 4.16663 10.0003 4.16663"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M10 13.3333C11.8417 13.3333 13.3333 11.8416 13.3333 9.99996C13.3333 8.15829 11.8417 6.66663 10 6.66663"
                                                        stroke="" stroke-width="1.25" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button t-attf-class="nav-link text-primary fw-semibold position-relative #{chart_name =='ks_funnel_chart'?'show active':''}"
                                                id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-five"
                                                type="button" role="tab" aria-controls="pills-profile" aria-selected="false"
                                                t-on-click="()=>this.switch_bar_chart('ks_funnel_chart')">
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M4.50065 1.75H15.5007C16.4173 1.75 17.1673 2.5 17.1673 3.41667V5.25C17.1673 5.91667 16.7507 6.75 16.334 7.16667L12.7507 10.3333C12.2507 10.75 11.9173 11.5833 11.9173 12.25V15.8333C11.9173 16.3333 11.584 17 11.1673 17.25L10.0007 18C8.91732 18.6667 7.41732 17.9167 7.41732 16.5833V12.1667C7.41732 11.5833 7.08398 10.8333 6.75065 10.4167L3.58398 7.08333C3.16732 6.66667 2.83398 5.91667 2.83398 5.41667V3.5C2.83398 2.5 3.58398 1.75 4.50065 1.75Z"
                                                        stroke="" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path d="M9.10833 1.75L5 8.33333" stroke="" stroke-width="1.25"
                                                        stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </t>
                            <div class="flex-grow-1 chart-insight position-relative" t-ref="graph_div">
                                <Ksdashboardgraph item="props.item"
                                                  dashboard_data="props.dashboard_data"
                                                  ksdatefilter="props.ksdatefilter"
                                                  pre_defined_filter="props.pre_defined_filter"
                                                  custom_filter="props.custom_filter"
                                                  hideButtons="0"
                                                  ks_speak="props.ks_speak"

                                />
                            </div>
                        </t>
                    </div>
                    <div id="explain_ai"  class="col-6 d-flex1 flex-column" style="min-height: 100%;">
                        <t t-if="state.switch_layout">
                        <t t-if="props.ks_dashboard_item_type != 'ks_to_do' and props.ks_dashboard_item_type != 'ks_kpi' and props.ks_dashboard_item_type != 'ks_tile' and props.item.ks_dashboard_item_type != 'ks_list_view' and props.item.ks_dashboard_item_type != state.newItem">

                            <div class="share-switch d-flex justify-content-end align-items-center p-3">
                                 <button class="dash-btn-red d-flex justify-content-center align-items-center me-2"
                                         t-on-click="()=>this.ks_switch_layout(selectedItem)" title="Switch chart on Dashboard">
                                    <span class="me-2">
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M10.7673 1.88333L16.1923 4.80833C16.8257 5.14999 16.8257 6.12499 16.1923 6.46666L10.7673 9.39166C10.284 9.64999 9.71732 9.64999 9.23398 9.39166L3.80898 6.46666C3.17565 6.12499 3.17565 5.14999 3.80898 4.80833L9.23398 1.88333C9.71732 1.62499 10.284 1.62499 10.7673 1.88333Z"
                                                stroke="#FFF" stroke-width="1.25" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path
                                                d="M3.00768 8.44161L8.04935 10.9666C8.67435 11.2833 9.07435 11.9249 9.07435 12.6249V17.3916C9.07435 18.0833 8.34935 18.5249 7.73268 18.2166L2.69102 15.6916C2.06602 15.3749 1.66602 14.7333 1.66602 14.0333V9.26661C1.66602 8.57494 2.39102 8.13327 3.00768 8.44161Z"
                                                stroke="#FFF" stroke-width="1.25" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path
                                                d="M16.9924 8.44161L11.9508 10.9666C11.3258 11.2833 10.9258 11.9249 10.9258 12.6249V17.3916C10.9258 18.0833 11.6508 18.5249 12.2674 18.2166L17.3091 15.6916C17.9341 15.3749 18.3341 14.7333 18.3341 14.0333V9.26661C18.3341 8.57494 17.6091 8.13327 16.9924 8.44161Z"
                                                stroke="#FFF" stroke-width="1.25" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>

                                    </span>
                                    <span>Switch Layout</span>
                                </button>
<!--                                    <div class="img-bg bg-F5F8FB">-->
<!--                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none"-->
<!--                                        xmlns="http://www.w3.org/2000/svg">-->
<!--                                        <path fill-rule="evenodd" clip-rule="evenodd"-->
<!--                                            d="M14.9993 2.50004C14.0789 2.50004 13.3327 3.24623 13.3327 4.16671C13.3327 5.08718 14.0789 5.83337 14.9993 5.83337C15.9198 5.83337 16.666 5.08718 16.666 4.16671C16.666 3.24623 15.9198 2.50004 14.9993 2.50004ZM11.666 4.16671C11.666 2.32576 13.1584 0.833374 14.9993 0.833374C16.8403 0.833374 18.3327 2.32576 18.3327 4.16671C18.3327 6.00766 16.8403 7.50004 14.9993 7.50004C13.1584 7.50004 11.666 6.00766 11.666 4.16671Z"-->
<!--                                            fill="#241C1D" />-->
<!--                                        <path fill-rule="evenodd" clip-rule="evenodd"-->
<!--                                            d="M4.99935 8.33337C4.07887 8.33337 3.33268 9.07957 3.33268 10C3.33268 10.9205 4.07887 11.6667 4.99935 11.6667C5.91982 11.6667 6.66602 10.9205 6.66602 10C6.66602 9.07957 5.91982 8.33337 4.99935 8.33337ZM1.66602 10C1.66602 8.15909 3.1584 6.66671 4.99935 6.66671C6.8403 6.66671 8.33268 8.15909 8.33268 10C8.33268 11.841 6.8403 13.3334 4.99935 13.3334C3.1584 13.3334 1.66602 11.841 1.66602 10Z"-->
<!--                                            fill="#241C1D" />-->
<!--                                        <path fill-rule="evenodd" clip-rule="evenodd"-->
<!--                                            d="M14.9993 14.1667C14.0789 14.1667 13.3327 14.9129 13.3327 15.8334C13.3327 16.7538 14.0789 17.5 14.9993 17.5C15.9198 17.5 16.666 16.7538 16.666 15.8334C16.666 14.9129 15.9198 14.1667 14.9993 14.1667ZM11.666 15.8334C11.666 13.9924 13.1584 12.5 14.9993 12.5C16.8403 12.5 18.3327 13.9924 18.3327 15.8334C18.3327 17.6743 16.8403 19.1667 14.9993 19.1667C13.1584 19.1667 11.666 17.6743 11.666 15.8334Z"-->
<!--                                            fill="#241C1D" />-->
<!--                                        <path fill-rule="evenodd" clip-rule="evenodd"-->
<!--                                            d="M6.43768 10.8388C6.6694 10.4412 7.1796 10.3066 7.57725 10.5384L13.2689 13.855C13.6666 14.0868 13.8011 14.597 13.5694 14.9946C13.3376 15.3923 12.8274 15.5268 12.4298 15.295L6.73812 11.9784C6.34047 11.7467 6.20596 11.2365 6.43768 10.8388Z"-->
<!--                                            fill="#241C1D" />-->
<!--                                        <path fill-rule="evenodd" clip-rule="evenodd"-->
<!--                                            d="M13.5608 5.00502C13.7927 5.40252 13.6585 5.91281 13.261 6.14478L7.57771 9.46145C7.18021 9.69342 6.66992 9.55923 6.43794 9.16173C6.20597 8.76423 6.34016 8.25394 6.73766 8.02197L12.421 4.7053C12.8185 4.47333 13.3288 4.60752 13.5608 5.00502Z"-->
<!--                                            fill="#241C1D" />-->
<!--                                    </svg>-->

<!--                                </div>-->
                            </div>

                        </t>
                        </t>
                        <div class="expalin-content-box">
                            <div class="d-flex justify-content-between align-items-center px-3 pt-3">
                                <div class="title"><t t-esc="state.explain_ai? '': 'Chat with AI'"/></div>
<!--                                <button class="chat-with-ai dash-default-btn" t-on-click="()=>{state.explain_ai = !state.explain_ai}">-->
<!--                                    <span class="me-2">-->
<!--                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none"-->
<!--                                            xmlns="http://www.w3.org/2000/svg">-->
<!--                                            <path-->
<!--                                                d="M11.9854 7.19334V9.86001C11.9854 10.0333 11.9787 10.2 11.9587 10.36C11.8054 12.16 10.7454 13.0533 8.79203 13.0533H8.52537C8.3587 13.0533 8.19869 13.1333 8.09869 13.2667L7.29871 14.3333C6.94537 14.8067 6.37203 14.8067 6.0187 14.3333L5.21869 13.2667C5.13202 13.1533 4.9387 13.0533 4.79203 13.0533H4.52537C2.3987 13.0533 1.33203 12.5267 1.33203 9.86001V7.19334C1.33203 5.24001 2.23204 4.18001 4.02537 4.02667C4.18537 4.00667 4.35204 4 4.52537 4H8.79203C10.9187 4 11.9854 5.06667 11.9854 7.19334Z"-->
<!--                                                stroke="#241C1D" stroke-miterlimit="10" stroke-linecap="round"-->
<!--                                                stroke-linejoin="round" />-->
<!--                                            <path-->
<!--                                                d="M14.654 4.52671V7.19338C14.654 9.15338 13.754 10.2067 11.9607 10.36C11.9807 10.2 11.9873 10.0334 11.9873 9.86005V7.19338C11.9873 5.06671 10.9207 4.00004 8.79401 4.00004H4.52734C4.35401 4.00004 4.18734 4.00671 4.02734 4.02671C4.18068 2.23338 5.24068 1.33337 7.19401 1.33337H11.4607C13.5873 1.33337 14.654 2.40005 14.654 4.52671Z"-->
<!--                                                stroke="#241C1D" stroke-miterlimit="10" stroke-linecap="round"-->
<!--                                                stroke-linejoin="round" />-->
<!--                                            <path d="M8.99635 8.83333H9.00235" stroke="#241C1D" stroke-width="1.33333"-->
<!--                                                stroke-linecap="round" stroke-linejoin="round" />-->
<!--                                            <path d="M6.66432 8.83333H6.67032" stroke="#241C1D" stroke-width="1.33333"-->
<!--                                                stroke-linecap="round" stroke-linejoin="round" />-->
<!--                                            <path d="M4.33033 8.83333H4.33633" stroke="#241C1D" stroke-width="1.33333"-->
<!--                                                stroke-linecap="round" stroke-linejoin="round" />-->
<!--                                        </svg>-->
<!--                                    </span>-->
<!--                                        <span>-->
<!--                                           <t t-esc="state.explain_ai? 'Chat with AI': 'Explain with AI'"/>-->
<!--                                        </span>-->
<!--                                </button>-->
                            </div>
                            <t t-if="state.explain_ai">
                                <div class="explain-ai-box px-3">
                                    <div class="description-box mt-4">
                                        <t t-esc="props.item.ks_ai_analysis"/>
                                    </div>
                                </div>
                            </t>
                            <t t-else="">
                                <div class="chat-ai-box px-3">
                                <div class="chat-sec mt-4">
                                    <t t-foreach="state.messages" t-as="message" t-key="message_index">
                                       <t t-if="message.sender == 'ai'">
                                            <div class="left">
                                        <div class="d-flex align-items-center mb-3">
                                            <span class="ai-icon me-2">
                                                <img src="/ks_dashboard_ninja/static/images/ai-icon.png" alt="ai-assistant" class="img-fluid"
                                                    loading="lazy"/>
                                            </span>
                                            <span class="title">AI Assistant</span>
                                        </div>
                                        <div class="answers">
                                            <t t-if="message.text == 'loading'">
                                               <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true" style="background:#e7495e;"></span>
                                               <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true" style="margin-left:5px;background:#e7495e;"></span>
                                               <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true" style="margin-left:5px;background:#e7495e;"></span>
                                            </t>
                                            <t t-else="">
                                                <t t-esc="message.text"/>
                                            </t>

                                        </div>
                                    </div>
                                       </t>
                                        <t t-else="">
                                            <div class="right  my-4 py-2">
                                        <div class="d-flex align-items-center mb-3 justify-content-end">
                                            <span class="title"><t t-esc="this.user"/></span>
                                            <span
                                                class="user-icon ms-2 d-flex justify-content-center align-items-center">
                                                <t t-esc="this.name_title"/>
                                            </span>
                                        </div>
                                        <div class="questions text-end">
                                           <t t-esc="message.text"/>
                                        </div>
                                    </div>
                                        </t>
                                    </t>
                                </div>
                                <div class="typer-box">
                                    <form class="typer d-flex align-items-end">
                                        <textarea name="" id="" placeholder="Chat with AI..." t-ref="ks_question" t-on-keyup="(ev)=>this.ks_key_check(ev)"></textarea>
                                        <button class="chat-logo" t-on-click="ks_send_request">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M5.54977 4.73999L11.9173 2.61749C14.7748 1.66499 16.3273 3.22499 15.3823 6.08249L13.2598 12.45C11.8348 16.7325 9.49477 16.7325 8.06977 12.45L7.43977 10.56L5.54977 9.92999C1.26727 8.50499 1.26727 6.17249 5.54977 4.73999Z"
                                                    stroke="white" stroke-width="1.5" stroke-linecap="round"
                                                    stroke-linejoin="round" />
                                                <path d="M7.58203 10.2375L10.267 7.54504L7.58203 10.2375Z"
                                                    fill="#241C1D" />
                                                <path d="M7.58203 10.2375L10.267 7.54504" stroke="white"
                                                    stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>

                                        </button>
                                    </form>
                                </div>
                            </div>
                            </t>
                        </div>
                    </div>
                </div>
                <t t-if="state.confirm_notification">
                        <div class="container tooltip-container">
                            <div class="d-flex align-items-center mx-auto tooltip-box">
                                <div class="tooltip-content">
                                    <img src="/ks_dashboard_ninja/static/images/demo-tooltip-img.png" alt="tootltip" class="img-fluid" loading="lazy"/>
                                </div>
                                <h5 class="tooltip-title mb-0">Your Chart has been Successfully Updated</h5>
                                <div class="ms-auto cursor-pointer" t-on-click="closeNotification">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                                        stroke="#241C1D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M9.16992 14.83L14.8299 9.17004" stroke="#241C1D" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M14.8299 14.83L9.16992 9.17004" stroke="#241C1D" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </t>
            </div>
        </Dialog>
    </t>
</templates>