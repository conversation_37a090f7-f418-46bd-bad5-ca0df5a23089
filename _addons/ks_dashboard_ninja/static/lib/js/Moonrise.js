"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[2480],{8283:function(e,r,l){l.r(r),l.d(r,{am5themes_Moonrise:function(){return f}});var o=l(1112),s=l(3409);class u extends s.Q{setupDefaultRules(){super.setupDefaultRules(),this.rule("ColorSet").setAll({colors:[o.Il.fromHex(3805954),o.Il.fromHex(6296069),o.<PERSON>.fromHex(9054989),o.<PERSON>.fromHex(13065764),o.<PERSON>.fromHex(13082457),o.<PERSON>.fromHex(10786154),o.<PERSON>.fromHex(8815977),o.Il.fromHex(7696225),o.Il.fromHex(5792096),o.Il.fromHex(6388099)],reuse:!0})}}const f=u}},function(e){var r=(8283,e(e.s=8283)),l=window;for(var o in r)l[o]=r[o];r.__esModule&&Object.defineProperty(l,"__esModule",{value:!0})}]);
//# sourceMappingURL=Moonrise.js.map