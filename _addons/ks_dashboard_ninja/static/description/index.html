<!DOCTYPE html>
<html lang="en">

<head>
    <title>Odoo Dashboard Ninja | Descrition</title>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link crossorigin href="https://fonts.gstatic.com" rel="preconnect">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap 5 CSS cdn -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .card-header .card-title img {
            transform: rotate(180deg);
        }

        .card-header.collapsed .card-title img {
            transform: rotate(0deg);
        }
    </style>
</head>

<body>
    <section class="py-3" style="background-color: #fff;">
        <div class="container">
            <div class="px-md-5 py-md-4 p-2 mb-4 bg-white shadow "
                style="border-radius: 20px; font-family: 'Inter', sans-serif;">
                <div class="ks-top-logos mb-4">
                    <div class="text-center mb-3">
                        <img src="img/Ksolves.png" class="img-fluid" alt="ks-logo" height="83" width="347"
                            loading="lazy">
                    </div>
                    <div class="d-flex justify-content-between align-items-center flex-lg-row flex-column"
                        style="row-gap: 16px">
                        <div>
                            <img src="./img/new-odoo.png" alt="" style="object-fit: cover" loading="lazy"
                                class="img-fluid">
                        </div>
                        <div class="odoo-logos d-flex align-items-center">
                            <img src="img/oddo-banner-logo.png" class="img-fluid" alt="odoo-logo" loading="lazy">
                        </div>
                    </div>
                </div>
                <!-- <div class="ks-top-logos pb-3">
                    <div class="text-center mb-md-4 mb-3">
                        <img src="./img/top-logo/logo1.png" class="img-fluid" alt="ks-logo" height="60" loading="lazy">
                    </div>
                    <div class="d-flex align-items-center justify-content-sm-between flex-column flex-sm-row">
                        <div class="odoo-logos d-flex align-items-center mb-sm-0 mb-3">
                            <img src="./img/top-logo/logo2.png" class="img-fluid" alt="logo" height="48" loading="lazy">
                        </div>
                        <div class="odoo-logos d-flex align-items-center">
                            <img src="./img/top-logo/logo3.png" class="img-fluid" alt="odoo-logo" height="32"
                                loading="lazy">
                        </div>
                    </div>
                </div> -->

                <!-- new div starts -->
                <div class="p-md-5 p-2 my-0 my-3" style="background-color: #163266; border-radius: 20px;">
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <h5
                                style="font-family: 'Inter', sans-serif; font-weight: bold; font-size: 32px; word-wrap: break-word; text-transform: capitalize; color: #00FFFF; font-size: calc(1.06rem + 1vw);">
                                Ksolves - Your one-stop solution
                            </h5>
                            <p
                                style="font-family: Inter; font-style: normal; font-weight: normal; font-size: 16px; line-height: 162.5%;text-align: center; color: #FFFFFF;">
                                We are available 24/7 for your service. Contact us today!
                            </p>
                        </div>
                    </div>
                    <div class=" mt-4 ">
                        <div class="row">
                            <div class="col-lg-5 col-12 text-center">
                                <p
                                    style="font-weight: 500; font-size: 16px; line-height: 21px; color:#fff; letter-spacing: 0.02em;">
                                    WATCH DEMO</p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-5 col-12">
                                <div class="text-center mb-3 mb-lg-0">
                                    <div class="position-relative pt-3">
                                        <div class="mt-3 mt-md-0"
                                            style="border:5px solid #fff;border-radius:5px;display:inline-block; ">
                                            <div class="s_panel_video position-relative " data-video-id="tczXPt0JvJs">
                                                <a class="s_figure_link p-0 position-absolute  w-100 h-100 d-flex align-items-center justify-content-center cursor-pointer"
                                                    href="https://www.youtube.com/watch?v=Kcye9yHu4rY&ab_channel=KsolvesIndiaLimited"
                                                    target="_blank">
                                                    <img alt="" src="./img/play-icon.png" />
                                                </a>
                                                <img alt="video-img" class="img-fluid w-100" loading="lazy"
                                                    src="./img/odoo_dash_img.png">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg col-md-6 col-12 mb-3 mb-md-0">
                                <div class="h-100 px-3 px-lg-3"
                                    style="border-left: 1px solid #00ffff; border-right: 1px solid #00ffff;">
                                    <div class="text-center">
                                        <div class="pb-3 pt-4">
                                            <img alt="User Guide" src="./img/user_guide.png" />
                                        </div>
                                        <div class="mb-3" style="font-size: 18px; color: #fff; font-weight: 500;">User
                                            Guide
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center"
                                        style="background-color: #0FBABA; border-radius: 4px; overflow: hidden;">
                                        <div class="bg-white p-2 d-flex align-items-center"
                                            style="height: 55px; font-weight: 500; line-height: 120%; color: #173E85; font-size: 13px;">
                                            Copy <br> Link
                                        </div>
                                        <div class="p-2"
                                            style="color: #fff; font-weight: 400; font-size: 12px; width: 195px; word-break: break-all;">
                                            https://www.ksolves.com/user-guide/odoo-dashboard-ninja
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg col-md-6 col-12 px-lg-2 pt-4 pt-xl-0">
                                <div class="text-center">
                                    <div class="mt-n2">
                                        <img alt=" contact-man-icon" class="img-fluid" src="./img/contact-place.png"
                                            style="width: 140px;">
                                    </div>
                                    <div class="row d-flex align-items-center justify-content-center"
                                        style="margin-top:-30px">
                                        <div class="col-10 px-0">
                                            <a class="d-flex align-items-center px-2 py-3"
                                                href="mailto:<EMAIL>"
                                                style="background-color: #DEEAFF; text-decoration: none; border-radius: 5px;font-style: normal; font-weight: 400; font-size: 14px; color: #4F4F4F; height:40px;">
                                                <img alt="mail-icon" class="img-fluid pe-2 ps-2 me-xl-2 me-lg-0 me-2"
                                                    loading="lazy" src="./img/mail.png"><EMAIL></a>
                                        </div>
                                        <div class="col-10 px-0" style="margin-top: 5px;">
                                            <a class=" d-flex align-items-center  px-2 py-3" href="tel:+91-8527471031"
                                                style="background-color: #DEEAFF; text-decoration: none; border-radius: 5px;font-style: normal; font-weight: 400; font-size: 14px; color: #4F4F4F; height:40px;">
                                                <img alt="phone" class="img-fluid pe-2 ps-2 me-xl-2 me-lg-0 me-2"
                                                    loading="lazy" src="./img/phone.png">+91-8527471031</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- new div ends -->

                <div class="my-2 position-relative d-lg-block d-flex justify-content-center align-items-center"
                    style="height: 451px; border-radius: 40px;display: flex; align-items: center;justify-content: center;">
                    <img alt="" class="position-absolute d-block d-xl-none img-fluid w-100 h-100 left_0"
                        style="border-radius: 40px;" src="./img/new-add-img/New-Theme.gif" />
                    <video class="position-absolute d-xl-block d-none img-fluid"
                        style="border-radius: 40px; width: 100%;" autoplay muted loop>
                        <source src="./img/new-add-img/New-Theme.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <div class="row py-md-4 py-3 position-relative justify-content-center">
                        <div class="col-md-8 py-2 text-center">
                            <h1 class="mb-0 mt-4 pt-md-4"
                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; word-wrap: break-word;line-height: 124%;color: #00FFFF;">
                                <span class="d-lg-block d-none" style="font-size: 48px;"> Dashboard Ninja with
                                    AI</span>
                                <span class="d-lg-none d-block" style="font-size: 25px;"> Dashboard Ninja <br> with
                                    AI</span>
                            </h1>
                        </div>
                        <div class="col-md-8 mx-auto py-2 text-center">
                            <p class="pb-0 d-md-block d-none px-md-4 px-2"
                                style="font-style: normal; font-weight: normal; font-size: 16px; line-height: 160%; letter-spacing: 0.02em; text-indent: 2px; color: #FFFFFF;">
                                Create amazing reports with the powerful & smart Odoo Dashboard Ninja app with a
                                refreshed, modern user interface for effortless navigation and enhanced user experience.
                                Enjoy the simplified workflow that makes everything easy, from generating dashboards to
                                collaborating with your team members.See
                                your
                                business from a 360-degree angle with an interactive, engaging, and beautiful dashboard.
                            </p>
                            <p class="pb-0 d-md-none d-block px-md-4 px-2"
                                style="font-style: normal; font-weight: normal; font-size: 12px; line-height: 160%; letter-spacing: 0.02em; text-indent: 2px; color: #FFFFFF;">
                                Create amazing reports with the powerful & smart Odoo Dashboard Ninja app with a
                                refreshed, modern user interface for effortless navigation and enhanced user experience.
                                Enjoy the simplified workflow that makes everything easy, from generating dashboards to
                                collaborating with your team members.See
                                your
                                business from a 360-degree angle with an interactive, engaging, and beautiful dashboard.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="row d-xl-block d-none position-relative" style="margin-top: -11%">
                    <div class="col-md-10 offset-md-1 text-center">
                        <img alt="" class="img-fluid" loading="lazy" src="./img/dashboard.png">
                    </div>
                </div>
                <div class="row d-xl-none d-block position-relative">
                    <div class="col-md-10 offset-md-1 text-center">
                        <img alt="" class="img-fluid" loading="lazy" src="./img/dashboard.png">
                    </div>
                </div>

                <!-- new div starts -->

                <!-- new div ends -->


                <div class="row pt-4">
                    <div class="col-md-12">
                        <h1
                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600;word-wrap: break-word; font-size: 48px; line-height: 120%;  text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #333333; font-size: calc(2.06rem + 1vw);">
                            What makes <br>
                            Odoo Dashboard Ninja
                            <span style="color: #2B5FB2; font-size: calc(2.06rem + 1vw);">Unique</span>
                        </h1>
                    </div>
                    <div class="col-md-8 offset-md-2">
                        <p
                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 160%;text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                            The ultimate time and money saving Odoo tool for your Business.
                        </p>
                    </div>
                    <div class="col-md-12 pt-md-5 py-2 text-center">
                        <div class="ms-md-5">
                            <img alt="" class="img-fluid" loading="lazy" src="./img/Main-Features.png">
                        </div>
                    </div>
                    <div class="col-md-12  pt-3  pb-md-5 pb-2 text-center">
                        <a class="d-inline-block px-4 py-3 text-center" href="#features"
                            style="font-family: Inter; text-decoration: none; background-color:#00A4EB;border-radius: 5px;font-style: normal; font-weight: 500; font-size: 18px; line-height: 110%; text-transform: capitalize; color: #fff;">All
                            Features <img alt="" class="ms-2" loading="lazy" src="./img/arrow.png"></a>
                    </div>
                </div>
            </div>
            <div class="mb-4 bg-white shadow" style="border-radius: 20px; font-family: 'Inter', sans-serif;">
                <div class="pt-4">
                    <ul class="nav nav-tabs justify-content-center bg-white pt-md-2" id="myTab" role="tablist"
                        style="border-bottom:1px solid #CFCFCF;">
                        <li class="nav-item">
                            <a aria-controls="overview" aria-bs-selected="true" class="nav-link active"
                                data-bs-toggle="tab" href="#overview" id="overview-tab" role="tab"
                                style="color: #4F4F4F;font-weight: 400; font-size: 14px;">
                                Overview</a>
                        </li>
                        <li class="nav-item">
                            <a aria-controls="why-ks" aria-bs-selected="false" class="nav-link py-2"
                                data-bs-toggle="tab" href="#why-ks" id="why-ks-tab" role="tab"
                                style="color: #4F4F4F;font-weight: 400; font-size: 14px;">Why Ksolves</a>
                        </li>
                        <li class="nav-item">
                            <a aria-controls="s_c" aria-bs-selected="false" class="nav-link" data-bs-toggle="tab"
                                href="#support" id="support-tab" role="tab"
                                style="color: #4F4F4F;font-weight: 400; font-size: 14px;">Support</a>
                        </li>
                        <li class="nav-item">
                            <a aria-controls="faq" aria-bs-selected="false" class="nav-link" data-bs-toggle="tab"
                                href="#faq" id="faq-tab" role="tab"
                                style="color: #4F4F4F;font-weight: 400; font-size: 14px;">
                                FAQs</a>
                        </li>
                        <li class="nav-item">
                            <a aria-controls="releases" aria-bs-selected="false" class="nav-link" data-bs-toggle="tab"
                                href="#releases" id="releases-tab" role="tab"
                                style="color: #4F4F4F;font-weight: 400; font-size: 14px;">Releases</a>
                        </li>
                    </ul>
                </div>
                <div class="tab-content p-md-5 p-2 py-3" id="myTabContent">
                    <div aria-labelledby="overview-tab" class="tab-pane fade show active" id="overview" role="tabpanel">
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/dyn_bg.png" />
                            <div class="p-lg-5 p-3 position-relative">
                                <div class="row py-4 py-lg-0">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold;word-wrap: break-word; font-size: 48px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2.06rem + 1vw); ">
                                            <span style="color: #2B5FB2; font-size: calc(2.06rem + 1vw);">Dynamic &
                                                Animated</span>
                                            <br>
                                            <span style="color: #333333; font-size: calc(2.06rem + 1vw);">Reporting
                                                Dashboards</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Supports 17 impressive Odoo dashboard item types for easy business data
                                            interpretation.
                                            (Tiles,
                                            Line Chart, List View, Bar Chart, Horizontal Bar Chart, Area Chart, To-do
                                            Item,
                                            Polar
                                            Area
                                            Chart, Pie Chart, Doughnut Charts, Flower Chart, Funnel Chart, Radial Chart,
                                            Radar Chart,
                                            Scatter Chart, Bullet Chart & Map view)
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical"
                                                    class="nav flex-row h-100 bg-transparent nav-pills mb-4"
                                                    id="v-pills-tab" role="tablist" style="border-radius:  10px;">
                                                    <!-- <a aria-controls="dashboard"
                                                        class="nav-link w-100 p-0 border-bottom active"
                                                        data-bs-toggle="tab" href="#dashboard" role="tab"
                                                        style="border-top-left-radius:10px; border-top-right-radius: 10px; padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-top-right-radius: 10px; border-top-left-radius: 10px;  ">
                                                            <div style="  width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon1.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Dashboard
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a> -->
                                                    <a aria-controls="tiles" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#tiles" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon2.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Tiles
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="line_chart"
                                                        class="nav-link w-100 p-0 border-bottom" data-bs-toggle="tab"
                                                        href="#line_chart" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon3.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Line Chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="ListView" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#ListView" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon4.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    List View
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="Bar_Chart"
                                                        class="nav-link w-100 p-0 border-bottom" data-bs-toggle="tab"
                                                        href="#Bar_Chart" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon5.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Bar Chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="horizontal_bar"
                                                        class="nav-link w-100 p-0 border-bottom" data-bs-toggle="tab"
                                                        href="#horizontal_bar" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon6.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Horizontal Bar Chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="todo_item"
                                                        class="nav-link w-100 p-0 border-bottom" data-bs-toggle="tab"
                                                        href="#todo_item" role="tab"
                                                        style=" padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon7.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    To-do Item
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="polar__chart"
                                                        class="nav-link w-100 p-0 border-bottom" data-bs-toggle="tab"
                                                        href="#polar__chart" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px">
                                                            <div style=" width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon8.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Polar Area Chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="pie_chart"
                                                        class="nav-link w-100 p-0 border-bottom" data-bs-toggle="tab"
                                                        href="#pie_chart" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon9.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Pie Chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="doughnut" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#doughnut" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/icon10.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Doughnut Charts
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="flower" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#flower" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/new-add-img/i1.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Flower chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="funnel" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#funnel" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/new-add-img/i7.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Funnel Chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="radial" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#radial" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/new-add-img/i2.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Radial chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="bullet" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#bullet" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/new-add-img/i3.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Bullet chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="scatter" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#scatter" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/new-add-img/i4.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Scatter Chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="radar" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#radar" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/new-add-img/i5.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Radar chart
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="mapview" class="nav-link w-100 p-0 border-bottom"
                                                        data-bs-toggle="tab" href="#mapview" role="tab"
                                                        style="border-radius:0;  padding-right:4px !important;height:45px;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                        <div class="d-flex media bg-white align-items-center px-2 py-3"
                                                            style="height:45px"
                                                            style="border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                                            <div style="    width:25px; height:25px; display: flex;
                                                      align-items: center; padding:0">
                                                                <img class="img-fluid align-top" loading="lazy"
                                                                    src="./img/new-add-img/i6.png" style="width:100%">
                                                            </div>
                                                            <div class="media-body ps-2 pe-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-start"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: normal; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #4F4F4F;">
                                                                    Map view
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 h-100 bg-white ms-md-4 ms-0 shadow-sm"
                                                    style="border-radius:5px">

                                                    <!-- <div class="tab-pane  h-100  active" id="dashboard" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/new-add-img/dashboard-video.gif">
                                                            </div>
                                                        </div>
                                                    </div> -->
                                                    <div class="tab-pane h-100 active" id="tiles" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Tiles.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100 fade" id="line_chart" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Line-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade h-100" id="ListView" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/List-View-new.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100 fade" id="Bar_Chart" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Bar-chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100 fade" id="horizontal_bar"
                                                        role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Horizontal-Bar-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100 fade" id="todo_item" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/To-Do-Item.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100 fade" id="polar__chart" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Polar-Area-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100 fade" id="pie_chart" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Pie-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane  h-100 fade" id="doughnut" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Doughnut-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane  h-100 fade" id="flower" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Flower-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane  h-100 fade" id="funnel" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Funnel-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane  h-100 fade" id="radial" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Radial-chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane  h-100 fade" id="bullet" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Bullet-chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane  h-100 fade" id="scatter" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Scatter-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane  h-100 fade" id="radar" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Radar-Chart.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane  h-100 fade" id="mapview" role="tabpanel">
                                                        <div
                                                            class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div class="text-center w-100">
                                                                <img alt="" class="img-fluid" loading="lazy"
                                                                    src="./img/Map-View.png">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./ai-img/o1.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row pt-4 ">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 48px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2rem + 1vw); color: #000000;">
                                            Generate Items with AI
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; text-indent: 2px; color: #4F4F4F;
                                    letter-spacing: 0.36px;">
                                            Generate multiple items for your Odoo Dashboard with AI. Gain the
                                            flexibility to choose from a range of charts and graphs and save them with
                                            ease.
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="bg-white mt-3 shadow"
                                            style="padding: 20px 25px; border-radius: 10px;">
                                            <!-- <img alt="gif" class="img-fluid w-100" loading="lazy"
                                                src="./ai-img/generate_item.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/generate-ai-modal.mp4" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./ai-img/o2.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row pt-4 ">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 48px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2rem + 1vw); color: #000000;">
                                            Generate keyword-focused Odoo Dashboard Items with AI
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; text-indent: 2px; color: #4F4F4F;
                                    letter-spacing: 0.36px;">
                                            Now just provide the keyword, and AI will create the dashboard items for you
                                            within seconds.
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="bg-white mt-3 shadow"
                                            style="padding: 20px 25px; border-radius: 10px;">
                                            <!-- <img alt="gif" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/generate-ai-keyword.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/generate-ai-keyword.mp4"
                                                    type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./ai-img/o3.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row pt-4 ">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 48px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2rem + 1vw); color: #000000;">
                                            Generate Complete Odoo Dashboard with AI
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; text-indent: 2px; color: #4F4F4F;
                                    letter-spacing: 0.36px;">
                                            Save your time and effort! Generate a complete Odoo Dashboard for a specific
                                            model within a few clicks with AI efficiency.
                                        </p>
                                    </div>
                                    <div class="col-md-6 mb-3 d-flex align-items-center justify-content-center">
                                        <div class="bg-white mt-3 shadow "
                                            style="padding: 20px 25px; border-radius: 10px;">
                                            <img alt="gif" class="img-fluid w-100" loading="lazy"
                                                src="./img/Generate-with-AI.png">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="bg-white mt-3 h-100 shadow"
                                            style="padding: 20px 25px; border-radius: 10px;">
                                            <!-- <img alt="gif" class="img-fluid w-100 h-100" loading="lazy"
                                                src="./img/new-add-img/generate-dashboard-ai.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/generate-dashboard-ai.mp4"
                                                    type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./ai-img/o2.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row pt-4 ">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 48px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2rem + 1vw); color: #000000;">
                                            Extract Chart Insights With AI

                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; text-indent: 2px; color: #4F4F4F;
                                    letter-spacing: 0.36px;">
                                            Interpret complex charts with single-click AI-generated explanations!
                                            Identify hidden patterns & trends and make informed data-backed business
                                            decisions.
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="bg-white mt-3 shadow"
                                            style="padding: 20px 25px; border-radius: 10px;">
                                            <!-- <img alt="gif" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/explain-ai-whole.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/explain-ai-whole.mp4" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Will be added later -->
<!--                        &lt;!&ndash; chat-with-ai &ndash;&gt;-->
<!--                        <div class="position-relative mb-4" style="border-radius: 10px;">-->
<!--                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"-->
<!--                                src="./img/drill2.png">-->
<!--                            <div class="p-md-5 p-3 position-relative">-->
<!--                                <div class="row">-->
<!--                                    <div class="col-md-12">-->
<!--                                        <h1-->
<!--                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 48px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2rem + 1vw); color: #000000;">-->
<!--                                            Chat with AI-->
<!--                                        </h1>-->
<!--                                    </div>-->
<!--                                    <div class="col-md-10 offset-md-1">-->
<!--                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; text-indent: 2px; color: #4F4F4F;-->
<!--                                    letter-spacing: 0.36px;">-->
<!--                                            Request any data in natural language from your database, and AI will-->
<!--                                            generate relevant responses within seconds.-->
<!--                                        </p>-->
<!--                                    </div>-->
<!--                                    <div class="col-md-12">-->
<!--                                        <div class="bg-white mt-3 shadow"-->
<!--                                            style="padding: 20px 25px; border-radius: 10px;">-->
<!--                                            &lt;!&ndash; <img alt="gif" class="img-fluid" loading="lazy"-->
<!--                                                src="./img/new-add-img/chat-with-AI.gif"> &ndash;&gt;-->
<!--                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>-->
<!--                                                <source src="./img/new-add-img/chat-with-AI.mp4" type="video/mp4">-->
<!--                                                Your browser does not support the video tag.-->
<!--                                            </video>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <!-- chat-with-ai-end -->
                        <!-- internal-chat -->
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/real_time.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row py-2 ">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 48px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2rem + 1vw); color: #000000;">
                                            Internal Chat
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; text-indent: 2px; color: #4F4F4F;
                                    letter-spacing: 0.36px;">
                                            Collaborate with your team members with the chat feature on every item of a
                                            dashboard. A dedicated channel is created for conversations on each item,
                                            and all channels are organized in a unified inbox.
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="bg-white mt-3 shadow"
                                            style="padding: 20px 25px; border-radius: 10px;">
                                            <!-- <img alt="gif" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/internal-chat.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/internal-chat.mp4" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- internal-chat -->
                        <!-- switch-layout -->
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/cdt_bg.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row py-2 ">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 48px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2rem + 1vw); color: #000000;">
                                            Switch Layout
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; text-indent: 2px; color: #4F4F4F;
                                    letter-spacing: 0.36px;">
                                            Switch from one graph style to another with just a single click, retaining
                                            the same data. Effortlessly analyze your data with the desired data
                                            visualization chart!
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="bg-white mt-3 shadow"
                                            style="padding: 20px 25px; border-radius: 10px;">
                                            <!-- <img alt="gif" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/switch-layout.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/switch-layout.mp4" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- switch-layout -->
                        <!-- bookmark-dashboard -->
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/dmd.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row py-2 ">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 48px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2rem + 1vw); color: #000000;">
                                            Bookmark Dashboard
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; text-indent: 2px; color: #4F4F4F;
                                    letter-spacing: 0.36px;">
                                            Bookmark your key dashboards and easily access them for faster data
                                            analysis. Effortlessly remove dashboards from the bookmark when you are done
                                            with them.
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="mt-1"
                                            style="padding: 8px 25px; border-radius: 10px; margin-top: -40px !important;">
                                            <img alt="gif" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/Bookmark-Dashboard.png">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- bookmark-dashboard -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img alt="" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/real_time.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-4 d-flex align-items-center">
                                        <div class="ps-md-2 ps-0">
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%;  text-transform: capitalize; color: #2B5FB2; font-size: calc(1.1rem + 1vw);">
                                                Real-Time
                                                Streaming
                                                <span
                                                    style="color: #333333; font-size: calc(1.1rem + 1vw);">Dashboard</span>
                                            </h2>
                                            <p
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Upgrades the data in real-time to
                                                give a 360 view of the business performance. This feature will update
                                                the
                                                data
                                                if
                                                any changes occur from the backend in real-time or after a specific
                                                interval
                                                of
                                                time.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="d-inline-block text-center p-3 shadow-sm"
                                            style="background-color: #fff;border-radius: 10px;">
                                            <!-- <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/real-time-stream.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/real-time-stream.mp4" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img alt="" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/responive_fluid.png" />
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-7">
                                        <div class="">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/Responsive-Fluid-Flexible-Layout.png">
                                        </div>
                                    </div>
                                    <div class="col-md-5 d-flex align-items-center ">
                                        <div class="px-md-3 px-0 mt-3 mt-md-0">
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 42px; line-height:90%; /* or 43px */ text-transform: capitalize; color: #E84A5F; font-size: calc(1.68rem + 1vw);">
                                                Responsive<br style="line-height: 0;">
                                                <span
                                                    style="font-size: calc(1.06rem + 1vw); color: #333333;font-weight: 600;font-size: 32px;line-height: 0;">Fluid
                                                    & Flexible<br>
                                                    Layout</span>
                                            </h2>
                                            <p
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Conveniently view your data over different devices such as Mobile,
                                                Desktop,
                                                or
                                                Tablet for easy access.
                                                It offers an optimized browsing experience to track business performance
                                                on
                                                the
                                                go.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img alt="advance" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/advance.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-5 d-flex align-items-center">
                                        <div>
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%;  text-transform: capitalize; color: #333333; font-size: calc(1.1rem + 1vw);">
                                                Advanced<br>
                                                <span
                                                    style="color: #2B5FB2;font-size: 42px; font-size: calc(1.68rem + 1vw);">Date
                                                    Filter <img alt="" class="ps-md-3 img-fluid mt-lg-n2"
                                                        src="./img/adv_filter.png">
                                                </span>
                                            </h2>
                                            <p
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Present data in a timely manner over a selected period in the date
                                                filter
                                                option.
                                                Select from 20 predefined date filters (Last 7 days, Last 30 days, Last
                                                90
                                                days,
                                                &
                                                more) or choose a custom date.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-7 ps-lg-0">
                                        <div class="" style="padding: 26px 22px; border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/Advanced-Date-Filter.png">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img alt="advance" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/new-add-img/excel-bg.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-7 ps-lg-0">
                                        <div class="" style="padding: 26px 22px; border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/Create-Charts-From-EXCEL-AND-CSV-FILES.png">
                                        </div>
                                    </div>
                                    <div class="col-md-5 d-flex align-items-center  mt-3 mt-md-0">
                                        <div>
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 37px;  text-transform: capitalize; color: #333333; font-size: calc(1.1rem + 1vw);">
                                                Create Charts <br class="d-md-block d-none">
                                                From <br class="d-md-none d-block">
                                                <span
                                                    style="color: #2B5FB2;font-size: 42px; font-size: calc(1.68rem + 1vw);">
                                                    EXCEL AND CSV FILES
                                                </span>
                                            </h2>
                                            <p
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Easily upload Excel or CSV files, and let the system automatically
                                                synchronize the data. It will then process the data and create charts.
                                            </p>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="px-md-5 pt-md-5 pb-md-2 p-2 mb-4"
                            style="border-radius: 10px;background-color: #F9F9F9; border-radius: 10px;">
                            <div class="row py-2 ">
                                <div class="col-md-12">
                                    <h1
                                        style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; word-wrap: break-word;font-size: 36px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw);">
                                        <span style="color: #EA5281; font-size: calc(1.1rem + 1vw);">Download Odoo
                                            Dashboard Items
                                        </span>
                                        <br>
                                        <span style="color: #333333; font-size: calc(1.1rem + 1vw);">(Excel, CSV, PDF,
                                            PNG)</span>
                                    </h1>
                                </div>
                                <div class="col-md-10 text-center offset-md-1">
                                    <p class="mb-0"
                                        style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                        Exported data items can be used for offline presentations, seminars, or emails.
                                    </p>
                                </div>
                                <div class="col-md-12 text-center">
                                    <img alt="" class="img-fluid my-4" loading="lazy"
                                        src="./img/Odoo-Dashboard-Items.png">
                                </div>
                            </div>
                        </div>
                        <div class="px-md-5 pt-md-5 pb-md-3 p-2 mb-4"
                            style="border-radius: 10px;background-color: #F9F9F9; border-radius: 10px;">
                            <div class="row py-2 ">
                                <div class="col-md-12">
                                    <h1
                                        style="font-family: 'Inter', sans-serif; font-style: normal; font-weight:600; word-wrap: break-word;font-size: 36px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw);">
                                        <span style="color: #2B5FB2; ">Export & Import <span
                                                style="color: #333333;">Dashboards
                                            </span>
                                        </span>
                                        <br>
                                        <span style="color: #333333;">or Specific Odoo Dashboard Items
                                        </span>
                                    </h1>
                                </div>
                                <div class="col-md-10 offset-md-1">
                                    <p
                                        style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                        Export an entire Odoo Dashboard or Specific Odoo Dashboard Item to a same system
                                        or different Odoo instances for saving time and effort while migrating from one
                                        system
                                        to another.
                                    </p>
                                </div>
                                <div class="col-md-12 py-md-3 py-2">
                                    <div class="" style="border-radius: 5px;">
                                        <img alt="" class="img-fluid" loading="lazy"
                                            src="./img/Export-Import-Dashboards.png">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="advance" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/acc_bg.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-4 d-flex align-items-center">
                                        <div>
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight:600; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #2B5FB2; font-size: calc(1.1rem + 1vw);">
                                                Access <br>
                                                <span
                                                    style="color: #333333;font-size: 45px; font-size: calc(1.88rem + 1vw);">
                                                    Control
                                                </span>
                                            </h2>
                                            <p
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Control who can access the Odoo dashboard with a single click
                                                for robust security.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="" style="padding: 30px; border-radius: 10px;">
                                            <img alt="access-control-img" class="img-fluid" loading="lazy"
                                                src="./img/Access-Control.png">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/pre.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class=" col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight:600; font-size: 36px; word-wrap: break-word;line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw);">
                                            <span style="color: #EA5281; font-size: calc(1.1rem + 1vw);">Paid</span>
                                            <span
                                                style="color: #EA5281; font-size: calc(1.1rem + 1vw);">Predefined</span>
                                            <span style="color: #333333; font-size: calc(1.1rem + 1vw);">
                                                Dashboards</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 text-center">
                                        <p
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                            5 Predefined Odoo dashboards are provided for different verticals of
                                            a company(Sales, CRM, Account, Inventory, & POS). Customize them as per your
                                            need.
                                        </p>
                                    </div>
                                    <div class="col-md-12 pt-md-4 pb-md-3">
                                        <div class="p-4 w-100  text-center bg-white" style="border-radius: 10px;">
                                            <div class="carousel slide" data-bs-ride="carousel"
                                                id="carouselExampleControls">
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img alt="" class="img-fluid" loading="lazy"
                                                            src="./img/account.png">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img alt="" class="img-fluid" loading="lazy"
                                                            src="./img/account2.jpg">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img alt="" class="img-fluid" loading="lazy"
                                                            src="./img/account3.jpg">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img alt="" class="img-fluid" loading="lazy"
                                                            src="./img/account4.jpg">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img alt="" class="img-fluid" loading="lazy"
                                                            src="./img/account5.jpg">
                                                    </div>
                                                </div>

                                                <a class="carousel-control-prev text-dark" data-bs-slide="prev"
                                                    href="#carouselExampleControls" role="button">
                                                    <span aria-hidden="true" class="carousel-control-prev-icon"
                                                        style="width: 30px; height: 30px;">
                                                        <img alt="arrow" width="30px" height="30px" class="img-fluid"
                                                            loading="lazy" src="./img/left.png">
                                                    </span>
                                                    <!-- <span class="sr-only">Previous</span> -->
                                                </a>
                                                <a class="carousel-control-next text-dark" data-bs-slide="next"
                                                    href="#carouselExampleControls" role="button">
                                                    <span aria-hidden="true" class="carousel-control-next-icon"
                                                        style="width: 30px; height: 30px;">
                                                        <img alt="arrow" width="30px" height="30px" class="img-fluid"
                                                            loading="lazy" src="./img/right.png">
                                                    </span>
                                                    <!-- <span class="sr-only">Next</span> -->
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute left_0" loading="lazy"
                                src="./img/drill2.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-4 d-flex align-items-center">
                                        <div>
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #2B5FB2; font-size: calc(1.1rem + 1vw);">
                                                Drill <span
                                                    style="color: #333333;font-weight: 600; font-size: calc(1.1rem + 1vw);">Down
                                                    /</span>
                                                <br> Drill <span
                                                    style="color: #333333;font-weight: 600; font-size: calc(1.1rem + 1vw);">Up
                                                    Data </span>
                                            </h2>
                                            <p class="pe-md-4"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Modify the degree of details presented in a dashboard entity. Drill-down
                                                to
                                                access detailed data or drill-up to get a broader view of information.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="d-inline-block text-center p-3 shadow"
                                            style="background-color: #fff;border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/Drill_Down_Drill_UP.gif">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/dfbg.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-4 d-flex align-items-center">
                                        <div>
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #2B5FB2; font-size: calc(1.1rem + 1vw);">
                                                Data Filtration -
                                                <br>
                                                <span style="color: #333333; font-size: calc(1.1rem + 1vw);">Group By,
                                                    Limit & Sort By</span>
                                            </h2>
                                            <p
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Group By, Sort By, Limit, filter condition to get desired data for any
                                                dashboard
                                                item as per your requirement.
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/Data-Filtration - Group-Limit-Sort-By.png">
                                        </div>
                                        <!-- <div class="float-right">
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/df2.png">
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/d_ite_bg.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row pt-4 ">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px;word-wrap: break-word; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw);">
                                            <span style="color: #333333; font-size: calc(1.1rem + 1vw);">Dashboard Item
                                            </span>
                                            <span style="color: #2B5FB2; font-size: calc(1.1rem + 1vw);">2 List View
                                                Styles</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                            Use the list-view item to view multi-value data sets in a grouped or
                                            ungrouped
                                            manner.
                                            Example: To see the total sales, average sales, total quotations of the top
                                            10
                                            sales
                                            persons.
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="mt-3 text-center" style="padding: 20px 25px; border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/Item-2-List-View-Styles.png">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px; background-color: #fbfafa;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/Dashboard_Personalization_Configuration_BG.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; word-wrap: break-word;line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw);">
                                            <span style="color: #333333; font-size: calc(1.1rem + 1vw);">Dashboard
                                            </span>
                                            <span
                                                style="color: #2B5FB2;word-wrap: break-word;font-weight: 600; font-size: calc(1.1rem + 1vw);">
                                                Personalization/Configuration</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-8 offset-md-2">
                                        <p
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                            Configure the dashboard name, menu, group access, choose the sequence,
                                            and more while creating interactive presentable dashboards for your
                                            business.
                                        </p>
                                    </div>

                                    <div class="col-md-6 text-center">
                                        <div class="bg-white mt-3 shadow"
                                            style="padding: 18px 13px; border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/data-personaliation-.png">
                                        </div>
                                    </div>
                                    <div class="col-md-6 text-center">
                                        <div class="bg-white mt-3 shadow"
                                            style="padding: 18px 13px; border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/data-personalization-2.png">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px; background-color: #f9f7f7;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/Design_your_own_Dashboard_Layout_BG.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal;word-wrap: break-word; font-weight:700; font-size: 36px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw);">
                                            <span style="color: #EA5281; font-size: calc(1.1rem + 1vw);">Design
                                            </span>
                                            <span
                                                style="color: #333333;font-weight: 600; font-size: calc(1.1rem + 1vw);">Your
                                                Own Dashboard Layout</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                            Design your dashboard layouts by resizing, dragging, repositioning the
                                            dashboard
                                            items,
                                            and renaming the dashboard heading for arranging all items as per your need.
                                        </p>
                                    </div>
                                    <div class="col-md-10 offset-md-1 text-center">
                                        <div class="d-inline-block p-3 shadow-sm"
                                            style="background-color: #fff;border-radius: 10px;">
                                            <!-- <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/Design_your_Dashbaord.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/Edit-Layout.mp4" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="position-relative mb-4" style="border-radius: 10px; background-color: #f9f9f8;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/Design_your_own_Dashboard_Layout_BG.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; word-wrap: break-word;font-weight: bold; font-size: 36px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw);">
                                            <span style="color: #333333; font-size: calc(1.1rem + 1vw);">View Dashboard
                                            </span>
                                            <span
                                                style="color: #2B5FB2;font-weight: 600; font-size: calc(1.1rem + 1vw);">
                                                Item Data</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-12">
                                        <p
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                            Users can hover over any dashboard chart and get quick access to the data
                                            from
                                            the
                                            dashboard window.
                                            By clicking on the Info Icon, the user can get every detail associated with
                                            a
                                            particular
                                            chart.
                                        </p>
                                    </div>
                                    <div class="col-md-12 text-center">
                                        <div class="d-inline-block p-3 shadow-sm"
                                            style="background-color: #fff;border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/View_Dashboard _tem_Data.gif">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <div class="position-relative mb-4" style="border-radius: 10px; background-color: #fbfafb;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/Multi_Color_Themes.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; word-wrap: break-word;font-weight: bold; font-size: 36px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw); ">
                                            <span style="color: #2B5FB2; font-size: calc(1.1rem + 1vw);"> Multi -
                                            </span>
                                            <span style="color:  #F04F65; font-size: calc(1.1rem + 1vw);"> Color </span>
                                            <span
                                                style="color: #333333;font-weight: 600; font-size: calc(1.1rem + 1vw);">Themes</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-8 offset-md-2">
                                        <p
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                            Beautiful, interactive, and outstanding multi-colored themes can be added to
                                            your
                                            dashboard.
                                        </p>
                                    </div>
                                    <div class="col-md-12 text-center">
                                        <div class="d-inline-block p-3 shadow-sm"
                                            style="background-color: #fff;border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/Multi-Color-Themes.png">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px; background-color: #f7f7f7;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/setbg.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-7 mb-3 mb-md-0">
                                        <div class="bg-white shadow my-lg-3"
                                            style="padding: 25px 10px; border-radius: 10px;">
                                            <img alt="set-target-img" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/Set-Targets.png">
                                        </div>
                                    </div>
                                    <div class="col-md-5 d-flex align-items-center">
                                        <div class="ps-lg-4">
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #333333; font-size: calc(1.1rem + 1vw);">
                                                Set
                                                <br style="line-height: 0;">
                                                <span class="inline-block"
                                                    style="color: #E84A5F;font-weight: 600; font-size: calc(1.1rem + 1vw);">Targets
                                                    <img alt="" class="img-fluid" loading="lazy"
                                                        src="./img/set_target.png" style="margin-top: -20px;">
                                                </span>
                                            </h2>
                                            <p class="pe-md-5"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Target feature helps to present clear and concise performance
                                                information
                                                without
                                                providing complex formulas or knowledge.
                                                <br>
                                                <br>
                                                Receive instant email notifications when your sales targets are met.
                                                Effortlessly track your KPIs, ensure your team stays motivated and
                                                aligned with your objectives.


                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px;  background-color: #f7f7f7;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/unique.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-4 d-flex align-items-center">
                                        <div>
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #2B5FB2; font-size: calc(1.1rem + 1vw);">
                                                Unique User ID
                                                <span
                                                    style="color: #333333;font-weight: 600; font-size: calc(1.1rem + 1vw);">(%UID)</span>
                                            </h2>
                                            <p
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Filter logged-in user data using %UID in domain filter to see the
                                                results
                                                related to
                                                the logged-in user.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="bg-white shadow-sm"
                                            style="padding: 16px 14px; border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/Unique-User-ID.png">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="position-relative mb-4" style="border-radius: 10px; background-color: #f6f6f6;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/Authentic_Mycompany_Filter.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-7">
                                        <div class="" style="padding: 18px 16px; border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/new-add-img/Authentic-MYCOMPANY-Filter.png">
                                        </div>
                                    </div>
                                    <div class="col-md-5 d-flex align-items-center">
                                        <div>
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #333333; font-size: calc(1.1rem + 1vw);">
                                                Authentic
                                                <span
                                                    style="color: #E84A5F;font-weight: 600; font-size: calc(1.1rem + 1vw);">%MYCOMPANY
                                                    Filter</span>
                                            </h2>
                                            <p class="pe-md-5"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Get company-oriented details by filtering the logged-in user data using
                                                this
                                                dynamic filter.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="position-relative mb-4" style="border-radius: 10px; background-color: #f2f2f2;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/todo_bg.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-4 d-flex align-items-center">
                                        <div>
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #2B5FB2; font-size: calc(1.1rem + 1vw);">
                                                To do
                                                <span style="color: #333333; font-size: calc(1.1rem + 1vw);">item
                                                </span>
                                            </h2>
                                            <p
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                A user can keep track of his/her activities with a to-do list. To-do
                                                items
                                                can
                                                be
                                                used as a useful reminder of specific tasks.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="bg-white shadow" style="padding: 15px; border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/to-do-img.png">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <div class="position-relative mb-4" style="border-radius: 10px; background: #f4f4f4;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/Instant_Edit_Mode_BG.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold;word-wrap: break-word; font-size: 36px; line-height: 120%;text-align: center; text-transform: capitalize;  font-size: calc(1.1rem + 1vw);">
                                            <span style="color: #EA5281; font-size: calc(1.1rem + 1vw);">Instant
                                            </span>
                                            <span
                                                style="color: #333333;font-weight: 600; font-size: calc(1.1rem + 1vw);">
                                                Edit Mode</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-8 offset-md-2">
                                        <p class="pb-md-4"
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                            A user can edit the basic information of any dashboard items
                                            instantly with Quick Edit Mode without going to the detailed edit screen.
                                        </p>
                                    </div>
                                    <div class="col-md-12 text-center ">
                                        <div class="d-inline-block p-3 shadow-sm"
                                            style="background-color: #fff;border-radius: 10px;">
                                            <!-- <img alt="" class="img-fluid" loading="lazy"
                                                src="./img/Instant_Edit_Mode.gif"> -->
                                            <video class="img-fluid w-100 h-100 left_0" autoplay muted loop>
                                                <source src="./img/new-add-img/edit-chart.mp4" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="position-relative mb-4" style="border-radius: 10px; background-color: #fafafa;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/Number_Systems.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h1
                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold;word-wrap: break-word; font-size: 36px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.1rem + 1vw);">
                                            <span style="color: #333333; font-size: calc(1.1rem + 1vw);"> Number
                                            </span>
                                            <span
                                                style="color: #2B5FB2;font-weight: 600; font-size: calc(1.1rem + 1vw);">
                                                Systems</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1">
                                        <p class="pb-md-4"
                                            style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; /* Gray 2 */ color: #4F4F4F;">
                                            Track the data in your country format with the pre-defined Number system <br
                                                class="d-md-block d-inline">
                                            options (English Format, Indian Format, Colombian Peso Format, and Exact
                                            Value)
                                        </p>
                                    </div>
                                    <div class="col-md-12 text-center">
                                        <div class="d-inline-block text-center p-3 shadow-sm"
                                            style="background-color: #fff;border-radius: 10px;">
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/Number_System.gif">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <!-- <div class="position-relative mb-4" style="border-radius: 10px; background-color: #f9f9f9;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/Show_Hide_Data_Values.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row">
                                    <div class="col-md-7">
                                        <div class="w-100 text-center p-2 bg-white shadow" style="border-radius:10px">
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/Show_Hide_Data.gif">
                                        </div>
                                    </div>
                                    <div class="col-md-5 ps-md-5 d-flex align-items-center">
                                        <div class="mt-3 mt-md-0">
                                            <h2 class="mb-3"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #333333; font-size: calc(1.1rem + 1vw);">
                                                Show/Hide <br style="line-height: 0;">
                                                <span style="color: #E84A5F; font-size: calc(1.1rem + 1vw);">Data
                                                    Values</span>
                                            </h2>
                                            <p class="pe-md-5"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Show/hide value on Dashboard Items to read the statistics easily for
                                                making
                                                impressive
                                                business decisions.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <!--                    <div class="position-relative mb-4" style="border-radius: 10px; background-color: #faf8f4;">-->
                        <!--                        <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"-->
                        <!--                             src="./img/Monetary_Custom_Units_BG.png">-->
                        <!--                        <div class="p-md-5 p-3 position-relative">-->
                        <!--                            <div class="row">-->
                        <!--                                <div class="col-md-4 d-flex align-items-center">-->
                        <!--                                    <div>-->
                        <!--                                        <h2 class="mb-3"-->
                        <!--                                            style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color:#333333;font-size: calc(1.1rem + 1vw);">-->
                        <!--                                            Monetary/-->
                        <!--                                            <span style="color: #2B5FB2;font-weight: 600; font-size: calc(1.1rem + 1vw);">Custom Units </span>-->
                        <!--                                        </h2>-->
                        <!--                                        <p class="pe-md-5"-->
                        <!--                                           style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">-->
                        <!--                                            A user can put the Monetary value like INR, Dollar, Euro etc., over the-->
                        <!--                                            charts-->
                        <!--                                            using the-->
                        <!--                                            Monetary unit type or create its own Custom unit.-->
                        <!--                                        </p>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                                <div class="col-md-8">-->
                        <!--                                    <div class="w-100 p-2 text-center bg-white shadow" style="border-radius:10px">-->
                        <!--                                        <img alt="" class="img-fluid" loading="lazy"-->
                        <!--                                             src="./img/Monesty_Custom_Units.gif">-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                        <!--                    </div>-->
                        <div class="position-relative" style="border-radius: 10px; background-color: #fdf9f9;">
                            <img alt="acc_bg" class="w-100 h-100 position-absolute img-fluid left_0" loading="lazy"
                                src="./img/RTL_Support_BG.png">
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12 d-flex align-items-center justify-content-center">
                                        <div>
                                            <h2 class="mb-3 text-center"
                                                style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: bold; font-size: 36px; line-height: 103%; /* or 43px */ text-transform: capitalize; color: #E84A5F; font-size: calc(1.1rem + 1vw);">
                                                RTL
                                                <span
                                                    style="color: #333333;font-weight: 600; font-size: calc(1.1rem + 1vw);">Support</span>
                                            </h2>
                                            <p class="pe-md-5 text-center"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #535456;">
                                                Odoo Dashboard Ninja gives you the freedom to work in Right-to-Left
                                                language-oriented
                                                interface for increasing productivity.
                                            </p>
                                        </div>
                                    </div>
                                    <!-- <div class="col-md-7">
                                        <div class="w-100 text-center position-relative">
                                            <div class="bg-white"
                                                style=" padding: 16px; border-radius:10px; display:inline-block;">
                                                <div class="s_panel_video" data-video-id="kjbeyB1G3lQ">
                                                    <a class="s_figure_link p-0 position-absolute  w-100 h-100 d-flex align-items-center justify-content-center cursor-pointer"
                                                        href="#">
                                                        <img alt="" class="mb-sm-0 mb-4" src="./img/play-btn-rtl.png" />
                                                    </a>
                                                    <img alt="video-img" class="img-fluid w-100" loading="lazy"
                                                        src="./img/odoo_dash_img_rtl.png">
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div aria-labelledby="why-ks-tab" class="tab-pane fade show py-1" id="why-ks" role="tabpanel">
                        <div class="p-3 p-lg-5" style="background-color: #FAFAFA; border-radius: 30px;">
                            <h5
                                style=" font-family: 'Inter', sans-serif; font-weight: 600;font-size: 30px;line-height: 155.19%;color: #252525; text-align: center;  font-family: 'Inter', sans-serif; font-size: calc(0.93rem + 1vw);">
                                Why
                                Ksolves
                            </h5>
                            <p
                                style="font-weight: 500;font-size: 16px;line-height: 155.19%;text-align: center; margin-bottom: 30px;  font-family: 'Inter', sans-serif;">
                                You know us as the
                                <strong style="color: #173E85;"> makers of the Odoo "Dashboard Ninja"</strong>
                                .
                            </p>

                            <ul class="list-unstyled row m-0">
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-1.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-3.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-2.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-4.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-5.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-6.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-7.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-8.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-9.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-10.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-12.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-add-img/p-11.png">
                                </li>
                            </ul>

                            <!-- <ul class="list-unstyled row m-0">
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_1.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_2.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_3.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_4.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_5.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_6.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_7.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_8.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_9.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_10.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_11.png">
                                </li>
                                <li class="col-6 col-md-4 col-lg-3 p-2">
                                    <img alt="logo-img" class="img-fluid h-100 w-100" loading="lazy"
                                        src="./img/new-dscr-img/ks_12.png">
                                </li>
                            </ul> -->
                        </div>

                        <div class="my-3 my-lg-5 p-0 py-lg-4">
                            <h5
                                style=" font-family: 'Inter', sans-serif; font-weight: 600;font-size: 30px;line-height: 155.19%;color: #252525; text-align: center;  font-family: 'Inter', sans-serif; font-size: calc(0.93rem + 1vw);">
                                Client
                                Success
                            </h5>
                            <p
                                style="font-weight: 500;font-size: 16px;line-height: 155.19%;text-align: center;color: #414141;  font-family: 'Inter', sans-serif;">
                                Invest
                                your trust like our elite clientele did!
                            </p>
                            <ul class="list-unstyled row m-0 justify-content-center">
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_1.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_2.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_3.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_4.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_5.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_6.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_7.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_8.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_9.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_10.png">
                                </li>
                                <li class="col-6 col-md-3 col-lg-2 p-1" style="text-align: center;">
                                    <img alt="logo-img" class="img-fluid" loading="lazy"
                                        src="./img/new-dscr-img/ks_c_11.png">
                                </li>
                            </ul>
                        </div>

                        <div class="p-3 p-lg-5" style="background-color: #FAFAFA; border-radius: 30px;">
                            <h1
                                style="font-weight: 600;font-size: 30px;line-height: 155.19%;color: #252525; text-align: center;   font-size: calc(0.93rem + 1vw);  font-family: 'Inter', sans-serif;">
                                Products That Blends Together
                            </h1>
                            <p
                                style="font-weight: 500;font-size: 16px;line-height: 155.19%;text-align: center; margin-bottom: 30px; font-family: 'Inter', sans-serif;">
                                Related Products You May Be Interested In!
                            </p>
                            <div>
                                <div class="table-responsive">
                                    <table class="table w-100">
                                        <tr>
                                            <td class="text-center" style="padding: 0.5rem;">
                                                <a class="d-block bg-white p-4 text-center mx-auto"
                                                    href="https://apps.odoo.com/apps/modules/15.0/ks_dashboard_ninja/"
                                                    style="border-radius: 10px;width: 180px;min-height: 248px; text-decoration: none;"
                                                    target="_blank">
                                                    <img alt="logo" src="./img/why-ksolves/ks_dashboard_ninja.png"
                                                        style="border-radius: 10px;">
                                                    <div class="mt-3"
                                                        style="color: #333;font-size: 16px;font-weight: 600;">
                                                        Odoo Dashboard Ninja
                                                    </div>
                                                </a>
                                            </td>
                                            <td class="text-center" class="px-2"
                                                style="padding: 0.5rem; vertical-align: middle;font-size: 20px;color: #333;font-weight: 700;">
                                                +
                                            </td>
                                            <td class="text-center" style="padding: 0.5rem;">
                                                <a class="d-block bg-white p-4 text-center mx-auto"
                                                    href="https://apps.odoo.com/apps/themes/14.0/ks_curved_backend_theme/"
                                                    style="border-radius: 10px;width: 180px;min-height: 248px; text-decoration: none;"
                                                    target="_blank">
                                                    <img alt="logo" height="136"
                                                        src="./img/why-ksolves/ks_curved_backend_theme.png"
                                                        style="border-radius: 10px;" width="135">
                                                    <div class="mt-3"
                                                        style="color: #333;font-size: 16px;font-weight: 600;">
                                                        Arc Backend Theme
                                                    </div>
                                                </a>
                                            </td>
                                            <td class="text-center" class="px-2"
                                                style=" padding: 0.5rem; vertical-align: middle;font-size: 20px;color: #333;font-weight: 700;">
                                                +
                                            </td>
                                            <td class="text-center" style="padding: 0.5rem;">
                                                <a class="d-block bg-white p-4 text-center mx-auto"
                                                    href="https://apps.odoo.com/apps/modules/15.0/ks_gantt_view_base/"
                                                    style="border-radius: 10px;width: 180px;min-height: 195px; text-decoration: none;"
                                                    target="_blank">
                                                    <img alt="logo" height="136"
                                                        src="./img/why-ksolves/ks_gantt_view_base.png"
                                                        style="border-radius: 10px;" width="135">
                                                    <div class="mt-3"
                                                        style="color: #333;font-size: 16px;font-weight: 600;">
                                                        Odoo Gantt View Base
                                                    </div>
                                                </a>
                                            </td>
                                            <td class="text-center" class="px-2"
                                                style="padding: 0.5rem; vertical-align: middle;font-size: 20px;color: #333;font-weight: 700;">
                                                =
                                            </td>
                                            <td class="text-center" style="padding: 0.5rem;">
                                                <div class="d-block bg-white p-4 text-center mx-auto"
                                                    style="border-radius: 10px;width: 180px;min-height: 248px;">
                                                    <img alt="logo" src="./img/why-ksolves/perfect_combo.png"
                                                        style="border-radius: 10px;">
                                                    <div class="mt-3"
                                                        style="color: #333;font-size: 16px;font-weight: 600;">
                                                        Perfect Combo
                                                    </div>
                                                </div>
                                            </td>

                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div aria-labelledby="support-tab" class="tab-pane fade show pt-1 py-lg-1" id="support"
                        role="tabpanel">
                        <div class=" text-center bg-white py-1">
                            <h3
                                style="font-size: 18px;line-height: 155.19%;color: #414141; margin-bottom: 36px; font-family: 'Inter', sans-serif;">
                                Ksolves is 24/7 open to discuss your thoughts and queries!
                            </h3>
                            <img alt="" class="img-fluid" loading="lazy" src="./img/new-dscr-img/solution.png">
                            <div class="text-center">
                                <div class="row d-flex align-items-center justify-content-center"
                                    style="margin-top:10px">
                                    <div class="col-lg-4 col-md-5 col-sm-9 col-11">
                                        <a class="d-flex align-items-center justify-content-center px-2 py-3"
                                            href="mailto:<EMAIL>"
                                            style="background-color: #173E85; text-decoration: none; border-radius: 5px;font-style: normal; font-weight: 400; font-size: 14px; color: #ffffff; height:45px;">
                                            <img alt="mail-icon" class="img-fluid pe-2 ps-2 me-xl-2 me-lg-0 me-2"
                                                loading="lazy" src="./img/mail.png"><EMAIL></a>
                                    </div>
                                    <div class="col-lg-4 col-md-5 col-sm-9 col-11 mt-md-0 mt-2">
                                        <a class=" d-flex align-items-center justify-content-center px-2 py-3"
                                            href="tel:+91-8527471031"
                                            style="background-color: #173E85; text-decoration: none; border-radius: 5px;font-style: normal; font-weight: 400; font-size: 14px; color: #ffffff; height:45px;">
                                            <img alt="phone" class="img-fluid pe-2 ps-2 me-xl-2 me-lg-0 me-2"
                                                loading="lazy" src="./img/phone.png">+91-8527471031</a>
                                    </div>
                                </div>
                            </div>

                            <div class="row justify-content-center mt-3 mt-lg-5">
                                <div class="col-12 col-md-8">
                                    <img alt="" class="w-100" loading="lazy" src="./img/new-dscr-img/ks_map_img.png">
                                </div>
                            </div>

                        </div>
                    </div>
                    <div aria-labelledby="faq-tab" class="tab-pane fade show" id="faq" role="tabpanel">
                        <div class="accordion" id="accordion6">
                            <div style="background-color:#fff">
                                <!-- Question 1 -->
                                <div aria-expanded="true" class="card-header" data-bs-toggle="collapse"
                                    href="#collapseFAQOne"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        How to get the AI API key?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="card-body collapse show" data-bs-parent="#accordion6" id="collapseFAQOne">
                                    <p style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                        You have to add your valid email id in General Settings under "AI API Key"
                                        Section and click on "Get API Key". You will receive the Key over mail once your
                                        credentials are verified.
                                    </p>
                                </div>
                                <!-- Question 2 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQTwo"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        Do you send data to use in AI systems?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="card-body collapse" data-bs-parent="#accordion6" id="collapseFAQTwo">
                                    <p style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                        We never send the data to AI; neither AI needs data to generate the charts. We
                                        only send the table structure to the Ksolves AI Server. Hence, we ensure data
                                        security of the customers.For AI insights feature we simply send the summarized
                                        and unlabeled data to our AI server for processing. Since the data is unlabeled,
                                        our AI server does not store it. Consequently, neither we nor our AI server can
                                        determine the company to which the data pertains.
                                    </p>
                                </div>
                                <!-- Question 3 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQThree"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        Can we use AI features free of cost after purchasing the module?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="card-body collapse" data-bs-parent="#accordion6" id="collapseFAQThree">
                                    <p style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                        Yes, you can use all AI features after purchasing the module.
                                    </p>
                                </div>
                                <!-- Question 4 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQFour"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        Is this app compatible with Odoo Enterprise?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="card-body collapse" data-bs-parent="#accordion6" id="collapseFAQFour">
                                    <p style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                        Yes, our app works with Odoo Enterprise as well as Community.
                                    </p>
                                </div>
                                <!-- Question 5 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQFive"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        Need some customization in this app, whom to contact?
                                        <img alt="" class="float-right pt-2 " src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="card-body collapse" data-bs-parent="#accordion6" id="collapseFAQFive">
                                    <p style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                        Please drop <NAME_EMAIL> or raise a ticket through Odoo store
                                        itself.
                                    </p>
                                </div>
                                <!-- Question 6 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQSix"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        Is it possible to select a created Ninja Dashboard as the user's home page?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="collapse" data-bs-parent="#accordion6" id="collapseFAQSix">
                                    <div class="card-body">
                                        <p
                                            style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                            When you create a dashboard, an action is created with name : Menu Name +
                                            "Action". So for example if you create a dashboard with name : Project
                                            Dashboard. Go to any user page -> Preference -> Home Action and can select
                                            action with name : Project Dashboard Action.
                                        </p>
                                    </div>
                                </div>
                                <!-- Question 7 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQSeven"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        Do you provide any free support?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="collapse" data-bs-parent="#accordion6" id="collapseFAQSeven">
                                    <div class="card-body">
                                        <p
                                            style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                            Yes, we do provide free support for 90 days for any queries or any bug/issue
                                            fixing.
                                        </p>
                                    </div>
                                </div>

                                <!-- Question 8 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQEight"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        I have mistakenly created a chart item on Sales Dashboard, instead of recreating
                                        it
                                        again, Can I move it or duplicate?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="collapse" data-bs-parent="#accordion6" id="collapseFAQEight">
                                    <div class="card-body">
                                        <p
                                            style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                            Yes, we have provided a feature to move and duplicate any item from one
                                            dashboard to another. When you mouseover any item you will see a copy icon
                                            or
                                            click on edit icon and from top center of screen there is Actions dropdown
                                            with
                                            options to move/duplicate items.
                                        </p>
                                    </div>
                                </div>

                                <!-- Question 9 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQNine"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        How to make a bar chart full size on the screen?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="collapse" data-bs-parent="#accordion6" id="collapseFAQNine">
                                    <div class="card-body">
                                        <p
                                            style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                            Pretty simple, just click on Gear like icon present at top right of screen
                                            and
                                            then click on edit layout option. Now, when you mouseover any tile or chart
                                            you
                                            will see resize icon at bottom right of item using which you can resize
                                            items as
                                            per your need.
                                        </p>
                                    </div>
                                </div>


                                <!-- Question 10 -->
                                <div aria-expanded="false" class="card-header collapsed" data-bs-parent="#accordion6"
                                    data-bs-toggle="collapse" href="#collapseFAQTen"
                                    style="background-color:transparent; border:none; border-top:1px solid #E8E8E8">
                                    <a class="card-title text-decoration-none d-flex align-content-center justify-content-between mb-0 cursor-pointer"
                                        style="font-size:18px; line-height:30px; font-weight:500; color: #4F4F4F;">
                                        Is it common behavior for Dashboard Ninja to change the layout of dashboards
                                        automatically after some time?
                                        <img alt="" class="float-right pt-2" src="./img/new-dscr-img/down-arrow.svg">
                                    </a>
                                </div>
                                <div class="collapse" data-bs-parent="#accordion6" id="collapseFAQTen">
                                    <div class="card-body">
                                        <p
                                            style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                            No, it is not typical behavior for Odoo Dashboard Ninja to automatically
                                            change the
                                            layout of
                                            dashboards unless there are underlying customizations to the code, record
                                            rules,
                                            group access,
                                            or custom addons/themes on your server that may be conflicting with
                                            Dashboard
                                            Ninja.
                                        </p>
                                        <p
                                            style="font-size:16px; line-height:27px; color:#414141; font-weight:normal; ">
                                            If you are experiencing unexpected changes to your dashboard layouts, we
                                            recommend reaching out to our
                                            <NAME_EMAIL>. We can help you debug the issue on your
                                            server
                                            and provide a suitable
                                            solution to address the problem.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div aria-labelledby="releases-tab" class="tab-pane fade show" id="releases" role="tabpanel">
                        <div class="py-2" style="margin-top: -38px; margin-bottom: -32px;">
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Latest Release
                                        2.3.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        2nd October,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Enhancement
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>New Theme - Dashboard Ninja with AI Launched with New Theme<span
                                                    style="color:#7A7979;">.
                                                </span>
                                            <li>Feature - Introduced new features - Chat With AI, Internal Chat,
                                                Bookmark Dashboard and Chart Insights<span style="color:#7A7979;">.
                                                </span>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.2.3
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        14th August,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Fixes
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fix - Custom filter search bar UI issue<span style="color:#7A7979;">.
                                                </span>
                                            <li>Fix - Automatically opening the data while exporting the item (KPI and
                                                Tile)<span style="color:#7A7979;">. </span>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.2.2
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        9th August,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Improvements
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Storing date filter and custom filters in cache to prevent them from
                                                disappearing after reloading<span style="color:#7A7979;">. </span>
                                            <li>Fix - When selecting the model in item it is not showing all models<span
                                                    style="color:#7A7979;">. </span>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.2.1
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        6th August,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Fixes
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fix -Showing error while importing dashboard or item<span
                                                    style="color:#7A7979;">. </span>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.2.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        22nd July,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Feature
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Enable users to set the target<span style="color:#7A7979;">. </span>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.1.4
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        19th July,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Fixes
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fix - Code conflict with List View Manager module(showing border in odoo
                                                default list view header)<span style="color:#7A7979;">. </span>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.1.3
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        12th July,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fix - Barchart and line chart UI issue when using negative multiplier
                                                value<span style="color:#7A7979;">. </span>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.1.2
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        4th July,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fixed dashboard multi layout selection not clickable issue<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Fixed List item code conflict with List View Manager module<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>

                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.1.1
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        2nd July,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fixed the KPI item data calculation issues<span style="color:#7A7979;">.
                                                </span>
                                            </li>
                                            <li>Fixed the tooltip UI issue in charts when user uses RTL language<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Improved the working of Custom Filter for non-admin users<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Fixed the amount calculation issue in tile when selecting the number
                                                system as the exact amount<span style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Fixed the fill temporal value issue when using datetime field in group
                                                by<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>

                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.1.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        13th June,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Feature
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Enable users to use %UID and %MYCOMPANY in Domain<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Enable List view in Drill Down<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.0.1
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        11th June,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Enhancement
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Code Enhancement for Excel and CSV<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        2.0.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        3rd June,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Feature
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Show/Hide Data values of charts<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.9.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        22nd May,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Feature
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Extract Chart Insights With AI<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.8.4
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        21st May,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fixed charts generation using AI<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.8.3
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        6th May,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fixed Warning logs<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.8.2
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        29th April,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Improvement
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Improved custom icon functionality for Tile item.<span
                                                    style="color:#7A7979;">. </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.8.1
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        25th April,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Issue fixed: sort by field while creating the Bar Chart.<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Document module issue fixed<span style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Field Data reading issue fixed<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.8.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        22nd April,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Feature
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Company Wise Items<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.7.6
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        19th April,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fixed Date custom filter issue<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.7.5
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        16th April,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Date conversion issue fixed in any other languages<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.7.4
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        12th April,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Bug Fixed
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Date conversion issue fixed<span style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Legends position issue fixed<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.7.3
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        20th March,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Improvement
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Functionality of the i button for charts<span style="color:#7A7979;">.
                                                </span>
                                            </li>
                                            <li> Manage legends position in Arabic for charts<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                            <li> Manage group by values in Arabic for charts<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                            <li> Dark Theme issue fixed<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.7.2
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        1st March,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Enhancement
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Implemented rotated labels in Horizontal Bar Chart,Bar Chart,Line
                                                Chart,Area Chart and Bullet chart<span style="color:#7A7979;">. </span>
                                            </li>
                                            <li> Manage legends position in Flower Chart,Radial Chart,Radar Chart and
                                                Polar Chart<span style="color:#7A7979;">. </span>
                                            </li>
                                            <li> Manage legend based on group by in Pie and Doughnut Chart<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.7.1
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        26th February,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Fixes
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fixed KPI sales target issue<span style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Fixed KPI progress bar view issue<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.7.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        15th February,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 0px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Feature
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Added custom filters on Dashboard<span style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div
                                        style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                        Fixes
                                    </div>
                                    <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                        <li>Fixed multi-dashboard layout bugs<span style="color:#7A7979;">. </span>
                                        </li>
                                    </ul>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.6.1
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        12th February,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Fixes
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Fixed the log issues<span style="color:#7A7979;">. </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.6.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        5th February,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Feature
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Added New UI theme for Dashboard<span style="color:#7A7979;">. </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.5.2
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        30th January,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Enhancement
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>UI changes for Demo user<span style="color:#7A7979;">. </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.5.1
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        29th January,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Enhancement
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Added Props for AI Dashboard.<span style="color:#7A7979;">. </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.5.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        24th January,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Features
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Added Pre-defined Filter on Dashboard.<span style="color:#7A7979;">.
                                                </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.4.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        19th January,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Features
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Added Date Filter on Dashboard.<span style="color:#7A7979;">. </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.3.1
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        17th January,
                                        2024
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                Improvement
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Deprecated method removed<span style="color:#7A7979;">. </span>
                                            </li>


                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.3.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        7th December,
                                        2023
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Features
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Added Excel and CSV functionality for the charts.<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Added Drill Down functionality for charts.<span style="color:#7A7979;">
                                                </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.2.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        1st December,
                                        2023
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Features
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Real time streaming<span style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Delete, Move and update item without reloading.<span
                                                    style="color:#7A7979;"> </span>
                                            </li>

                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.1.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        30th November,
                                        2023
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New Features
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li>Generate Dashboard items with AI<span style="color:#7A7979;">. </span>
                                            </li>
                                            <li>Generate Complete Dashboard with AI<span style="color:#7A7979;">.
                                                </span>
                                            </li>
                                            <li>Generate Dashboard items using keywords with AI<span
                                                    style="color:#7A7979;">. </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4
                                        style="font-family:'Inter', sans-serif; font-size:16px; color:#514F4F; margin:0; line-height:26px">
                                        Release
                                        1.0.0
                                    </h4>
                                    <span style="font-size:14px; color:#7A7979; display:block; margin-bottom:20px">
                                        27th November,
                                        2023
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div
                                                style="display:inline-block; padding:0px 8px; color:#514F4F; background-color:#FFD8D8; border-radius:20px">
                                                New version
                                            </div>
                                        </div>
                                        <ul class="ps-3" style="color:#7A7979; font-family:'Inter', sans-serif">
                                            <li><span style="color:#7A7979;"> Latest version 17 release of Dashboard
                                                    Ninja .</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div style="padding:0px 0 10px; border-bottom:1px solid #E3E3E3">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-md-4 p-2 mb-3 bg-white shadow" id="features"
                style="border-radius: 20px; font-family: 'Inter', sans-serif;">
                <div class="row pt-4 pb-3">
                    <div class="col-md-12">
                        <h1
                            style="font-family: 'Inter', sans-serif; font-style: normal; word-wrap: break-word;font-weight: 600; font-size: 36px; line-height: 120%;  text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #333333; font-size: calc(1.1rem + 1vw);">
                            All
                            <span style="color: #2B5FB2; font-size: calc(1.1rem + 1vw);">Features</span>
                        </h1>
                    </div>
                </div>
                <div class="px-md-5 pt-5 p-3 pb-4"
                    style="background-image: url(./img/af_bg.png);background-repeat: no-repeat; background-size: cover;">
                    <div class="row">
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2 ">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px; background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100 ">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/Group 2315.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Generate Complete<br style="line-height: 0;">
                                                Dashboard With AI
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/Group 2316.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Generate Items<br style="line-height: 0;">
                                                With AI
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/Group 2317.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Generate Keyword-Focused <br style="line-height: 0;">
                                                Dashboard Items With AI
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/Group 2316.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Extract Chart Insights<br style="line-height: 0;">
                                                With AI
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-xl-4 mb-2 px-md-2 ">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px; background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100 ">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_1.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Dynamic & Animated Reporting Dashboards
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_2.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Real-Time<br style="line-height: 0;">
                                                Streaming Dashboard
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_3.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Responsive: Fluid<br style="line-height: 0;">
                                                & Flexible Layout
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_4.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Advanced<br style="line-height: 0;">
                                                Date Filter
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_5.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 pe-md-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0 px-md-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Download Dashboard Items
                                                (Excel, CSV, PDF, PNG)
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_6.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 px-md-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Export & Import Dashboards
                                                or Specific Dashboard Items
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_7.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Access<br style="line-height: 0;">
                                                Control
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_8.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Predefined<br style="line-height: 0;">
                                                Dashboards
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_9.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Drill Down/<br style="line-height: 0;">
                                                Drill Up Data
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_10.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Community/Enterprise/<br style="line-height: 0;">
                                                Odoo.sh Support
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_11.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Duplicate/<br style="line-height: 0;">
                                                Move Dashboard
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_12.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Customizable<br style="line-height: 0;">
                                                Dashboard Tile Items
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_13.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0 px-md-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Data Filtration -<br style="line-height: 0;">
                                                Group By, Limit & Sort By
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_14.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Dashboard Item -<br style="line-height: 0;">
                                                2 List View Styles
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_15.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Design Multiple<br style="line-height: 0;">
                                                Dashboards
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_16.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Dashboard Personalization<br style="line-height: 0;">
                                                /Configuration
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_17.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Personalized & Interactive
                                                Dashboard Layouts
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_18.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                View Dashboard<br style="line-height: 0;">
                                                Item Data
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_19.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Multi-Color<br style="line-height: 0;">
                                                Themes
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon_20.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Set Targets
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon21.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Unique User <br style="line-height: 0;">
                                                ID (%UID)
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-position:center;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon22.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Authentic<br style="line-height: 0;">
                                                %MYCOMPANY Filter
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon23.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                To do item
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon24.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Instant<br style="line-height: 0;">
                                                Edit Mode
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon25.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Number<br style="line-height: 0;">
                                                Systems
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon26.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Show/Hide<br style="line-height: 0;">
                                                Data Values
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon27.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Monetary/<br style="line-height: 0;">
                                                Custom Units
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon28.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Multi-Company<br style="line-height: 0;"> Support
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon29.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Multi-Language <br style="line-height: 0;">Support
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-4 mb-2 px-md-2">
                            <div class="p-2 d-flex align-items-center"
                                style="border-radius: 5px;height:85px;background-color: #485c69; background: linear-gradient(89.9deg, #3A3D55 0.04%, #527479 99.85%);">
                                <div class="row w-100">
                                    <div class="col-3 justify-content-center d-flex align-items-center">
                                        <div>
                                            <img alt="" class="img-fluid" loading="lazy" src="./img/icon30.png">
                                        </div>
                                    </div>
                                    <div class="col-9 d-flex align-items-center ps-0 ">
                                        <div class="w-100">
                                            <p class="pb-0 mb-0"
                                                style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 16px; line-height: 140%; /* or 22px */ text-transform: capitalize; color: #FFFFFF;">
                                                Multi-Currency<br style="line-height: 0;"> Support
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--        <div class="positiove-relative">-->
            <!--                <div style="border-radius: 20px; background-color: #F8F9FA;" class="pt-lg-5 pb-lg-4 py-4 px-3 px-sm-4">-->
            <!--                    <div>-->
            <!--                        <h4 style="color: #2B5FB2; text-align: center; font-size: 48px; font-style: normal; font-weight: 700; line-height: 120%; text-transform: capitalize; font-size: calc(2.13rem + 1vw);"-->
            <!--                            class="mb-lg-4 mb-3">-->
            <!--                            Explore Dashboard Ninja Addons-->
            <!--                        </h4>-->
            <!--                    </div>-->
            <!--                    <div class="row">-->
            <!--                        <div class="col-lg-6 col-sm-6 col-12 mb-3">-->
            <!--                            <a target="_abc" href="https://apps.odoo.com/apps/modules/16.0/ks_website_dashboard_ninja/"-->
            <!--                               class="h-100 d-flex align-items-center justify-content-start" style="border-radius: 5px;-->
            <!--                            background-color: #527479; text-decoration: none; padding: 16px 0;">-->
            <!--                                <div class="mx-sm-3 mx-2">-->
            <!--                                    <img src="./img/new-add-img/e2.png" alt="icon" width="48px" height="48px">-->
            <!--                                </div>-->
            <!--                                <div class="">-->
            <!--                                    <p class="mb-0 pe-1"-->
            <!--                                       style="color: #FFF; font-size: 16px; font-style: normal; font-weight: 600; line-height: 25.6px; letter-spacing: 0.32px;">-->
            <!--                                        Website Dashboard-->
            <!--                                        Ninja-->
            <!--                                    </p>-->
            <!--                                </div>-->
            <!--                            </a>-->
            <!--                        </div>-->
            <!--                        <div class="col-lg-6 col-sm-6 col-12 mb-3">-->
            <!--                            <a target="_abc" href="https://apps.odoo.com/apps/modules/16.0/ks_sale_dashboard_ninja/"-->
            <!--                               class="h-100 d-flex align-items-center justify-content-start" style="border-radius: 5px;-->
            <!--                            background-color: #527479; text-decoration: none; padding: 16px 0;">-->
            <!--                                <div class="mx-sm-3 mx-2">-->
            <!--                                    <img src="./img/new-add-img/e3.png" alt="icon" width="48px" height="48px">-->
            <!--                                </div>-->
            <!--                                <div class="">-->
            <!--                                    <p class="mb-0 pe-1"-->
            <!--                                       style="color: #FFF; font-size: 16px; font-style: normal; font-weight: 600; line-height: 25.6px; letter-spacing: 0.32px;">-->
            <!--                                        Sale Dashboard-->
            <!--                                        Ninja-->
            <!--                                    </p>-->
            <!--                                </div>-->
            <!--                            </a>-->
            <!--                        </div>-->
            <!--                        <div class="col-lg-6 col-sm-6 col-12 mb-3">-->
            <!--                            <a target="_abc" href="https://apps.odoo.com/apps/modules/16.0/ks_inventory_dashboard/"-->
            <!--                               class="h-100 d-flex align-items-center justify-content-start" style="border-radius: 5px;-->
            <!--                            background-color: #527479; text-decoration: none; padding: 16px 0;">-->
            <!--                                <div class="mx-sm-3 mx-2">-->
            <!--                                    <img src="./img/new-add-img/e4.png" alt="icon" width="48px" height="48px">-->
            <!--                                </div>-->
            <!--                                <div class="">-->
            <!--                                    <p class="mb-0 pe-1"-->
            <!--                                       style="color: #FFF; font-size: 16px; font-style: normal; font-weight: 600; line-height: 25.6px; letter-spacing: 0.32px;">-->
            <!--                                        Inventory Dashboard-->
            <!--                                        Ninja-->
            <!--                                    </p>-->
            <!--                                </div>-->
            <!--                            </a>-->
            <!--                        </div>-->
            <!--                        <div class="col-lg-6 col-sm-6 col-12 mb-3">-->
            <!--                            <a target="_abc" href="https://apps.odoo.com/apps/modules/16.0/ks_dn_live_update/"-->
            <!--                               class="h-100 d-flex align-items-center justify-content-start" style="border-radius: 5px;-->
            <!--                            background-color: #527479; text-decoration: none; padding: 16px 0;">-->
            <!--                                <div class="mx-sm-3 mx-2">-->
            <!--                                    <img src="./img/new-add-img/e5.png" alt="icon" width="48px" height="48px">-->
            <!--                                </div>-->
            <!--                                <div class="">-->
            <!--                                    <p class="mb-0 pe-1"-->
            <!--                                       style="color: #FFF; font-size: 16px; font-style: normal; font-weight: 600; line-height: 25.6px; letter-spacing: 0.32px;">-->
            <!--                                        Dashboard Ninja-->
            <!--                                        Live Update-->
            <!--                                    </p>-->
            <!--                                </div>-->
            <!--                            </a>-->
            <!--                        </div>-->
            <!--                        <div class="col-lg-6 col-sm-6 col-12 mb-3">-->
            <!--                            <a target="_abc" href="https://apps.odoo.com/apps/modules/16.0/ks_dn_advance/"-->
            <!--                               class="h-100 d-flex align-items-center justify-content-start" style="border-radius: 5px;-->
            <!--                            background-color: #527479; text-decoration: none; padding: 16px 0;">-->
            <!--                                <div class="mx-sm-3 mx-2">-->
            <!--                                    <img src="./img/new-add-img/e6.png" alt="icon" width="48px" height="48px">-->
            <!--                                </div>-->
            <!--                                <div class="">-->
            <!--                                    <p class="mb-0 pe-1"-->
            <!--                                       style="color: #FFF; font-size: 16px; font-style: normal; font-weight: 600; line-height: 25.6px; letter-spacing: 0.32px;">-->
            <!--                                        Dashboard Ninja-->
            <!--                                        Advance-->
            <!--                                    </p>-->
            <!--                                </div>-->
            <!--                            </a>-->
            <!--                        </div>-->
            <!--                        <div class="col-lg-6 col-sm-6 col-12 mb-3">-->
            <!--                            <a target="_abc" href="https://apps.odoo.com/apps/modules/16.0/ks_crm_dashboard_ninja/"-->
            <!--                               class="h-100 d-flex align-items-center justify-content-start" style="border-radius: 5px;-->
            <!--                            background-color: #527479; text-decoration: none; padding: 16px 0;">-->
            <!--                                <div class="mx-sm-3 mx-2">-->
            <!--                                    <img src="./img/new-add-img/e7.png" alt="icon" width="48px" height="48px">-->
            <!--                                </div>-->
            <!--                                <div class="">-->
            <!--                                    <p class="mb-0 pe-1"-->
            <!--                                       style="color: #FFF; font-size: 16px; font-style: normal; font-weight: 600; line-height: 25.6px; letter-spacing: 0.32px;">-->
            <!--                                        CRM Dashboard-->
            <!--                                        Ninja-->
            <!--                                    </p>-->
            <!--                                </div>-->
            <!--                            </a>-->
            <!--                        </div>-->
            <!--                        <div class="col-lg-6 col-sm-6 col-12 mb-3">-->
            <!--                            <a target="_abc" href="https://apps.odoo.com/apps/modules/16.0/ks_dashboard_theme/"-->
            <!--                               class="h-100 d-flex align-items-center justify-content-start" style="border-radius: 5px;-->
            <!--                            background-color: #527479; text-decoration: none; padding: 16px 0;">-->
            <!--                                <div class="mx-sm-3 mx-2">-->
            <!--                                    <img src="./img/new-add-img/e8.png" alt="icon" width="48px" height="48px">-->
            <!--                                </div>-->
            <!--                                <div class="">-->
            <!--                                    <p class="mb-0 pe-1"-->
            <!--                                       style="color: #FFF; font-size: 16px; font-style: normal; font-weight: 600; line-height: 25.6px; letter-spacing: 0.32px;">-->
            <!--                                        Dashboard Ninja-->
            <!--                                        Theme-->
            <!--                                    </p>-->
            <!--                                </div>-->
            <!--                            </a>-->
            <!--                        </div>-->
            <!--                        <div class="col-lg-6 col-sm-6 col-12 mb-3">-->
            <!--                            <a target="_abc" href="https://apps.odoo.com/apps/modules/16.0/ks_account_dashboard/"-->
            <!--                               class="h-100 d-flex align-items-center justify-content-start" style="border-radius: 5px;-->
            <!--                            background-color: #527479; text-decoration: none; padding: 16px 0;">-->
            <!--                                <div class="mx-sm-3 mx-2">-->
            <!--                                    <img src="./img/new-add-img/e9.png" alt="icon" width="48px" height="48px">-->
            <!--                                </div>-->
            <!--                                <div class="">-->
            <!--                                    <p class="mb-0 pe-1"-->
            <!--                                       style="color: #FFF; font-size: 16px; font-style: normal; font-weight: 600; line-height: 25.6px; letter-spacing: 0.32px;">-->
            <!--                                        Account Dashboard-->
            <!--                                        Ninja-->
            <!--                                    </p>-->
            <!--                                </div>-->
            <!--                            </a>-->
            <!--                        </div>-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            </div>-->

            <section>
                <div class="shadow pt-lg-5 pb-4 bg-white" id="free_support"
                    style="margin-top: 42px; border-radius: 20px;">
                    <div class="row d-flex align-items-center justify-content-center">
                        <div class="col-11 px-0 mx-auto pb-lg-5 position-relative overflow-hidden"
                            style="border-radius: 20px; background-color: #fffefd;">
                            <img alt="bg-img" class="h-100 w-100 position-absolute fix-bottom img-fluid" loading="lazy"
                                src="./img/support/support-bg.png" style="object-fit: cover;top: 0;left: 0;">
                            <div class="row position-relative pt-lg-5 px-lg-5 pb-lg-3 p-md-4 p-3 mx-auto d-flex align-items-center"
                                style="color: #333333;font-weight: 500;font-size: 16px;width: 95%;">
                                <div class="col-lg-auto col-sm-12">
                                    <img src="./img/support/support.png" width="219" />
                                </div>
                                <div class="col ps-lg-5 ps-0 pt-lg-0 pt-3">
                                    <h5 class="text-start"
                                        style=" font-family: 'Inter', sans-serif;margin-bottom: 15px;font-style: normal;font-weight: 600;font-size: 36px;line-height: 44px;letter-spacing: 0.02em;text-transform: uppercase;color: #FFFFFF;">
                                        Free <span
                                            style="color:#9EFFFF; font-weight: 800; font-family: 'Inter', sans-serif;">90
                                            Days</span>
                                        Support
                                    </h5>
                                    <p
                                        style="font-style: normal;font-weight: 500;font-size: 18px;line-height: 28px;text-transform: capitalize;color: #FFFFFF;margin: 0; font-family: 'Inter', sans-serif;">
                                        Ksolves will provide FREE 90 days support for any doubt, queries, and bug fixing
                                        (excluding data recovery) or any type of issues related to this module. This is
                                        applicable from the date of purchase.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row d-flex align-items-center justify-content-center m-0">
                        <div class="col-12 p-0 pt-4 overflow-hidden text-center">
                            <p
                                style="font-style: normal;font-weight: 500;font-size: 18px;line-height: 28px;letter-spacing: 0.01em;color: #535456;margin: 0;">
                                <span
                                    style="font-style: normal;font-weight: 600;font-size: 24px;line-height: 28px;letter-spacing: 0.01em;color: #283C63;margin: 0 10px 0 0;">Note</span>
                                Extensively Tested on Odoo Vanilla with Ubuntu OS
                            </p>
                        </div>
                    </div>
                </div>

                <div class="shadow bg-white" id="all_suggested-app"
                    style="margin-top: 42px; border-radius: 20px;  padding-top: 41px; padding-bottom: 30px;">
                    <h5 class="text-center"
                        style="color: #2B5FB2; font-weight: 700; font-size: 32px; line-height:43px; margin-bottom: 25px; font-size: calc(1.5rem + 0.4vw); font-family: 'Inter', sans-serif;">
                        <span style="color:#333333; font-family: 'Inter', sans-serif;">Ksolves Suggested Apps</span>
                    </h5>
                    <div class="row d-flex align-items-center justify-content-center">
                        <div class="col-11 pt-lg-3 px-lg-5 pb-lg-2 p-md-4 p-3 position-relative overflow-hidden"
                            style="border-radius: 10px; background-color: #fffefd;">
                            <div class="row position-relative"
                                style="color: #333333; font-weight: 500; font-size: 16px; ">

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_dashboard_ninja/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy"
                                                    src="./img/support/Dashboard-Ninja.png" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    Dashboard Ninja
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_dn_advance/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy"
                                                    src="./img/support/Dashboard-Ninja-Advance.png" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    Dashboard Ninja Advance
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_woocommerce/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy"
                                                    src="./img/support/Odoo-WooCommerce.png" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    Odoo WooCommerce Connector
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/themes/14.0/ks_curved_backend_theme/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy"
                                                    src="./img/support/arc-backend-theme.png" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    Arc Backend Theme
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_gantt_view_base/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy"
                                                    src="./img/support/odoo-base.png" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    Odoo Gantt View Base
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_list_view_manager/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy"
                                                    src="./img/support/list-view-manager.png" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    List View Manager
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_custom_report/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy"
                                                    src="./img/support/report.png" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    ReportMate
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_dynamic_financial_report/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC; border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy" src="./img/support/dfr.png"
                                                    style="border-radius: 5px;" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    Dynamic Financial Report
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pe-lg-0 pe-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_shopify/"
                                        style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3"
                                            style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img alt="icon" height="100" loading="lazy"
                                                    src="./img/support/shopify.png" width="100">
                                            </div>
                                            <div class="w-100 ps-3">
                                                <p class="mb-0"
                                                    style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;">
                                                    Odoo Shopify Connector
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>

                <div class="shadow bg-white p-0" id="free_support"
                    style="margin-top: 42px; border-radius: 20px;  padding-top: 41px; padding-bottom: 41px;">
                    <div class="row d-flex align-items-center justify-content-center m-0">
                        <div class="col-12 px-0 position-relative overflow-hidden"
                            style="border-radius: 20px; background-color: #fffefd;">
                            <img alt="bg-img" class="h-100 w-100 position-absolute fix-bottom img-fluid" loading="lazy"
                                src="./img/support/ks-odoo-services-bg.png" style="object-fit: cover;top: 0;left: 0;">
                            <div class="row position-relative pt-lg-3 px-lg-5 pb-lg-3 p-md-4 p-3 mx-auto d-flex align-items-center"
                                style="color: #333333;font-weight: 500;font-size: 16px;width: 100%;gap: 35px 70px;">
                                <div class="col text-center py-4">
                                    <h5 class="text-center mb-4"
                                        style="font-style: normal;font-weight: 700;font-size: 32px;line-height: 48px;letter-spacing: -0.02em;text-indent: 2px;color: #9EFFFF; font-size: calc(1.5rem + 0.4vw); font-family: 'Inter', sans-serif;">
                                        Ksolves Odoo Services
                                    </h5>
                                    <img class="img-fluid" src="./img/support/services-img.png" width="685" />
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </section>
        </div>
    </section>

    <!-- Bootstrap 5 JS cdn -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>