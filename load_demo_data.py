#!/usr/bin/env python3
"""
Script to manually load demo data for the nebular module
"""

import os
import sys
import logging

# Add the Odoo directory to the Python path
sys.path.append('/Users/<USER>/Documents/Laplace/Projects/odoo18')

import odoo
from odoo import api, SUPERUSER_ID
from odoo.tools import convert

# Configure logging
logging.basicConfig(level=logging.INFO)
_logger = logging.getLogger(__name__)

def load_demo_data():
    """Load demo data for the nebular module"""
    
    # Initialize Odoo
    odoo.tools.config.parse_config([
        '-c', '/Users/<USER>/Documents/Laplace/Projects/odoo18/config/nebular18.conf',
        '-d', 'NEBULAR_18_202509'
    ])
    
    # Get database registry
    registry = odoo.registry('NEBULAR_18_202509')
    
    with registry.cursor() as cr:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Check if device types exist
        device_types = env['nebular.device.type'].search([])
        _logger.info(f"Found {len(device_types)} device types")
        for dt in device_types:
            _logger.info(f"Device Type: {dt.name} ({dt.code})")
        
        # Check if buildings exist
        buildings = env['nebular.building'].search([])
        _logger.info(f"Found {len(buildings)} buildings")
        for building in buildings:
            _logger.info(f"Building: {building.name} ({building.code})")
        
        # Check if demo data is already loaded
        demo_building = env['nebular.building'].search([('code', '=', 'HQ-001')])
        if demo_building:
            _logger.info("Demo data appears to be already loaded")
            return
        
        # Load demo data files manually
        demo_files = [
            '/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_nebular/nebular/data/demo.xml',
            '/Users/<USER>/Documents/Laplace/Projects/odoo18/addons_nebular/nebular/data/event_demo.xml'
        ]
        
        for demo_file in demo_files:
            if os.path.exists(demo_file):
                _logger.info(f"Loading demo data from: {demo_file}")
                try:
                    convert.convert_file(cr, 'nebular', demo_file, {}, 'init', False, 'data')
                    _logger.info(f"Successfully loaded: {demo_file}")
                except Exception as e:
                    _logger.error(f"Error loading {demo_file}: {e}")
            else:
                _logger.error(f"Demo file not found: {demo_file}")
        
        # Commit the transaction
        cr.commit()
        
        # Verify demo data was loaded
        demo_building = env['nebular.building'].search([('code', '=', 'HQ-001')])
        if demo_building:
            _logger.info("Demo data successfully loaded!")
        else:
            _logger.error("Demo data was not loaded properly")

if __name__ == '__main__':
    load_demo_data()
